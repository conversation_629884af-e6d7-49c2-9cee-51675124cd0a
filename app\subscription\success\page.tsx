"use client"

import { useEffect, useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { CheckCircle, Crown, Zap } from "lucide-react"
import { useNotifications } from "@/components/notification-system"

export default function SubscriptionSuccessPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { addNotification } = useNotifications()
  const [loading, setLoading] = useState(true)
  const [subscriptionData, setSubscriptionData] = useState<any>(null)
  
  useEffect(() => {
    const sessionId = searchParams.get('session_id')
    
    if (sessionId) {
      // Verify the subscription with your backend
      const verifySubscription = async () => {
        try {
          const response = await fetch(`/api/verify-subscription?session_id=${sessionId}`, {
            method: 'GET',
          })
          
          const data = await response.json()
          
          if (data.success) {
            setSubscriptionData(data)
            addNotification({
              type: "success",
              title: "Subscription Activated!",
              message: `Welcome to ${data.planName}! Your subscription is now active.`,
            })
          } else {
            addNotification({
              type: "error",
              title: "Subscription Error",
              message: "There was an issue activating your subscription. Please contact support.",
            })
          }
        } catch (error) {
          console.error('Error verifying subscription:', error)
          addNotification({
            type: "error",
            title: "Verification Error",
            message: "Unable to verify your subscription. Please contact support.",
          })
        } finally {
          setLoading(false)
        }
      }
      
      verifySubscription()
    } else {
      setLoading(false)
    }
  }, [searchParams, addNotification])
  
  const getPlanIcon = (planName: string) => {
    if (planName?.toLowerCase().includes('premium')) {
      return <Crown className="h-8 w-8 text-yellow-500" />
    } else if (planName?.toLowerCase().includes('pro')) {
      return <Zap className="h-8 w-8 text-emerald-500" />
    }
    return <CheckCircle className="h-8 w-8 text-emerald-500" />
  }
  
  if (loading) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center p-4">
        <Card className="w-full max-w-md bg-slate-800 border-slate-700">
          <CardContent className="p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-500 mx-auto mb-4"></div>
            <p className="text-slate-300">Verifying your subscription...</p>
          </CardContent>
        </Card>
      </div>
    )
  }
  
  return (
    <div className="min-h-screen bg-slate-900 flex items-center justify-center p-4">
      <Card className="w-full max-w-md bg-slate-800 border-slate-700">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 bg-emerald-500/20 p-3 rounded-full w-16 h-16 flex items-center justify-center">
            {subscriptionData ? getPlanIcon(subscriptionData.planName) : <CheckCircle className="h-8 w-8 text-emerald-500" />}
          </div>
          <CardTitle className="text-2xl text-white">
            {subscriptionData ? 'Subscription Activated!' : 'Welcome to Investry!'}
          </CardTitle>
          <CardDescription>
            {subscriptionData 
              ? `You're now subscribed to ${subscriptionData.planName}`
              : 'Your subscription is being processed'
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {subscriptionData && (
            <div className="bg-slate-700/50 rounded-lg p-4 space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-slate-400">Plan:</span>
                <span className="text-white font-medium">{subscriptionData.planName}</span>
              </div>
              {subscriptionData.trialEnd && (
                <div className="flex justify-between text-sm">
                  <span className="text-slate-400">Trial ends:</span>
                  <span className="text-white">{new Date(subscriptionData.trialEnd).toLocaleDateString()}</span>
                </div>
              )}
              <div className="flex justify-between text-sm">
                <span className="text-slate-400">Status:</span>
                <span className="text-emerald-400 font-medium">Active</span>
              </div>
            </div>
          )}
          
          <div className="text-center text-slate-300">
            <p className="mb-4">
              {subscriptionData?.trialEnd 
                ? "Your free trial has started! Explore all premium features."
                : "Start exploring your new investment tools and features."
              }
            </p>
          </div>
          
          <div className="flex flex-col space-y-2">
            <Button 
              className="w-full bg-emerald-600 hover:bg-emerald-700"
              onClick={() => router.push("/dashboard")}
            >
              Go to Dashboard
            </Button>
            <Button 
              variant="outline" 
              className="w-full border-slate-600 text-slate-300 hover:bg-slate-700"
              onClick={() => router.push("/settings")}
            >
              Manage Subscription
            </Button>
          </div>
          
          <div className="text-center">
            <p className="text-sm text-slate-400">
              Need help? <a href="/support" className="text-emerald-400 hover:underline">Contact Support</a>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
