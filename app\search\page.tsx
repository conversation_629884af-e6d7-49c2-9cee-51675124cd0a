"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { ArrowLeft, Search, TrendingUp, TrendingDown } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { type StockData } from "@/lib/stock-api"
import { getAllCategories } from "@/lib/stock-classifier"

export default function SearchPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const initialCategory = searchParams.get("category")
  const initialQuery = searchParams.get("q")

  const [searchQuery, setSearchQuery] = useState("")
  const [searchResults, setSearchResults] = useState<StockData[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<string | null>(initialCategory)
  const [allCategories] = useState(getAllCategories())

  useEffect(() => {
    if (initialCategory) {
      setSelectedCategory(initialCategory)
      setSearchQuery("")
      handleCategorySearch(initialCategory)
    } else if (initialQuery) {
      setSearchQuery(initialQuery)
      handleSearch(initialQuery)
    }
  }, [initialCategory, initialQuery])

  const handleSearch = async (query: string) => {
    if (!query.trim()) return

    setLoading(true)
    setSelectedCategory(null)
    try {
      // Use our new API-based search
      const response = await fetch(`/api/stocks/search?q=${encodeURIComponent(query)}&limit=20`)

      if (!response.ok) {
        throw new Error(`Search failed: ${response.statusText}`)
      }

      const result = await response.json()

      if (result.error) {
        throw new Error(result.error)
      }

      // Convert API response to StockData format
      const stockData: StockData[] = result.data.map((stock: any) => ({
        symbol: stock.symbol || stock.ticker,
        name: stock.name || stock.company_name,
        price: stock.price || 0,
        change: stock.change || 0,
        changePercent: stock.changePercent || stock.change_percent || 0,
        volume: stock.volume || 0,
        marketCap: stock.marketCap || stock.market_cap || 0,
        category: stock.category || stock.sector || 'Unknown',
        tags: stock.tags || [],
        lastUpdated: stock.lastUpdated || stock.last_updated,
        source: stock.source || 'database'
      }))

      setSearchResults(stockData)
    } catch (error) {
      console.error("Search error:", error)
      setSearchResults([])
    } finally {
      setLoading(false)
    }
  }

  const handleCategorySearch = async (category: string) => {
    setLoading(true)
    setSelectedCategory(category)
    setSearchQuery("")
    try {
      // Search by category using our API
      const response = await fetch(`/api/stocks/search?q=${encodeURIComponent(category)}&limit=20`)

      if (!response.ok) {
        throw new Error(`Category search failed: ${response.statusText}`)
      }

      const result = await response.json()

      if (result.error) {
        throw new Error(result.error)
      }

      // Convert API response to StockData format
      const stockData: StockData[] = result.data.map((stock: any) => ({
        symbol: stock.symbol || stock.ticker,
        name: stock.name || stock.company_name,
        price: stock.price || 0,
        change: stock.change || 0,
        changePercent: stock.changePercent || stock.change_percent || 0,
        volume: stock.volume || 0,
        marketCap: stock.marketCap || stock.market_cap || 0,
        category: stock.category || stock.sector || 'Unknown',
        tags: stock.tags || [],
        lastUpdated: stock.lastUpdated || stock.last_updated,
        source: stock.source || 'database'
      }))

      setSearchResults(stockData)
    } catch (error) {
      console.error("Category search error:", error)
      setSearchResults([])
    } finally {
      setLoading(false)
    }
  }

  const handleStockClick = (symbol: string) => {
    router.push(`/investments/${symbol.toLowerCase()}`)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch(searchQuery)
    }
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-6 py-8 max-w-4xl">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.back()}
            className="text-muted-foreground hover:text-foreground"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="flex-1">
            <h1 className="text-3xl font-bold text-foreground">
              {selectedCategory ? `${selectedCategory} Investments` : "Search Investments"}
            </h1>
            <p className="text-muted-foreground">
              {selectedCategory
                ? `Browse all ${selectedCategory.toLowerCase()} investments`
                : "Find stocks and ETFs by name, symbol, or category"}
            </p>
          </div>
        </div>

        {/* Search Bar */}
        <div className="mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={
                selectedCategory
                  ? `Searching "${selectedCategory}" - Enter any other keywords...`
                  : "Search stocks, ETFs, or keywords..."
              }
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyDown={handleKeyDown}
              className="pl-10 bg-background border-border text-foreground"
            />
          </div>
          <div className="flex gap-2 mt-3">
            <Button
              onClick={() => handleSearch(searchQuery)}
              className="bg-emerald-500 hover:bg-emerald-600"
              disabled={!searchQuery.trim() || loading}
            >
              {loading ? "Searching..." : "Search"}
            </Button>
            {selectedCategory && (
              <Button
                variant="outline"
                onClick={() => {
                  setSelectedCategory(null)
                  setSearchQuery("")
                  setSearchResults([])
                }}
                className="bg-transparent border-border"
              >
                Clear Category
              </Button>
            )}
          </div>
        </div>

        {/* Categories */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-foreground mb-4">Browse by Category</h2>
          <div className="flex flex-wrap gap-2">
            {allCategories.slice(0, 20).map((category) => (
              <Badge
                key={category}
                onClick={() => handleCategorySearch(category)}
                className={`cursor-pointer transition-colors ${
                  selectedCategory === category
                    ? "bg-emerald-500 text-white hover:bg-emerald-600"
                    : "bg-slate-700 text-slate-300 hover:bg-slate-600"
                }`}
              >
                {category}
              </Badge>
            ))}
          </div>
        </div>

        {/* Search Results */}
        {searchResults.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-foreground">
                {selectedCategory ? `${selectedCategory} Investments` : "Search Results"}
              </h2>
              <span className="text-sm text-muted-foreground">
                {searchResults.length} result{searchResults.length !== 1 ? "s" : ""}
              </span>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              {searchResults.map((stock) => (
                <Card
                  key={stock.symbol}
                  onClick={() => handleStockClick(stock.symbol)}
                  className="bg-card border-border cursor-pointer hover:bg-card/70 transition-colors"
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-lg text-foreground">{stock.symbol}</CardTitle>
                        <p className="text-sm text-muted-foreground">
                          {stock.classification?.name || `${stock.symbol} Inc.`}
                        </p>
                        <p className="text-xs text-muted-foreground mt-1">
                          {stock.classification?.industry} • {stock.classification?.type}
                        </p>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-foreground">${stock.price.toFixed(2)}</div>
                        <div
                          className={`text-sm flex items-center gap-1 ${
                            stock.change >= 0 ? "text-emerald-400" : "text-red-400"
                          }`}
                        >
                          {stock.change >= 0 ? (
                            <TrendingUp className="h-3 w-3" />
                          ) : (
                            <TrendingDown className="h-3 w-3" />
                          )}
                          {stock.change >= 0 ? "+" : ""}
                          {stock.changePercent.toFixed(2)}%
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {stock.classification?.categories && (
                      <div className="flex flex-wrap gap-1">
                        {stock.classification.categories.slice(0, 3).map((category) => (
                          <Badge
                            key={category}
                            variant="outline"
                            className="text-xs border-border text-muted-foreground"
                          >
                            {category}
                          </Badge>
                        ))}
                        {stock.classification.categories.length > 3 && (
                          <Badge variant="outline" className="text-xs border-border text-muted-foreground">
                            +{stock.classification.categories.length - 3} more
                          </Badge>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* No Results */}
        {!loading && searchResults.length === 0 && (searchQuery || selectedCategory) && (
          <div className="text-center py-12">
            <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-foreground mb-2">No Results Found</h3>
            <p className="text-muted-foreground">
              Try searching with different keywords or browse our categories above.
            </p>
          </div>
        )}

        {/* Loading */}
        {loading && (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-400 mx-auto mb-4"></div>
            <p className="text-muted-foreground">Searching investments...</p>
          </div>
        )}
      </div>
    </div>
  )
}
