/**
 * PortfolioFallback - Provides fallback portfolio generation when LLM fails
 */

import type { IPortfolioFallback, PromptData } from './types'
import type { PersonalizedPortfolio } from '@/lib/portfolio-generator'
import { generatePersonalizedPortfolio } from '@/lib/portfolio-generator'

export class PortfolioFallback implements IPortfolioFallback {
  private isEnabled: boolean

  constructor(isEnabled: boolean = true) {
    this.isEnabled = isEnabled
  }

  /**
   * Generate a fallback portfolio using the original rule-based system
   */
  async generateFallbackPortfolio(data: PromptData): Promise<PersonalizedPortfolio> {
    try {
      if (!this.isEnabled) {
        throw new Error('Fallback portfolio generation is disabled')
      }

      // Use the existing rule-based portfolio generator as fallback
      const portfolio = generatePersonalizedPortfolio(
        data.surveyData,
        data.userId,
        data.userMajor
      )

      // Add a note to the rationale indicating this is a fallback
      const fallbackPortfolio: PersonalizedPortfolio = {
        ...portfolio,
        rationale: `${portfolio.rationale}\n\nNote: This portfolio was generated using our rule-based system as a fallback when our AI service was unavailable.`
      }

      return fallbackPortfolio
    } catch (error) {
      console.error('PortfolioFallback.generateFallbackPortfolio error:', error)
      
      // If even the fallback fails, return a very basic conservative portfolio
      return this.generateEmergencyPortfolio(data)
    }
  }

  /**
   * Check if fallback is available
   */
  isAvailable(): boolean {
    return this.isEnabled
  }

  /**
   * Enable or disable fallback
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled
  }

  /**
   * Generate an emergency portfolio when everything else fails
   */
  private generateEmergencyPortfolio(data: PromptData): PersonalizedPortfolio {
    const { surveyData } = data
    const riskLevel = this.getRiskLevel(surveyData.riskTolerance)
    
    // Create a very basic, safe portfolio
    const emergencyAllocations = this.getEmergencyAllocations(riskLevel)
    
    return {
      allocations: emergencyAllocations,
      riskLevel: riskLevel.charAt(0).toUpperCase() + riskLevel.slice(1),
      expectedReturn: this.getExpectedReturn(riskLevel),
      strategy: this.getEmergencyStrategy(riskLevel),
      rebalanceFrequency: 'Quarterly',
      rationale: `This is an emergency portfolio generated when our primary systems were unavailable. It provides a basic, diversified allocation suitable for your risk tolerance level. We recommend reviewing and customizing this portfolio when our full service is restored.`
    }
  }

  /**
   * Get risk level from tolerance number
   */
  private getRiskLevel(riskTolerance: number): 'conservative' | 'moderate' | 'aggressive' {
    if (riskTolerance <= 2) return 'conservative'
    if (riskTolerance <= 3) return 'moderate'
    return 'aggressive'
  }

  /**
   * Get emergency allocations based on risk level
   */
  private getEmergencyAllocations(riskLevel: string) {
    const baseAllocations = {
      conservative: [
        { symbol: 'VTI', name: 'Vanguard Total Stock Market ETF', allocation: 40, category: 'ETFs / Index Funds', rationale: 'Broad market exposure with low fees' },
        { symbol: 'BND', name: 'Vanguard Total Bond Market ETF', allocation: 40, category: 'Bonds', rationale: 'Stable income and capital preservation' },
        { symbol: 'VTIAX', name: 'Vanguard Total International Stock Index', allocation: 15, category: 'International', rationale: 'International diversification' },
        { symbol: 'VNQ', name: 'Vanguard Real Estate ETF', allocation: 5, category: 'REITs', rationale: 'Real estate exposure for inflation protection' }
      ],
      moderate: [
        { symbol: 'VTI', name: 'Vanguard Total Stock Market ETF', allocation: 50, category: 'ETFs / Index Funds', rationale: 'Core equity holding' },
        { symbol: 'BND', name: 'Vanguard Total Bond Market ETF', allocation: 25, category: 'Bonds', rationale: 'Stability and income' },
        { symbol: 'VTIAX', name: 'Vanguard Total International Stock Index', allocation: 20, category: 'International', rationale: 'Global diversification' },
        { symbol: 'VNQ', name: 'Vanguard Real Estate ETF', allocation: 5, category: 'REITs', rationale: 'Alternative asset exposure' }
      ],
      aggressive: [
        { symbol: 'VTI', name: 'Vanguard Total Stock Market ETF', allocation: 60, category: 'ETFs / Index Funds', rationale: 'Primary growth vehicle' },
        { symbol: 'VTIAX', name: 'Vanguard Total International Stock Index', allocation: 25, category: 'International', rationale: 'International growth exposure' },
        { symbol: 'VGT', name: 'Vanguard Information Technology ETF', allocation: 10, category: 'Growth Stocks', rationale: 'Technology sector growth' },
        { symbol: 'BND', name: 'Vanguard Total Bond Market ETF', allocation: 5, category: 'Bonds', rationale: 'Minimal stability component' }
      ]
    }

    return baseAllocations[riskLevel as keyof typeof baseAllocations] || baseAllocations.moderate
  }

  /**
   * Get expected return based on risk level
   */
  private getExpectedReturn(riskLevel: string): string {
    const returns = {
      conservative: '6-8%',
      moderate: '8-12%',
      aggressive: '12-16%'
    }
    
    return returns[riskLevel as keyof typeof returns] || '8-12%'
  }

  /**
   * Get emergency strategy description
   */
  private getEmergencyStrategy(riskLevel: string): string {
    const strategies = {
      conservative: 'Focus on capital preservation with stable, low-cost index funds and bonds',
      moderate: 'Balanced approach combining growth potential with stability through diversified ETFs',
      aggressive: 'Growth-oriented strategy emphasizing equity exposure with minimal fixed income'
    }
    
    return strategies[riskLevel as keyof typeof strategies] || 'Balanced diversified approach'
  }

  /**
   * Generate a template-based portfolio for specific scenarios
   */
  async generateTemplatePortfolio(
    template: 'conservative' | 'moderate' | 'aggressive' | 'student' | 'retirement',
    data: PromptData
  ): Promise<PersonalizedPortfolio> {
    const templates = {
      conservative: () => this.generateEmergencyPortfolio({ ...data, surveyData: { ...data.surveyData, riskTolerance: 1 } }),
      moderate: () => this.generateEmergencyPortfolio({ ...data, surveyData: { ...data.surveyData, riskTolerance: 3 } }),
      aggressive: () => this.generateEmergencyPortfolio({ ...data, surveyData: { ...data.surveyData, riskTolerance: 5 } }),
      student: () => this.generateStudentPortfolio(data),
      retirement: () => this.generateRetirementPortfolio(data)
    }

    return templates[template]()
  }

  /**
   * Generate a student-focused portfolio
   */
  private generateStudentPortfolio(data: PromptData): PersonalizedPortfolio {
    return {
      allocations: [
        { symbol: 'VTI', name: 'Vanguard Total Stock Market ETF', allocation: 70, category: 'ETFs / Index Funds', rationale: 'Long-term growth for young investors' },
        { symbol: 'VTIAX', name: 'Vanguard Total International Stock Index', allocation: 20, category: 'International', rationale: 'Global diversification' },
        { symbol: 'VGT', name: 'Vanguard Information Technology ETF', allocation: 10, category: 'Growth Stocks', rationale: 'Technology exposure for future growth' }
      ],
      riskLevel: 'Aggressive',
      expectedReturn: '12-16%',
      strategy: 'Student-focused growth strategy with emphasis on long-term wealth building',
      rebalanceFrequency: 'Quarterly',
      rationale: 'This portfolio is designed for students with a long investment horizon, emphasizing growth through low-cost index funds with minimal complexity.'
    }
  }

  /**
   * Generate a retirement-focused portfolio
   */
  private generateRetirementPortfolio(data: PromptData): PersonalizedPortfolio {
    return {
      allocations: [
        { symbol: 'BND', name: 'Vanguard Total Bond Market ETF', allocation: 50, category: 'Bonds', rationale: 'Income and capital preservation' },
        { symbol: 'VTI', name: 'Vanguard Total Stock Market ETF', allocation: 30, category: 'ETFs / Index Funds', rationale: 'Growth component for inflation protection' },
        { symbol: 'VTIAX', name: 'Vanguard Total International Stock Index', allocation: 15, category: 'International', rationale: 'International diversification' },
        { symbol: 'VNQ', name: 'Vanguard Real Estate ETF', allocation: 5, category: 'REITs', rationale: 'Income and inflation hedge' }
      ],
      riskLevel: 'Conservative',
      expectedReturn: '6-8%',
      strategy: 'Retirement-focused strategy prioritizing income and capital preservation',
      rebalanceFrequency: 'Semi-annually',
      rationale: 'This portfolio is designed for retirement planning, emphasizing income generation and capital preservation while maintaining some growth potential.'
    }
  }

  /**
   * Validate that fallback portfolio meets basic requirements
   */
  private validateFallbackPortfolio(portfolio: PersonalizedPortfolio): boolean {
    // Check that allocations sum to 100%
    const totalAllocation = portfolio.allocations.reduce((sum, allocation) => sum + allocation.allocation, 0)
    if (Math.abs(totalAllocation - 100) > 0.01) {
      return false
    }

    // Check that we have at least 2 allocations
    if (portfolio.allocations.length < 2) {
      return false
    }

    // Check that all required fields are present
    if (!portfolio.riskLevel || !portfolio.expectedReturn || !portfolio.strategy || !portfolio.rationale) {
      return false
    }

    return true
  }
}
