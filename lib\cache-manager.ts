/**
 * Centralized Cache Manager
 * Comprehensive caching system with TTL support, cache invalidation, and dynamic configuration
 */

export interface CacheEntry<T = any> {
  data: T
  timestamp: number
  ttl: number
  key: string
  tags?: string[]
  accessCount: number
  lastAccessed: number
}

export interface CacheStats {
  totalEntries: number
  totalSize: number
  hitRate: number
  missRate: number
  oldestEntry: number
  newestEntry: number
  memoryUsage: number
}

export interface CacheConfig {
  defaultTTL: number
  maxEntries: number
  cleanupInterval: number
  enableBrowserStorage: boolean
  enableCompression: boolean
  enableMetrics: boolean
}

export type CacheEventType = 'hit' | 'miss' | 'set' | 'delete' | 'expire' | 'clear'

export interface CacheEvent {
  type: CacheEventType
  key: string
  timestamp: number
  data?: any
}

export class CacheManager {
  private cache = new Map<string, CacheEntry>()
  private config: CacheConfig
  private stats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    expires: 0
  }
  private cleanupTimer: NodeJS.Timeout | null = null
  private eventListeners: Array<(event: CacheEvent) => void> = []

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      defaultTTL: 60 * 1000, // 1 minute default
      maxEntries: 1000,
      cleanupInterval: 30 * 1000, // 30 seconds
      enableBrowserStorage: typeof window !== 'undefined',
      enableCompression: false,
      enableMetrics: true,
      ...config
    }

    this.startCleanupTimer()
    this.loadFromBrowserStorage()
  }

  /**
   * Get item from cache
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key)
    
    if (!entry) {
      this.stats.misses++
      this.emitEvent('miss', key)
      return null
    }

    // Check if expired
    if (Date.now() > entry.timestamp + entry.ttl) {
      this.cache.delete(key)
      this.stats.expires++
      this.emitEvent('expire', key)
      return null
    }

    // Update access stats
    entry.accessCount++
    entry.lastAccessed = Date.now()
    this.stats.hits++
    this.emitEvent('hit', key, entry.data)

    return entry.data as T
  }

  /**
   * Set item in cache
   */
  set<T>(key: string, data: T, ttl?: number, tags?: string[]): void {
    const now = Date.now()
    const cacheTTL = ttl || this.config.defaultTTL

    // Check if we need to evict entries
    if (this.cache.size >= this.config.maxEntries) {
      this.evictLeastRecentlyUsed()
    }

    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      ttl: cacheTTL,
      key,
      tags,
      accessCount: 0,
      lastAccessed: now
    }

    this.cache.set(key, entry)
    this.stats.sets++
    this.emitEvent('set', key, data)

    // Save to browser storage if enabled
    if (this.config.enableBrowserStorage) {
      this.saveToBrowserStorage(key, entry)
    }
  }

  /**
   * Delete item from cache
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key)
    if (deleted) {
      this.stats.deletes++
      this.emitEvent('delete', key)
      
      if (this.config.enableBrowserStorage) {
        this.removeFromBrowserStorage(key)
      }
    }
    return deleted
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear()
    this.emitEvent('clear', 'all')
    
    if (this.config.enableBrowserStorage) {
      this.clearBrowserStorage()
    }
  }

  /**
   * Clear cache entries by tags
   */
  clearByTags(tags: string[]): number {
    let cleared = 0
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.tags && entry.tags.some(tag => tags.includes(tag))) {
        this.cache.delete(key)
        cleared++
        this.emitEvent('delete', key)
      }
    }

    return cleared
  }

  /**
   * Check if key exists and is not expired
   */
  has(key: string): boolean {
    const entry = this.cache.get(key)
    if (!entry) return false
    
    if (Date.now() > entry.timestamp + entry.ttl) {
      this.cache.delete(key)
      return false
    }
    
    return true
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    const entries = Array.from(this.cache.values())
    const totalRequests = this.stats.hits + this.stats.misses
    
    return {
      totalEntries: this.cache.size,
      totalSize: this.calculateCacheSize(),
      hitRate: totalRequests > 0 ? this.stats.hits / totalRequests : 0,
      missRate: totalRequests > 0 ? this.stats.misses / totalRequests : 0,
      oldestEntry: entries.length > 0 ? Math.min(...entries.map(e => e.timestamp)) : 0,
      newestEntry: entries.length > 0 ? Math.max(...entries.map(e => e.timestamp)) : 0,
      memoryUsage: this.calculateMemoryUsage()
    }
  }

  /**
   * Update cache configuration
   */
  updateConfig(newConfig: Partial<CacheConfig>): void {
    this.config = { ...this.config, ...newConfig }
    
    // Restart cleanup timer if interval changed
    if (newConfig.cleanupInterval) {
      this.stopCleanupTimer()
      this.startCleanupTimer()
    }
  }

  /**
   * Get all cache keys
   */
  getKeys(): string[] {
    return Array.from(this.cache.keys())
  }

  /**
   * Get cache entries by pattern
   */
  getByPattern(pattern: RegExp): Array<{ key: string; data: any }> {
    const results: Array<{ key: string; data: any }> = []
    
    for (const [key, entry] of this.cache.entries()) {
      if (pattern.test(key)) {
        // Check if not expired
        if (Date.now() <= entry.timestamp + entry.ttl) {
          results.push({ key, data: entry.data })
        }
      }
    }
    
    return results
  }

  /**
   * Warm cache with data
   */
  warm<T>(entries: Array<{ key: string; data: T; ttl?: number; tags?: string[] }>): void {
    entries.forEach(({ key, data, ttl, tags }) => {
      this.set(key, data, ttl, tags)
    })
  }

  /**
   * Add event listener
   */
  addEventListener(listener: (event: CacheEvent) => void): void {
    this.eventListeners.push(listener)
  }

  /**
   * Remove event listener
   */
  removeEventListener(listener: (event: CacheEvent) => void): void {
    const index = this.eventListeners.indexOf(listener)
    if (index > -1) {
      this.eventListeners.splice(index, 1)
    }
  }

  /**
   * Private methods
   */
  private evictLeastRecentlyUsed(): void {
    let oldestKey = ''
    let oldestTime = Date.now()

    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed
        oldestKey = key
      }
    }

    if (oldestKey) {
      this.delete(oldestKey)
    }
  }

  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup()
    }, this.config.cleanupInterval)
  }

  private stopCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = null
    }
  }

  private cleanup(): void {
    const now = Date.now()
    const expiredKeys: string[] = []

    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.timestamp + entry.ttl) {
        expiredKeys.push(key)
      }
    }

    expiredKeys.forEach(key => {
      this.cache.delete(key)
      this.stats.expires++
      this.emitEvent('expire', key)
    })
  }

  private calculateCacheSize(): number {
    return this.cache.size
  }

  private calculateMemoryUsage(): number {
    // Rough estimation of memory usage
    let size = 0
    for (const entry of this.cache.values()) {
      size += JSON.stringify(entry).length
    }
    return size
  }

  private emitEvent(type: CacheEventType, key: string, data?: any): void {
    if (!this.config.enableMetrics) return

    const event: CacheEvent = {
      type,
      key,
      timestamp: Date.now(),
      data
    }

    this.eventListeners.forEach(listener => {
      try {
        listener(event)
      } catch (error) {
        console.warn('Cache event listener error:', error)
      }
    })
  }

  private saveToBrowserStorage(key: string, entry: CacheEntry): void {
    if (typeof window === 'undefined') return

    try {
      const storageKey = `cache:${key}`
      const storageData = {
        data: entry.data,
        timestamp: entry.timestamp,
        ttl: entry.ttl,
        tags: entry.tags
      }
      localStorage.setItem(storageKey, JSON.stringify(storageData))
    } catch (error) {
      console.warn('Failed to save to browser storage:', error)
    }
  }

  private loadFromBrowserStorage(): void {
    if (typeof window === 'undefined') return

    try {
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key?.startsWith('cache:')) {
          const cacheKey = key.replace('cache:', '')
          const data = localStorage.getItem(key)
          
          if (data) {
            const parsed = JSON.parse(data)
            const now = Date.now()
            
            // Check if not expired
            if (now <= parsed.timestamp + parsed.ttl) {
              const entry: CacheEntry = {
                data: parsed.data,
                timestamp: parsed.timestamp,
                ttl: parsed.ttl,
                key: cacheKey,
                tags: parsed.tags,
                accessCount: 0,
                lastAccessed: now
              }
              this.cache.set(cacheKey, entry)
            } else {
              // Remove expired entry from localStorage
              localStorage.removeItem(key)
            }
          }
        }
      }
    } catch (error) {
      console.warn('Failed to load from browser storage:', error)
    }
  }

  private removeFromBrowserStorage(key: string): void {
    if (typeof window === 'undefined') return

    try {
      localStorage.removeItem(`cache:${key}`)
    } catch (error) {
      console.warn('Failed to remove from browser storage:', error)
    }
  }

  private clearBrowserStorage(): void {
    if (typeof window === 'undefined') return

    try {
      const keysToRemove: string[] = []
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key?.startsWith('cache:')) {
          keysToRemove.push(key)
        }
      }
      keysToRemove.forEach(key => localStorage.removeItem(key))
    } catch (error) {
      console.warn('Failed to clear browser storage:', error)
    }
  }

  /**
   * Cleanup on destroy
   */
  destroy(): void {
    this.stopCleanupTimer()
    this.clear()
    this.eventListeners = []
  }
}

// Singleton instance
let cacheManagerInstance: CacheManager | null = null

export function getCacheManager(): CacheManager {
  if (!cacheManagerInstance) {
    cacheManagerInstance = new CacheManager()
  }
  return cacheManagerInstance
}

// Utility functions
export function createCacheKey(...parts: (string | number)[]): string {
  return parts.join(':')
}

export function getCacheKeysByPattern(pattern: string): string[] {
  const cache = getCacheManager()
  const regex = new RegExp(pattern.replace(/\*/g, '.*'))
  return cache.getKeys().filter(key => regex.test(key))
}
