-- Test Portfolio System
-- Run this after the main schema to verify everything works

-- Test 1: Check if all tables exist
SELECT 
    table_name,
    CASE 
        WHEN table_name IN (
            'user_balances', 'wallet_transactions', 'portfolio_performance_cache',
            'portfolio_performance_history', 'financial_audit_log', 'portfolios',
            'portfolio_holdings', 'transactions'
        ) THEN 'EXISTS'
        ELSE 'MISSING'
    END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN (
    'user_balances', 'wallet_transactions', 'portfolio_performance_cache',
    'portfolio_performance_history', 'financial_audit_log', 'portfolios',
    'portfolio_holdings', 'transactions'
)
ORDER BY table_name;

-- Test 2: Check if all functions exist
SELECT 
    routine_name,
    routine_type,
    'EXISTS' as status
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN (
    'process_investment_transaction',
    'calculate_portfolio_value',
    'get_cached_portfolio_performance',
    'update_portfolio_performance',
    'initialize_user_portfolio_system'
)
ORDER BY routine_name;

-- Test 3: Check RLS policies
SELECT 
    tablename,
    policyname,
    'EXISTS' as status
FROM pg_policies 
WHERE schemaname = 'public'
AND tablename IN (
    'user_balances', 'wallet_transactions', 'portfolio_performance_cache',
    'portfolio_performance_history', 'financial_audit_log'
)
ORDER BY tablename, policyname;

-- Test 4: Check if views exist
SELECT 
    table_name,
    table_type,
    'EXISTS' as status
FROM information_schema.views 
WHERE table_schema = 'public' 
AND table_name IN ('user_portfolio_overview', 'portfolio_performance_summary')
ORDER BY table_name;

-- Test 5: Sample data insertion test (will be rolled back)
BEGIN;

-- Create a test user (this would normally be done by Supabase Auth)
-- We'll use a dummy UUID for testing
DO $$
DECLARE
    test_user_id UUID := '00000000-0000-0000-0000-000000000001';
BEGIN
    -- Test user balance creation
    INSERT INTO user_balances (user_id, balance, currency, balance_type, available_for_investment)
    VALUES (test_user_id, 10000.00, 'USD', 'cash', 10000.00);
    
    RAISE NOTICE 'User balance created successfully';
    
    -- Test portfolio creation
    INSERT INTO portfolios (user_id, name, description, is_default, initial_value)
    VALUES (test_user_id, 'Test Portfolio', 'Test portfolio for validation', true, 10000.00);
    
    RAISE NOTICE 'Portfolio created successfully';
    
    -- Test wallet transaction
    INSERT INTO wallet_transactions (user_id, transaction_type, amount, currency, description, status)
    VALUES (test_user_id, 'deposit', 10000.00, 'USD', 'Initial deposit', 'completed');
    
    RAISE NOTICE 'Wallet transaction created successfully';
    
    RAISE NOTICE 'All basic operations completed successfully!';
    
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Error during testing: %', SQLERRM;
END $$;

-- Rollback the test data
ROLLBACK;

-- Test 6: Check constraints and data types
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name IN ('user_balances', 'wallet_transactions', 'portfolio_holdings')
ORDER BY table_name, ordinal_position;

-- Final status report
SELECT 
    'Portfolio System Status' as component,
    CASE 
        WHEN (
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN (
                'user_balances', 'wallet_transactions', 'portfolio_performance_cache',
                'portfolio_performance_history', 'financial_audit_log'
            )
        ) >= 5 THEN 'READY'
        ELSE 'INCOMPLETE'
    END as status,
    NOW() as checked_at;
