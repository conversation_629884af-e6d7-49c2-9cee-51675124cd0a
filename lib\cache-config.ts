/**
 * Cache Configuration System
 * Environment-based cache configuration with different TTL settings for different data types
 */

export interface CacheTTLConfig {
  // Real-time data (very short TTL)
  realTimeQuotes: number
  marketStatus: number
  tickData: number
  
  // Market data (short TTL)
  stockQuotes: number
  marketMovers: number
  stockSearch: number
  
  // Financial data (medium TTL)
  financials: number
  earnings: number
  dividends: number
  
  // News and analysis (medium TTL)
  news: number
  analysis: number
  sentiment: number
  
  // Historical data (longer TTL)
  historicalDaily: number
  historicalIntraday: number
  historicalWeekly: number
  
  // Portfolio data (medium TTL)
  portfolioPerformance: number
  portfolioAllocations: number
  portfolioAnalysis: number
  
  // Reference data (long TTL)
  companyProfiles: number
  sectorData: number
  exchangeInfo: number
  
  // Technical indicators (short TTL)
  technicalIndicators: number
  chartData: number
  
  // User data (short TTL)
  userPreferences: number
  watchlists: number
  
  // Static data (very long TTL)
  staticContent: number
  configurations: number
}

export interface CacheEnvironmentConfig {
  development: CacheTTLConfig
  production: CacheTTLConfig
  testing: CacheTTLConfig
}

// Default cache TTL configurations (in milliseconds)
const DEFAULT_CACHE_CONFIG: CacheEnvironmentConfig = {
  development: {
    // Real-time data (10-30 seconds in dev)
    realTimeQuotes: 10 * 1000,
    marketStatus: 30 * 1000,
    tickData: 5 * 1000,
    
    // Market data (1-2 minutes in dev)
    stockQuotes: 60 * 1000,
    marketMovers: 2 * 60 * 1000,
    stockSearch: 60 * 1000,
    
    // Financial data (5-10 minutes in dev)
    financials: 5 * 60 * 1000,
    earnings: 10 * 60 * 1000,
    dividends: 10 * 60 * 1000,
    
    // News and analysis (5-15 minutes in dev)
    news: 5 * 60 * 1000,
    analysis: 15 * 60 * 1000,
    sentiment: 10 * 60 * 1000,
    
    // Historical data (10-30 minutes in dev)
    historicalDaily: 30 * 60 * 1000,
    historicalIntraday: 10 * 60 * 1000,
    historicalWeekly: 60 * 60 * 1000,
    
    // Portfolio data (2-5 minutes in dev)
    portfolioPerformance: 2 * 60 * 1000,
    portfolioAllocations: 5 * 60 * 1000,
    portfolioAnalysis: 10 * 60 * 1000,
    
    // Reference data (1-2 hours in dev)
    companyProfiles: 60 * 60 * 1000,
    sectorData: 2 * 60 * 60 * 1000,
    exchangeInfo: 2 * 60 * 60 * 1000,
    
    // Technical indicators (1-5 minutes in dev)
    technicalIndicators: 60 * 1000,
    chartData: 2 * 60 * 1000,
    
    // User data (30 seconds - 2 minutes in dev)
    userPreferences: 30 * 1000,
    watchlists: 2 * 60 * 1000,
    
    // Static data (10 minutes in dev)
    staticContent: 10 * 60 * 1000,
    configurations: 10 * 60 * 1000
  },
  
  production: {
    // Real-time data (30 seconds - 2 minutes in prod)
    realTimeQuotes: 30 * 1000,
    marketStatus: 2 * 60 * 1000,
    tickData: 10 * 1000,
    
    // Market data (2-5 minutes in prod)
    stockQuotes: 2 * 60 * 1000,
    marketMovers: 5 * 60 * 1000,
    stockSearch: 3 * 60 * 1000,
    
    // Financial data (15-60 minutes in prod)
    financials: 60 * 60 * 1000,
    earnings: 60 * 60 * 1000,
    dividends: 60 * 60 * 1000,
    
    // News and analysis (15-30 minutes in prod)
    news: 15 * 60 * 1000,
    analysis: 30 * 60 * 1000,
    sentiment: 20 * 60 * 1000,
    
    // Historical data (1-4 hours in prod)
    historicalDaily: 4 * 60 * 60 * 1000,
    historicalIntraday: 60 * 60 * 1000,
    historicalWeekly: 24 * 60 * 60 * 1000,
    
    // Portfolio data (5-15 minutes in prod)
    portfolioPerformance: 5 * 60 * 1000,
    portfolioAllocations: 15 * 60 * 1000,
    portfolioAnalysis: 30 * 60 * 1000,
    
    // Reference data (4-24 hours in prod)
    companyProfiles: 4 * 60 * 60 * 1000,
    sectorData: 24 * 60 * 60 * 1000,
    exchangeInfo: 24 * 60 * 60 * 1000,
    
    // Technical indicators (2-10 minutes in prod)
    technicalIndicators: 2 * 60 * 1000,
    chartData: 5 * 60 * 1000,
    
    // User data (2-10 minutes in prod)
    userPreferences: 2 * 60 * 1000,
    watchlists: 10 * 60 * 1000,
    
    // Static data (2-24 hours in prod)
    staticContent: 2 * 60 * 60 * 1000,
    configurations: 24 * 60 * 60 * 1000
  },
  
  testing: {
    // Very short TTLs for testing (5-30 seconds)
    realTimeQuotes: 5 * 1000,
    marketStatus: 10 * 1000,
    tickData: 2 * 1000,
    
    stockQuotes: 10 * 1000,
    marketMovers: 15 * 1000,
    stockSearch: 10 * 1000,
    
    financials: 30 * 1000,
    earnings: 30 * 1000,
    dividends: 30 * 1000,
    
    news: 15 * 1000,
    analysis: 30 * 1000,
    sentiment: 20 * 1000,
    
    historicalDaily: 60 * 1000,
    historicalIntraday: 30 * 1000,
    historicalWeekly: 2 * 60 * 1000,
    
    portfolioPerformance: 15 * 1000,
    portfolioAllocations: 30 * 1000,
    portfolioAnalysis: 60 * 1000,
    
    companyProfiles: 2 * 60 * 1000,
    sectorData: 5 * 60 * 1000,
    exchangeInfo: 5 * 60 * 1000,
    
    technicalIndicators: 10 * 1000,
    chartData: 15 * 1000,
    
    userPreferences: 5 * 1000,
    watchlists: 15 * 1000,
    
    staticContent: 60 * 1000,
    configurations: 60 * 1000
  }
}

export class CacheConfigManager {
  private config: CacheTTLConfig
  private environment: keyof CacheEnvironmentConfig
  private customOverrides: Partial<CacheTTLConfig> = {}

  constructor() {
    this.environment = this.getEnvironment()
    this.config = { ...DEFAULT_CACHE_CONFIG[this.environment] }
    this.loadCustomOverrides()
  }

  /**
   * Get TTL for a specific data type
   */
  getTTL(dataType: keyof CacheTTLConfig): number {
    return this.customOverrides[dataType] ?? this.config[dataType]
  }

  /**
   * Set custom TTL for a data type
   */
  setTTL(dataType: keyof CacheTTLConfig, ttl: number): void {
    this.customOverrides[dataType] = ttl
    this.saveCustomOverrides()
  }

  /**
   * Get all TTL configurations
   */
  getAllTTLs(): CacheTTLConfig {
    return { ...this.config, ...this.customOverrides }
  }

  /**
   * Reset TTL to default for a data type
   */
  resetTTL(dataType: keyof CacheTTLConfig): void {
    delete this.customOverrides[dataType]
    this.saveCustomOverrides()
  }

  /**
   * Reset all TTLs to defaults
   */
  resetAllTTLs(): void {
    this.customOverrides = {}
    this.saveCustomOverrides()
  }

  /**
   * Update environment and reload config
   */
  setEnvironment(env: keyof CacheEnvironmentConfig): void {
    this.environment = env
    this.config = { ...DEFAULT_CACHE_CONFIG[env] }
  }

  /**
   * Get current environment
   */
  getEnvironment(): keyof CacheEnvironmentConfig {
    const nodeEnv = process.env.NODE_ENV
    if (nodeEnv === 'production') return 'production'
    if (nodeEnv === 'test') return 'testing'
    return 'development'
  }

  /**
   * Get TTL based on market hours (shorter TTL during market hours)
   */
  getMarketAwareTTL(dataType: keyof CacheTTLConfig): number {
    const baseTTL = this.getTTL(dataType)
    
    // Check if market is open (simplified - US market hours)
    const now = new Date()
    const hour = now.getHours()
    const day = now.getDay()
    
    // Market hours: Monday-Friday, 9:30 AM - 4:00 PM ET
    const isMarketHours = day >= 1 && day <= 5 && hour >= 9 && hour < 16
    
    // Reduce TTL by 50% during market hours for real-time data
    if (isMarketHours && this.isRealTimeData(dataType)) {
      return Math.floor(baseTTL * 0.5)
    }
    
    return baseTTL
  }

  /**
   * Get cache tags for a data type
   */
  getCacheTags(dataType: keyof CacheTTLConfig): string[] {
    const tagMap: Record<keyof CacheTTLConfig, string[]> = {
      realTimeQuotes: ['quotes', 'realtime'],
      marketStatus: ['market', 'status'],
      tickData: ['ticks', 'realtime'],
      
      stockQuotes: ['quotes', 'stocks'],
      marketMovers: ['market', 'movers'],
      stockSearch: ['search', 'stocks'],
      
      financials: ['financials', 'fundamentals'],
      earnings: ['earnings', 'fundamentals'],
      dividends: ['dividends', 'fundamentals'],
      
      news: ['news'],
      analysis: ['analysis'],
      sentiment: ['sentiment'],
      
      historicalDaily: ['historical', 'daily'],
      historicalIntraday: ['historical', 'intraday'],
      historicalWeekly: ['historical', 'weekly'],
      
      portfolioPerformance: ['portfolio', 'performance'],
      portfolioAllocations: ['portfolio', 'allocations'],
      portfolioAnalysis: ['portfolio', 'analysis'],
      
      companyProfiles: ['company', 'profiles'],
      sectorData: ['sector', 'reference'],
      exchangeInfo: ['exchange', 'reference'],
      
      technicalIndicators: ['technical', 'indicators'],
      chartData: ['charts', 'technical'],
      
      userPreferences: ['user', 'preferences'],
      watchlists: ['user', 'watchlists'],
      
      staticContent: ['static'],
      configurations: ['config', 'static']
    }

    return tagMap[dataType] || []
  }

  /**
   * Check if data type is real-time
   */
  private isRealTimeData(dataType: keyof CacheTTLConfig): boolean {
    const realTimeTypes: (keyof CacheTTLConfig)[] = [
      'realTimeQuotes',
      'tickData',
      'stockQuotes',
      'marketMovers',
      'technicalIndicators'
    ]
    return realTimeTypes.includes(dataType)
  }

  /**
   * Load custom overrides from storage
   */
  private loadCustomOverrides(): void {
    if (typeof window === 'undefined') return

    try {
      const stored = localStorage.getItem('cache-ttl-overrides')
      if (stored) {
        this.customOverrides = JSON.parse(stored)
      }
    } catch (error) {
      console.warn('Failed to load cache TTL overrides:', error)
    }
  }

  /**
   * Save custom overrides to storage
   */
  private saveCustomOverrides(): void {
    if (typeof window === 'undefined') return

    try {
      localStorage.setItem('cache-ttl-overrides', JSON.stringify(this.customOverrides))
    } catch (error) {
      console.warn('Failed to save cache TTL overrides:', error)
    }
  }

  /**
   * Get human-readable TTL description
   */
  getTTLDescription(ttl: number): string {
    if (ttl < 60 * 1000) {
      return `${Math.floor(ttl / 1000)} seconds`
    } else if (ttl < 60 * 60 * 1000) {
      return `${Math.floor(ttl / (60 * 1000))} minutes`
    } else if (ttl < 24 * 60 * 60 * 1000) {
      return `${Math.floor(ttl / (60 * 60 * 1000))} hours`
    } else {
      return `${Math.floor(ttl / (24 * 60 * 60 * 1000))} days`
    }
  }
}

// Singleton instance
let cacheConfigInstance: CacheConfigManager | null = null

export function getCacheConfig(): CacheConfigManager {
  if (!cacheConfigInstance) {
    cacheConfigInstance = new CacheConfigManager()
  }
  return cacheConfigInstance
}

// Utility functions
export function getCacheTTL(dataType: keyof CacheTTLConfig): number {
  return getCacheConfig().getTTL(dataType)
}

export function getMarketAwareCacheTTL(dataType: keyof CacheTTLConfig): number {
  return getCacheConfig().getMarketAwareTTL(dataType)
}

export function getCacheTagsForDataType(dataType: keyof CacheTTLConfig): string[] {
  return getCacheConfig().getCacheTags(dataType)
}
