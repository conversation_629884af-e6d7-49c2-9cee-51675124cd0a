/**
 * Stock Financials API Route
 * GET /api/stocks/financials?symbol=AAPL
 */

import { NextRequest, NextResponse } from 'next/server'
import { getStockDataService } from '@/lib/stock-config'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const symbol = searchParams.get('symbol')

    // Validate input
    if (!symbol) {
      return NextResponse.json(
        { error: 'Missing required parameter: symbol' },
        { status: 400 }
      )
    }

    const stockService = getStockDataService()
    const financials = await stockService.getStockFinancials(symbol.toUpperCase())

    if (!financials) {
      return NextResponse.json(
        { error: 'Financial data not found for this symbol' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      data: financials,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Financials API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
