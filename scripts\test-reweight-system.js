/**
 * Test Script for Portfolio Reweighting System
 * Run with: node scripts/test-reweight-system.js
 */

const { PromptTemplateBuilder } = require('../lib/llm-portfolio/prompt-template-builder')

// Mock portfolio data
const mockCurrentPortfolio = {
  allocations: [
    {
      symbol: "VTI",
      name: "Vanguard Total Stock Market ETF",
      allocation: 40,
      category: "ETFs / Index Funds",
      rationale: "Broad market exposure"
    },
    {
      symbol: "BND",
      name: "Vanguard Total Bond Market ETF",
      allocation: 30,
      category: "Bonds",
      rationale: "Stability and income"
    },
    {
      symbol: "VTIAX",
      name: "Vanguard Total International Stock Index",
      allocation: 20,
      category: "International",
      rationale: "International diversification"
    },
    {
      symbol: "VNQ",
      name: "Vanguard Real Estate ETF",
      allocation: 10,
      category: "REITs",
      rationale: "Real estate exposure"
    }
  ],
  riskLevel: "Moderate",
  expectedReturn: "8-12%",
  strategy: "Balanced approach with diversified ETFs",
  rebalanceFrequency: "Quarterly",
  rationale: "Well-diversified portfolio suitable for moderate risk tolerance"
}

const mockSurveyData = {
  primaryGoal: "Long-term wealth building",
  timeHorizon: "10+ years",
  riskTolerance: 3,
  experienceLevel: "Intermediate",
  interestedThemes: ["Technology", "Healthcare"],
  monthlyInvestment: 1000
}

const mockPromptData = {
  surveyData: mockSurveyData,
  userId: 'test-user-123',
  userMajor: 'computer-science'
}

async function testReweightSystem() {
  console.log('🔄 Testing Portfolio Reweighting System\n')

  try {
    // Test 1: Prompt Template Builder for Reweighting
    console.log('1️⃣ Testing Reweighting Prompt Generation...')
    const promptBuilder = new PromptTemplateBuilder()
    
    const reweightReason = "I want to reduce risk and increase bond allocation to 50% because I'm getting closer to retirement"
    
    const reweightPrompt = await promptBuilder.buildReweightingPrompt(
      mockPromptData,
      mockCurrentPortfolio,
      reweightReason
    )

    console.log('✅ Reweighting prompt generated successfully')
    console.log('Prompt length:', reweightPrompt.length, 'characters')
    console.log('Contains current portfolio:', reweightPrompt.includes('Current Allocations') ? '✅' : '❌')
    console.log('Contains reweight reason:', reweightPrompt.includes(reweightReason) ? '✅' : '❌')
    console.log('Contains JSON format:', reweightPrompt.includes('```json') ? '✅' : '❌')
    console.log('')

    // Test 2: Prompt Hash Generation
    console.log('2️⃣ Testing Prompt Hash Generation...')
    const hash1 = promptBuilder.generatePromptHash(reweightPrompt)
    const hash2 = promptBuilder.generatePromptHash(reweightPrompt)
    
    console.log('Hash 1:', hash1.substring(0, 16) + '...')
    console.log('Hash 2:', hash2.substring(0, 16) + '...')
    console.log('Hashes match:', hash1 === hash2 ? '✅' : '❌')
    console.log('Hash length:', hash1.length, '(should be 64)')
    console.log('')

    // Test 3: Different Reweight Reasons
    console.log('3️⃣ Testing Different Reweight Scenarios...')
    
    const scenarios = [
      "Add more technology exposure, I want 30% in tech stocks",
      "Make the portfolio more conservative, increase bonds to 60%",
      "I want to add international exposure, reduce US allocation",
      "Focus on dividend-paying stocks for income generation"
    ]

    for (let i = 0; i < scenarios.length; i++) {
      const scenario = scenarios[i]
      const scenarioPrompt = await promptBuilder.buildReweightingPrompt(
        mockPromptData,
        mockCurrentPortfolio,
        scenario
      )
      
      console.log(`Scenario ${i + 1}: "${scenario.substring(0, 40)}..."`)
      console.log('- Prompt generated:', scenarioPrompt.length > 1000 ? '✅' : '❌')
      console.log('- Contains scenario:', scenarioPrompt.includes(scenario) ? '✅' : '❌')
      console.log('- Unique hash:', promptBuilder.generatePromptHash(scenarioPrompt) !== hash1 ? '✅' : '❌')
    }
    console.log('')

    // Test 4: Portfolio Validation Logic
    console.log('4️⃣ Testing Portfolio Validation...')
    
    // Valid portfolio
    const validPortfolio = { ...mockCurrentPortfolio }
    console.log('Valid portfolio structure:', isValidPortfolioStructure(validPortfolio) ? '✅' : '❌')
    
    // Invalid portfolio - missing allocations
    const invalidPortfolio1 = { ...mockCurrentPortfolio, allocations: [] }
    console.log('Invalid portfolio (no allocations):', !isValidPortfolioStructure(invalidPortfolio1) ? '✅' : '❌')
    
    // Invalid portfolio - allocations don't sum to 100%
    const invalidPortfolio2 = {
      ...mockCurrentPortfolio,
      allocations: [
        { symbol: "VTI", name: "Test", allocation: 50, category: "ETFs", rationale: "Test" },
        { symbol: "BND", name: "Test", allocation: 30, category: "Bonds", rationale: "Test" }
        // Only sums to 80%
      ]
    }
    console.log('Invalid portfolio (wrong sum):', !isValidPortfolioStructure(invalidPortfolio2) ? '✅' : '❌')
    console.log('')

    // Test 5: API Endpoint Structure Test
    console.log('5️⃣ Testing API Endpoint Structure...')
    
    const mockApiRequest = {
      currentPortfolio: mockCurrentPortfolio,
      reweightReason: reweightReason,
      surveyData: mockSurveyData,
      userMajor: 'computer-science'
    }
    
    console.log('Request structure valid:', validateApiRequest(mockApiRequest) ? '✅' : '❌')
    console.log('Current portfolio included:', !!mockApiRequest.currentPortfolio ? '✅' : '❌')
    console.log('Reweight reason included:', !!mockApiRequest.reweightReason ? '✅' : '❌')
    console.log('Survey data included:', !!mockApiRequest.surveyData ? '✅' : '❌')
    console.log('')

    console.log('✅ All reweighting system tests completed successfully!')
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
    console.error('Stack trace:', error.stack)
    process.exit(1)
  }
}

/**
 * Validate portfolio structure (simplified version)
 */
function isValidPortfolioStructure(portfolio) {
  if (!portfolio || typeof portfolio !== 'object') {
    return false
  }

  const required = ['allocations', 'riskLevel', 'expectedReturn', 'strategy', 'rationale']
  for (const field of required) {
    if (!portfolio[field]) {
      return false
    }
  }

  if (!Array.isArray(portfolio.allocations) || portfolio.allocations.length === 0) {
    return false
  }

  // Validate allocations
  for (const alloc of portfolio.allocations) {
    if (!alloc.symbol || !alloc.name || typeof alloc.allocation !== 'number' || 
        !alloc.category || !alloc.rationale) {
      return false
    }
  }

  // Check allocations sum to approximately 100%
  const totalAllocation = portfolio.allocations.reduce((sum, alloc) => sum + alloc.allocation, 0)
  if (Math.abs(totalAllocation - 100) > 1) { // Allow 1% tolerance
    return false
  }

  return true
}

/**
 * Validate API request structure
 */
function validateApiRequest(request) {
  if (!request || typeof request !== 'object') {
    return false
  }

  const required = ['currentPortfolio', 'reweightReason', 'surveyData']
  for (const field of required) {
    if (!request[field]) {
      return false
    }
  }

  if (typeof request.reweightReason !== 'string' || request.reweightReason.length < 10) {
    return false
  }

  if (!isValidPortfolioStructure(request.currentPortfolio)) {
    return false
  }

  return true
}

// Run the test
if (require.main === module) {
  testReweightSystem()
    .then(() => {
      console.log('\n🎉 Portfolio reweighting system test completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('\n💥 Test suite failed:', error)
      process.exit(1)
    })
}

module.exports = { testReweightSystem }
