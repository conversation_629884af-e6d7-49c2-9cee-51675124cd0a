import { getStockDataService } from './stock-config'
import { StockQuote as ServiceStockQuote } from './stock-data-service'
import {
  getStocksByCategory,
  searchStocksByKeyword as searchByKeyword,
  getStockBySymbol as getStockBySymbolFallback,
  getTrendingStocks as getTrending,
  getTopGainers as getGainers,
  getTopLosers as getLosers,
  getMostActive as getActive,
  type StockQuote,
} from "./stock-classifier"

export interface StockData {
  symbol: string
  name: string
  price: number
  change: number
  changePercent: number
  volume: number
  marketCap: number
  category: string
  tags: string[]
  lastUpdated?: string
  source?: string
}

// Convert ServiceStockQuote to StockData format
function convertServiceQuoteToStockData(quote: ServiceStockQuote): StockData {
  return {
    symbol: quote.symbol,
    name: quote.name,
    price: quote.price,
    change: quote.change,
    changePercent: quote.changePercent,
    volume: quote.volume || 1000000,
    marketCap: quote.marketCap || quote.price * 1000000,
    category: quote.sector || 'Unknown',
    tags: [], // Could be enhanced with sector-based tags
    lastUpdated: quote.lastUpdated,
    source: quote.source
  }
}

// Convert StockQuote to StockData format (fallback)
function convertToStockData(quote: StockQuote): StockData {
  return {
    symbol: quote.symbol,
    name: quote.name,
    price: quote.price,
    change: quote.change,
    changePercent: quote.changePercent,
    volume: quote.volume || 1000000,
    marketCap: quote.marketCap || quote.price * 1000000,
    category: quote.category,
    tags: quote.tags,
  }
}

// Get a single stock quote with real-time data
export async function getStockQuote(symbol: string): Promise<StockData | null> {
  try {
    const stockService = getStockDataService()
    const quote = await stockService.getStockQuote(symbol)

    if (quote) {
      return convertServiceQuoteToStockData(quote)
    }

    // Fallback to mock data
    const fallbackStock = getStockBySymbolFallback(symbol)
    if (fallbackStock) {
      return convertToStockData(fallbackStock)
    }

    return null
  } catch (error) {
    console.error("Error fetching stock quote:", error)

    // Try fallback data on error
    const fallbackStock = getStockBySymbolFallback(symbol)
    if (fallbackStock) {
      return convertToStockData(fallbackStock)
    }

    return null
  }
}

// Get multiple stock quotes efficiently
export async function getMultipleStockQuotes(symbols: string[]): Promise<StockData[]> {
  try {
    const stockService = getStockDataService()
    const quotes = await stockService.getMultipleQuotes(symbols)

    return quotes.map(convertServiceQuoteToStockData)
  } catch (error) {
    console.error("Error fetching multiple stock quotes:", error)

    // Fallback to individual requests
    const results: StockData[] = []
    for (const symbol of symbols) {
      const quote = await getStockQuote(symbol)
      if (quote) results.push(quote)
    }

    return results
  }
}

// Get stock quote with retry logic
export async function getStockQuoteWithRetry(symbol: string, maxRetries = 3): Promise<StockData | null> {
  for (let i = 0; i < maxRetries; i++) {
    try {
      const result = await getStockQuote(symbol)
      if (result) return result
    } catch (error) {
      if (i === maxRetries - 1) {
        console.error(`Failed to get quote for ${symbol} after ${maxRetries} retries:`, error)
        return null
      }
      await new Promise((resolve) => setTimeout(resolve, 1000 * (i + 1)))
    }
  }
  return null
}

// Search stocks by category
export async function searchStocksByCategory(category: string): Promise<StockData[]> {
  try {
    const stocks = getStocksByCategory(category)
    return stocks.map(convertToStockData)
  } catch (error) {
    console.error("Error searching stocks by category:", error)
    return []
  }
}

// Search stocks by keyword
export async function searchStocksByKeyword(keyword: string): Promise<StockData[]> {
  try {
    const stocks = searchByKeyword(keyword)
    return stocks.map(convertToStockData)
  } catch (error) {
    console.error("Error searching stocks by keyword:", error)
    return []
  }
}

// Get trending stocks
export async function getTrendingStocks(): Promise<StockData[]> {
  try {
    const stocks = getTrending()
    return stocks.map(convertToStockData)
  } catch (error) {
    console.error("Error fetching trending stocks:", error)
    return []
  }
}

// Get market movers
export async function getMarketMovers(): Promise<{
  gainers: StockData[]
  losers: StockData[]
  mostActive: StockData[]
}> {
  try {
    const gainers = getGainers().map(convertToStockData)
    const losers = getLosers().map(convertToStockData)
    const mostActive = getActive().map(convertToStockData)

    return { gainers, losers, mostActive }
  } catch (error) {
    console.error("Error fetching market movers:", error)
    return { gainers: [], losers: [], mostActive: [] }
  }
}

// Export types
export type { StockData as StockQuote }
