/**
 * Stock API Configuration and Service Initialization
 */

import { StockDataService, ApiConfig } from './stock-data-service'

// Environment variables for API keys
const API_CONFIG: ApiConfig = {
  alphavantage: {
    apiKey: process.env.ALPHA_VANTAGE_API_KEY || '',
    baseUrl: 'https://www.alphavantage.co',
    dailyLimit: 25
  },
  finnhub: {
    apiKey: process.env.FINNHUB_API_KEY || '',
    baseUrl: 'https://finnhub.io/api/v1',
    minuteLimit: 60
  }
}

// Singleton instance
let stockDataService: StockDataService | null = null

/**
 * Get or create the stock data service instance
 */
export function getStockDataService(): StockDataService {
  if (!stockDataService) {
    stockDataService = new StockDataService(API_CONFIG)
  }
  return stockDataService
}

/**
 * Check if APIs are properly configured
 */
export function isApiConfigured(): boolean {
  return !!(API_CONFIG.alphavantage?.apiKey || API_CONFIG.finnhub?.apiKey)
}

/**
 * Get API configuration status
 */
export function getApiStatus() {
  return {
    alphavantage: {
      configured: !!API_CONFIG.alphavantage?.apiKey,
      dailyLimit: API_CONFIG.alphavantage?.dailyLimit || 25
    },
    finnhub: {
      configured: !!API_CONFIG.finnhub?.apiKey,
      minuteLimit: API_CONFIG.finnhub?.minuteLimit || 60
    }
  }
}

/**
 * Popular stock symbols for testing and demo
 */
export const POPULAR_STOCKS = [
  'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA',
  'META', 'NVDA', 'NFLX', 'DIS', 'BABA',
  'V', 'JPM', 'JNJ', 'WMT', 'PG',
  'UNH', 'HD', 'MA', 'BAC', 'ABBV'
]

/**
 * Stock sectors for categorization
 */
export const STOCK_SECTORS = [
  'Technology',
  'Healthcare',
  'Financial Services',
  'Consumer Discretionary',
  'Communication Services',
  'Industrials',
  'Consumer Staples',
  'Energy',
  'Utilities',
  'Real Estate',
  'Materials'
]

/**
 * Market exchanges
 */
export const EXCHANGES = [
  'NASDAQ',
  'NYSE',
  'AMEX',
  'OTC'
]



/**
 * API rate limiting configuration
 */
export const RATE_LIMITS = {
  alphavantage: {
    requests: 25,
    period: 'day',
    resetTime: '00:00 UTC'
  },
  finnhub: {
    requests: 60,
    period: 'minute',
    resetTime: 'rolling'
  },
  yahoo: {
    requests: 2000,
    period: 'hour',
    resetTime: 'rolling'
  }
}

/**
 * Cache TTL configuration (in milliseconds)
 */
export const CACHE_TTL = {
  quote: 60 * 1000,        // 1 minute for real-time quotes
  profile: 24 * 60 * 60 * 1000,  // 24 hours for company profiles
  historical: 60 * 60 * 1000,     // 1 hour for historical data
  search: 30 * 60 * 1000,         // 30 minutes for search results
  fundamentals: 24 * 60 * 60 * 1000  // 24 hours for fundamental data
}

/**
 * Market hours configuration (Eastern Time)
 */
export const MARKET_HOURS = {
  premarket: {
    start: '04:00',
    end: '09:30'
  },
  regular: {
    start: '09:30',
    end: '16:00'
  },
  afterhours: {
    start: '16:00',
    end: '20:00'
  }
}

/**
 * Check if market is currently open
 */
export function isMarketOpen(): boolean {
  const now = new Date()
  const easternTime = new Date(now.toLocaleString("en-US", {timeZone: "America/New_York"}))
  const day = easternTime.getDay() // 0 = Sunday, 6 = Saturday
  
  // Weekend check
  if (day === 0 || day === 6) return false
  
  const time = easternTime.toTimeString().slice(0, 5) // HH:MM format
  
  return time >= MARKET_HOURS.regular.start && time <= MARKET_HOURS.regular.end
}

/**
 * Get market status
 */
export function getMarketStatus(): 'closed' | 'premarket' | 'open' | 'afterhours' {
  const now = new Date()
  const easternTime = new Date(now.toLocaleString("en-US", {timeZone: "America/New_York"}))
  const day = easternTime.getDay()
  
  // Weekend
  if (day === 0 || day === 6) return 'closed'
  
  const time = easternTime.toTimeString().slice(0, 5)
  
  if (time >= MARKET_HOURS.premarket.start && time < MARKET_HOURS.regular.start) {
    return 'premarket'
  } else if (time >= MARKET_HOURS.regular.start && time <= MARKET_HOURS.regular.end) {
    return 'open'
  } else if (time > MARKET_HOURS.regular.end && time <= MARKET_HOURS.afterhours.end) {
    return 'afterhours'
  } else {
    return 'closed'
  }
}

/**
 * Error handling configuration
 */
export const ERROR_MESSAGES = {
  API_LIMIT_EXCEEDED: 'API rate limit exceeded. Please try again later.',
  API_KEY_MISSING: 'API key not configured. Using fallback data.',
  NETWORK_ERROR: 'Network error. Please check your connection.',
  INVALID_SYMBOL: 'Invalid stock symbol provided.',
  NO_DATA_AVAILABLE: 'No data available for this symbol.',
  SERVICE_UNAVAILABLE: 'Stock data service temporarily unavailable.'
}

/**
 * Logging configuration
 */
export const LOG_LEVELS = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3
}

export const LOG_LEVEL = process.env.NODE_ENV === 'development' ? LOG_LEVELS.DEBUG : LOG_LEVELS.INFO
