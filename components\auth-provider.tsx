"use client"

import type React from "react"
import { createContext, useContext, useEffect, useState, useMemo } from "react"
import { createClient, isSupabaseConfigured } from "@/lib/supabase"
import type { User, AuthError } from "@supabase/supabase-js"

interface AuthContextType {
  user: User | null
  loading: boolean
  signOut: () => Promise<void>
  signUp: (email: string, password: string, userData?: any) => Promise<{ data: any; error: AuthError | null }>
  signIn: (email: string, password: string) => Promise<{ data: any; error: AuthError | null }>
  isConfigured: boolean
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  signOut: async () => {},
  signUp: async () => ({ data: null, error: null }),
  signIn: async () => ({ data: null, error: null }),
  isConfigured: false,
})

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error("useAuth must be used within AuthProvider")
  }
  return context
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  const configured = useMemo(() => {
    try {
      return isSupabaseConfigured()
    } catch {
      return false
    }
  }, [])

  const supabase = useMemo(() => {
    try {
      return configured ? createClient() : null
    } catch {
      return null
    }
  }, [configured])

  useEffect(() => {
    let mounted = true

    if (!configured || !supabase) {
      console.warn("Supabase not configured. Please set up your environment variables.")
      if (mounted) {
        setLoading(false)
      }
      return
    }

    // Get initial session
    const getSession = async () => {
      try {
        const {
          data: { session },
        } = await supabase.auth.getSession()
        console.log("Initial session check:", session?.user?.email || "no user")
        if (mounted) {
          setUser(session?.user ?? null)
          setLoading(false)
        }
      } catch (error) {
        console.error("Auth error:", error)
        if (mounted) {
          setUser(null)
          setLoading(false)
        }
      }
    }

    getSession()

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, session) => {
      console.log("Auth state change:", event, session?.user?.email || "no user")
      if (mounted) {
        setUser(session?.user ?? null)
        setLoading(false)
      }
    })

    return () => {
      mounted = false
      subscription.unsubscribe()
    }
  }, [configured, supabase])

  const signOut = async () => {
    if (!configured || !supabase) {
      console.warn("Cannot sign out: Supabase not configured")
      return
    }

    // Clean up any old non-user-specific localStorage data to prevent cross-contamination
    const keysToClean = [
      'demo-portfolio',
      'user-portfolio',
      'investry_survey',
      'portfolio-metadata'
    ]

    keysToClean.forEach(key => {
      if (localStorage.getItem(key)) {
        console.log(`Cleaning up old localStorage key: ${key}`)
        localStorage.removeItem(key)
      }
    })

    await supabase.auth.signOut()
  }

  const signUp = async (email: string, password: string, userData?: any) => {
    if (!configured || !supabase) {
      return {
        data: null,
        error: { message: "Supabase not configured" } as AuthError
      }
    }

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: userData || {}
      }
    })

    return { data, error }
  }

  const signIn = async (email: string, password: string) => {
    if (!configured || !supabase) {
      return {
        data: null,
        error: { message: "Supabase not configured" } as AuthError
      }
    }

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })

    return { data, error }
  }

  const contextValue = useMemo(
    () => ({
      user,
      loading,
      signOut,
      signUp,
      signIn,
      isConfigured: configured,
    }),
    [user, loading, configured],
  )

  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
}
