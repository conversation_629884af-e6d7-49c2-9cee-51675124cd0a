"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Pie<PERSON>hart, TrendingUp, Target, Clock, BarChart3, CheckCircle, ArrowRight } from "lucide-react"
import type { PersonalizedPortfolio } from "@/lib/portfolio-generator"

interface PortfolioResultsProps {
  portfolio: PersonalizedPortfolio
  onAccept: () => void
}

export function PortfolioResults({ portfolio, onAccept }: PortfolioResultsProps) {
  const [activeTab, setActiveTab] = useState("overview")

  const totalAllocation = portfolio.allocations.reduce((sum, allocation) => sum + allocation.allocation, 0)

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-2 mb-4">
            <CheckCircle className="h-8 w-8 text-emerald-500" />
            <h1 className="text-3xl font-bold text-foreground">Your Personalized Portfolio</h1>
          </div>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Based on your responses, we've created a unique investment portfolio tailored to your goals, risk tolerance,
            and interests.
          </p>
        </div>

        {/* Portfolio Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="bg-card border-border hover-lift">
            <CardContent className="p-4 text-center">
              <TrendingUp className="h-8 w-8 text-emerald-500 mx-auto mb-2" />
              <div className="text-2xl font-bold text-foreground">{portfolio.expectedReturn}</div>
              <div className="text-sm text-muted-foreground">Expected Annual Return</div>
            </CardContent>
          </Card>
          <Card className="bg-card border-border hover-lift">
            <CardContent className="p-4 text-center">
              <Target className="h-8 w-8 text-blue-500 mx-auto mb-2" />
              <div className="text-2xl font-bold text-foreground">{portfolio.riskLevel}</div>
              <div className="text-sm text-muted-foreground">Risk Level</div>
            </CardContent>
          </Card>
          <Card className="bg-card border-border hover-lift">
            <CardContent className="p-4 text-center">
              <BarChart3 className="h-8 w-8 text-purple-500 mx-auto mb-2" />
              <div className="text-2xl font-bold text-foreground">{portfolio.allocations.length}</div>
              <div className="text-sm text-muted-foreground">Holdings</div>
            </CardContent>
          </Card>
          <Card className="bg-card border-border hover-lift">
            <CardContent className="p-4 text-center">
              <Clock className="h-8 w-8 text-orange-500 mx-auto mb-2" />
              <div className="text-2xl font-bold text-foreground">{portfolio.rebalanceFrequency}</div>
              <div className="text-sm text-muted-foreground">Rebalancing</div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="holdings">Holdings</TabsTrigger>
            <TabsTrigger value="strategy">Strategy</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Allocation Chart */}
              <Card className="bg-card border-border">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <PieChart className="h-5 w-5" />
                    Portfolio Allocation
                  </CardTitle>
                  <CardDescription>How your investments are distributed</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {portfolio.allocations.map((allocation, index) => (
                    <div key={allocation.symbol} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{
                              backgroundColor: `hsl(${(index * 137.5) % 360}, 70%, 50%)`,
                            }}
                          />
                          <span className="font-medium text-foreground">{allocation.symbol}</span>
                          <Badge variant="secondary" className="text-xs">
                            {allocation.category}
                          </Badge>
                        </div>
                        <span className="text-sm font-medium text-foreground">{allocation.allocation.toFixed(1)}%</span>
                      </div>
                      <Progress
                        value={allocation.allocation}
                        className="h-2"
                        style={
                          {
                            "--progress-color": `hsl(${(index * 137.5) % 360}, 70%, 50%)`,
                          } as React.CSSProperties
                        }
                      />
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Portfolio Rationale */}
              <Card className="bg-card border-border">
                <CardHeader>
                  <CardTitle>Why This Portfolio?</CardTitle>
                  <CardDescription>Tailored to your investment profile</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground leading-relaxed">{portfolio.rationale}</p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="holdings" className="space-y-6">
            <Card className="bg-card border-border">
              <CardHeader>
                <CardTitle>Individual Holdings</CardTitle>
                <CardDescription>Detailed breakdown of your portfolio components</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {portfolio.allocations.map((allocation, index) => (
                    <div
                      key={allocation.symbol}
                      className="flex items-center justify-between p-4 rounded-lg bg-accent/30 hover:bg-accent/50 transition-colors"
                    >
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <div
                            className="w-4 h-4 rounded-full"
                            style={{
                              backgroundColor: `hsl(${(index * 137.5) % 360}, 70%, 50%)`,
                            }}
                          />
                          <div>
                            <h3 className="font-semibold text-foreground">{allocation.symbol}</h3>
                            <p className="text-sm text-muted-foreground">{allocation.name}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2 mb-2">
                          <Badge variant="outline">{allocation.category}</Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">{allocation.rationale}</p>
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-foreground">{allocation.allocation.toFixed(1)}%</div>
                        <div className="text-sm text-muted-foreground">of portfolio</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="strategy" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-card border-border">
                <CardHeader>
                  <CardTitle>Investment Strategy</CardTitle>
                  <CardDescription>Your personalized approach</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-start gap-3">
                    <Target className="h-5 w-5 text-emerald-500 mt-1" />
                    <div>
                      <h4 className="font-medium text-foreground mb-1">Strategy</h4>
                      <p className="text-sm text-muted-foreground">{portfolio.strategy}</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <TrendingUp className="h-5 w-5 text-blue-500 mt-1" />
                    <div>
                      <h4 className="font-medium text-foreground mb-1">Expected Performance</h4>
                      <p className="text-sm text-muted-foreground">
                        Annual returns of {portfolio.expectedReturn} based on historical data and current market
                        conditions.
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Clock className="h-5 w-5 text-purple-500 mt-1" />
                    <div>
                      <h4 className="font-medium text-foreground mb-1">Rebalancing</h4>
                      <p className="text-sm text-muted-foreground">
                        We'll automatically rebalance your portfolio {portfolio.rebalanceFrequency.toLowerCase()} to
                        maintain optimal allocation.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-card border-border">
                <CardHeader>
                  <CardTitle>Next Steps</CardTitle>
                  <CardDescription>Getting started with your portfolio</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <div className="w-6 h-6 rounded-full bg-emerald-500 text-white text-xs flex items-center justify-center font-bold">
                        1
                      </div>
                      <span className="text-sm text-muted-foreground">Accept your personalized portfolio</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-6 h-6 rounded-full bg-muted text-muted-foreground text-xs flex items-center justify-center font-bold">
                        2
                      </div>
                      <span className="text-sm text-muted-foreground">Connect your bank account</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-6 h-6 rounded-full bg-muted text-muted-foreground text-xs flex items-center justify-center font-bold">
                        3
                      </div>
                      <span className="text-sm text-muted-foreground">Start investing with round-ups</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-6 h-6 rounded-full bg-muted text-muted-foreground text-xs flex items-center justify-center font-bold">
                        4
                      </div>
                      <span className="text-sm text-muted-foreground">Track your progress and learn</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* Action Button */}
        <div className="text-center">
          <Button
            onClick={onAccept}
            size="lg"
            className="bg-emerald-500 hover:bg-emerald-600 text-white px-8 py-3 text-lg hover-glow"
          >
            Accept Portfolio & Start Investing
            <ArrowRight className="ml-2 h-5 w-5" />
          </Button>
          <p className="text-sm text-muted-foreground mt-2">
            You can always adjust your portfolio later as your goals change
          </p>
        </div>
      </div>
    </div>
  )
}
