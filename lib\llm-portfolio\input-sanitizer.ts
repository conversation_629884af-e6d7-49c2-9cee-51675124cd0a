/**
 * Input Sanitization Service for LLM Portfolio System
 * SECURITY: Prevents prompt injection and data leakage
 */

export interface SanitizationOptions {
  maxLength?: number
  allowSpecialChars?: boolean
  strictMode?: boolean
}

export class InputSanitizer {
  
  /**
   * SECURITY: Comprehensive input sanitization
   */
  static sanitizeUserInput(input: string, options: SanitizationOptions = {}): string {
    if (!input || typeof input !== 'string') {
      return ''
    }

    const {
      maxLength = 1000,
      allowSpecialChars = false,
      strictMode = true
    } = options

    let sanitized = input

    // 1. Remove potential PII
    sanitized = this.removePII(sanitized)

    // 2. Remove prompt injection attempts
    sanitized = this.removePromptInjection(sanitized)

    // 3. Remove code injection attempts
    sanitized = this.removeCodeInjection(sanitized)

    // 4. Handle special characters
    if (!allowSpecialChars) {
      sanitized = this.removeExcessiveSpecialChars(sanitized)
    }

    // 5. Apply strict mode filters
    if (strictMode) {
      sanitized = this.applyStrictFilters(sanitized)
    }

    // 6. Normalize whitespace
    sanitized = this.normalizeWhitespace(sanitized)

    // 7. Apply length limits
    if (sanitized.length > maxLength) {
      sanitized = sanitized.substring(0, maxLength) + '...'
    }

    return sanitized.trim()
  }

  /**
   * SECURITY: Remove personally identifiable information
   */
  private static removePII(text: string): string {
    return text
      // Email addresses
      .replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '[EMAIL_REMOVED]')
      // Phone numbers (various formats)
      .replace(/\b(?:\+?1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}\b/g, '[PHONE_REMOVED]')
      // SSN patterns
      .replace(/\b\d{3}-\d{2}-\d{4}\b/g, '[SSN_REMOVED]')
      // Credit card patterns (basic)
      .replace(/\b(?:\d{4}[-\s]?){3}\d{4}\b/g, '[CARD_REMOVED]')
      // Account numbers (8+ consecutive digits)
      .replace(/\b\d{8,}\b/g, '[ACCOUNT_REMOVED]')
      // Names with common patterns
      .replace(/\b(my name is|i am|i'm|call me)\s+[A-Za-z]+\b/gi, '[NAME_REMOVED]')
      // Addresses (basic patterns)
      .replace(/\b\d+\s+[A-Za-z\s]+(?:street|st|avenue|ave|road|rd|drive|dr|lane|ln|boulevard|blvd)\b/gi, '[ADDRESS_REMOVED]')
  }

  /**
   * SECURITY: Remove prompt injection attempts
   */
  private static removePromptInjection(text: string): string {
    return text
      // Direct instruction overrides
      .replace(/\b(ignore|forget|disregard|override)\s+(previous|above|all|prior)\s+(instructions?|prompts?|rules?|commands?)\b/gi, '[INSTRUCTION_FILTERED]')
      // Role playing attempts
      .replace(/\b(act as|pretend to be|you are now|roleplay as|simulate)\s+[A-Za-z\s]+/gi, '[ROLE_FILTERED]')
      // System/admin commands
      .replace(/\b(system|admin|root|developer|sudo)\s+(prompt|instruction|command|mode)\b/gi, '[SYSTEM_FILTERED]')
      // Jailbreak attempts
      .replace(/\b(jailbreak|DAN|do anything now)\b/gi, '[JAILBREAK_FILTERED]')
      // Prompt continuation attempts
      .replace(/\b(continue|resume|restart)\s+(prompt|conversation|chat)\b/gi, '[CONTINUATION_FILTERED]')
      // Direct model instructions
      .replace(/\b(GPT|ChatGPT|Claude|AI|model),?\s+(please|now|you must|you should)\b/gi, '[MODEL_INSTRUCTION_FILTERED]')
  }

  /**
   * SECURITY: Remove code injection attempts
   */
  private static removeCodeInjection(text: string): string {
    return text
      // Script tags
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '[SCRIPT_REMOVED]')
      // JavaScript protocols
      .replace(/javascript:/gi, '[JS_REMOVED]')
      // Data URLs
      .replace(/data:[^;]+;base64,[A-Za-z0-9+/=]+/gi, '[DATA_URL_REMOVED]')
      // SQL injection patterns
      .replace(/\b(union|select|insert|update|delete|drop|create|alter)\s+/gi, '[SQL_REMOVED]')
      // Command injection
      .replace(/[;&|`$(){}[\]\\]/g, '')
      // HTML/XML tags
      .replace(/<[^>]*>/g, '[TAG_REMOVED]')
  }

  /**
   * SECURITY: Remove excessive special characters
   */
  private static removeExcessiveSpecialChars(text: string): string {
    // Replace sequences of 3+ special characters with a single space
    return text.replace(/[!@#$%^&*()_+=\[\]{}|;':",./<>?`~]{3,}/g, ' ')
  }

  /**
   * SECURITY: Apply strict mode filters
   */
  private static applyStrictFilters(text: string): string {
    return text
      // Remove markdown/formatting attempts
      .replace(/[*_`#]{2,}/g, '')
      // Remove potential encoding attempts
      .replace(/%[0-9A-Fa-f]{2}/g, '[ENCODED_REMOVED]')
      // Remove unicode escape attempts
      .replace(/\\u[0-9A-Fa-f]{4}/g, '[UNICODE_REMOVED]')
      // Remove excessive punctuation
      .replace(/[.!?]{4,}/g, '...')
  }

  /**
   * SECURITY: Normalize whitespace
   */
  private static normalizeWhitespace(text: string): string {
    return text
      // Replace multiple spaces with single space
      .replace(/\s+/g, ' ')
      // Remove leading/trailing whitespace
      .trim()
  }

  /**
   * SECURITY: Sanitize financial amounts to ranges
   */
  static sanitizeFinancialAmount(amount: number | string): string {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount
    
    if (isNaN(numAmount) || numAmount <= 0) {
      return 'Minimal Range'
    }

    // Convert to ranges to prevent exact amount disclosure
    if (numAmount <= 100) return 'Low Range ($0-$100)'
    if (numAmount <= 500) return 'Low-Medium Range ($100-$500)'
    if (numAmount <= 1000) return 'Medium Range ($500-$1000)'
    if (numAmount <= 2000) return 'Medium-High Range ($1000-$2000)'
    if (numAmount <= 5000) return 'High Range ($2000-$5000)'
    return 'Very High Range ($5000+)'
  }

  /**
   * SECURITY: Validate and sanitize investment themes
   */
  static sanitizeInvestmentThemes(themes: string[]): string[] {
    if (!Array.isArray(themes)) {
      return ['Diversified']
    }

    const allowedThemes = [
      'Technology', 'Healthcare', 'Finance', 'Energy', 'Real Estate',
      'International', 'Bonds', 'ETFs', 'Growth', 'Value', 'Dividend',
      'ESG', 'Small Cap', 'Large Cap', 'Emerging Markets', 'Commodities',
      'Infrastructure', 'Utilities', 'Consumer Goods', 'Industrial'
    ]

    const sanitizedThemes = themes
      .filter(theme => typeof theme === 'string')
      .map(theme => this.sanitizeUserInput(theme, { maxLength: 50, strictMode: true }))
      .filter(theme => allowedThemes.includes(theme))
      .slice(0, 5) // Limit to 5 themes

    return sanitizedThemes.length > 0 ? sanitizedThemes : ['Diversified']
  }

  /**
   * SECURITY: Sanitize user major/background
   */
  static sanitizeUserMajor(major: string): string {
    if (!major || typeof major !== 'string') {
      return 'General Studies'
    }

    const sanitized = this.sanitizeUserInput(major, { maxLength: 100, strictMode: true })
    
    // Map to broad categories to prevent specific identification
    const majorCategories: Record<string, string> = {
      'computer': 'Technology/Engineering',
      'software': 'Technology/Engineering',
      'engineering': 'Technology/Engineering',
      'technology': 'Technology/Engineering',
      'information': 'Technology/Engineering',
      'business': 'Business/Finance',
      'finance': 'Business/Finance',
      'economics': 'Business/Finance',
      'accounting': 'Business/Finance',
      'marketing': 'Business/Finance',
      'management': 'Business/Finance',
      'healthcare': 'Healthcare/Life Sciences',
      'medical': 'Healthcare/Life Sciences',
      'biology': 'Healthcare/Life Sciences',
      'nursing': 'Healthcare/Life Sciences',
      'education': 'Education/Liberal Arts',
      'psychology': 'Education/Liberal Arts',
      'english': 'Education/Liberal Arts',
      'history': 'Education/Liberal Arts',
      'art': 'Education/Liberal Arts',
      'liberal': 'Education/Liberal Arts'
    }

    const normalizedMajor = sanitized.toLowerCase()
    
    for (const [key, category] of Object.entries(majorCategories)) {
      if (normalizedMajor.includes(key)) {
        return category
      }
    }

    return 'Other'
  }

  /**
   * SECURITY: Check if input contains potential security risks
   */
  static containsSecurityRisks(input: string): boolean {
    if (!input || typeof input !== 'string') {
      return false
    }

    const riskPatterns = [
      // Prompt injection indicators
      /\b(ignore|forget|disregard)\s+(previous|above|all)\s+(instructions?|prompts?)\b/i,
      // Code injection indicators
      /<script|javascript:|data:/i,
      // SQL injection indicators
      /\b(union|select|insert|update|delete)\s+/i,
      // System command indicators
      /\b(system|admin|root)\s+(prompt|command)\b/i,
      // Excessive special characters (potential obfuscation)
      /[!@#$%^&*()_+=\[\]{}|;':",./<>?`~]{10,}/
    ]

    return riskPatterns.some(pattern => pattern.test(input))
  }

  /**
   * SECURITY: Generate sanitization report
   */
  static generateSanitizationReport(original: string, sanitized: string): {
    originalLength: number
    sanitizedLength: number
    changesDetected: boolean
    riskLevel: 'low' | 'medium' | 'high'
    modifications: string[]
  } {
    const modifications: string[] = []
    
    if (original.length !== sanitized.length) {
      modifications.push('Length modified')
    }
    
    if (original !== sanitized) {
      modifications.push('Content sanitized')
    }

    const riskLevel = this.containsSecurityRisks(original) ? 'high' : 
                     modifications.length > 0 ? 'medium' : 'low'

    return {
      originalLength: original.length,
      sanitizedLength: sanitized.length,
      changesDetected: original !== sanitized,
      riskLevel,
      modifications
    }
  }
}
