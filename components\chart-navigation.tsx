/**
 * Chart Navigation Components
 * Quick access to different chart types and navigation helpers
 */

'use client'

import React from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  BarChart3,
  LineChart,
  Activity,
  TrendingUp,
  PieChart,
  ChevronDown,
  ExternalLink,
  ArrowLeft
} from 'lucide-react'

/**
 * Chart Quick Access Menu
 */
export function ChartQuickAccess() {
  const router = useRouter()

  const chartTypes = [
    {
      name: 'Stock Charts',
      description: 'View individual stock price movements',
      icon: LineChart,
      href: '/charts?tab=stock',
      badge: 'Live Data'
    },
    {
      name: 'Portfolio Performance',
      description: 'Track your portfolio over time',
      icon: TrendingUp,
      href: '/charts?tab=portfolio',
      badge: 'Real-time'
    },
    {
      name: 'Compact Charts',
      description: 'Quick overview widgets',
      icon: BarChart3,
      href: '/charts?tab=compact',
      badge: 'Dashboard'
    },
    {
      name: 'Interactive Demo',
      description: 'Live chart demonstrations',
      icon: Activity,
      href: '/charts?tab=demo',
      badge: 'Demo'
    }
  ]

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="gap-2">
          <BarChart3 className="h-4 w-4" />
          Charts
          <ChevronDown className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80">
        <DropdownMenuLabel>Financial Charts</DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {chartTypes.map((chart, index) => (
          <DropdownMenuItem
            key={index}
            onClick={() => router.push(chart.href)}
            className="flex items-start gap-3 p-3 cursor-pointer"
          >
            <chart.icon className="h-5 w-5 mt-0.5 text-muted-foreground" />
            <div className="flex-1 space-y-1">
              <div className="flex items-center gap-2">
                <span className="font-medium">{chart.name}</span>
                <Badge variant="secondary" className="text-xs">
                  {chart.badge}
                </Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                {chart.description}
              </p>
            </div>
            <ExternalLink className="h-4 w-4 text-muted-foreground" />
          </DropdownMenuItem>
        ))}
        
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => router.push('/test-charts')}>
          <Activity className="h-4 w-4 mr-2" />
          Chart Testing
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

/**
 * Chart Navigation Cards
 */
export function ChartNavigationCards() {
  const chartSections = [
    {
      title: 'Stock Price Charts',
      description: 'Interactive stock price movements with time range controls',
      icon: LineChart,
      href: '/charts?tab=stock',
      features: ['Real-time data', 'Multiple timeframes', '$ and % views', 'Interactive tooltips']
    },
    {
      title: 'Portfolio Performance',
      description: 'Track your investment portfolio performance over time',
      icon: TrendingUp,
      href: '/charts?tab=portfolio',
      features: ['Portfolio timeline', 'Deposit tracking', 'Return calculations', 'Performance metrics']
    },
    {
      title: 'Dashboard Widgets',
      description: 'Compact charts perfect for dashboard integration',
      icon: BarChart3,
      href: '/charts?tab=compact',
      features: ['Compact design', 'Quick overview', 'Multiple symbols', 'Responsive layout']
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {chartSections.map((section, index) => (
        <Card key={index} className="hover:shadow-lg transition-shadow">
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <section.icon className="h-6 w-6 text-primary" />
              </div>
              <div>
                <CardTitle className="text-lg">{section.title}</CardTitle>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              {section.description}
            </p>
            
            <ul className="space-y-1">
              {section.features.map((feature, featureIndex) => (
                <li key={featureIndex} className="text-sm flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-primary rounded-full" />
                  {feature}
                </li>
              ))}
            </ul>
            
            <Link href={section.href}>
              <Button className="w-full" variant="outline">
                View Charts
                <ExternalLink className="h-4 w-4 ml-2" />
              </Button>
            </Link>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

/**
 * Back to Dashboard Button
 */
export function BackToDashboard() {
  const router = useRouter()

  return (
    <Button
      variant="outline"
      onClick={() => router.push('/dashboard')}
      className="gap-2"
    >
      <ArrowLeft className="h-4 w-4" />
      Back to Dashboard
    </Button>
  )
}

/**
 * Chart Navigation Breadcrumb
 */
export function ChartBreadcrumb({ currentChart }: { currentChart?: string }) {
  const breadcrumbItems = [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Charts', href: '/charts' }
  ]

  if (currentChart) {
    breadcrumbItems.push({ label: currentChart })
  }

  return (
    <nav className="flex items-center space-x-2 text-sm text-muted-foreground">
      {breadcrumbItems.map((item, index) => (
        <React.Fragment key={index}>
          {index > 0 && <span>/</span>}
          {item.href ? (
            <Link href={item.href} className="hover:text-foreground">
              {item.label}
            </Link>
          ) : (
            <span className="text-foreground font-medium">{item.label}</span>
          )}
        </React.Fragment>
      ))}
    </nav>
  )
}

/**
 * Chart Type Selector
 */
export interface ChartTypeSelectorProps {
  currentType: string
  onTypeChange: (type: string) => void
  types: Array<{
    id: string
    label: string
    icon: React.ComponentType<{ className?: string }>
    description?: string
  }>
}

export function ChartTypeSelector({ currentType, onTypeChange, types }: ChartTypeSelectorProps) {
  return (
    <div className="flex flex-wrap gap-2">
      {types.map((type) => (
        <Button
          key={type.id}
          variant={currentType === type.id ? 'default' : 'outline'}
          size="sm"
          onClick={() => onTypeChange(type.id)}
          className="gap-2"
        >
          <type.icon className="h-4 w-4" />
          {type.label}
        </Button>
      ))}
    </div>
  )
}

/**
 * Chart Help Button
 */
export function ChartHelp() {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm">
          <PieChart className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-64">
        <DropdownMenuLabel>Chart Help</DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        <div className="p-3 space-y-2 text-sm">
          <div>
            <strong>Time Ranges:</strong>
            <p className="text-muted-foreground">1D, 1W, 1M, 3M, 1Y, All</p>
          </div>
          
          <div>
            <strong>Value Types:</strong>
            <p className="text-muted-foreground">$ (absolute) or % (percentage)</p>
          </div>
          
          <div>
            <strong>Interactions:</strong>
            <p className="text-muted-foreground">Hover for tooltips, click time ranges</p>
          </div>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
