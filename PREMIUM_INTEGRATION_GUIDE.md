# Premium Feature Integration Guide

This guide shows you how to integrate premium feature gates throughout your Investry application.

## Components Available

### 1. PremiumGate Component
Use this to wrap entire features or sections that require premium access.

```tsx
import { PremiumGate } from "@/components/premium-gate"

// Wrap premium features
<PremiumGate 
  feature="Portfolio Rebalancing" 
  description="Automatically rebalance your portfolio based on your target allocation"
>
  {/* Your premium feature content here */}
  <PortfolioRebalancingTool />
</PremiumGate>
```

### 2. PremiumBadge Component
Use this for inline upgrade prompts next to feature names.

```tsx
import { PremiumBadge } from "@/components/premium-gate"

<div className="flex items-center gap-2">
  <h3>Advanced Analytics</h3>
  <PremiumBadge feature="Advanced Analytics" />
</div>
```

### 3. usePremiumAccess Hook
Use this for conditional logic in your components.

```tsx
import { usePremiumAccess } from "@/components/premium-gate"

function MyComponent() {
  const { hasAccess, showUpgrade } = usePremiumAccess()
  
  const handlePremiumFeature = () => {
    if (!hasAccess) {
      showUpgrade()
      return
    }
    // Execute premium feature
  }
}
```

## Integration Examples

### Example 1: Portfolio Rebalancing Feature

```tsx
// In your portfolio page
import { PremiumGate } from "@/components/premium-gate"

function PortfolioPage() {
  return (
    <div>
      {/* Regular portfolio content */}
      <PortfolioOverview />
      
      {/* Premium feature */}
      <PremiumGate 
        feature="Automatic Portfolio Rebalancing" 
        description="Keep your portfolio aligned with your target allocation automatically"
      >
        <AutoRebalancingSettings />
      </PremiumGate>
    </div>
  )
}
```

### Example 2: Advanced Charts

```tsx
// In your stock analysis page
import { PremiumGate, PremiumBadge } from "@/components/premium-gate"

function StockAnalysisPage() {
  return (
    <div>
      {/* Basic chart - always available */}
      <BasicStockChart />
      
      {/* Premium charts */}
      <div className="mt-6">
        <div className="flex items-center gap-2 mb-4">
          <h3>Advanced Technical Analysis</h3>
          <PremiumBadge feature="Advanced Charts" />
        </div>
        
        <PremiumGate 
          feature="Advanced Technical Analysis" 
          description="Access professional-grade charting tools and indicators"
        >
          <AdvancedTechnicalCharts />
        </PremiumGate>
      </div>
    </div>
  )
}
```

### Example 3: Conditional Feature Access

```tsx
// In a component that needs conditional premium logic
import { usePremiumAccess } from "@/components/premium-gate"

function InvestmentButton() {
  const { hasAccess } = usePremiumAccess()
  
  return (
    <Button 
      onClick={() => {
        if (hasAccess) {
          // Execute premium investment strategy
          executeAdvancedStrategy()
        } else {
          // Redirect to upgrade
          router.push("/pricing")
        }
      }}
      className={hasAccess ? "bg-emerald-500" : "bg-slate-600"}
    >
      {hasAccess ? "Execute Advanced Strategy" : "Upgrade for Advanced Strategies"}
    </Button>
  )
}
```

## Where to Add Premium Gates

### High-Value Features to Gate:

1. **Portfolio Management**
   - Automatic rebalancing
   - Advanced portfolio analytics
   - Risk analysis tools
   - Performance attribution

2. **Research & Analysis**
   - Advanced stock screening
   - Professional research reports
   - Real-time market data
   - Custom indicators

3. **Alerts & Automation**
   - Custom price alerts
   - Portfolio alerts
   - Automated investing rules
   - Email/SMS notifications

4. **Data & Export**
   - Data export capabilities
   - Historical data access
   - API access
   - Advanced reporting

### Implementation Strategy:

1. **Start with high-impact features** that users will want to upgrade for
2. **Keep core functionality free** (basic portfolio tracking, stock search, etc.)
3. **Show value before asking** - let users see what they're missing
4. **Make upgrading easy** - always provide clear upgrade paths

## Current Integration Status

✅ **Navigation**: Premium badge for premium users, upgrade prompts in profile/settings
✅ **Profile Page**: Upgrade section for free users
✅ **Settings Page**: Upgrade section for free users  
✅ **Subscription Management**: Full subscription lifecycle management
✅ **Premium Gate Component**: Ready to use throughout the app

## Next Steps

1. **Identify premium features** in your existing codebase
2. **Wrap them with PremiumGate** components
3. **Add PremiumBadge** components to feature lists
4. **Test the upgrade flow** end-to-end
5. **Monitor conversion rates** and adjust messaging

## Example Usage in Existing Pages

### Dashboard Premium Features
```tsx
// Add to dashboard for premium analytics
<PremiumGate feature="Advanced Portfolio Analytics">
  <AdvancedPortfolioCharts />
</PremiumGate>
```

### Investments Page Premium Features
```tsx
// Add to investments for advanced tools
<PremiumGate feature="Portfolio Rebalancing">
  <RebalancingTool />
</PremiumGate>
```

### Stock Detail Premium Features
```tsx
// Add to stock pages for advanced analysis
<PremiumGate feature="Professional Research Reports">
  <ResearchReports symbol={symbol} />
</PremiumGate>
```

This system provides a clean, consistent way to gate premium features while providing clear upgrade paths for users.
