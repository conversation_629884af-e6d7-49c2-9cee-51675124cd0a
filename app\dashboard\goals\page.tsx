"use client"

import { TopNav } from "@/components/top-nav"
import { BottomNav } from "@/components/bottom-nav"
import { GoalTracker } from "@/components/goal-tracker"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import { useRouter } from "next/navigation"

export default function GoalsPage() {
  const router = useRouter()

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      <TopNav />
      
      <div className="flex-1 pb-20">
        <div className="container mx-auto px-4 py-6 space-y-6">
          {/* Header */}
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push("/dashboard")}
              className="text-slate-400 hover:text-white"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
            <div className="space-y-1">
              <h1 className="text-3xl font-bold text-white">Financial Goals</h1>
              <p className="text-slate-400">Track your progress toward achieving your financial objectives.</p>
            </div>
          </div>

          {/* Goal Tracker Component - Full Version */}
          <GoalTracker showAddButton={true} />
        </div>
      </div>

      <BottomNav />
    </div>
  )
}
