import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { AuthProvider } from "@/components/auth-provider"
import { AuthGuard } from "@/components/auth-guard"
import { NotificationProvider } from "@/components/notification-system"
import { PersistentNotificationProvider } from "@/components/persistent-notifications"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "investry - AI-Backed Smart Investing",
  description: "Learn to invest with confidence. Built for college students."
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="dark" enableSystem disableTransitionOnChange>
          <AuthProvider>
            <AuthGuard>
              <PersistentNotificationProvider>
                <NotificationProvider>{children}</NotificationProvider>
              </PersistentNotificationProvider>
            </AuthGuard>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
