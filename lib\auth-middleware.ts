/**
 * Authentication Middleware
 * Handles Supabase session validation for protected routes
 */

import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export interface AuthenticatedUser {
  id: string
  email: string
  user_metadata?: any
  app_metadata?: any
}

export interface AuthMiddlewareResult {
  success: boolean
  user?: AuthenticatedUser
  error?: string
  response?: NextResponse
}

/**
 * Middleware to authenticate requests using Supabase
 */
export async function authenticateRequest(request: NextRequest): Promise<AuthMiddlewareResult> {
  try {
    // Get auth token from header
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return {
        success: false,
        error: 'Authentication required - missing token',
        response: NextResponse.json(
          {
            error: 'Authentication required',
            code: 'MISSING_TOKEN',
            message: 'Authorization header with Bearer token is required'
          },
          { status: 401 }
        )
      }
    }

    const token = authHeader.replace('Bearer ', '')

    // Create Supabase client for server-side token validation
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    )

    // Get user from Supabase auth using the token
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError) {
      console.error('Auth error:', authError)
      return {
        success: false,
        error: 'Authentication failed',
        response: NextResponse.json(
          {
            error: 'Authentication failed',
            code: 'AUTH_FAILED',
            message: authError.message
          },
          { status: 401 }
        )
      }
    }

    if (!user) {
      return {
        success: false,
        error: 'No user found',
        response: NextResponse.json(
          { 
            error: 'Authentication required',
            code: 'AUTH_REQUIRED',
            message: 'Please log in to access this resource'
          },
          { status: 401 }
        )
      }
    }

    // Return authenticated user
    return {
      success: true,
      user: {
        id: user.id,
        email: user.email || '',
        user_metadata: user.user_metadata,
        app_metadata: user.app_metadata
      }
    }

  } catch (error) {
    console.error('Authentication middleware error:', error)
    return {
      success: false,
      error: 'Internal authentication error',
      response: NextResponse.json(
        { 
          error: 'Internal server error',
          code: 'INTERNAL_ERROR',
          message: 'Authentication service unavailable'
        },
        { status: 500 }
      )
    }
  }
}

/**
 * Higher-order function to wrap API routes with authentication
 */
export function withAuth<T extends any[]>(
  handler: (request: NextRequest, user: AuthenticatedUser, ...args: T) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    const authResult = await authenticateRequest(request)
    
    if (!authResult.success) {
      return authResult.response!
    }

    try {
      return await handler(request, authResult.user!, ...args)
    } catch (error) {
      console.error('Handler error:', error)
      return NextResponse.json(
        { 
          error: 'Internal server error',
          code: 'HANDLER_ERROR',
          message: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      )
    }
  }
}

/**
 * Validate user has access to specific portfolio
 */
export async function validatePortfolioAccess(
  userId: string, 
  portfolioId?: string
): Promise<{ success: boolean; portfolioId?: string; error?: string }> {
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )
    
    let query = supabase
      .from('portfolios')
      .select('id, user_id, name')
      .eq('user_id', userId)

    if (portfolioId) {
      query = query.eq('id', portfolioId)
    } else {
      query = query.eq('is_default', true)
    }

    const { data, error } = await query.single()

    if (error) {
      console.error('Portfolio access validation error:', error)
      return {
        success: false,
        error: portfolioId ? 'Portfolio not found or access denied' : 'No default portfolio found'
      }
    }

    if (!data) {
      return {
        success: false,
        error: 'Portfolio not found'
      }
    }

    return {
      success: true,
      portfolioId: data.id
    }

  } catch (error) {
    console.error('Portfolio validation error:', error)
    return {
      success: false,
      error: 'Failed to validate portfolio access'
    }
  }
}

/**
 * Check if user has required permissions
 */
export async function checkUserPermissions(
  userId: string,
  requiredPermissions: string[] = []
): Promise<{ success: boolean; permissions?: string[]; error?: string }> {
  try {
    // For now, all authenticated users have basic permissions
    // This can be extended with role-based access control
    const basicPermissions = [
      'portfolio:read',
      'portfolio:write',
      'transactions:read',
      'transactions:write',
      'balance:read'
    ]

    // Check if user has all required permissions
    const hasAllPermissions = requiredPermissions.every(perm => 
      basicPermissions.includes(perm)
    )

    if (!hasAllPermissions) {
      return {
        success: false,
        error: 'Insufficient permissions'
      }
    }

    return {
      success: true,
      permissions: basicPermissions
    }

  } catch (error) {
    console.error('Permission check error:', error)
    return {
      success: false,
      error: 'Failed to check permissions'
    }
  }
}

/**
 * Rate limiting middleware (basic implementation)
 */
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()

export function checkRateLimit(
  userId: string,
  maxRequests: number = 100,
  windowMs: number = 60 * 1000 // 1 minute
): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now()
  const userLimit = rateLimitMap.get(userId)

  if (!userLimit || now >= userLimit.resetTime) {
    // Reset or initialize rate limit
    rateLimitMap.set(userId, {
      count: 1,
      resetTime: now + windowMs
    })
    return {
      allowed: true,
      remaining: maxRequests - 1,
      resetTime: now + windowMs
    }
  }

  if (userLimit.count >= maxRequests) {
    return {
      allowed: false,
      remaining: 0,
      resetTime: userLimit.resetTime
    }
  }

  userLimit.count++
  return {
    allowed: true,
    remaining: maxRequests - userLimit.count,
    resetTime: userLimit.resetTime
  }
}

/**
 * Enhanced auth wrapper with rate limiting and permissions
 */
export function withAuthAndPermissions<T extends any[]>(
  handler: (request: NextRequest, user: AuthenticatedUser, ...args: T) => Promise<NextResponse>,
  options: {
    requiredPermissions?: string[]
    rateLimit?: { maxRequests: number; windowMs: number }
    validatePortfolio?: boolean
  } = {}
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    // Authenticate user
    const authResult = await authenticateRequest(request)
    if (!authResult.success) {
      return authResult.response!
    }

    const user = authResult.user!

    // Check rate limiting
    if (options.rateLimit) {
      const rateCheck = checkRateLimit(
        user.id,
        options.rateLimit.maxRequests,
        options.rateLimit.windowMs
      )

      if (!rateCheck.allowed) {
        return NextResponse.json(
          {
            error: 'Rate limit exceeded',
            code: 'RATE_LIMIT_EXCEEDED',
            resetTime: rateCheck.resetTime
          },
          { 
            status: 429,
            headers: {
              'X-RateLimit-Limit': options.rateLimit.maxRequests.toString(),
              'X-RateLimit-Remaining': rateCheck.remaining.toString(),
              'X-RateLimit-Reset': rateCheck.resetTime.toString()
            }
          }
        )
      }
    }

    // Check permissions
    if (options.requiredPermissions && options.requiredPermissions.length > 0) {
      const permissionCheck = await checkUserPermissions(user.id, options.requiredPermissions)
      if (!permissionCheck.success) {
        return NextResponse.json(
          {
            error: permissionCheck.error,
            code: 'INSUFFICIENT_PERMISSIONS'
          },
          { status: 403 }
        )
      }
    }

    // Validate portfolio access if required
    if (options.validatePortfolio) {
      const { searchParams } = new URL(request.url)
      const portfolioId = searchParams.get('portfolio_id')
      
      const portfolioCheck = await validatePortfolioAccess(user.id, portfolioId || undefined)
      if (!portfolioCheck.success) {
        return NextResponse.json(
          {
            error: portfolioCheck.error,
            code: 'PORTFOLIO_ACCESS_DENIED'
          },
          { status: 403 }
        )
      }
    }

    try {
      return await handler(request, user, ...args)
    } catch (error) {
      console.error('Handler error:', error)
      return NextResponse.json(
        { 
          error: 'Internal server error',
          code: 'HANDLER_ERROR',
          message: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      )
    }
  }
}
