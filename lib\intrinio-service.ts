/**
 * Intrinio Integration
 * Enterprise tier service for valuation models, ESG data, and peer comparisons
 */

import { StockFinancials } from './stock-data-service'

export interface IntrinioValuation {
  symbol: string
  enterprise_value: number
  market_capitalization: number
  price_to_earnings: number
  price_to_book: number
  price_to_sales: number
  ev_to_revenue: number
  ev_to_ebitda: number
  debt_to_equity: number
  return_on_equity: number
  return_on_assets: number
  current_ratio: number
  quick_ratio: number
  gross_margin: number
  operating_margin: number
  net_margin: number
  asset_turnover: number
  inventory_turnover: number
  receivables_turnover: number
}

export interface IntrinioESGData {
  symbol: string
  esg_score: number
  environment_score: number
  social_score: number
  governance_score: number
  controversy_score: number
  esg_grade: 'A+' | 'A' | 'A-' | 'B+' | 'B' | 'B-' | 'C+' | 'C' | 'C-' | 'D+' | 'D' | 'D-'
  carbon_intensity: number
  water_intensity: number
  waste_intensity: number
  board_diversity: number
  executive_compensation_ratio: number
  data_privacy_score: number
  last_updated: string
}

export interface IntrinioPeerComparison {
  symbol: string
  peers: Array<{
    symbol: string
    name: string
    market_cap: number
    pe_ratio: number
    price_to_book: number
    dividend_yield: number
    beta: number
    similarity_score: number
  }>
  industry_averages: {
    pe_ratio: number
    price_to_book: number
    debt_to_equity: number
    roe: number
    gross_margin: number
    operating_margin: number
  }
  percentile_rankings: {
    market_cap: number
    pe_ratio: number
    roe: number
    debt_to_equity: number
    gross_margin: number
  }
}

export interface IntrinioAnalystEstimates {
  symbol: string
  fiscal_year: number
  fiscal_period: string
  estimates: {
    revenue: {
      mean: number
      high: number
      low: number
      std_dev: number
      count: number
    }
    eps: {
      mean: number
      high: number
      low: number
      std_dev: number
      count: number
    }
    ebitda: {
      mean: number
      high: number
      low: number
      std_dev: number
      count: number
    }
  }
  revisions: {
    revenue_revisions_up: number
    revenue_revisions_down: number
    eps_revisions_up: number
    eps_revisions_down: number
  }
}

export class IntrinioService {
  private apiKey: string
  private baseUrl = 'https://api-v2.intrinio.com'
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>()

  constructor(apiKey: string) {
    this.apiKey = apiKey
  }

  /**
   * Get comprehensive valuation metrics
   */
  async getValuationMetrics(symbol: string): Promise<IntrinioValuation | null> {
    const cacheKey = `intrinio-valuation:${symbol}`
    
    const cached = this.getFromCache<IntrinioValuation>(cacheKey)
    if (cached) return cached

    try {
      // Get fundamental data
      const response = await fetch(
        `${this.baseUrl}/companies/${symbol}/data_point/market_capitalization,enterprise_value,pricetoearnings,pricetobook,pricetosales,evtorevenue,evtoebitda,debtoequity,returnonequity,returnonassets,currentratio,quickratio,grossmargin,operatingmargin,netmargin,assetturnover,inventoryturnover,receivablesturnover?api_key=${this.apiKey}`
      )

      if (!response.ok) return null

      const data = await response.json()
      if (!data.data) return null

      // Parse the data points
      const dataPoints = data.data.reduce((acc: any, item: any) => {
        acc[item.tag] = item.value
        return acc
      }, {})

      const valuation: IntrinioValuation = {
        symbol,
        enterprise_value: dataPoints.enterprise_value || 0,
        market_capitalization: dataPoints.market_capitalization || 0,
        price_to_earnings: dataPoints.pricetoearnings || 0,
        price_to_book: dataPoints.pricetobook || 0,
        price_to_sales: dataPoints.pricetosales || 0,
        ev_to_revenue: dataPoints.evtorevenue || 0,
        ev_to_ebitda: dataPoints.evtoebitda || 0,
        debt_to_equity: dataPoints.debtoequity || 0,
        return_on_equity: dataPoints.returnonequity || 0,
        return_on_assets: dataPoints.returnonassets || 0,
        current_ratio: dataPoints.currentratio || 0,
        quick_ratio: dataPoints.quickratio || 0,
        gross_margin: dataPoints.grossmargin || 0,
        operating_margin: dataPoints.operatingmargin || 0,
        net_margin: dataPoints.netmargin || 0,
        asset_turnover: dataPoints.assetturnover || 0,
        inventory_turnover: dataPoints.inventoryturnover || 0,
        receivables_turnover: dataPoints.receivablesturnover || 0
      }

      this.setCache(cacheKey, valuation, 60 * 60 * 1000) // 1 hour cache
      return valuation
    } catch (error) {
      console.error('Intrinio valuation error:', error)
      return null
    }
  }

  /**
   * Get ESG (Environmental, Social, Governance) data
   */
  async getESGData(symbol: string): Promise<IntrinioESGData | null> {
    const cacheKey = `intrinio-esg:${symbol}`
    
    const cached = this.getFromCache<IntrinioESGData>(cacheKey)
    if (cached) return cached

    try {
      const response = await fetch(
        `${this.baseUrl}/companies/${symbol}/esg?api_key=${this.apiKey}`
      )

      if (!response.ok) return null

      const data = await response.json()
      if (!data) return null

      const esgData: IntrinioESGData = {
        symbol,
        esg_score: data.esg_score || 0,
        environment_score: data.environment_score || 0,
        social_score: data.social_score || 0,
        governance_score: data.governance_score || 0,
        controversy_score: data.controversy_score || 0,
        esg_grade: data.esg_grade || 'C',
        carbon_intensity: data.carbon_intensity || 0,
        water_intensity: data.water_intensity || 0,
        waste_intensity: data.waste_intensity || 0,
        board_diversity: data.board_diversity || 0,
        executive_compensation_ratio: data.executive_compensation_ratio || 0,
        data_privacy_score: data.data_privacy_score || 0,
        last_updated: data.last_updated || new Date().toISOString()
      }

      this.setCache(cacheKey, esgData, 24 * 60 * 60 * 1000) // 24 hour cache
      return esgData
    } catch (error) {
      console.error('Intrinio ESG error:', error)
      return null
    }
  }

  /**
   * Get peer comparison analysis
   */
  async getPeerComparison(symbol: string): Promise<IntrinioPeerComparison | null> {
    const cacheKey = `intrinio-peers:${symbol}`
    
    const cached = this.getFromCache<IntrinioPeerComparison>(cacheKey)
    if (cached) return cached

    try {
      // Get company peers
      const peersResponse = await fetch(
        `${this.baseUrl}/companies/${symbol}/peers?api_key=${this.apiKey}`
      )

      if (!peersResponse.ok) return null

      const peersData = await peersResponse.json()
      if (!peersData.peers) return null

      // Get industry averages
      const companyResponse = await fetch(
        `${this.baseUrl}/companies/${symbol}?api_key=${this.apiKey}`
      )

      let industryCode = 'unknown'
      if (companyResponse.ok) {
        const companyData = await companyResponse.json()
        industryCode = companyData.industry_code || 'unknown'
      }

      // Get detailed peer data
      const peerDetails = await Promise.all(
        peersData.peers.slice(0, 10).map(async (peerSymbol: string) => {
          try {
            const peerResponse = await fetch(
              `${this.baseUrl}/companies/${peerSymbol}/data_point/market_capitalization,pricetoearnings,pricetobook,dividendyield,beta?api_key=${this.apiKey}`
            )

            if (!peerResponse.ok) return null

            const peerData = await peerResponse.json()
            const peerDataPoints = peerData.data.reduce((acc: any, item: any) => {
              acc[item.tag] = item.value
              return acc
            }, {})

            return {
              symbol: peerSymbol,
              name: peerSymbol, // Would need separate call for company name
              market_cap: peerDataPoints.market_capitalization || 0,
              pe_ratio: peerDataPoints.pricetoearnings || 0,
              price_to_book: peerDataPoints.pricetobook || 0,
              dividend_yield: peerDataPoints.dividendyield || 0,
              beta: peerDataPoints.beta || 0,
              similarity_score: Math.random() * 100 // Placeholder - would need complex calculation
            }
          } catch (error) {
            return null
          }
        })
      )

      const validPeers = peerDetails.filter(peer => peer !== null)

      // Calculate industry averages from peers
      const industryAverages = {
        pe_ratio: validPeers.reduce((sum, peer) => sum + peer.pe_ratio, 0) / validPeers.length,
        price_to_book: validPeers.reduce((sum, peer) => sum + peer.price_to_book, 0) / validPeers.length,
        debt_to_equity: 0, // Would need additional data
        roe: 0, // Would need additional data
        gross_margin: 0, // Would need additional data
        operating_margin: 0 // Would need additional data
      }

      // Calculate percentile rankings (simplified)
      const percentileRankings = {
        market_cap: 50, // Placeholder
        pe_ratio: 50, // Placeholder
        roe: 50, // Placeholder
        debt_to_equity: 50, // Placeholder
        gross_margin: 50 // Placeholder
      }

      const peerComparison: IntrinioPeerComparison = {
        symbol,
        peers: validPeers,
        industry_averages: industryAverages,
        percentile_rankings: percentileRankings
      }

      this.setCache(cacheKey, peerComparison, 4 * 60 * 60 * 1000) // 4 hour cache
      return peerComparison
    } catch (error) {
      console.error('Intrinio peer comparison error:', error)
      return null
    }
  }

  /**
   * Get analyst estimates
   */
  async getAnalystEstimates(symbol: string, fiscalYear?: number): Promise<IntrinioAnalystEstimates | null> {
    const cacheKey = `intrinio-estimates:${symbol}:${fiscalYear || 'current'}`
    
    const cached = this.getFromCache<IntrinioAnalystEstimates>(cacheKey)
    if (cached) return cached

    try {
      const year = fiscalYear || new Date().getFullYear()
      
      const response = await fetch(
        `${this.baseUrl}/companies/${symbol}/zacks/analyst_ratings?fiscal_year=${year}&api_key=${this.apiKey}`
      )

      if (!response.ok) return null

      const data = await response.json()
      if (!data.estimates) return null

      const estimates: IntrinioAnalystEstimates = {
        symbol,
        fiscal_year: year,
        fiscal_period: data.fiscal_period || 'FY',
        estimates: {
          revenue: {
            mean: data.estimates.revenue_mean || 0,
            high: data.estimates.revenue_high || 0,
            low: data.estimates.revenue_low || 0,
            std_dev: data.estimates.revenue_std_dev || 0,
            count: data.estimates.revenue_count || 0
          },
          eps: {
            mean: data.estimates.eps_mean || 0,
            high: data.estimates.eps_high || 0,
            low: data.estimates.eps_low || 0,
            std_dev: data.estimates.eps_std_dev || 0,
            count: data.estimates.eps_count || 0
          },
          ebitda: {
            mean: data.estimates.ebitda_mean || 0,
            high: data.estimates.ebitda_high || 0,
            low: data.estimates.ebitda_low || 0,
            std_dev: data.estimates.ebitda_std_dev || 0,
            count: data.estimates.ebitda_count || 0
          }
        },
        revisions: {
          revenue_revisions_up: data.revisions?.revenue_up || 0,
          revenue_revisions_down: data.revisions?.revenue_down || 0,
          eps_revisions_up: data.revisions?.eps_up || 0,
          eps_revisions_down: data.revisions?.eps_down || 0
        }
      }

      this.setCache(cacheKey, estimates, 2 * 60 * 60 * 1000) // 2 hour cache
      return estimates
    } catch (error) {
      console.error('Intrinio analyst estimates error:', error)
      return null
    }
  }

  /**
   * Get enhanced financial data with Intrinio's premium metrics
   */
  async getEnhancedFinancials(symbol: string): Promise<StockFinancials | null> {
    try {
      const valuation = await this.getValuationMetrics(symbol)
      if (!valuation) return null

      return {
        symbol,
        peRatio: valuation.price_to_earnings,
        priceToBook: valuation.price_to_book,
        priceToSales: valuation.price_to_sales,
        enterpriseValue: valuation.enterprise_value,
        evToRevenue: valuation.ev_to_revenue,
        evToEbitda: valuation.ev_to_ebitda,
        roe: valuation.return_on_equity,
        roa: valuation.return_on_assets,
        debtToEquity: valuation.debt_to_equity,
        currentRatio: valuation.current_ratio,
        quickRatio: valuation.quick_ratio,
        grossMargin: valuation.gross_margin,
        operatingMargin: valuation.operating_margin,
        netMargin: valuation.net_margin,
        marketCap: valuation.market_capitalization,
        lastUpdated: new Date().toISOString(),
        source: 'intrinio'
      }
    } catch (error) {
      console.error('Intrinio enhanced financials error:', error)
      return null
    }
  }

  /**
   * Cache management
   */
  private getFromCache<T>(key: string): T | null {
    const cached = this.cache.get(key)
    if (!cached) return null

    if (Date.now() > cached.timestamp + cached.ttl) {
      this.cache.delete(key)
      return null
    }

    return cached.data as T
  }

  private setCache(key: string, data: any, ttl: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }
}

// Singleton instance
let intrinioServiceInstance: IntrinioService | null = null

export function getIntrinioService(): IntrinioService | null {
  const apiKey = process.env.INTRINIO_API_KEY
  if (!apiKey) return null

  if (!intrinioServiceInstance) {
    intrinioServiceInstance = new IntrinioService(apiKey)
  }
  return intrinioServiceInstance
}
