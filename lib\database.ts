import { supabase, isSupabaseConfigured } from './supabase'
import type {
  UserProfile,
  Portfolio,
  PortfolioHolding,
  Transaction,
  UserSettings,
  FinancialGoal,
  Notification
} from './supabase'

// User Profile Operations
export const userProfileService = {
  async getProfile(userId: string): Promise<UserProfile | null> {
    if (!isSupabaseConfigured() || !supabase) {
      console.warn('Supabase not configured')
      return null
    }

    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', userId)
      .single()

    if (error) {
      console.error('Error fetching user profile:', error)
      return null
    }

    return data
  },

  async updateProfile(userId: string, updates: Partial<UserProfile>): Promise<boolean> {
    const { error } = await supabase
      .from('user_profiles')
      .update(updates)
      .eq('id', userId)

    if (error) {
      console.error('Error updating user profile:', error)
      return false
    }

    return true
  },

  async createProfile(profile: Omit<UserProfile, 'created_at' | 'updated_at'>): Promise<boolean> {
    const { error } = await supabase
      .from('user_profiles')
      .insert(profile)

    if (error) {
      console.error('Error creating user profile:', error)
      return false
    }

    return true
  }
}

// Portfolio Operations
export const portfolioService = {
  async getUserPortfolios(userId: string): Promise<Portfolio[]> {
    const { data, error } = await supabase
      .from('portfolios')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching portfolios:', error)
      return []
    }

    return data || []
  },

  async createPortfolio(portfolio: Omit<Portfolio, 'id' | 'created_at' | 'updated_at'>): Promise<Portfolio | null> {
    const { data, error } = await supabase
      .from('portfolios')
      .insert(portfolio)
      .select()
      .single()

    if (error) {
      console.error('Error creating portfolio:', error)
      return null
    }

    return data
  },

  async getPortfolioHoldings(portfolioId: string): Promise<PortfolioHolding[]> {
    const { data, error } = await supabase
      .from('portfolio_holdings')
      .select('*')
      .eq('portfolio_id', portfolioId)

    if (error) {
      console.error('Error fetching portfolio holdings:', error)
      return []
    }

    return data || []
  },

  async addHolding(holding: Omit<PortfolioHolding, 'id' | 'created_at' | 'updated_at'>): Promise<boolean> {
    const { error } = await supabase
      .from('portfolio_holdings')
      .upsert(holding, { 
        onConflict: 'portfolio_id,symbol',
        ignoreDuplicates: false 
      })

    if (error) {
      console.error('Error adding portfolio holding:', error)
      return false
    }

    return true
  }
}

// Transaction Operations
export const transactionService = {
  async getUserTransactions(userId: string, limit = 50): Promise<Transaction[]> {
    const { data, error } = await supabase
      .from('transactions')
      .select('*')
      .eq('user_id', userId)
      .order('transaction_date', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Error fetching transactions:', error)
      return []
    }

    return data || []
  },

  async addTransaction(transaction: Omit<Transaction, 'id' | 'created_at'>): Promise<boolean> {
    const { error } = await supabase
      .from('transactions')
      .insert(transaction)

    if (error) {
      console.error('Error adding transaction:', error)
      return false
    }

    return true
  }
}

// User Settings Operations
export const settingsService = {
  async getUserSettings(userId: string): Promise<UserSettings | null> {
    const { data, error } = await supabase
      .from('user_settings')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (error) {
      console.error('Error fetching user settings:', error)
      return null
    }

    return data
  },

  async updateSettings(userId: string, settings: Partial<UserSettings>): Promise<boolean> {
    const { error } = await supabase
      .from('user_settings')
      .upsert({ user_id: userId, ...settings }, { onConflict: 'user_id' })

    if (error) {
      console.error('Error updating user settings:', error)
      return false
    }

    return true
  }
}

// Financial Goals Operations
export const goalsService = {
  async getUserGoals(userId: string): Promise<FinancialGoal[]> {
    const { data, error } = await supabase
      .from('financial_goals')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching financial goals:', error)
      return []
    }

    return data || []
  },

  async createGoal(goal: Omit<FinancialGoal, 'id' | 'created_at' | 'updated_at'>): Promise<boolean> {
    const { error } = await supabase
      .from('financial_goals')
      .insert(goal)

    if (error) {
      console.error('Error creating financial goal:', error)
      return false
    }

    return true
  },

  async updateGoal(goalId: string, updates: Partial<FinancialGoal>): Promise<boolean> {
    const { error } = await supabase
      .from('financial_goals')
      .update(updates)
      .eq('id', goalId)

    if (error) {
      console.error('Error updating financial goal:', error)
      return false
    }

    return true
  }
}

// Notifications Operations
export const notificationService = {
  async getUserNotifications(userId: string, limit = 20): Promise<Notification[]> {
    const { data, error } = await supabase
      .from('notifications')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Error fetching notifications:', error)
      return []
    }

    return data || []
  },

  async markAsRead(notificationId: string): Promise<boolean> {
    const { error } = await supabase
      .from('notifications')
      .update({ is_read: true })
      .eq('id', notificationId)

    if (error) {
      console.error('Error marking notification as read:', error)
      return false
    }

    return true
  },

  async createNotification(notification: Omit<Notification, 'id' | 'created_at'>): Promise<boolean> {
    const { error } = await supabase
      .from('notifications')
      .insert(notification)

    if (error) {
      console.error('Error creating notification:', error)
      return false
    }

    return true
  }
}
