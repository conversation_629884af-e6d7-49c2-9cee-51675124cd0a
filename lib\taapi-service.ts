/**
 * TAAPI.IO Integration
 * Advanced technical analysis indicators service
 */

export interface TaapiIndicator {
  indicator: string
  value: number | { [key: string]: number }
  signal?: 'BUY' | 'SELL' | 'NEUTRAL'
  timestamp: string
  confidence?: number
}

export interface IchimokuData {
  tenkan_sen: number
  kijun_sen: number
  senkou_span_a: number
  senkou_span_b: number
  chikou_span: number
  signal: 'BUY' | 'SELL' | 'NEUTRAL'
}

export interface BollingerBandsData {
  upper: number
  middle: number
  lower: number
  bandwidth: number
  percent_b: number
  signal: 'BUY' | 'SELL' | 'NEUTRAL'
}

export interface VWAPData {
  vwap: number
  signal: 'BUY' | 'SELL' | 'NEUTRAL'
  deviation_bands?: {
    upper_1: number
    lower_1: number
    upper_2: number
    lower_2: number
  }
}

export class TaapiService {
  private apiKey: string
  private baseUrl = 'https://api.taapi.io'
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>()

  constructor(apiKey: string) {
    this.apiKey = apiKey
  }

  /**
   * Get Ichimoku Cloud analysis
   */
  async getIchimoku(symbol: string, exchange: string = 'binance', interval: string = '1d'): Promise<IchimokuData | null> {
    const cacheKey = `ichimoku:${symbol}:${exchange}:${interval}`
    
    const cached = this.getFromCache<IchimokuData>(cacheKey)
    if (cached) return cached

    try {
      const response = await fetch(
        `${this.baseUrl}/ichimoku?secret=${this.apiKey}&exchange=${exchange}&symbol=${symbol}&interval=${interval}`
      )

      if (!response.ok) return null

      const data = await response.json()
      if (data.error) return null

      const ichimoku: IchimokuData = {
        tenkan_sen: data.tenkan_sen,
        kijun_sen: data.kijun_sen,
        senkou_span_a: data.senkou_span_a,
        senkou_span_b: data.senkou_span_b,
        chikou_span: data.chikou_span,
        signal: this.analyzeIchimokuSignal(data)
      }

      this.setCache(cacheKey, ichimoku, 5 * 60 * 1000) // 5 minute cache
      return ichimoku
    } catch (error) {
      console.error('TAAPI Ichimoku error:', error)
      return null
    }
  }

  /**
   * Get Bollinger Bands analysis
   */
  async getBollingerBands(symbol: string, exchange: string = 'binance', interval: string = '1d'): Promise<BollingerBandsData | null> {
    const cacheKey = `bbands:${symbol}:${exchange}:${interval}`
    
    const cached = this.getFromCache<BollingerBandsData>(cacheKey)
    if (cached) return cached

    try {
      const response = await fetch(
        `${this.baseUrl}/bbands?secret=${this.apiKey}&exchange=${exchange}&symbol=${symbol}&interval=${interval}`
      )

      if (!response.ok) return null

      const data = await response.json()
      if (data.error) return null

      // Get current price for %B calculation
      const priceResponse = await fetch(
        `${this.baseUrl}/price?secret=${this.apiKey}&exchange=${exchange}&symbol=${symbol}`
      )
      const priceData = await priceResponse.json()
      const currentPrice = priceData.value || 0

      const bandwidth = ((data.valueUpperBand - data.valueLowerBand) / data.valueMiddleBand) * 100
      const percent_b = (currentPrice - data.valueLowerBand) / (data.valueUpperBand - data.valueLowerBand)

      const bbands: BollingerBandsData = {
        upper: data.valueUpperBand,
        middle: data.valueMiddleBand,
        lower: data.valueLowerBand,
        bandwidth,
        percent_b,
        signal: this.analyzeBollingerSignal(currentPrice, data)
      }

      this.setCache(cacheKey, bbands, 5 * 60 * 1000)
      return bbands
    } catch (error) {
      console.error('TAAPI Bollinger Bands error:', error)
      return null
    }
  }

  /**
   * Get VWAP (Volume Weighted Average Price)
   */
  async getVWAP(symbol: string, exchange: string = 'binance', interval: string = '1d'): Promise<VWAPData | null> {
    const cacheKey = `vwap:${symbol}:${exchange}:${interval}`
    
    const cached = this.getFromCache<VWAPData>(cacheKey)
    if (cached) return cached

    try {
      const response = await fetch(
        `${this.baseUrl}/vwap?secret=${this.apiKey}&exchange=${exchange}&symbol=${symbol}&interval=${interval}`
      )

      if (!response.ok) return null

      const data = await response.json()
      if (data.error) return null

      // Get current price for signal analysis
      const priceResponse = await fetch(
        `${this.baseUrl}/price?secret=${this.apiKey}&exchange=${exchange}&symbol=${symbol}`
      )
      const priceData = await priceResponse.json()
      const currentPrice = priceData.value || 0

      const vwap: VWAPData = {
        vwap: data.value,
        signal: currentPrice > data.value ? 'BUY' : currentPrice < data.value ? 'SELL' : 'NEUTRAL'
      }

      this.setCache(cacheKey, vwap, 5 * 60 * 1000)
      return vwap
    } catch (error) {
      console.error('TAAPI VWAP error:', error)
      return null
    }
  }

  /**
   * Get KDJ indicator
   */
  async getKDJ(symbol: string, exchange: string = 'binance', interval: string = '1d'): Promise<TaapiIndicator | null> {
    const cacheKey = `kdj:${symbol}:${exchange}:${interval}`
    
    const cached = this.getFromCache<TaapiIndicator>(cacheKey)
    if (cached) return cached

    try {
      const response = await fetch(
        `${this.baseUrl}/stoch?secret=${this.apiKey}&exchange=${exchange}&symbol=${symbol}&interval=${interval}`
      )

      if (!response.ok) return null

      const data = await response.json()
      if (data.error) return null

      // KDJ is based on Stochastic with J line
      const k = data.valueK
      const d = data.valueD
      const j = 3 * k - 2 * d // J line calculation

      let signal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'
      if (k > 80 && d > 80) signal = 'SELL'
      else if (k < 20 && d < 20) signal = 'BUY'

      const kdj: TaapiIndicator = {
        indicator: 'KDJ',
        value: { k, d, j },
        signal,
        timestamp: new Date().toISOString()
      }

      this.setCache(cacheKey, kdj, 5 * 60 * 1000)
      return kdj
    } catch (error) {
      console.error('TAAPI KDJ error:', error)
      return null
    }
  }

  /**
   * Get multiple indicators at once
   */
  async getMultipleIndicators(
    symbol: string, 
    indicators: string[], 
    exchange: string = 'binance', 
    interval: string = '1d'
  ): Promise<TaapiIndicator[]> {
    const results: TaapiIndicator[] = []

    for (const indicator of indicators) {
      try {
        const response = await fetch(
          `${this.baseUrl}/${indicator}?secret=${this.apiKey}&exchange=${exchange}&symbol=${symbol}&interval=${interval}`
        )

        if (!response.ok) continue

        const data = await response.json()
        if (data.error) continue

        results.push({
          indicator: indicator.toUpperCase(),
          value: data.value || data,
          signal: this.analyzeGenericSignal(indicator, data),
          timestamp: new Date().toISOString()
        })
      } catch (error) {
        console.warn(`Failed to get ${indicator} for ${symbol}:`, error)
      }
    }

    return results
  }

  /**
   * Signal analysis helpers
   */
  private analyzeIchimokuSignal(data: any): 'BUY' | 'SELL' | 'NEUTRAL' {
    // Simplified Ichimoku signal analysis
    if (data.tenkan_sen > data.kijun_sen && data.senkou_span_a > data.senkou_span_b) {
      return 'BUY'
    } else if (data.tenkan_sen < data.kijun_sen && data.senkou_span_a < data.senkou_span_b) {
      return 'SELL'
    }
    return 'NEUTRAL'
  }

  private analyzeBollingerSignal(currentPrice: number, data: any): 'BUY' | 'SELL' | 'NEUTRAL' {
    if (currentPrice <= data.valueLowerBand) return 'BUY'
    if (currentPrice >= data.valueUpperBand) return 'SELL'
    return 'NEUTRAL'
  }

  private analyzeGenericSignal(indicator: string, data: any): 'BUY' | 'SELL' | 'NEUTRAL' | undefined {
    switch (indicator.toLowerCase()) {
      case 'rsi':
        if (data.value > 70) return 'SELL'
        if (data.value < 30) return 'BUY'
        return 'NEUTRAL'
      case 'macd':
        if (data.valueMACD > data.valueMACDSignal) return 'BUY'
        if (data.valueMACD < data.valueMACDSignal) return 'SELL'
        return 'NEUTRAL'
      default:
        return undefined
    }
  }

  /**
   * Cache management
   */
  private getFromCache<T>(key: string): T | null {
    const cached = this.cache.get(key)
    if (!cached) return null

    if (Date.now() > cached.timestamp + cached.ttl) {
      this.cache.delete(key)
      return null
    }

    return cached.data as T
  }

  private setCache(key: string, data: any, ttl: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }
}

// Available TAAPI indicators
export const TAAPI_INDICATORS = [
  'rsi', 'macd', 'bbands', 'stoch', 'ema', 'sma', 'vwap', 'adx', 'cci',
  'williams', 'momentum', 'roc', 'trix', 'ultosc', 'willr', 'aroon',
  'mfi', 'bop', 'cmo', 'sar', 'atr', 'obv', 'ad', 'ichimoku', 'kdj'
]

// Singleton instances for different tiers
let taapiStarterInstance: TaapiService | null = null
let taapiStandardInstance: TaapiService | null = null
let taapiUnlimitedInstance: TaapiService | null = null

export function getTaapiService(tier: 'starter' | 'standard' | 'unlimited' = 'starter'): TaapiService | null {
  let apiKey: string | undefined

  switch (tier) {
    case 'starter':
      apiKey = process.env.TAAPI_IO_API_KEY
      if (!apiKey) return null
      if (!taapiStarterInstance) {
        taapiStarterInstance = new TaapiService(apiKey)
      }
      return taapiStarterInstance

    case 'standard':
      apiKey = process.env.TAAPI_IO_STANDARD_API_KEY
      if (!apiKey) return null
      if (!taapiStandardInstance) {
        taapiStandardInstance = new TaapiService(apiKey)
      }
      return taapiStandardInstance

    case 'unlimited':
      apiKey = process.env.TAAPI_IO_UNLIMITED_API_KEY
      if (!apiKey) return null
      if (!taapiUnlimitedInstance) {
        taapiUnlimitedInstance = new TaapiService(apiKey)
      }
      return taapiUnlimitedInstance

    default:
      return null
  }
}
