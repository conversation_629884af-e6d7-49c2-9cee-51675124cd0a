/**
 * Enhanced Portfolio Performance API
 * Integrates PortfolioCalculator with StockHistoryService
 */

import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase'
import { getPortfolioStockIntegration } from '@/lib/portfolio-stock-integration'
import type { Timeframe } from '@/lib/types/stock-history'

const VALID_TIMEFRAMES: Timeframe[] = ['1D', '1W', '1M', '3M', '1Y', 'All']

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Get query parameters
    const portfolioId = searchParams.get('portfolio_id')
    const timeframe = (searchParams.get('timeframe') || '1M').toUpperCase() as Timeframe
    const forceRefresh = searchParams.get('refresh') === 'true'

    // Validate timeframe
    if (!VALID_TIMEFRAMES.includes(timeframe)) {
      return NextResponse.json(
        { 
          error: `Invalid timeframe. Valid options: ${VALID_TIMEFRAMES.join(', ')}`,
          validTimeframes: VALID_TIMEFRAMES
        },
        { status: 400 }
      )
    }

    // Get user from auth token in header
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authentication required - missing token' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const supabase = createClient()

    // Verify the token and get user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      console.error('Auth error:', authError)
      return NextResponse.json(
        { error: 'Authentication failed' },
        { status: 401 }
      )
    }

    // Get portfolio performance using the integration service
    const portfolioIntegration = getPortfolioStockIntegration()
    const performanceData = await portfolioIntegration.calculatePortfolioPerformance(
      user.id,
      timeframe,
      portfolioId || undefined
    )

    if (!performanceData) {
      return NextResponse.json(
        { error: 'Failed to calculate portfolio performance' },
        { status: 500 }
      )
    }

    // Format response
    const response = {
      success: true,
      data: {
        portfolio_id: performanceData.portfolioId,
        user_id: performanceData.userId,
        timeframe: performanceData.timeframe,
        
        // Current metrics
        current_metrics: {
          total_value: performanceData.currentMetrics.totalValue,
          total_invested: performanceData.currentMetrics.totalInvested,
          total_cash: performanceData.currentMetrics.totalCash,
          total_return: performanceData.currentMetrics.totalReturn,
          total_return_percent: performanceData.currentMetrics.totalReturnPercent,
          holdings_count: performanceData.currentMetrics.holdingsCount
        },

        // Time series data for charts
        time_series: performanceData.timeSeries.map(point => ({
          date: point.date,
          timestamp: point.timestamp,
          portfolio_value: point.portfolioValue,
          cash_value: point.cashValue,
          total_value: point.totalValue,
          daily_return: point.dailyReturn,
          daily_return_percent: point.dailyReturnPercent,
          total_return: point.totalReturn,
          total_return_percent: point.totalReturnPercent
        })),

        // Holdings with performance data
        holdings: performanceData.holdings.map(holding => ({
          symbol: holding.symbol,
          shares: holding.shares,
          average_cost: holding.averageCost,
          current_price: holding.currentPrice,
          total_value: holding.totalValue,
          gain_loss: holding.gainLoss,
          gain_loss_percent: holding.gainLossPercent,
          historical_data_points: holding.historicalPrices.length
        })),

        // Summary statistics
        summary: {
          best_performing_stock: performanceData.holdings.length > 0 
            ? performanceData.holdings.reduce((best, current) => 
                current.gainLossPercent > best.gainLossPercent ? current : best
              ).symbol
            : null,
          worst_performing_stock: performanceData.holdings.length > 0
            ? performanceData.holdings.reduce((worst, current) => 
                current.gainLossPercent < worst.gainLossPercent ? current : worst
              ).symbol
            : null,
          total_stocks: performanceData.holdings.length,
          data_points: performanceData.timeSeries.length
        },

        // Metadata
        last_updated: performanceData.lastUpdated,
        cache_status: forceRefresh ? 'refreshed' : 'cached',
        data_source: 'integrated_services'
      }
    }

    // Set cache headers
    const cacheMaxAge = forceRefresh ? 60 : 300 // 1 min for refresh, 5 min for cached
    
    return NextResponse.json(response, {
      headers: {
        'Cache-Control': `public, max-age=${cacheMaxAge}`,
        'X-Data-Source': 'portfolio-stock-integration',
        'X-Timeframe': timeframe
      }
    })

  } catch (error) {
    console.error('Portfolio performance API error:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * POST endpoint for initializing user portfolio system
 */
export async function POST(request: NextRequest) {
  try {
    // Get user from auth token in header
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authentication required - missing token' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const supabase = createClient()

    // Verify the token and get user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      console.error('Auth error:', authError)
      return NextResponse.json(
        { error: 'Authentication failed' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const initialBalance = body.initial_balance || 10000

    // Initialize user capital system
    const { getUserCapitalResolver } = await import('@/lib/user-capital-resolver')
    const capitalResolver = getUserCapitalResolver()
    
    const initialized = await capitalResolver.initializeUserCapital(user.id, initialBalance)

    if (!initialized) {
      return NextResponse.json(
        { error: 'Failed to initialize user capital system' },
        { status: 500 }
      )
    }

    // Initialize default portfolio using the database function
    const { error: portfolioError } = await supabase.rpc('initialize_user_portfolio_system', {
      p_user_id: user.id
    })

    if (portfolioError) {
      console.error('Error initializing portfolio:', portfolioError)
      return NextResponse.json(
        { error: 'Failed to initialize portfolio system' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Portfolio system initialized successfully',
      user_id: user.id,
      initial_balance: initialBalance,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Portfolio initialization error:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to initialize portfolio system',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
