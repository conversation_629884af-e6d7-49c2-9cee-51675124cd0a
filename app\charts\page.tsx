/**
 * Charts Demo Page
 * Demonstrates all chart components with real data integration
 */

'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import {
  StockPriceChart,
  CompactStockPriceChart,
  PortfolioPerformanceChart,
  CompactPortfolioChart,
  TimeRange,
  ValueType
} from '@/components/charts'
import { PageHeader } from '@/components/breadcrumb'
import { TopNav } from '@/components/top-nav'
import { BottomNav } from '@/components/bottom-nav'
import { BackToDashboard, ChartHelp } from '@/components/chart-navigation'
import { TrendingUp, BarChart3, LineChart, Activity } from 'lucide-react'

export default function ChartsPage() {
  const [stockSymbol, setStockSymbol] = useState('AAPL')
  const [timeRange, setTimeRange] = useState<TimeRange>('1M')
  const [valueType, setValueType] = useState<ValueType>('absolute')

  const popularSymbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META', 'NFLX']

  return (
    <div className="min-h-screen bg-background">
      <TopNav currentPage="charts" />
      
      <div className="container mx-auto p-6 space-y-8">
        <PageHeader
          title="Financial Charts"
          description="Interactive charts for stock prices and portfolio performance with real-time data integration."
          showBackButton={true}
          actions={
            <div className="flex items-center gap-2">
              <ChartHelp />
              <BackToDashboard />
            </div>
          }
        />

        <Tabs defaultValue="stock" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="stock" className="flex items-center gap-2">
              <LineChart className="h-4 w-4" />
              Stock Charts
            </TabsTrigger>
            <TabsTrigger value="portfolio" className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Portfolio Charts
            </TabsTrigger>
            <TabsTrigger value="compact" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Compact Charts
            </TabsTrigger>
            <TabsTrigger value="demo" className="flex items-center gap-2">
              <Activity className="h-4 w-4" />
              Live Demo
            </TabsTrigger>
          </TabsList>

          <TabsContent value="stock" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Stock Price Chart</CardTitle>
                <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
                  <div className="flex items-center gap-2">
                    <Label htmlFor="symbol">Symbol:</Label>
                    <Input
                      id="symbol"
                      value={stockSymbol}
                      onChange={(e) => setStockSymbol(e.target.value.toUpperCase())}
                      className="w-24"
                      placeholder="AAPL"
                    />
                  </div>
                  <div className="flex gap-1">
                    {popularSymbols.map((symbol) => (
                      <Button
                        key={symbol}
                        variant={stockSymbol === symbol ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setStockSymbol(symbol)}
                        className="h-8 px-3 text-xs"
                      >
                        {symbol}
                      </Button>
                    ))}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <StockPriceChart
                  symbol={stockSymbol}
                  height={400}
                  autoRefresh={true}
                  refreshInterval={60000}
                  showControls={true}
                  showPerformance={true}
                  initialTimeRange="1M"
                  initialValueType="absolute"
                />
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {['MSFT', 'GOOGL'].map((symbol) => (
                <Card key={symbol}>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg">{symbol}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <StockPriceChart
                      symbol={symbol}
                      height={300}
                      showControls={false}
                      showPerformance={true}
                      initialTimeRange="1W"
                    />
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="portfolio" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Portfolio Performance</CardTitle>
                <p className="text-sm text-muted-foreground">
                  Track your portfolio value over time with capital deposits marked.
                </p>
              </CardHeader>
              <CardContent>
                <PortfolioPerformanceChart
                  height={450}
                  autoRefresh={true}
                  refreshInterval={300000}
                  showControls={true}
                  showPerformance={true}
                  showDeposits={true}
                  chartType="area"
                  initialTimeRange="3M"
                  initialValueType="absolute"
                />
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Area Chart</CardTitle>
                </CardHeader>
                <CardContent>
                  <PortfolioPerformanceChart
                    height={300}
                    showControls={false}
                    showPerformance={false}
                    chartType="area"
                    initialTimeRange="1M"
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Line Chart</CardTitle>
                </CardHeader>
                <CardContent>
                  <PortfolioPerformanceChart
                    height={300}
                    showControls={false}
                    showPerformance={false}
                    chartType="line"
                    initialTimeRange="1M"
                  />
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="compact" className="space-y-6">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {popularSymbols.slice(0, 6).map((symbol) => (
                <Card key={symbol}>
                  <CardContent className="p-4">
                    <CompactStockPriceChart
                      symbol={symbol}
                      timeRange="1D"
                      height={80}
                      showChange={true}
                    />
                  </CardContent>
                </Card>
              ))}
            </div>

            <Separator />

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {['1W', '1M', '3M'].map((range) => (
                <Card key={range}>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm">Portfolio - {range}</CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <CompactPortfolioChart
                      timeRange={range as TimeRange}
                      height={100}
                      showValue={true}
                    />
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="demo" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Interactive Demo</CardTitle>
                <p className="text-sm text-muted-foreground">
                  Real-time charts with live data updates and interactive controls.
                </p>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex flex-wrap gap-4 items-center p-4 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Label>Symbol:</Label>
                    <Input
                      value={stockSymbol}
                      onChange={(e) => setStockSymbol(e.target.value.toUpperCase())}
                      className="w-20"
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    <Label>Time Range:</Label>
                    <div className="flex gap-1">
                      {(['1D', '1W', '1M', '3M'] as TimeRange[]).map((range) => (
                        <Button
                          key={range}
                          variant={timeRange === range ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setTimeRange(range)}
                          className="h-8 px-3 text-xs"
                        >
                          {range}
                        </Button>
                      ))}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Label>Value Type:</Label>
                    <div className="flex gap-1">
                      <Button
                        variant={valueType === 'absolute' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setValueType('absolute')}
                        className="h-8 px-3 text-xs"
                      >
                        $
                      </Button>
                      <Button
                        variant={valueType === 'percentage' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setValueType('percentage')}
                        className="h-8 px-3 text-xs"
                      >
                        %
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <LineChart className="h-5 w-5" />
                        Stock Price
                        <Badge variant="secondary">Live</Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <StockPriceChart
                        symbol={stockSymbol}
                        height={300}
                        autoRefresh={true}
                        refreshInterval={30000}
                        showControls={false}
                        showPerformance={true}
                        initialTimeRange={timeRange}
                        initialValueType={valueType}
                      />
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <TrendingUp className="h-5 w-5" />
                        Portfolio Performance
                        <Badge variant="secondary">Live</Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <PortfolioPerformanceChart
                        height={300}
                        autoRefresh={true}
                        refreshInterval={60000}
                        showControls={false}
                        showPerformance={true}
                        showDeposits={true}
                        chartType="area"
                        initialTimeRange={timeRange}
                        initialValueType={valueType}
                      />
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <Card>
          <CardHeader>
            <CardTitle>Chart Features</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium">📊 Chart Types</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Line charts for price trends</li>
                  <li>• Area charts for portfolio value</li>
                  <li>• Compact charts for dashboards</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">⚡ Real-time Features</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Auto-refresh capabilities</li>
                  <li>• Live data integration</li>
                  <li>• Intelligent caching</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">🎛️ Interactive Controls</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Time range switching</li>
                  <li>• $ and % value toggles</li>
                  <li>• Hover tooltips</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      <BottomNav />
    </div>
  )
}
