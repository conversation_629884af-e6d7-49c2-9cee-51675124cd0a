import { type NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { message, userId, sessionId } = body

    // Validate required fields
    if (!message || !userId) {
      return NextResponse.json({ error: "Message and userId are required" }, { status: 400 })
    }

    // Basic chatbot response logic
    // This is a simple implementation - in production you would integrate with:
    // - OpenAI API, Claude API, or other LLM services
    // - Custom trained models
    // - Third-party chatbot platforms

    console.log("Chat webhook received:", {
      message,
      userId,
      sessionId,
      timestamp: new Date().toISOString(),
    })

    // Simple response logic based on message content
    let response = "I'm here to help with your investment questions!"

    const messageLower = message.toLowerCase()

    if (messageLower.includes('portfolio') || messageLower.includes('invest')) {
      response = "I can help you with portfolio management and investment strategies. What specific questions do you have about investing?"
    } else if (messageLower.includes('stock') || messageLower.includes('ticker')) {
      response = "I can provide information about stocks and market data. Which stocks are you interested in learning about?"
    } else if (messageLower.includes('help') || messageLower.includes('how')) {
      response = "I'm here to assist you with investment education and portfolio management. You can ask me about stocks, investment strategies, or how to use the platform."
    } else if (messageLower.includes('hello') || messageLower.includes('hi')) {
      response = "Hello! I'm your investment assistant. How can I help you with your investment journey today?"
    }

    // Return the generated response
    const botResponse = {
      message: response,
      timestamp: new Date().toISOString(),
      sessionId: sessionId || `session-${Date.now()}`,
      type: "bot",
    }

    return NextResponse.json({
      success: true,
      response: botResponse,
    })
  } catch (error) {
    console.error("Chat webhook error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function GET() {
  return NextResponse.json({
    message: "Chat webhook endpoint is active",
    timestamp: new Date().toISOString(),
    endpoints: {
      POST: "/api/chat/webhook - Send chat messages",
    },
  })
}
