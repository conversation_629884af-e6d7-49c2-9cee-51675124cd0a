-- Investry Premium subscription plan
-- Run this after creating the payment tables

-- Note: Replace the stripe_price_id value with your actual Stripe price ID
-- You can create this in your Stripe Dashboard under Products

-- Clear existing plans (optional)
-- DELETE FROM subscription_plans;

-- Insert Investry Premium plan
INSERT INTO subscription_plans (name, description, stripe_price_id, amount, currency, interval, interval_count, features, is_active) VALUES
(
  'Investry Premium',
  'Unlock the full power of professional investing',
  'price_1QdGJKPBPh8XhQPYOKJvGJvG', -- Replace with your actual Stripe price ID
  19.99,
  'USD',
  'month',
  1,
  '["Unlimited portfolio tracking", "Advanced analytics & charts", "Real-time market data", "Price alerts & notifications", "Research reports & insights", "Advanced screening tools", "Options trading insights", "Custom alerts & automations", "Priority customer support", "Export capabilities", "Advanced portfolio analysis"]'::jsonb,
  true
);

-- You can also add more plans or update existing ones
-- For example, a basic plan or enterprise plans

-- Basic/Starter plan (optional)
INSERT INTO subscription_plans (name, description, stripe_price_id, amount, currency, interval, interval_count, features, is_active) VALUES
(
  'Starter Monthly',
  'Perfect for new investors',
  'price_starter_monthly', -- Replace with actual Stripe price ID
  4.99,
  'USD',
  'month',
  1,
  '["Portfolio tracking (up to 25 stocks)", "Basic analytics", "Email notifications", "Mobile app access", "Basic market news"]'::jsonb,
  true
);

-- Enterprise plan (optional)
INSERT INTO subscription_plans (name, description, stripe_price_id, amount, currency, interval, interval_count, features, is_active) VALUES
(
  'Enterprise',
  'Custom solutions for institutions',
  'price_enterprise_monthly', -- Replace with actual Stripe price ID
  99.99,
  'USD',
  'month',
  1,
  '["Everything in Premium", "Custom integrations", "Dedicated infrastructure", "24/7 phone support", "Custom reporting", "Multi-user accounts", "Advanced compliance tools"]'::jsonb,
  true
);

-- View the inserted plans
SELECT 
  name,
  description,
  amount,
  currency,
  interval,
  stripe_price_id,
  is_active
FROM subscription_plans
ORDER BY amount ASC;
