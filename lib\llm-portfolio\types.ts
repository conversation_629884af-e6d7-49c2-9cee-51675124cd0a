/**
 * Type definitions for LLM Portfolio Generation System
 */

import type { SurveyData } from "@/components/onboarding-survey"
import type { PersonalizedPortfolio, PortfolioAllocation } from "@/lib/portfolio-generator"

// LLM Configuration
export interface LLMConfig {
  apiKey: string
  model: string
  maxTokens: number
  temperature: number
  maxRetries: number
  timeoutMs: number
}

// Cache Configuration
export interface CacheConfig {
  ttlHours: number
  maxEntries: number
  cleanupIntervalMs: number
}

// System Configuration
export interface LLMSystemConfig {
  llm: LLMConfig
  cache: CacheConfig
  rateLimitPerUser: number
  enableFallback: boolean
  enableAuditLogging: boolean
}

// Prompt Template Data
export interface PromptData {
  surveyData: SurveyData
  userId: string
  userMajor?: string
  userMetadata?: Record<string, any>
  additionalContext?: string
}

// LLM Request/Response Types
export interface LLMRequest {
  prompt: string
  promptHash: string
  userId: string
  promptData: PromptData
  metadata?: Record<string, any>
}

export interface LLMResponse {
  id: string
  promptId: string
  responseText: string
  tokensUsed: number
  model: string
  provider: string
  processingTimeMs: number
  success: boolean
  errorMessage?: string
  createdAt: Date
}

// Portfolio Generation Result
export interface LLMPortfolioResult {
  portfolio: PersonalizedPortfolio
  source: 'llm' | 'cache' | 'fallback'
  confidence: number
  processingTimeMs: number
  llmResponse?: LLMResponse
  cacheHit?: boolean
  fallbackReason?: string
  validationErrors?: string[]
}

// Cache Entry
export interface CacheEntry {
  id: string
  cacheKey: string
  userId: string
  promptHash: string
  portfolioData: PersonalizedPortfolio
  hitCount: number
  lastAccessed: Date
  expiresAt: Date
  createdAt: Date
}

// Audit Log Entry
export interface AuditLogEntry {
  id: string
  userId: string
  action: string
  details: Record<string, any>
  ipAddress?: string
  userAgent?: string
  apiEndpoint?: string
  executionTimeMs?: number
  success: boolean
  errorDetails?: Record<string, any>
  createdAt: Date
}

// API Usage Tracking
export interface ApiUsageEntry {
  id: string
  userId: string
  apiProvider: string
  model: string
  tokensUsed: number
  costUsd: number
  requestType: string
  success: boolean
  createdAt: Date
}

// Database Models
export interface LLMPromptRecord {
  id: string
  user_id: string
  prompt_hash: string
  prompt_text: string
  prompt_type: string
  survey_data: SurveyData
  user_metadata: Record<string, any>
  created_at: string
}

export interface LLMResponseRecord {
  id: string
  prompt_id: string
  user_id: string
  response_text: string
  response_tokens: number
  model_used: string
  api_provider: string
  processing_time_ms: number
  success: boolean
  error_message?: string
  created_at: string
}

export interface LLMGeneratedPortfolioRecord {
  id: string
  response_id: string
  user_id: string
  portfolio_data: PersonalizedPortfolio
  risk_level: string
  expected_return: string
  strategy: string
  rebalance_frequency: string
  rationale: string
  allocations: PortfolioAllocation[]
  validation_status: 'pending' | 'valid' | 'invalid'
  validation_errors?: Record<string, any>
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface PortfolioCacheRecord {
  id: string
  cache_key: string
  user_id: string
  prompt_hash: string
  portfolio_id: string
  hit_count: number
  last_accessed: string
  expires_at: string
  created_at: string
}

// Error Types
export class LLMError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: Record<string, any>
  ) {
    super(message)
    this.name = 'LLMError'
  }
}

export class CacheError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: Record<string, any>
  ) {
    super(message)
    this.name = 'CacheError'
  }
}

export class ValidationError extends Error {
  constructor(
    message: string,
    public errors: string[],
    public details?: Record<string, any>
  ) {
    super(message)
    this.name = 'ValidationError'
  }
}

// Service Interfaces
export interface IPromptTemplateBuilder {
  buildPortfolioPrompt(data: PromptData): Promise<string>
  generatePromptHash(prompt: string): string
}

export interface IPortfolioCache {
  get(cacheKey: string): Promise<CacheEntry | null>
  set(cacheKey: string, portfolio: PersonalizedPortfolio, userId: string, promptHash: string): Promise<void>
  invalidate(userId: string): Promise<void>
  cleanup(): Promise<number>
  getStats(userId: string): Promise<{ hits: number; misses: number; size: number }>
}

export interface IPortfolioFallback {
  generateFallbackPortfolio(data: PromptData): Promise<PersonalizedPortfolio>
  isAvailable(): boolean
}

export interface ISupabaseLogger {
  logPrompt(request: LLMRequest): Promise<string>
  logResponse(response: LLMResponse): Promise<void>
  logPortfolio(portfolioId: string, portfolio: PersonalizedPortfolio, responseId: string): Promise<void>
  logAudit(entry: Omit<AuditLogEntry, 'id' | 'createdAt'>): Promise<void>
  logApiUsage(usage: Omit<ApiUsageEntry, 'id' | 'createdAt'>): Promise<void>
}

export interface ILLMPortfolioEngine {
  generatePortfolio(data: PromptData): Promise<LLMPortfolioResult>
  validatePortfolio(portfolio: PersonalizedPortfolio): Promise<{ isValid: boolean; errors: string[] }>
  getGenerationStats(userId: string): Promise<{
    totalGenerations: number
    cacheHits: number
    fallbackUses: number
    avgProcessingTime: number
    lastGeneration: Date | null
  }>
}

// Constants
export const LLM_PROVIDERS = {
  OPENAI: 'openai',
  ANTHROPIC: 'anthropic',
  GOOGLE: 'google'
} as const

export const CACHE_ACTIONS = {
  HIT: 'cache_hit',
  MISS: 'cache_miss',
  SET: 'cache_set',
  INVALIDATE: 'cache_invalidate'
} as const

export const AUDIT_ACTIONS = {
  GENERATE_PORTFOLIO: 'generate_portfolio',
  CACHE_HIT: 'cache_hit',
  FALLBACK_USED: 'fallback_used',
  VALIDATION_FAILED: 'validation_failed',
  API_ERROR: 'api_error'
} as const

export type LLMProvider = typeof LLM_PROVIDERS[keyof typeof LLM_PROVIDERS]
export type CacheAction = typeof CACHE_ACTIONS[keyof typeof CACHE_ACTIONS]
export type AuditAction = typeof AUDIT_ACTIONS[keyof typeof AUDIT_ACTIONS]
