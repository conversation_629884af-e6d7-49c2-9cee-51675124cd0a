/**
 * Tiered API Configuration System
 * Manages different API tiers based on usage, user count, and subscription level
 */

export type ApiTier = 'startup' | 'mid-tier' | 'enterprise'

export interface ApiConfig {
  tier: ApiTier
  maxUsers: number
  monthlyCost: number
  features: {
    realTimeData: boolean
    technicalIndicators: boolean
    internationalMarkets: boolean
    optionsData: boolean
    esgData: boolean
    tickLevelData: boolean
    unlimitedRequests: boolean
  }
  apis: {
    // Startup tier
    alphavantage?: {
      apiKey: string
      baseUrl: string
      dailyLimit: number
    }
    finnhub?: {
      apiKey: string
      baseUrl: string
      minuteLimit: number
    }
    taapiStarter?: {
      apiKey: string
      baseUrl: string
      monthlyLimit: number
    }
    
    // Mid-tier
    twelveData?: {
      apiKey: string
      baseUrl: string
      monthlyLimit: number
    }
    taapiStandard?: {
      apiKey: string
      baseUrl: string
      monthlyLimit: number
    }
    
    // Enterprise tier
    polygon?: {
      apiKey: string
      baseUrl: string
      unlimitedRequests: boolean
    }
    intrinio?: {
      apiKey: string
      baseUrl: string
      unlimitedRequests: boolean
    }
    taapiUnlimited?: {
      apiKey: string
      baseUrl: string
      unlimitedRequests: boolean
    }
  }
}

export const API_TIER_CONFIGS: Record<ApiTier, ApiConfig> = {
  startup: {
    tier: 'startup',
    maxUsers: 100,
    monthlyCost: 50,
    features: {
      realTimeData: true,
      technicalIndicators: true,
      internationalMarkets: false,
      optionsData: false,
      esgData: false,
      tickLevelData: false,
      unlimitedRequests: false
    },
    apis: {
      alphavantage: {
        apiKey: process.env.ALPHA_VANTAGE_API_KEY || '',
        baseUrl: 'https://www.alphavantage.co/query',
        dailyLimit: 25
      },
      finnhub: {
        apiKey: process.env.FINNHUB_API_KEY || '',
        baseUrl: 'https://finnhub.io/api/v1',
        minuteLimit: 60
      },
      taapiStarter: {
        apiKey: process.env.TAAPI_IO_API_KEY || '',
        baseUrl: 'https://api.taapi.io',
        monthlyLimit: 10000
      }
    }
  },
  
  'mid-tier': {
    tier: 'mid-tier',
    maxUsers: 10000,
    monthlyCost: 300,
    features: {
      realTimeData: true,
      technicalIndicators: true,
      internationalMarkets: true,
      optionsData: false,
      esgData: false,
      tickLevelData: false,
      unlimitedRequests: false
    },
    apis: {
      twelveData: {
        apiKey: process.env.TWELVE_DATA_API_KEY || '',
        baseUrl: 'https://api.twelvedata.com',
        monthlyLimit: 100000
      },
      taapiStandard: {
        apiKey: process.env.TAAPI_IO_STANDARD_API_KEY || '',
        baseUrl: 'https://api.taapi.io',
        monthlyLimit: 50000
      },
      // Keep startup APIs as fallback
      finnhub: {
        apiKey: process.env.FINNHUB_API_KEY || '',
        baseUrl: 'https://finnhub.io/api/v1',
        minuteLimit: 60
      }
    }
  },
  
  enterprise: {
    tier: 'enterprise',
    maxUsers: Infinity,
    monthlyCost: 6000,
    features: {
      realTimeData: true,
      technicalIndicators: true,
      internationalMarkets: true,
      optionsData: true,
      esgData: true,
      tickLevelData: true,
      unlimitedRequests: true
    },
    apis: {
      polygon: {
        apiKey: process.env.POLYGON_IO_API_KEY || '',
        baseUrl: 'https://api.polygon.io',
        unlimitedRequests: true
      },
      intrinio: {
        apiKey: process.env.INTRINIO_API_KEY || '',
        baseUrl: 'https://api-v2.intrinio.com',
        unlimitedRequests: true
      },
      taapiUnlimited: {
        apiKey: process.env.TAAPI_IO_UNLIMITED_API_KEY || '',
        baseUrl: 'https://api.taapi.io',
        unlimitedRequests: true
      },
      // Keep lower tier APIs as fallback
      twelveData: {
        apiKey: process.env.TWELVE_DATA_API_KEY || '',
        baseUrl: 'https://api.twelvedata.com',
        monthlyLimit: 100000
      },
      finnhub: {
        apiKey: process.env.FINNHUB_API_KEY || '',
        baseUrl: 'https://finnhub.io/api/v1',
        minuteLimit: 60
      }
    }
  }
}

/**
 * Get current API tier from environment
 */
export function getCurrentApiTier(): ApiTier {
  const tier = process.env.STOCK_API_TIER as ApiTier
  return tier && ['startup', 'mid-tier', 'enterprise'].includes(tier) ? tier : 'startup'
}

/**
 * Get API configuration for current tier
 */
export function getApiConfig(): ApiConfig {
  const currentTier = getCurrentApiTier()
  return API_TIER_CONFIGS[currentTier]
}

/**
 * Determine appropriate tier based on user count
 */
export function getRecommendedTier(userCount: number): ApiTier {
  if (userCount > 10000) return 'enterprise'
  if (userCount > 100) return 'mid-tier'
  return 'startup'
}

/**
 * Check if a feature is available in current tier
 */
export function isFeatureAvailable(feature: keyof ApiConfig['features']): boolean {
  const config = getApiConfig()
  return config.features[feature]
}

/**
 * Get fallback API chain for current tier
 */
export function getApiFallbackChain(): string[] {
  const currentTier = getCurrentApiTier()
  
  switch (currentTier) {
    case 'enterprise':
      return ['polygon', 'intrinio', 'twelveData', 'finnhub', 'alphavantage']
    case 'mid-tier':
      return ['twelveData', 'finnhub', 'alphavantage']
    case 'startup':
    default:
      return ['finnhub', 'alphavantage']
  }
}

/**
 * Get API usage statistics
 */
export interface ApiUsageStats {
  tier: ApiTier
  monthlyCost: number
  requestsUsed: number
  requestsLimit: number
  featuresEnabled: string[]
  recommendedUpgrade?: ApiTier
}

export function getApiUsageStats(userCount: number, requestsUsed: number): ApiUsageStats {
  const config = getApiConfig()
  const recommendedTier = getRecommendedTier(userCount)
  
  return {
    tier: config.tier,
    monthlyCost: config.monthlyCost,
    requestsUsed,
    requestsLimit: config.features.unlimitedRequests ? Infinity : 100000,
    featuresEnabled: Object.entries(config.features)
      .filter(([_, enabled]) => enabled)
      .map(([feature, _]) => feature),
    recommendedUpgrade: recommendedTier !== config.tier ? recommendedTier : undefined
  }
}
