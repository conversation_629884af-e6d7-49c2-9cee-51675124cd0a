"use client"

import { useEffect, useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { CheckCircle } from "lucide-react"
import { useNotifications } from "@/components/notification-system"

export default function PaymentSuccessPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { addNotification } = useNotifications()
  const [loading, setLoading] = useState(true)
  
  useEffect(() => {
    const sessionId = searchParams.get('session_id')
    
    if (sessionId) {
      // Verify the payment with your backend
      const verifyPayment = async () => {
        try {
          const response = await fetch(`/api/verify-payment?session_id=${sessionId}`, {
            method: 'GET',
          })
          
          const data = await response.json()
          
          if (data.success) {
            addNotification({
              type: "success",
              title: "Payment Successful",
              message: `$${data.amount} has been added to your account.`,
            })
          }
        } catch (error) {
          console.error('Error verifying payment:', error)
        } finally {
          setLoading(false)
        }
      }
      
      verifyPayment()
    } else {
      setLoading(false)
    }
  }, [searchParams, addNotification])
  
  return (
    <div className="min-h-screen bg-slate-900 flex items-center justify-center p-4">
      <Card className="w-full max-w-md bg-slate-800 border-slate-700">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 bg-emerald-500/20 p-3 rounded-full w-16 h-16 flex items-center justify-center">
            <CheckCircle className="h-8 w-8 text-emerald-500" />
          </div>
          <CardTitle className="text-2xl text-white">Payment Successful!</CardTitle>
          <CardDescription>Your account has been funded successfully</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-center text-slate-300">
            Your funds are now available for investment. Start exploring investment opportunities now!
          </p>
          <div className="flex flex-col space-y-2">
            <Button onClick={() => router.push('/dashboard')}>
              Go to Dashboard
            </Button>
            <Button variant="outline" onClick={() => router.push('/investments')}>
              Explore Investments
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}