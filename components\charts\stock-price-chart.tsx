/**
 * Stock Price Chart Component
 * Displays stock price data with time range controls and price/percentage toggles
 */

'use client'

import React from 'react'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ReferenceLine
} from 'recharts'
import { useTheme } from 'next-themes'
import {
  ChartContainer,
  ChartControls,
  EmptyChartState,
  PerformanceIndicator,
  TimeRange,
  ValueType,
  chartUtils
} from './chart-base'
import {
  useStockChartData,
  useChartState,
  useTransformedChartData,
  useChartStats
} from '@/hooks/use-chart-data'

export interface StockPriceChartProps {
  symbol: string
  title?: string
  height?: number
  autoRefresh?: boolean
  refreshInterval?: number
  showControls?: boolean
  showPerformance?: boolean
  className?: string
  initialTimeRange?: TimeRange
  initialValueType?: ValueType
}

export function StockPriceChart({
  symbol,
  title,
  height = 400,
  autoRefresh = false,
  refreshInterval = 60000,
  showControls = true,
  showPerformance = true,
  className,
  initialTimeRange = '1M',
  initialValueType = 'absolute'
}: StockPriceChartProps) {
  const { theme } = useTheme()
  const { timeRange, valueType, setTimeRange, setValueType } = useChartState(
    initialTimeRange,
    initialValueType
  )

  const {
    data: rawData,
    loading,
    error,
    lastUpdated,
    refresh
  } = useStockChartData({
    symbol,
    timeRange,
    autoRefresh,
    refreshInterval
  })

  const baseValue = rawData.length > 0 ? rawData[0].value : 0
  const transformedData = useTransformedChartData(rawData, valueType, baseValue)
  const stats = useChartStats(transformedData)
  const colors = chartUtils.getChartColors(theme as 'light' | 'dark')

  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      const value = payload[0].value
      const timestamp = data.timestamp

      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="text-sm font-medium">
            {chartUtils.formatDate(timestamp, timeRange)}
          </p>
          <p className="text-sm text-muted-foreground">
            {new Date(timestamp).toLocaleString()}
          </p>
          <p className="text-sm font-semibold mt-1">
            {valueType === 'percentage' 
              ? chartUtils.formatPercentage(value)
              : chartUtils.formatCurrency(value)
            }
          </p>
          {valueType === 'percentage' && (
            <p className="text-xs text-muted-foreground">
              vs. {chartUtils.formatCurrency(baseValue)}
            </p>
          )}
        </div>
      )
    }
    return null
  }

  // Format Y-axis values
  const formatYAxis = (value: number) => {
    if (valueType === 'percentage') {
      return `${value.toFixed(1)}%`
    }
    return value >= 1000 
      ? `$${(value / 1000).toFixed(1)}k`
      : `$${value.toFixed(0)}`
  }

  // Format X-axis values
  const formatXAxis = (timestamp: number) => {
    return chartUtils.formatDate(timestamp, timeRange)
  }

  const chartTitle = title || `${symbol} Stock Price`
  const subtitle = lastUpdated 
    ? `Last updated: ${lastUpdated.toLocaleTimeString()}`
    : undefined

  return (
    <ChartContainer
      title={chartTitle}
      subtitle={subtitle}
      loading={loading}
      error={error}
      onRefresh={refresh}
      className={className}
    >
      <div className="space-y-4">
        {/* Performance Indicator */}
        {showPerformance && transformedData.length > 0 && (
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <PerformanceIndicator
              value={stats.last}
              previousValue={stats.first}
              format={valueType === 'percentage' ? 'percentage' : 'currency'}
            />
            <div className="text-sm text-muted-foreground">
              <span>Range: </span>
              <span>
                {valueType === 'percentage' 
                  ? `${stats.min.toFixed(2)}% - ${stats.max.toFixed(2)}%`
                  : `${chartUtils.formatCurrency(stats.min)} - ${chartUtils.formatCurrency(stats.max)}`
                }
              </span>
            </div>
          </div>
        )}

        {/* Chart Controls */}
        {showControls && (
          <ChartControls
            timeRange={timeRange}
            valueType={valueType}
            onTimeRangeChange={setTimeRange}
            onValueTypeChange={setValueType}
            disabled={loading}
            showValueType={true}
          />
        )}

        {/* Chart */}
        {transformedData.length === 0 ? (
          <EmptyChartState
            title="No stock data available"
            description={`No price data found for ${symbol} in the selected time range.`}
            action={{
              label: 'Refresh',
              onClick: refresh
            }}
          />
        ) : (
          <div style={{ height: `${height}px` }}>
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={transformedData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid 
                  strokeDasharray="3 3" 
                  stroke={colors.muted}
                  opacity={0.3}
                />
                <XAxis
                  dataKey="timestamp"
                  tickFormatter={formatXAxis}
                  stroke={colors.muted}
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                />
                <YAxis
                  tickFormatter={formatYAxis}
                  stroke={colors.muted}
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                  domain={['dataMin - 5', 'dataMax + 5']}
                />
                <Tooltip content={<CustomTooltip />} />
                
                {/* Reference line at zero for percentage view */}
                {valueType === 'percentage' && (
                  <ReferenceLine 
                    y={0} 
                    stroke={colors.muted} 
                    strokeDasharray="2 2"
                    opacity={0.5}
                  />
                )}
                
                <Line
                  type="monotone"
                  dataKey="value"
                  stroke={stats.change >= 0 ? colors.success : colors.danger}
                  strokeWidth={2}
                  dot={false}
                  activeDot={{ 
                    r: 4, 
                    fill: stats.change >= 0 ? colors.success : colors.danger,
                    stroke: colors.background,
                    strokeWidth: 2
                  }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        )}

        {/* Chart Statistics */}
        {transformedData.length > 0 && (
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 pt-4 border-t">
            <div className="text-center">
              <p className="text-xs text-muted-foreground">Change</p>
              <p className={`text-sm font-medium ${
                stats.change >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {stats.change >= 0 ? '+' : ''}
                {valueType === 'percentage' 
                  ? chartUtils.formatPercentage(stats.change)
                  : chartUtils.formatCurrency(stats.change)
                }
              </p>
            </div>
            <div className="text-center">
              <p className="text-xs text-muted-foreground">Change %</p>
              <p className={`text-sm font-medium ${
                stats.changePercent >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {stats.changePercent >= 0 ? '+' : ''}
                {stats.changePercent.toFixed(2)}%
              </p>
            </div>
            <div className="text-center">
              <p className="text-xs text-muted-foreground">High</p>
              <p className="text-sm font-medium">
                {valueType === 'percentage' 
                  ? chartUtils.formatPercentage(stats.max)
                  : chartUtils.formatCurrency(stats.max)
                }
              </p>
            </div>
            <div className="text-center">
              <p className="text-xs text-muted-foreground">Low</p>
              <p className="text-sm font-medium">
                {valueType === 'percentage' 
                  ? chartUtils.formatPercentage(stats.min)
                  : chartUtils.formatCurrency(stats.min)
                }
              </p>
            </div>
          </div>
        )}
      </div>
    </ChartContainer>
  )
}

/**
 * Compact Stock Price Chart (for smaller spaces)
 */
export interface CompactStockPriceChartProps {
  symbol: string
  timeRange?: TimeRange
  height?: number
  showChange?: boolean
  className?: string
}

export function CompactStockPriceChart({
  symbol,
  timeRange = '1D',
  height = 100,
  showChange = true,
  className
}: CompactStockPriceChartProps) {
  const { theme } = useTheme()
  const { data, loading, error } = useStockChartData({
    symbol,
    timeRange
  })

  const stats = useChartStats(data)
  const colors = chartUtils.getChartColors(theme as 'light' | 'dark')

  if (loading || error || data.length === 0) {
    return (
      <div className={`flex items-center justify-center ${className}`} style={{ height: `${height}px` }}>
        <div className="text-xs text-muted-foreground">
          {loading ? 'Loading...' : error ? 'Error' : 'No data'}
        </div>
      </div>
    )
  }

  return (
    <div className={className}>
      {showChange && (
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium">{symbol}</span>
          <span className={`text-xs ${
            stats.change >= 0 ? 'text-green-600' : 'text-red-600'
          }`}>
            {stats.change >= 0 ? '+' : ''}{stats.changePercent.toFixed(2)}%
          </span>
        </div>
      )}
      <div style={{ height: `${height}px` }}>
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={data} margin={{ top: 0, right: 0, left: 0, bottom: 0 }}>
            <Line
              type="monotone"
              dataKey="value"
              stroke={stats.change >= 0 ? colors.success : colors.danger}
              strokeWidth={1.5}
              dot={false}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  )
}
