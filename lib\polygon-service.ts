/**
 * Polygon.io Integration
 * Enterprise tier service for tick-level real-time data and options
 */

import { StockQuote, MarketMover, StockFinancials } from './stock-data-service'

export interface PolygonTick {
  symbol: string
  price: number
  size: number
  timestamp: number
  exchange: number
  conditions: number[]
}

export interface PolygonAggregates {
  symbol: string
  open: number
  high: number
  low: number
  close: number
  volume: number
  vwap: number
  timestamp: number
  transactions: number
}

export interface PolygonOptionsContract {
  ticker: string
  underlying_ticker: string
  contract_type: 'call' | 'put'
  strike_price: number
  expiration_date: string
  implied_volatility?: number
  delta?: number
  gamma?: number
  theta?: number
  vega?: number
  open_interest?: number
  volume?: number
  bid?: number
  ask?: number
  last_price?: number
}

export interface PolygonMarketStatus {
  market: string
  serverTime: string
  exchanges: {
    nasdaq: string
    nyse: string
    otc: string
  }
  currencies: {
    fx: string
    crypto: string
  }
}

export class PolygonService {
  private apiKey: string
  private baseUrl = 'https://api.polygon.io'
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>()

  constructor(apiKey: string) {
    this.apiKey = apiKey
  }

  /**
   * Get real-time stock quote with tick-level precision
   */
  async getRealTimeQuote(symbol: string): Promise<StockQuote | null> {
    const cacheKey = `polygon-quote:${symbol}`
    
    const cached = this.getFromCache<StockQuote>(cacheKey)
    if (cached) return cached

    try {
      // Get previous day's aggregates for baseline
      const prevResponse = await fetch(
        `${this.baseUrl}/v2/aggs/ticker/${symbol}/prev?adjusted=true&apikey=${this.apiKey}`
      )

      if (!prevResponse.ok) return null

      const prevData = await prevResponse.json()
      if (!prevData.results?.[0]) return null

      const result = prevData.results[0]

      // Get real-time last trade
      const lastTradeResponse = await fetch(
        `${this.baseUrl}/v2/last/trade/${symbol}?apikey=${this.apiKey}`
      )

      let currentPrice = result.c
      if (lastTradeResponse.ok) {
        const lastTradeData = await lastTradeResponse.json()
        if (lastTradeData.results?.p) {
          currentPrice = lastTradeData.results.p
        }
      }

      // Get company details
      const detailsResponse = await fetch(
        `${this.baseUrl}/v3/reference/tickers/${symbol}?apikey=${this.apiKey}`
      )

      let companyName = symbol
      let sector = 'Unknown'
      if (detailsResponse.ok) {
        const detailsData = await detailsResponse.json()
        if (detailsData.results) {
          companyName = detailsData.results.name || symbol
          sector = detailsData.results.sic_description || 'Unknown'
        }
      }

      const quote: StockQuote = {
        symbol,
        name: companyName,
        price: currentPrice,
        change: currentPrice - result.o,
        changePercent: ((currentPrice - result.o) / result.o) * 100,
        volume: result.v,
        high: result.h,
        low: result.l,
        open: result.o,
        previousClose: result.c,
        exchange: 'POLYGON',
        sector,
        lastUpdated: new Date().toISOString(),
        source: 'polygon'
      }

      this.setCache(cacheKey, quote, 10 * 1000) // 10 second cache for real-time
      return quote
    } catch (error) {
      console.error('Polygon real-time quote error:', error)
      return null
    }
  }

  /**
   * Get tick-level data stream
   */
  async getTickData(symbol: string, date?: string): Promise<PolygonTick[]> {
    try {
      const dateStr = date || new Date().toISOString().split('T')[0]
      
      const response = await fetch(
        `${this.baseUrl}/v3/trades/${symbol}?timestamp.gte=${dateStr}&limit=1000&apikey=${this.apiKey}`
      )

      if (!response.ok) return []

      const data = await response.json()
      if (!data.results) return []

      return data.results.map((tick: any) => ({
        symbol,
        price: tick.price,
        size: tick.size,
        timestamp: tick.participant_timestamp,
        exchange: tick.exchange,
        conditions: tick.conditions || []
      }))
    } catch (error) {
      console.error('Polygon tick data error:', error)
      return []
    }
  }

  /**
   * Get market movers with enterprise-level data
   */
  async getMarketMovers(): Promise<{ gainers: MarketMover[]; losers: MarketMover[]; mostActive: MarketMover[] } | null> {
    try {
      // Get market snapshot for all tickers
      const response = await fetch(
        `${this.baseUrl}/v2/snapshot/locale/us/markets/stocks/tickers?apikey=${this.apiKey}`
      )

      if (!response.ok) return null

      const data = await response.json()
      if (!data.tickers) return null

      // Filter and process tickers
      const validTickers = data.tickers
        .filter((ticker: any) => 
          ticker.day && 
          ticker.day.c > 0 && 
          ticker.day.v > 100000 && // Minimum volume filter
          ticker.ticker.length <= 5 && // Filter out complex tickers
          !ticker.ticker.includes('.')
        )
        .slice(0, 100) // Limit to top 100 for processing

      const movers: MarketMover[] = validTickers.map((ticker: any) => ({
        symbol: ticker.ticker,
        name: ticker.ticker, // Would need separate call for company names
        price: ticker.day.c,
        change: ticker.day.c - ticker.day.o,
        changePercent: ((ticker.day.c - ticker.day.o) / ticker.day.o) * 100,
        volume: ticker.day.v,
        marketCap: ticker.market_cap,
        lastUpdated: new Date().toISOString(),
        source: 'polygon'
      }))

      // Sort and categorize
      const gainers = movers
        .filter(m => m.changePercent > 0)
        .sort((a, b) => b.changePercent - a.changePercent)
        .slice(0, 10)

      const losers = movers
        .filter(m => m.changePercent < 0)
        .sort((a, b) => a.changePercent - b.changePercent)
        .slice(0, 10)

      const mostActive = movers
        .sort((a, b) => b.volume - a.volume)
        .slice(0, 10)

      return { gainers, losers, mostActive }
    } catch (error) {
      console.error('Polygon market movers error:', error)
      return null
    }
  }

  /**
   * Get options data
   */
  async getOptionsChain(symbol: string, expiration?: string): Promise<PolygonOptionsContract[]> {
    try {
      let url = `${this.baseUrl}/v3/reference/options/contracts?underlying_ticker=${symbol}&limit=1000&apikey=${this.apiKey}`
      
      if (expiration) {
        url += `&expiration_date=${expiration}`
      }

      const response = await fetch(url)

      if (!response.ok) return []

      const data = await response.json()
      if (!data.results) return []

      return data.results.map((contract: any) => ({
        ticker: contract.ticker,
        underlying_ticker: contract.underlying_ticker,
        contract_type: contract.contract_type,
        strike_price: contract.strike_price,
        expiration_date: contract.expiration_date,
        // Additional Greeks would require separate market data calls
      }))
    } catch (error) {
      console.error('Polygon options chain error:', error)
      return []
    }
  }

  /**
   * Get market status
   */
  async getMarketStatus(): Promise<PolygonMarketStatus | null> {
    try {
      const response = await fetch(
        `${this.baseUrl}/v1/marketstatus/now?apikey=${this.apiKey}`
      )

      if (!response.ok) return null

      const data = await response.json()
      
      return {
        market: data.market || 'unknown',
        serverTime: data.serverTime || new Date().toISOString(),
        exchanges: {
          nasdaq: data.exchanges?.nasdaq || 'unknown',
          nyse: data.exchanges?.nyse || 'unknown',
          otc: data.exchanges?.otc || 'unknown'
        },
        currencies: {
          fx: data.currencies?.fx || 'unknown',
          crypto: data.currencies?.crypto || 'unknown'
        }
      }
    } catch (error) {
      console.error('Polygon market status error:', error)
      return null
    }
  }

  /**
   * Get financial data with enterprise-level details
   */
  async getFinancials(symbol: string): Promise<StockFinancials | null> {
    try {
      const response = await fetch(
        `${this.baseUrl}/vX/reference/financials?ticker=${symbol}&limit=1&apikey=${this.apiKey}`
      )

      if (!response.ok) return null

      const data = await response.json()
      if (!data.results?.[0]) return null

      const financials = data.results[0]
      const balanceSheet = financials.financials?.balance_sheet
      const incomeStatement = financials.financials?.income_statement
      const cashFlow = financials.financials?.cash_flow_statement

      return {
        symbol,
        // Calculate metrics from raw financial data
        peRatio: incomeStatement?.basic_earnings_per_share ? 
          (await this.getRealTimeQuote(symbol))?.price / incomeStatement.basic_earnings_per_share : undefined,
        eps: incomeStatement?.basic_earnings_per_share?.value,
        marketCap: balanceSheet?.equity?.value,
        dividendYield: incomeStatement?.preferred_stock_dividends_and_other_adjustments?.value,
        debtToEquity: balanceSheet?.liabilities?.value && balanceSheet?.equity?.value ?
          balanceSheet.liabilities.value / balanceSheet.equity.value : undefined,
        currentRatio: balanceSheet?.current_assets?.value && balanceSheet?.current_liabilities?.value ?
          balanceSheet.current_assets.value / balanceSheet.current_liabilities.value : undefined,
        lastUpdated: new Date().toISOString(),
        source: 'polygon'
      }
    } catch (error) {
      console.error('Polygon financials error:', error)
      return null
    }
  }

  /**
   * Cache management
   */
  private getFromCache<T>(key: string): T | null {
    const cached = this.cache.get(key)
    if (!cached) return null

    if (Date.now() > cached.timestamp + cached.ttl) {
      this.cache.delete(key)
      return null
    }

    return cached.data as T
  }

  private setCache(key: string, data: any, ttl: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }
}

// Singleton instance
let polygonServiceInstance: PolygonService | null = null

export function getPolygonService(): PolygonService | null {
  const apiKey = process.env.POLYGON_IO_API_KEY
  if (!apiKey) return null

  if (!polygonServiceInstance) {
    polygonServiceInstance = new PolygonService(apiKey)
  }
  return polygonServiceInstance
}
