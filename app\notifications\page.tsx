"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { TopNav } from "@/components/top-nav"
import { Bell, TrendingUp, AlertCircle, CheckCircle, Info, Settings, Trash2, Filter } from "lucide-react"
import { useNotifications } from "@/components/notification-system"
import { usePersistentNotifications } from "@/components/persistent-notifications"
import { useAuth } from "@/components/auth-provider"

export default function NotificationsPage() {
  const router = useRouter()
  const { addNotification } = useNotifications()
  const {
    notifications,
    markAsRead,
    markAsUnread,
    markAllAsRead,
    markAllAsUnread,
    deleteNotification,
    unreadCount
  } = usePersistentNotifications()
  const { user, loading: authLoading } = useAuth()
  const [loading, setLoading] = useState(true)
  const [settings, setSettings] = useState({
    priceAlerts: true,
    marketUpdates: true,
    portfolioUpdates: true,
    educationalContent: false,
    newsAlerts: true,
    emailNotifications: true,
    pushNotifications: true,
  })

  useEffect(() => {
    // Wait for auth to load
    if (authLoading) return

    // Check if user is authenticated with Supabase
    if (!user) {
      router.push("/auth/signin")
      return
    }

    // Load notification settings
    const savedSettings = localStorage.getItem("investry_notification_settings")
    if (savedSettings) {
      setSettings(JSON.parse(savedSettings))
    }

    setLoading(false)
  }, [user, authLoading, router])



  const handleSettingChange = (key: string, value: boolean) => {
    const newSettings = { ...settings, [key]: value }
    setSettings(newSettings)
    localStorage.setItem("investry_notification_settings", JSON.stringify(newSettings))

    addNotification({
      type: "success",
      title: "Settings updated",
      message: `${key.replace(/([A-Z])/g, " $1").toLowerCase()} ${value ? "enabled" : "disabled"}`,
    })
  }

  const handleMarkAsRead = (id: string) => {
    markAsRead(id)
  }

  const handleMarkAsUnread = (id: string) => {
    markAsUnread(id)
  }

  const handleDeleteNotification = (id: string) => {
    deleteNotification(id)
    addNotification({
      type: "info",
      title: "Notification deleted",
      message: "Notification has been removed",
    })
  }

  const handleMarkAllAsRead = () => {
    markAllAsRead()
    addNotification({
      type: "success",
      title: "All notifications marked as read",
      message: "Your notification list has been updated",
    })
  }

  const handleMarkAllAsUnread = () => {
    markAllAsUnread()
    addNotification({
      type: "info",
      title: "All notifications marked as unread",
      message: "Your notification list has been updated",
    })
  }

  const getTimeAgo = (timestamp: Date) => {
    const now = new Date()
    const diff = now.getTime() - timestamp.getTime()
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(hours / 24)

    if (days > 0) {
      return `${days} day${days > 1 ? "s" : ""} ago`
    } else if (hours > 0) {
      return `${hours} hour${hours > 1 ? "s" : ""} ago`
    } else {
      return "Just now"
    }
  }

  if (authLoading || loading || !user) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-white">Loading...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-900 text-white">
      <TopNav />

      <div className="container mx-auto px-4 py-8 space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Notifications</h1>
            <p className="text-slate-400 mt-1">
              Stay updated with your investments and market changes
              {unreadCount > 0 && (
                <Badge variant="secondary" className="ml-2 bg-emerald-500/20 text-emerald-400">
                  {unreadCount} unread
                </Badge>
              )}
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleMarkAllAsRead}
              className="border-slate-600 text-slate-300 bg-transparent"
              disabled={unreadCount === 0}
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Mark All Read ({unreadCount})
            </Button>
            <Button
              variant="outline"
              onClick={handleMarkAllAsUnread}
              className="border-slate-600 text-slate-300 bg-transparent"
              disabled={notifications.length === 0 || unreadCount === notifications.length}
            >
              <Bell className="h-4 w-4 mr-2" />
              Mark All Unread
            </Button>
            <Button variant="outline" className="border-slate-600 text-slate-300 bg-transparent">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Notifications List */}
          <div className="lg:col-span-2 space-y-4">
            <h2 className="text-xl font-semibold">Recent Notifications</h2>

            {notifications.length === 0 ? (
              <Card className="bg-slate-800/50 border-slate-700">
                <CardContent className="p-8 text-center">
                  <Bell className="h-16 w-16 text-slate-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-white mb-2">No notifications</h3>
                  <p className="text-slate-400">You're all caught up! New notifications will appear here.</p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-3">
                {notifications.map((notification) => {
                  // Map notification types to icons and colors
                  const getNotificationIcon = (type: string) => {
                    switch (type) {
                      case 'portfolio': return { icon: TrendingUp, color: 'text-emerald-400' }
                      case 'goal': return { icon: Target, color: 'text-yellow-400' }
                      case 'market': return { icon: Info, color: 'text-blue-400' }
                      case 'educational': return { icon: CheckCircle, color: 'text-purple-400' }
                      case 'system': return { icon: Bell, color: 'text-slate-400' }
                      default: return { icon: Bell, color: 'text-slate-400' }
                    }
                  }

                  const { icon: IconComponent, color } = getNotificationIcon(notification.type)

                  return (
                    <Card
                      key={notification.id}
                      className={`border-slate-700 transition-colors cursor-pointer ${
                        notification.read ? "bg-slate-800/30" : "bg-slate-800/50 border-l-4 border-l-emerald-400"
                      }`}
                      onClick={() => handleMarkAsRead(notification.id)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-start gap-3">
                          <div className={`mt-1 ${color}`}>
                            <IconComponent className="h-5 w-5" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <h4 className={`font-medium ${notification.read ? "text-slate-300" : "text-white"}`}>
                                  {notification.title}
                                </h4>
                                <p
                                  className={`text-sm mt-1 ${notification.read ? "text-slate-500" : "text-slate-400"}`}
                                >
                                  {notification.message}
                                </p>
                                <p className="text-xs text-slate-500 mt-2">{getTimeAgo(notification.createdAt)}</p>
                              </div>
                              <div className="flex gap-1 ml-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    if (notification.read) {
                                      handleMarkAsUnread(notification.id)
                                    } else {
                                      handleMarkAsRead(notification.id)
                                    }
                                  }}
                                  className={`text-slate-400 hover:text-blue-400 ${
                                    notification.read ? 'hover:text-yellow-400' : 'hover:text-green-400'
                                  }`}
                                  title={notification.read ? "Mark as unread" : "Mark as read"}
                                >
                                  {notification.read ? (
                                    <Bell className="h-4 w-4" />
                                  ) : (
                                    <CheckCircle className="h-4 w-4" />
                                  )}
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    handleDeleteNotification(notification.id)
                                  }}
                                  className="text-slate-400 hover:text-red-400"
                                  title="Delete notification"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            )}
          </div>

          {/* Notification Settings */}
          <div className="space-y-6">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5 text-emerald-400" />
                  Notification Settings
                </CardTitle>
                <CardDescription>Customize what notifications you receive</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-white">Price Alerts</p>
                      <p className="text-sm text-slate-400">Get notified when stocks hit target prices</p>
                    </div>
                    <Switch
                      checked={settings.priceAlerts}
                      onCheckedChange={(value) => handleSettingChange("priceAlerts", value)}
                    />
                  </div>

                  <Separator className="bg-slate-700" />

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-white">Market Updates</p>
                      <p className="text-sm text-slate-400">Daily market summaries and trends</p>
                    </div>
                    <Switch
                      checked={settings.marketUpdates}
                      onCheckedChange={(value) => handleSettingChange("marketUpdates", value)}
                    />
                  </div>

                  <Separator className="bg-slate-700" />

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-white">Portfolio Updates</p>
                      <p className="text-sm text-slate-400">Changes in your portfolio performance</p>
                    </div>
                    <Switch
                      checked={settings.portfolioUpdates}
                      onCheckedChange={(value) => handleSettingChange("portfolioUpdates", value)}
                    />
                  </div>

                  <Separator className="bg-slate-700" />

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-white">Educational Content</p>
                      <p className="text-sm text-slate-400">New courses and learning materials</p>
                    </div>
                    <Switch
                      checked={settings.educationalContent}
                      onCheckedChange={(value) => handleSettingChange("educationalContent", value)}
                    />
                  </div>

                  <Separator className="bg-slate-700" />

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-white">News Alerts</p>
                      <p className="text-sm text-slate-400">Breaking financial news and updates</p>
                    </div>
                    <Switch
                      checked={settings.newsAlerts}
                      onCheckedChange={(value) => handleSettingChange("newsAlerts", value)}
                    />
                  </div>
                </div>

                <Separator className="bg-slate-700" />

                <div className="space-y-4">
                  <h4 className="font-medium text-white">Delivery Methods</h4>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-white">Email Notifications</p>
                      <p className="text-sm text-slate-400">Receive notifications via email</p>
                    </div>
                    <Switch
                      checked={settings.emailNotifications}
                      onCheckedChange={(value) => handleSettingChange("emailNotifications", value)}
                    />
                  </div>

                  <Separator className="bg-slate-700" />

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-white">Push Notifications</p>
                      <p className="text-sm text-slate-400">Browser and mobile notifications</p>
                    </div>
                    <Switch
                      checked={settings.pushNotifications}
                      onCheckedChange={(value) => handleSettingChange("pushNotifications", value)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle>Notification Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-slate-400">Total notifications</span>
                    <span className="text-white font-medium">{notifications.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-400">Unread</span>
                    <span className="text-emerald-400 font-medium">{unreadCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-400">This week</span>
                    <span className="text-white font-medium">
                      {notifications.filter((n) => n.timestamp > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)).length}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
