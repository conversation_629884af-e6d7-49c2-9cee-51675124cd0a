"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Calendar, TrendingUp, TrendingDown, DollarSign, Clock, ArrowLeft } from "lucide-react"
import { useRouter } from "next/navigation"

export default function HistoryPage() {
  const router = useRouter()
  const [transactions, setTransactions] = useState([
    {
      id: 1,
      type: "buy",
      symbol: "AAPL",
      name: "Apple Inc.",
      shares: 10,
      price: 150.25,
      total: 1502.5,
      date: "2024-01-15",
      time: "09:30 AM",
    },
    {
      id: 2,
      type: "sell",
      symbol: "GOOGL",
      name: "Alphabet Inc.",
      shares: 5,
      price: 2800.0,
      total: 14000.0,
      date: "2024-01-14",
      time: "02:15 PM",
    },
    {
      id: 3,
      type: "buy",
      symbol: "MSFT",
      name: "Microsoft Corporation",
      shares: 8,
      price: 380.75,
      total: 3046.0,
      date: "2024-01-12",
      time: "11:45 AM",
    },
  ])

  const [portfolioHistory, setPortfolioHistory] = useState([
    { date: "2024-01-15", value: 25000, change: 2.5 },
    { date: "2024-01-14", value: 24390, change: -1.2 },
    { date: "2024-01-13", value: 24685, change: 0.8 },
    { date: "2024-01-12", value: 24490, change: 1.5 },
  ])

  const totalGainLoss = transactions.reduce((acc, transaction) => {
    return transaction.type === "sell" ? acc + transaction.total : acc - transaction.total
  }, 0)

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8 space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Investment History</h1>
            <p className="text-muted-foreground">Track your investment performance and transactions</p>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-green-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Total Invested</p>
                  <p className="text-2xl font-bold">
                    $
                    {transactions
                      .filter((t) => t.type === "buy")
                      .reduce((acc, t) => acc + t.total, 0)
                      .toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-2">
                {totalGainLoss >= 0 ? (
                  <TrendingUp className="h-5 w-5 text-green-500" />
                ) : (
                  <TrendingDown className="h-5 w-5 text-red-500" />
                )}
                <div>
                  <p className="text-sm text-muted-foreground">Total Gain/Loss</p>
                  <p className={`text-2xl font-bold ${totalGainLoss >= 0 ? "text-green-600" : "text-red-600"}`}>
                    {totalGainLoss >= 0 ? "+" : ""}${totalGainLoss.toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Total Transactions</p>
                  <p className="text-2xl font-bold">{transactions.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* History Tabs */}
        <Tabs defaultValue="transactions" className="space-y-6">
          <TabsList>
            <TabsTrigger value="transactions">Transactions</TabsTrigger>
            <TabsTrigger value="portfolio">Portfolio Value</TabsTrigger>
          </TabsList>

          <TabsContent value="transactions" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Recent Transactions</CardTitle>
                <CardDescription>Your latest buy and sell orders</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {transactions.map((transaction) => (
                    <div key={transaction.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <Badge variant={transaction.type === "buy" ? "default" : "secondary"}>
                          {transaction.type.toUpperCase()}
                        </Badge>
                        <div>
                          <h4 className="font-semibold">{transaction.symbol}</h4>
                          <p className="text-sm text-muted-foreground">{transaction.name}</p>
                        </div>
                      </div>

                      <div className="text-center">
                        <p className="font-semibold">{transaction.shares} shares</p>
                        <p className="text-sm text-muted-foreground">@ ${transaction.price}</p>
                      </div>

                      <div className="text-right">
                        <p className="font-semibold">${transaction.total.toLocaleString()}</p>
                        <div className="flex items-center gap-1 text-sm text-muted-foreground">
                          <Clock className="h-3 w-3" />
                          <span>
                            {transaction.date} {transaction.time}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="portfolio" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Portfolio Value History</CardTitle>
                <CardDescription>Track your portfolio performance over time</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {portfolioHistory.map((entry, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <Calendar className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <h4 className="font-semibold">{entry.date}</h4>
                          <p className="text-sm text-muted-foreground">Portfolio Value</p>
                        </div>
                      </div>

                      <div className="text-right">
                        <p className="font-semibold">${entry.value.toLocaleString()}</p>
                        <div className="flex items-center gap-1">
                          {entry.change >= 0 ? (
                            <TrendingUp className="h-3 w-3 text-green-500" />
                          ) : (
                            <TrendingDown className="h-3 w-3 text-red-500" />
                          )}
                          <span className={`text-sm ${entry.change >= 0 ? "text-green-600" : "text-red-600"}`}>
                            {entry.change >= 0 ? "+" : ""}
                            {entry.change}%
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
