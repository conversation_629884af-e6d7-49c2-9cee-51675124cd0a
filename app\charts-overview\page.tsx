/**
 * Charts Overview Page
 * Landing page for chart navigation and overview
 */

'use client'

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { PageHeader } from '@/components/breadcrumb'
import { TopNav } from '@/components/top-nav'
import { BottomNav } from '@/components/bottom-nav'
import { ChartNavigationCards, BackToDashboard } from '@/components/chart-navigation'
import {
  BarChart3,
  LineChart,
  TrendingUp,
  Activity,
  Zap,
  Eye,
  MousePointer,
  Smartphone
} from 'lucide-react'
import Link from 'next/link'

export default function ChartsOverviewPage() {
  const features = [
    {
      icon: Zap,
      title: 'Real-time Data',
      description: 'Live stock prices and portfolio updates with intelligent caching'
    },
    {
      icon: Eye,
      title: 'Interactive Controls',
      description: 'Time range switching, value type toggles, and responsive design'
    },
    {
      icon: MousePointer,
      title: 'Rich Tooltips',
      description: 'Detailed information on hover with timestamps and values'
    },
    {
      icon: Smartphone,
      title: 'Mobile Optimized',
      description: 'Touch-friendly controls and responsive layouts for all devices'
    }
  ]

  const quickLinks = [
    {
      title: 'Stock Charts',
      description: 'View AAPL, MSFT, GOOGL and more',
      href: '/charts?tab=stock',
      icon: LineChart,
      badge: 'Popular'
    },
    {
      title: 'Portfolio Performance',
      description: 'Track your investment returns',
      href: '/charts?tab=portfolio',
      icon: TrendingUp,
      badge: 'Personal'
    },
    {
      title: 'Chart Testing',
      description: 'Test all chart components',
      href: '/test-charts',
      icon: Activity,
      badge: 'Demo'
    }
  ]

  return (
    <div className="min-h-screen bg-background">
      <TopNav />
      
      <div className="container mx-auto p-6 space-y-8">
        {/* Page Header */}
        <PageHeader
          title="Financial Charts Overview"
          description="Explore interactive charts for stocks and portfolio performance"
          showBackButton={true}
          actions={<BackToDashboard />}
        />

        {/* Quick Links */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          {quickLinks.map((link, index) => (
            <Link key={index} href={link.href}>
              <Card className="hover:shadow-lg transition-all duration-200 hover:scale-105 cursor-pointer">
                <CardContent className="p-6 text-center space-y-3">
                  <div className="flex items-center justify-center">
                    <div className="p-3 bg-primary/10 rounded-full">
                      <link.icon className="h-8 w-8 text-primary" />
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center justify-center gap-2">
                      <h3 className="font-semibold">{link.title}</h3>
                      <Badge variant="secondary" className="text-xs">
                        {link.badge}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {link.description}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        {/* Chart Navigation Cards */}
        <div className="space-y-4">
          <h2 className="text-2xl font-bold">Chart Types</h2>
          <ChartNavigationCards />
        </div>

        {/* Features Section */}
        <div className="space-y-6">
          <h2 className="text-2xl font-bold">Chart Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature, index) => (
              <Card key={index}>
                <CardContent className="p-6 text-center space-y-3">
                  <div className="flex items-center justify-center">
                    <div className="p-3 bg-primary/10 rounded-full">
                      <feature.icon className="h-6 w-6 text-primary" />
                    </div>
                  </div>
                  <div className="space-y-1">
                    <h3 className="font-semibold">{feature.title}</h3>
                    <p className="text-sm text-muted-foreground">
                      {feature.description}
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Getting Started */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Getting Started with Charts
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <h4 className="font-semibold">For Stock Analysis</h4>
                <ol className="space-y-2 text-sm text-muted-foreground">
                  <li className="flex items-start gap-2">
                    <span className="bg-primary text-primary-foreground rounded-full w-5 h-5 flex items-center justify-center text-xs font-medium">1</span>
                    Navigate to Stock Charts section
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="bg-primary text-primary-foreground rounded-full w-5 h-5 flex items-center justify-center text-xs font-medium">2</span>
                    Enter a stock symbol (e.g., AAPL, MSFT)
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="bg-primary text-primary-foreground rounded-full w-5 h-5 flex items-center justify-center text-xs font-medium">3</span>
                    Select time range and value type
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="bg-primary text-primary-foreground rounded-full w-5 h-5 flex items-center justify-center text-xs font-medium">4</span>
                    Hover over chart for detailed information
                  </li>
                </ol>
              </div>
              
              <div className="space-y-3">
                <h4 className="font-semibold">For Portfolio Tracking</h4>
                <ol className="space-y-2 text-sm text-muted-foreground">
                  <li className="flex items-start gap-2">
                    <span className="bg-primary text-primary-foreground rounded-full w-5 h-5 flex items-center justify-center text-xs font-medium">1</span>
                    Go to Portfolio Performance section
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="bg-primary text-primary-foreground rounded-full w-5 h-5 flex items-center justify-center text-xs font-medium">2</span>
                    View your portfolio timeline
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="bg-primary text-primary-foreground rounded-full w-5 h-5 flex items-center justify-center text-xs font-medium">3</span>
                    Track deposits and performance
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="bg-primary text-primary-foreground rounded-full w-5 h-5 flex items-center justify-center text-xs font-medium">4</span>
                    Analyze returns over different periods
                  </li>
                </ol>
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <Link href="/charts">
                <Button className="w-full sm:w-auto">
                  Start Exploring Charts
                </Button>
              </Link>
              <Link href="/test-charts">
                <Button variant="outline" className="w-full sm:w-auto">
                  View Chart Demo
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* Chart Types Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Available Chart Types</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="space-y-2">
                <h4 className="font-semibold flex items-center gap-2">
                  <LineChart className="h-4 w-4" />
                  Line Charts
                </h4>
                <p className="text-muted-foreground">
                  Perfect for tracking price movements and trends over time
                </p>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-semibold flex items-center gap-2">
                  <Activity className="h-4 w-4" />
                  Area Charts
                </h4>
                <p className="text-muted-foreground">
                  Great for visualizing portfolio value with filled areas
                </p>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-semibold flex items-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  Compact Charts
                </h4>
                <p className="text-muted-foreground">
                  Small widgets perfect for dashboard integration
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      <BottomNav />
    </div>
  )
}
