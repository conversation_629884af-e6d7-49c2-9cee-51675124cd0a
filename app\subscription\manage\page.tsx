"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { TopNav } from "@/components/top-nav"
import { 
  Crown, 
  Calendar, 
  CreditCard, 
  Settings, 
  AlertTriangle,
  CheckCircle,
  ArrowLeft,
  Star,
  Zap
} from "lucide-react"
import { useNotifications } from "@/components/notification-system"
import { useAuth } from "@/components/auth-provider"

interface SubscriptionStatus {
  hasActiveSubscription: boolean
  plan: string
  planName: string
  status: string
  currentPeriodStart?: string
  currentPeriodEnd?: string
  cancelAtPeriodEnd?: boolean
  canceledAt?: string
  isTrialing?: boolean
  trialEnd?: string
  amount?: number
  currency?: string
  interval?: string
  stripeSubscriptionId?: string
  nextBillingDate?: string
  daysUntilRenewal?: number
}

export default function SubscriptionManagePage() {
  const router = useRouter()
  const { addNotification } = useNotifications()
  const { user, loading: authLoading } = useAuth()
  const [loading, setLoading] = useState(true)
  const [subscription, setSubscription] = useState<SubscriptionStatus | null>(null)

  useEffect(() => {
    if (authLoading) return

    if (!user) {
      router.push("/auth/signin")
      return
    }

    fetchSubscriptionStatus()
  }, [user, authLoading, router])

  const fetchSubscriptionStatus = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/subscription-status?user_id=${user?.id}`)
      
      if (response.ok) {
        const data = await response.json()
        setSubscription(data)
      } else {
        throw new Error('Failed to fetch subscription status')
      }
    } catch (error) {
      console.error('Error fetching subscription status:', error)
      addNotification({
        type: "error",
        title: "Error",
        message: "Failed to load subscription information. Please try again.",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleUpgrade = () => {
    router.push("/pricing")
  }

  const handleCancelSubscription = async () => {
    if (!subscription?.stripeSubscriptionId) return

    try {
      setLoading(true)
      const response = await fetch('/api/cancel-subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscriptionId: subscription.stripeSubscriptionId,
          userId: user?.id
        }),
      })

      if (response.ok) {
        addNotification({
          type: "success",
          title: "Subscription Canceled",
          message: "Your subscription will remain active until the end of your billing period.",
        })
        fetchSubscriptionStatus() // Refresh status
      } else {
        throw new Error('Failed to cancel subscription')
      }
    } catch (error) {
      console.error('Error canceling subscription:', error)
      addNotification({
        type: "error",
        title: "Cancellation Error",
        message: "Failed to cancel subscription. Please contact support.",
      })
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-900 text-white">
        <TopNav />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-500"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-900 text-white">
      <TopNav />
      
      <div className="container mx-auto px-4 py-8 space-y-8">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push("/settings")}
            className="border-slate-600 text-slate-300 hover:bg-slate-700"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Settings
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Subscription Management</h1>
            <p className="text-slate-400 mt-1">Manage your Investry subscription</p>
          </div>
        </div>

        {/* Current Plan */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {subscription?.plan === 'premium' ? (
                <Crown className="h-5 w-5 text-yellow-500" />
              ) : (
                <Star className="h-5 w-5 text-slate-400" />
              )}
              Current Plan
            </CardTitle>
            <CardDescription>Your current subscription details</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-xl font-semibold text-white">
                  {subscription?.planName || 'Investry Free'}
                </h3>
                <p className="text-slate-400">
                  {subscription?.hasActiveSubscription 
                    ? `$${subscription.amount}/${subscription.interval}`
                    : 'Free forever'
                  }
                </p>
              </div>
              <div className="flex items-center gap-3">
                {subscription?.isTrialing && (
                  <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">
                    Trial Active
                  </Badge>
                )}
                {subscription?.hasActiveSubscription ? (
                  <Badge className="bg-emerald-500/20 text-emerald-400 border-emerald-500/30">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Active
                  </Badge>
                ) : (
                  <Badge className="bg-slate-500/20 text-slate-400 border-slate-500/30">
                    Free Plan
                  </Badge>
                )}
              </div>
            </div>

            {subscription?.hasActiveSubscription && (
              <div className="grid md:grid-cols-2 gap-4 p-4 bg-slate-700/50 rounded-lg">
                <div>
                  <p className="text-sm text-slate-400">Next billing date</p>
                  <p className="font-medium text-white">
                    {subscription.nextBillingDate ? formatDate(subscription.nextBillingDate) : 'N/A'}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-slate-400">Days until renewal</p>
                  <p className="font-medium text-white">
                    {subscription.daysUntilRenewal || 0} days
                  </p>
                </div>
              </div>
            )}

            {subscription?.isTrialing && subscription.trialEnd && (
              <div className="p-4 bg-blue-500/10 border border-blue-500/30 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Zap className="h-4 w-4 text-blue-400" />
                  <p className="font-medium text-blue-400">Free Trial Active</p>
                </div>
                <p className="text-sm text-slate-300">
                  Your free trial ends on {formatDate(subscription.trialEnd)}. 
                  You'll be charged ${subscription.amount} on that date unless you cancel.
                </p>
              </div>
            )}

            {subscription?.cancelAtPeriodEnd && (
              <div className="p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-400" />
                  <p className="font-medium text-yellow-400">Subscription Ending</p>
                </div>
                <p className="text-sm text-slate-300">
                  Your subscription will end on {subscription.nextBillingDate ? formatDate(subscription.nextBillingDate) : 'N/A'}. 
                  You'll still have access to premium features until then.
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="grid md:grid-cols-2 gap-6">
          {!subscription?.hasActiveSubscription ? (
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Crown className="h-5 w-5 text-yellow-500" />
                  Upgrade to Premium
                </CardTitle>
                <CardDescription>Unlock advanced features and insights</CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  className="w-full bg-emerald-600 hover:bg-emerald-700"
                  onClick={handleUpgrade}
                >
                  <Zap className="h-4 w-4 mr-2" />
                  Upgrade Now
                </Button>
              </CardContent>
            </Card>
          ) : (
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5 text-slate-400" />
                  Manage Subscription
                </CardTitle>
                <CardDescription>Update your subscription settings</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  variant="outline"
                  className="w-full border-slate-600 text-slate-300 hover:bg-slate-700"
                  onClick={() => router.push("/payments/manage")}
                >
                  <CreditCard className="h-4 w-4 mr-2" />
                  Payment History
                </Button>
                {!subscription.cancelAtPeriodEnd && (
                  <Button
                    variant="outline"
                    className="w-full border-red-600 text-red-400 hover:bg-red-600/10"
                    onClick={handleCancelSubscription}
                  >
                    Cancel Subscription
                  </Button>
                )}
              </CardContent>
            </Card>
          )}

          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-slate-400" />
                Billing Information
              </CardTitle>
              <CardDescription>Manage your billing details</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-slate-400 mb-4">
                Update payment methods and billing information
              </p>
              <Button
                variant="outline"
                className="w-full border-slate-600 text-slate-300 hover:bg-slate-700"
                disabled
              >
                <CreditCard className="h-4 w-4 mr-2" />
                Manage Payment Methods
                <span className="ml-2 text-xs text-slate-500">(Coming Soon)</span>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
