"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { TopNav } from "@/components/top-nav"
import { ThemeToggle } from "@/components/theme-toggle"
import {
  User,
  Shield,
  Bell,
  Palette,
  Globe,
  CreditCard,
  LogOut,
  Edit2,
  Check,
  X,
  Mail,
  Lock,
  Smartphone,
  Crown,
  Zap,
} from "lucide-react"
import { useNotifications } from "@/components/notification-system"
import { useAuth } from "@/components/auth-provider"
import { useSubscription } from "@/hooks/use-subscription"
import { getUserFullName, updateUserPro<PERSON>le, deleteUserAccount, clearUserLocalStorage } from "@/lib/user-service"

export default function SettingsPage() {
  const router = useRouter()
  const { addNotification } = useNotifications()
  const { signOut, user, loading: authLoading } = useAuth()
  const subscription = useSubscription()
  const [loading, setLoading] = useState(true)
  const [isEditingEmail, setIsEditingEmail] = useState(false)
  const [editedEmail, setEditedEmail] = useState("")
  const [isEditingName, setIsEditingName] = useState(false)
  const [editedFirstName, setEditedFirstName] = useState("")
  const [editedLastName, setEditedLastName] = useState("")
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [deleteConfirmText, setDeleteConfirmText] = useState("")
  const [settings, setSettings] = useState({
    darkMode: true,
    notifications: true,
    emailUpdates: true,
    twoFactorAuth: false,
    dataSharing: false,
    autoInvest: false,
    currency: "USD",
    language: "English",
  })

  useEffect(() => {
    // Wait for auth to load
    if (authLoading) return

    // Check if user is authenticated with Supabase
    if (!user) {
      router.push("/auth/signin")
      return
    }

    // Load user data and settings
    loadUserData()
  }, [user, authLoading, router])

  const loadUserData = () => {
    // User data comes from Supabase auth context
    if (user) {
      setEditedEmail(user.email || "")
      setEditedFirstName(user.user_metadata?.first_name || "")
      setEditedLastName(user.user_metadata?.last_name || "")
    }

    // Load settings from localStorage (can be migrated to Supabase later)
    const savedSettings = localStorage.getItem("investry_settings")
    if (savedSettings) {
      setSettings({ ...settings, ...JSON.parse(savedSettings) })
    }

    setLoading(false)
  }

  const handleSettingChange = (key: string, value: boolean | string) => {
    const newSettings = { ...settings, [key]: value }
    setSettings(newSettings)
    localStorage.setItem("investry_settings", JSON.stringify(newSettings))

    addNotification({
      type: "success",
      title: "Settings updated",
      message: `${key.replace(/([A-Z])/g, " $1").toLowerCase()} has been updated`,
    })
  }

  const handleSaveEmail = () => {
    if (editedEmail.trim() && editedEmail !== user.email) {
      const updatedUser = { ...user, email: editedEmail.trim() }
      setUser(updatedUser)
      localStorage.setItem("investry_user", JSON.stringify(updatedUser))
      setIsEditingEmail(false)

      addNotification({
        type: "success",
        title: "Email updated",
        message: "Your email address has been updated successfully.",
      })
    } else {
      setIsEditingEmail(false)
      setEditedEmail(user.email || "")
    }
  }

  const handleSignOut = async () => {
    try {
      await signOut()

      addNotification({
        type: "info",
        title: "Signed out",
        message: "You've been successfully signed out.",
      })

      router.push("/")
    } catch (error) {
      addNotification({
        type: "error",
        title: "Sign out failed",
        message: "An error occurred while signing out.",
      })
    }
  }

  const handleSaveName = async () => {
    try {
      const result = await updateUserProfile({
        first_name: editedFirstName.trim(),
        last_name: editedLastName.trim()
      })

      if (result.success) {
        setIsEditingName(false)
        addNotification({
          type: "success",
          title: "Name updated",
          message: "Your name has been updated successfully.",
        })
      } else {
        addNotification({
          type: "error",
          title: "Update failed",
          message: result.error || "Failed to update your name.",
        })
      }
    } catch (error) {
      addNotification({
        type: "error",
        title: "Update failed",
        message: "An error occurred while updating your name.",
      })
    }
  }

  const handleDeleteAccount = async () => {
    if (deleteConfirmText !== "DELETE") {
      addNotification({
        type: "error",
        title: "Confirmation required",
        message: "Please type 'DELETE' to confirm account deletion.",
      })
      return
    }

    try {
      const result = await deleteUserAccount(user.id)

      if (result.success) {
        // Clear all user data from localStorage
        clearUserLocalStorage(user.id)

        addNotification({
          type: "success",
          title: "Account deleted",
          message: "Your account has been permanently deleted.",
        })

        // Sign out and redirect
        await signOut()
        router.push("/auth/signin")
      } else {
        addNotification({
          type: "error",
          title: "Deletion failed",
          message: result.error || "Failed to delete your account.",
        })
      }
    } catch (error) {
      addNotification({
        type: "error",
        title: "Deletion failed",
        message: "An error occurred while deleting your account.",
      })
    }
  }

  if (authLoading || loading || !user) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-white">Loading...</div>
      </div>
    )
  }

  const settingSections = [
    {
      title: "Account",
      icon: User,
      items: [
        {
          label: "Full Name",
          description: "Your first and last name",
          type: "name",
          value: getUserFullName(user),
        },
        {
          label: "Email Address",
          description: "Your account email address",
          type: "email",
          value: user.email,
        },
        {
          label: "Password",
          description: "Change your account password",
          type: "action",
          action: () => console.log("Change password"),
        },
        {
          label: "Two-Factor Authentication",
          description: "Add an extra layer of security",
          type: "toggle",
          key: "twoFactorAuth",
        },
      ],
    },
    {
      title: "Notifications",
      icon: Bell,
      items: [
        {
          label: "Push Notifications",
          description: "Receive notifications in your browser",
          type: "toggle",
          key: "notifications",
        },
        {
          label: "Email Updates",
          description: "Get updates via email",
          type: "toggle",
          key: "emailUpdates",
        },
      ],
    },
    {
      title: "Preferences",
      icon: Palette,
      items: [
        {
          label: "Theme",
          description: "Choose your preferred theme",
          type: "theme",
        },
        {
          label: "Currency",
          description: "Display currency preference",
          type: "select",
          key: "currency",
          options: ["USD", "EUR", "GBP", "CAD"],
        },
        {
          label: "Language",
          description: "Interface language",
          type: "select",
          key: "language",
          options: ["English", "Spanish", "French", "German"],
        },
      ],
    },
    {
      title: "Subscription",
      icon: Crown,
      items: [
        {
          label: "Manage Subscription",
          description: "View and manage your investry Premium subscription",
          type: "action",
          action: () => router.push("/subscription/manage"),
        },
        {
          label: "Billing History",
          description: "View your payment history and invoices",
          type: "action",
          action: () => router.push("/payments/manage"),
        },
      ],
    },
    {
      title: "Investment",
      icon: CreditCard,
      items: [
        {
          label: "Auto-Invest",
          description: "Automatically invest spare change",
          type: "toggle",
          key: "autoInvest",
        },
        {
          label: "Data Sharing",
          description: "Share anonymized data for research",
          type: "toggle",
          key: "dataSharing",
        },
      ],
    },
  ]

  return (
    <div className="min-h-screen bg-slate-900 text-white">
      <TopNav />

      <div className="container mx-auto px-4 py-8 space-y-8">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold">Settings</h1>
          <p className="text-slate-400 mt-1">Manage your account preferences and security settings</p>
        </div>

        {/* Profile Summary */}
        <Card className="bg-slate-800/50 border-slate-700">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="w-16 h-16 bg-emerald-500/20 rounded-full flex items-center justify-center">
                <User className="h-8 w-8 text-emerald-400" />
              </div>
              <div className="flex-1">
                <h2 className="text-xl font-semibold text-white">{user.name}</h2>
                <p className="text-slate-400">{user.email}</p>
                {user.school && (
                  <Badge variant="outline" className="mt-2 border-emerald-400/30 text-emerald-400">
                    {user.school}
                  </Badge>
                )}
              </div>
              <Button variant="outline" onClick={() => router.push("/profile")} className="border-slate-600">
                View Profile
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Upgrade to Premium Section */}
        {!subscription.hasActiveSubscription && (
          <Card className="bg-gradient-to-r from-yellow-500/10 to-emerald-500/10 border-yellow-500/30">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Crown className="h-5 w-5 text-yellow-400" />
                Upgrade to investry Premium
              </CardTitle>
              <CardDescription>
                Unlock advanced features and professional investment tools
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <h4 className="font-medium text-white">Analytics</h4>
                  <ul className="text-sm text-slate-300 space-y-1">
                    <li>• Advanced charts</li>
                    <li>• Real-time data</li>
                    <li>• Custom indicators</li>
                  </ul>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium text-white">Portfolio Tools</h4>
                  <ul className="text-sm text-slate-300 space-y-1">
                    <li>• Auto-rebalancing</li>
                    <li>• Risk analysis</li>
                    <li>• Performance tracking</li>
                  </ul>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium text-white">Research</h4>
                  <ul className="text-sm text-slate-300 space-y-1">
                    <li>• Professional reports</li>
                    <li>• Market insights</li>
                    <li>• Screening tools</li>
                  </ul>
                </div>
              </div>
              <div className="flex items-center justify-between pt-4 border-t border-slate-700">
                <div>
                  <p className="text-2xl font-bold text-white">$29.99<span className="text-sm text-slate-400">/month</span></p>
                  <p className="text-sm text-slate-400">or $304.99/year • 14-day free trial • Cancel anytime</p>
                </div>
                <Button
                  onClick={() => router.push("/pricing")}
                  className="bg-gradient-to-r from-yellow-500 to-emerald-500 hover:from-yellow-600 hover:to-emerald-600 text-white"
                >
                  <Zap className="h-4 w-4 mr-2" />
                  Start Free Trial
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Settings Sections */}
        <div className="space-y-6">
          {settingSections.map((section, index) => (
            <Card key={index} className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <section.icon className="h-5 w-5 text-emerald-400" />
                  {section.title}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {section.items.map((item, itemIndex) => (
                  <div key={itemIndex}>
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <Label className="text-white font-medium">{item.label}</Label>
                          {item.type === "email" && (
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => setIsEditingEmail(true)}
                              className="text-slate-400 hover:text-white p-1"
                            >
                              <Edit2 className="h-3 w-3" />
                            </Button>
                          )}
                          {item.type === "name" && (
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => setIsEditingName(true)}
                              className="text-slate-400 hover:text-white p-1"
                            >
                              <Edit2 className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                        <p className="text-sm text-slate-400 mt-1">{item.description}</p>
                      </div>

                      <div className="ml-4">
                        {item.type === "toggle" && item.key && (
                          <Switch
                            checked={settings[item.key as keyof typeof settings] as boolean}
                            onCheckedChange={(value) => handleSettingChange(item.key!, value)}
                          />
                        )}

                        {item.type === "email" && (
                          <div className="flex items-center gap-2">
                            {isEditingEmail ? (
                              <>
                                <Input
                                  type="email"
                                  value={editedEmail}
                                  onChange={(e) => setEditedEmail(e.target.value)}
                                  className="bg-slate-700 border-slate-600 text-white w-64"
                                  placeholder="Enter email address"
                                />
                                <Button
                                  size="sm"
                                  onClick={handleSaveEmail}
                                  className="bg-emerald-500 hover:bg-emerald-600"
                                >
                                  <Check className="h-4 w-4" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => {
                                    setIsEditingEmail(false)
                                    setEditedEmail(user.email || "")
                                  }}
                                  className="border-slate-600"
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                              </>
                            ) : (
                              <span className="text-slate-300">{item.value}</span>
                            )}
                          </div>
                        )}

                        {item.type === "action" && (
                          <Button variant="outline" onClick={item.action} className="border-slate-600 bg-transparent">
                            Change
                          </Button>
                        )}

                        {item.type === "theme" && <ThemeToggle />}

                        {item.type === "select" && item.key && item.options && (
                          <select
                            value={settings[item.key as keyof typeof settings] as string}
                            onChange={(e) => handleSettingChange(item.key!, e.target.value)}
                            className="bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-white"
                          >
                            {item.options.map((option) => (
                              <option key={option} value={option}>
                                {option}
                              </option>
                            ))}
                          </select>
                        )}
                      </div>
                    </div>
                    {itemIndex < section.items.length - 1 && <Separator className="bg-slate-700 mt-4" />}
                  </div>
                ))}
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Security Section */}
        <Card className="bg-slate-800/50 border-slate-700">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-emerald-400" />
              Security & Privacy
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid md:grid-cols-2 gap-6">
              <div className="flex items-center gap-3">
                <Lock className="h-5 w-5 text-slate-400" />
                <div>
                  <p className="font-medium text-white">Password</p>
                  <p className="text-sm text-slate-400">Last changed 30 days ago</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Smartphone className="h-5 w-5 text-slate-400" />
                <div>
                  <p className="font-medium text-white">Two-Factor Auth</p>
                  <p className="text-sm text-slate-400">{settings.twoFactorAuth ? "Enabled" : "Not configured"}</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Mail className="h-5 w-5 text-slate-400" />
                <div>
                  <p className="font-medium text-white">Email Verified</p>
                  <p className="text-sm text-emerald-400">Verified</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Globe className="h-5 w-5 text-slate-400" />
                <div>
                  <p className="font-medium text-white">Data Export</p>
                  <p className="text-sm text-slate-400">Download your data</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>



        {/* Danger Zone */}
        <Card className="bg-red-500/10 border-red-400/30">
          <CardHeader>
            <CardTitle className="text-red-400">Danger Zone</CardTitle>
            <CardDescription>These actions cannot be undone</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-white">Sign Out</p>
                <p className="text-sm text-slate-400">Sign out of your account on this device</p>
              </div>
              <Button
                variant="outline"
                onClick={handleSignOut}
                className="border-red-600 text-red-400 hover:bg-red-600/10 bg-transparent"
              >
                <LogOut className="h-4 w-4 mr-2" />
                Sign Out
              </Button>
            </div>
            <Separator className="bg-red-400/20" />
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-white">Delete Account</p>
                <p className="text-sm text-slate-400">Permanently delete your account and all data</p>
              </div>
              <Button variant="outline" className="border-red-600 text-red-400 hover:bg-red-600/10 bg-transparent">
                Delete Account
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
