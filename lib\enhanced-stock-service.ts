/**
 * Enhanced Stock Data Service with Tiered API System
 * Automatically selects the best available API based on current tier
 */

import { getApiConfig, getApiFallbackChain, getCurrentApiTier } from './api-tier-config'
import { StockQuote, StockFinancials, MarketMovers, StockNews } from './stock-data-service'
import { getPolygonService } from './polygon-service'
import { getIntrinioService } from './intrinio-service'

export class EnhancedStockService {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>()
  private apiUsage = {
    requests: 0,
    lastReset: Date.now()
  }

  constructor() {
    // Reset usage counter daily
    setInterval(() => {
      this.apiUsage.requests = 0
      this.apiUsage.lastReset = Date.now()
    }, 24 * 60 * 60 * 1000)
  }

  /**
   * Get stock quote using tiered API system
   */
  async getStockQuote(symbol: string): Promise<StockQuote | null> {
    const cacheKey = `quote:${symbol}`
    
    // Check cache first
    const cached = this.getFromCache<StockQuote>(cacheKey)
    if (cached) return cached

    const config = getApiConfig()
    const fallbackChain = getApiFallbackChain()

    // Try APIs in fallback order
    for (const apiName of fallbackChain) {
      try {
        let quote: StockQuote | null = null

        switch (apiName) {
          case 'polygon':
            if (config.apis.polygon?.apiKey) {
              const polygonService = getPolygonService()
              if (polygonService) {
                quote = await polygonService.getRealTimeQuote(symbol)
              }
            }
            break
          case 'intrinio':
            if (config.apis.intrinio?.apiKey) {
              // Intrinio doesn't provide quotes directly, skip for quote requests
            }
            break
          case 'twelveData':
            if (config.apis.twelveData?.apiKey) {
              quote = await this.getTwelveDataQuote(symbol)
            }
            break
          case 'finnhub':
            if (config.apis.finnhub?.apiKey) {
              quote = await this.getFinnhubQuote(symbol)
            }
            break
          case 'alphavantage':
            if (config.apis.alphavantage?.apiKey) {
              quote = await this.getAlphaVantageQuote(symbol)
            }
            break
        }

        if (quote) {
          this.setCache(cacheKey, quote, 60 * 1000) // 1 minute cache
          return quote
        }
      } catch (error) {
        console.warn(`${apiName} API failed for ${symbol}:`, error)
        continue // Try next API in chain
      }
    }

    return null
  }

  /**
   * Get market movers using tiered system
   */
  async getMarketMovers(): Promise<MarketMovers | null> {
    const cacheKey = 'market-movers'
    
    const cached = this.getFromCache<MarketMovers>(cacheKey)
    if (cached) return cached

    const config = getApiConfig()
    const fallbackChain = getApiFallbackChain()

    for (const apiName of fallbackChain) {
      try {
        let movers: MarketMovers | null = null

        switch (apiName) {
          case 'polygon':
            if (config.apis.polygon?.apiKey) {
              const polygonService = getPolygonService()
              if (polygonService) {
                const polygonMovers = await polygonService.getMarketMovers()
                if (polygonMovers) {
                  movers = {
                    gainers: polygonMovers.gainers,
                    losers: polygonMovers.losers,
                    mostActive: polygonMovers.mostActive,
                    lastUpdated: new Date().toISOString(),
                    source: 'polygon'
                  }
                }
              }
            }
            break
          case 'twelveData':
            if (config.apis.twelveData?.apiKey) {
              movers = await this.getTwelveDataMarketMovers()
            }
            break
          case 'finnhub':
            if (config.apis.finnhub?.apiKey) {
              movers = await this.getFinnhubMarketMovers()
            }
            break
        }

        if (movers) {
          this.setCache(cacheKey, movers, 5 * 60 * 1000) // 5 minute cache
          return movers
        }
      } catch (error) {
        console.warn(`${apiName} market movers failed:`, error)
        continue
      }
    }

    return null
  }

  /**
   * Polygon.io integration (Enterprise tier)
   */
  private async getPolygonQuote(symbol: string): Promise<StockQuote | null> {
    const config = getApiConfig()
    if (!config.apis.polygon?.apiKey) return null

    this.apiUsage.requests++

    const response = await fetch(
      `${config.apis.polygon.baseUrl}/v2/aggs/ticker/${symbol}/prev?adjusted=true&apikey=${config.apis.polygon.apiKey}`
    )

    if (!response.ok) return null

    const data = await response.json()
    if (!data.results?.[0]) return null

    const result = data.results[0]
    
    return {
      symbol,
      name: symbol, // Would need separate call for company name
      price: result.c,
      change: result.c - result.o,
      changePercent: ((result.c - result.o) / result.o) * 100,
      volume: result.v,
      high: result.h,
      low: result.l,
      open: result.o,
      previousClose: result.c,
      exchange: 'UNKNOWN',
      lastUpdated: new Date().toISOString(),
      source: 'polygon'
    }
  }

  /**
   * Twelve Data integration (Mid-tier)
   */
  private async getTwelveDataQuote(symbol: string): Promise<StockQuote | null> {
    const config = getApiConfig()
    if (!config.apis.twelveData?.apiKey) return null

    this.apiUsage.requests++

    const response = await fetch(
      `${config.apis.twelveData.baseUrl}/quote?symbol=${symbol}&apikey=${config.apis.twelveData.apiKey}`
    )

    if (!response.ok) return null

    const data = await response.json()
    if (data.status === 'error') return null

    return {
      symbol,
      name: data.name || symbol,
      price: parseFloat(data.close),
      change: parseFloat(data.change),
      changePercent: parseFloat(data.percent_change),
      volume: parseInt(data.volume),
      high: parseFloat(data.high),
      low: parseFloat(data.low),
      open: parseFloat(data.open),
      previousClose: parseFloat(data.previous_close),
      exchange: data.exchange || 'UNKNOWN',
      lastUpdated: new Date().toISOString(),
      source: 'twelvedata'
    }
  }

  /**
   * Finnhub integration (All tiers)
   */
  private async getFinnhubQuote(symbol: string): Promise<StockQuote | null> {
    const config = getApiConfig()
    if (!config.apis.finnhub?.apiKey) return null

    this.apiUsage.requests++

    const response = await fetch(
      `${config.apis.finnhub.baseUrl}/quote?symbol=${symbol}&token=${config.apis.finnhub.apiKey}`
    )

    if (!response.ok) return null

    const data = await response.json()
    if (data.c === 0) return null

    return {
      symbol,
      name: symbol, // Would need profile call for name
      price: data.c,
      change: data.d,
      changePercent: data.dp,
      volume: 0,
      high: data.h,
      low: data.l,
      open: data.o,
      previousClose: data.pc,
      exchange: 'UNKNOWN',
      lastUpdated: new Date().toISOString(),
      source: 'finnhub'
    }
  }

  /**
   * Alpha Vantage integration (Startup tier)
   */
  private async getAlphaVantageQuote(symbol: string): Promise<StockQuote | null> {
    const config = getApiConfig()
    if (!config.apis.alphavantage?.apiKey) return null

    this.apiUsage.requests++

    const response = await fetch(
      `${config.apis.alphavantage.baseUrl}?function=GLOBAL_QUOTE&symbol=${symbol}&apikey=${config.apis.alphavantage.apiKey}`
    )

    if (!response.ok) return null

    const data = await response.json()
    const quote = data['Global Quote']
    if (!quote) return null

    return {
      symbol,
      name: symbol,
      price: parseFloat(quote['05. price']),
      change: parseFloat(quote['09. change']),
      changePercent: parseFloat(quote['10. change percent'].replace('%', '')),
      volume: parseInt(quote['06. volume']),
      high: parseFloat(quote['03. high']),
      low: parseFloat(quote['04. low']),
      open: parseFloat(quote['02. open']),
      previousClose: parseFloat(quote['08. previous close']),
      exchange: 'UNKNOWN',
      lastUpdated: new Date().toISOString(),
      source: 'alphavantage'
    }
  }

  /**
   * Market movers implementations
   */
  private async getPolygonMarketMovers(): Promise<MarketMovers | null> {
    // Implementation for Polygon market movers
    // This would use Polygon's gainers/losers endpoints
    return null // Placeholder
  }

  private async getTwelveDataMarketMovers(): Promise<MarketMovers | null> {
    // Implementation for Twelve Data market movers
    return null // Placeholder
  }

  private async getFinnhubMarketMovers(): Promise<MarketMovers | null> {
    // Use existing Finnhub implementation from stock-data-service.ts
    return null // Placeholder - would import from existing service
  }

  /**
   * Cache management
   */
  private getFromCache<T>(key: string): T | null {
    const cached = this.cache.get(key)
    if (!cached) return null

    if (Date.now() > cached.timestamp + cached.ttl) {
      this.cache.delete(key)
      return null
    }

    return cached.data as T
  }

  private setCache(key: string, data: any, ttl: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }

  /**
   * Get enhanced financial data using enterprise services
   */
  async getEnhancedFinancials(symbol: string): Promise<StockFinancials | null> {
    const config = getApiConfig()

    // Try enterprise services first
    if (config.tier === 'enterprise') {
      const intrinioService = getIntrinioService()
      if (intrinioService) {
        const financials = await intrinioService.getEnhancedFinancials(symbol)
        if (financials) return financials
      }

      const polygonService = getPolygonService()
      if (polygonService) {
        const financials = await polygonService.getFinancials(symbol)
        if (financials) return financials
      }
    }

    // Fallback to standard financial data
    return null // Would integrate with existing stock-data-service
  }

  /**
   * Get ESG data (enterprise tier only)
   */
  async getESGData(symbol: string) {
    const config = getApiConfig()

    if (config.tier === 'enterprise') {
      const intrinioService = getIntrinioService()
      if (intrinioService) {
        return await intrinioService.getESGData(symbol)
      }
    }

    return null
  }

  /**
   * Get peer comparison (enterprise tier only)
   */
  async getPeerComparison(symbol: string) {
    const config = getApiConfig()

    if (config.tier === 'enterprise') {
      const intrinioService = getIntrinioService()
      if (intrinioService) {
        return await intrinioService.getPeerComparison(symbol)
      }
    }

    return null
  }

  /**
   * Get options data (enterprise tier only)
   */
  async getOptionsData(symbol: string, expiration?: string) {
    const config = getApiConfig()

    if (config.tier === 'enterprise') {
      const polygonService = getPolygonService()
      if (polygonService) {
        return await polygonService.getOptionsChain(symbol, expiration)
      }
    }

    return []
  }

  /**
   * Get API usage statistics
   */
  getUsageStats() {
    const config = getApiConfig()
    return {
      tier: config.tier,
      requests: this.apiUsage.requests,
      monthlyCost: config.monthlyCost,
      lastReset: this.apiUsage.lastReset
    }
  }
}

// Singleton instance
let enhancedStockServiceInstance: EnhancedStockService | null = null

export function getEnhancedStockService(): EnhancedStockService {
  if (!enhancedStockServiceInstance) {
    enhancedStockServiceInstance = new EnhancedStockService()
  }
  return enhancedStockServiceInstance
}
