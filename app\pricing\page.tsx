"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { TopNav } from "@/components/top-nav"
import { Check, Star, Zap, Crown } from "lucide-react"
import { useNotifications } from "@/components/notification-system"
import { useAuth } from "@/components/auth-provider"

interface PricingPlan {
  id: string
  name: string
  description: string
  price: number
  interval: string
  stripePriceId: string
  features: string[]
  popular?: boolean
  icon: React.ReactNode
  buttonText: string
  buttonVariant: "default" | "outline" | "secondary"
}

const pricingPlans: PricingPlan[] = [
  {
    id: "free",
    name: "Investry Free",
    description: "Perfect for getting started with investing",
    price: 0,
    interval: "forever",
    stripePriceId: "",
    features: [
      "Basic stock search and quotes",
      "Portfolio tracking (up to 10 stocks)",
      "Basic market news",
      "Email notifications",
      "Mobile app access",
      "Community access"
    ],
    icon: <Star className="h-6 w-6" />,
    buttonText: "Get Started Free",
    buttonVariant: "outline"
  },
  {
    id: "premium-monthly",
    name: "Investry Premium",
    description: "Unlock the full power of professional investing",
    price: 29.99,
    interval: "month",
    stripePriceId: "price_1Rkf6zPBPh8XhQPYSbVArBUJ", // Replace with your actual Stripe price ID
    features: [
      "Everything in Free",
      "Unlimited portfolio tracking",
      "Advanced analytics & charts",
      "Real-time market data",
      "Price alerts & notifications",
      "Research reports & insights",
      "Advanced screening tools",
      "Options trading insights",
      "Custom alerts & automations",
      "Priority customer support",
      "Export capabilities",
      "Advanced portfolio analysis"
    ],
    popular: false,
    icon: <Crown className="h-6 w-6" />,
    buttonText: "Start Monthly Plan",
    buttonVariant: "default"
  },
  {
    id: "premium-yearly",
    name: "Investry Premium",
    description: "Best value - Save 15% with annual billing",
    price: 304.99,
    interval: "year",
    stripePriceId: "price_1RkfSqPBPh8XhQPY5aoPNdH2", // Replace with your actual Stripe price ID
    features: [
      "Everything in Free",
      "Unlimited portfolio tracking",
      "Advanced analytics & charts",
      "Real-time market data",
      "Price alerts & notifications",
      "Research reports & insights",
      "Advanced screening tools",
      "Options trading insights",
      "Custom alerts & automations",
      "Priority customer support",
      "Export capabilities",
      "Advanced portfolio analysis",
      "15% savings vs monthly",
      "Priority feature access"
    ],
    popular: true,
    icon: <Crown className="h-6 w-6" />,
    buttonText: "Start Yearly Plan",
    buttonVariant: "default"
  }
]

export default function PricingPage() {
  const router = useRouter()
  const { addNotification } = useNotifications()
  const { user, loading: authLoading } = useAuth()
  const [loading, setLoading] = useState(false)
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null)

  const handlePlanSelection = async (plan: PricingPlan) => {
    if (!user) {
      router.push("/auth/signin")
      return
    }

    if (plan.id === "free") {
      addNotification({
        type: "success",
        title: "Welcome to Investry!",
        message: "You're all set with the free plan. Start exploring!",
      })
      router.push("/dashboard")
      return
    }

    try {
      setLoading(true)
      setSelectedPlan(plan.id)

      // Create subscription checkout session
      const response = await fetch('/api/create-subscription-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          priceId: plan.stripePriceId,
          userId: user.id,
          planName: plan.name
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to create checkout session')
      }

      const { url } = await response.json()

      // Redirect to Stripe Checkout
      window.location.href = url

    } catch (error) {
      console.error('Subscription error:', error)
      addNotification({
        type: "error",
        title: "Subscription Error",
        message: "There was an error setting up your subscription. Please try again.",
      })
    } finally {
      setLoading(false)
      setSelectedPlan(null)
    }
  }

  return (
    <div className="min-h-screen bg-slate-900">
      <TopNav />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-white mb-4">
            Choose Your Investment Plan
          </h1>
          <p className="text-xl text-slate-400 max-w-2xl mx-auto">
            Unlock powerful investment tools and insights to grow your portfolio
          </p>
        </div>

        {/* Pricing Cards */}
        <div className="grid md:grid-cols-3 gap-6 max-w-6xl mx-auto">
          {pricingPlans.map((plan) => (
            <Card 
              key={plan.id} 
              className={`relative bg-slate-800 border-slate-700 ${
                plan.popular ? 'ring-2 ring-emerald-500' : ''
              }`}
            >
              {plan.popular && (
                <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-emerald-500 text-white">
                  Most Popular
                </Badge>
              )}
              
              <CardHeader className="text-center">
                <div className="flex justify-center mb-4">
                  <div className={`p-3 rounded-full ${
                    plan.popular ? 'bg-emerald-500/20 text-emerald-400' : 'bg-slate-700 text-slate-400'
                  }`}>
                    {plan.icon}
                  </div>
                </div>
                
                <CardTitle className="text-2xl text-white">{plan.name}</CardTitle>
                <CardDescription className="text-slate-400">
                  {plan.description}
                </CardDescription>
                
                <div className="mt-4">
                  <span className="text-4xl font-bold text-white">
                    ${plan.price}
                  </span>
                  <span className="text-slate-400">
                    /{plan.interval}
                  </span>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-6">
                <ul className="space-y-3">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-3">
                      <Check className="h-5 w-5 text-emerald-400 mt-0.5 flex-shrink-0" />
                      <span className="text-slate-300">{feature}</span>
                    </li>
                  ))}
                </ul>
                
                <Button
                  className={`w-full ${
                    plan.popular 
                      ? 'bg-emerald-600 hover:bg-emerald-700' 
                      : plan.buttonVariant === 'outline'
                      ? 'border-slate-600 text-slate-300 hover:bg-slate-700'
                      : 'bg-slate-700 hover:bg-slate-600'
                  }`}
                  variant={plan.buttonVariant}
                  onClick={() => handlePlanSelection(plan)}
                >
                  {plan.buttonText}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* FAQ Section */}
        <div className="mt-16 max-w-4xl mx-auto">
          <h2 className="text-2xl font-bold text-white text-center mb-8">
            Frequently Asked Questions
          </h2>
          
          <div className="grid md:grid-cols-2 gap-8">
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-white mb-2">
                  Can I change plans anytime?
                </h3>
                <p className="text-slate-400">
                  Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-white mb-2">
                  Is there a free trial?
                </h3>
                <p className="text-slate-400">
                  Yes, all paid plans come with a 14-day free trial. No credit card required.
                </p>
              </div>
            </div>
            
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-white mb-2">
                  What payment methods do you accept?
                </h3>
                <p className="text-slate-400">
                  We accept all major credit cards, debit cards, and bank transfers through Stripe.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-white mb-2">
                  Can I cancel anytime?
                </h3>
                <p className="text-slate-400">
                  Absolutely. You can cancel your subscription at any time with no cancellation fees.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
