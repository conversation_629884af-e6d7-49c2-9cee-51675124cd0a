/**
 * Chart Controls Components
 * Advanced controls for chart customization and interaction
 */

'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  Settings,
  Download,
  Share2,
  Maximize2,
  RefreshCw,
  TrendingUp,
  BarChart3,
  LineChart,
  Activity
} from 'lucide-react'
import { TimeRange, ValueType } from './chart-base'
import { cn } from '@/lib/utils'

export interface AdvancedChartControlsProps {
  timeRange: TimeRange
  valueType: ValueType
  onTimeRangeChange: (range: TimeRange) => void
  onValueTypeChange: (type: ValueType) => void
  onRefresh?: () => void
  onExport?: (format: 'png' | 'svg' | 'csv') => void
  onShare?: () => void
  onFullscreen?: () => void
  loading?: boolean
  autoRefresh?: boolean
  onAutoRefreshChange?: (enabled: boolean) => void
  chartType?: 'line' | 'area' | 'bar'
  onChartTypeChange?: (type: 'line' | 'area' | 'bar') => void
  showGrid?: boolean
  onShowGridChange?: (show: boolean) => void
  showTooltips?: boolean
  onShowTooltipsChange?: (show: boolean) => void
  className?: string
}

export function AdvancedChartControls({
  timeRange,
  valueType,
  onTimeRangeChange,
  onValueTypeChange,
  onRefresh,
  onExport,
  onShare,
  onFullscreen,
  loading,
  autoRefresh,
  onAutoRefreshChange,
  chartType = 'line',
  onChartTypeChange,
  showGrid = true,
  onShowGridChange,
  showTooltips = true,
  onShowTooltipsChange,
  className
}: AdvancedChartControlsProps) {
  const [isSettingsOpen, setIsSettingsOpen] = useState(false)

  const timeRanges: { value: TimeRange; label: string; description: string }[] = [
    { value: '1D', label: '1D', description: 'Last 24 hours' },
    { value: '1W', label: '1W', description: 'Last 7 days' },
    { value: '1M', label: '1M', description: 'Last 30 days' },
    { value: '3M', label: '3M', description: 'Last 3 months' },
    { value: '1Y', label: '1Y', description: 'Last 12 months' },
    { value: 'All', label: 'All', description: 'All available data' }
  ]

  const chartTypes = [
    { value: 'line' as const, label: 'Line', icon: LineChart },
    { value: 'area' as const, label: 'Area', icon: Activity },
    { value: 'bar' as const, label: 'Bar', icon: BarChart3 }
  ]

  return (
    <div className={cn('flex flex-col sm:flex-row gap-3 items-start sm:items-center justify-between', className)}>
      {/* Primary Controls */}
      <div className="flex flex-wrap gap-2">
        {/* Time Range Selector */}
        <div className="flex gap-1">
          {timeRanges.map((range) => (
            <Button
              key={range.value}
              variant={timeRange === range.value ? 'default' : 'outline'}
              size="sm"
              onClick={() => onTimeRangeChange(range.value)}
              disabled={loading}
              className="h-8 px-3 text-xs"
              title={range.description}
            >
              {range.label}
            </Button>
          ))}
        </div>

        {/* Value Type Toggle */}
        <div className="flex gap-1">
          <Button
            variant={valueType === 'absolute' ? 'default' : 'outline'}
            size="sm"
            onClick={() => onValueTypeChange('absolute')}
            disabled={loading}
            className="h-8 px-3 text-xs"
            title="Show absolute values"
          >
            $
          </Button>
          <Button
            variant={valueType === 'percentage' ? 'default' : 'outline'}
            size="sm"
            onClick={() => onValueTypeChange('percentage')}
            disabled={loading}
            className="h-8 px-3 text-xs"
            title="Show percentage change"
          >
            %
          </Button>
        </div>
      </div>

      {/* Secondary Controls */}
      <div className="flex gap-2">
        {/* Chart Type Selector */}
        {onChartTypeChange && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-8 px-3">
                {React.createElement(
                  chartTypes.find(t => t.value === chartType)?.icon || LineChart,
                  { className: 'h-4 w-4' }
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Chart Type</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {chartTypes.map((type) => (
                <DropdownMenuItem
                  key={type.value}
                  onClick={() => onChartTypeChange(type.value)}
                  className="flex items-center gap-2"
                >
                  <type.icon className="h-4 w-4" />
                  {type.label}
                  {chartType === type.value && (
                    <Badge variant="secondary" className="ml-auto">
                      Active
                    </Badge>
                  )}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        {/* Refresh Button */}
        {onRefresh && (
          <Button
            variant="outline"
            size="sm"
            onClick={onRefresh}
            disabled={loading}
            className="h-8 px-3"
            title="Refresh data"
          >
            <RefreshCw className={cn('h-4 w-4', loading && 'animate-spin')} />
          </Button>
        )}

        {/* Export Menu */}
        {onExport && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-8 px-3">
                <Download className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Export Chart</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => onExport('png')}>
                Export as PNG
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onExport('svg')}>
                Export as SVG
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onExport('csv')}>
                Export Data as CSV
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        {/* Settings Popover */}
        <Popover open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
          <PopoverTrigger asChild>
            <Button variant="outline" size="sm" className="h-8 px-3">
              <Settings className="h-4 w-4" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80" align="end">
            <div className="space-y-4">
              <div>
                <h4 className="font-medium">Chart Settings</h4>
                <p className="text-sm text-muted-foreground">
                  Customize chart appearance and behavior
                </p>
              </div>

              <Separator />

              {/* Auto Refresh */}
              {onAutoRefreshChange && (
                <div className="flex items-center justify-between">
                  <Label htmlFor="auto-refresh" className="text-sm">
                    Auto Refresh
                  </Label>
                  <Switch
                    id="auto-refresh"
                    checked={autoRefresh}
                    onCheckedChange={onAutoRefreshChange}
                  />
                </div>
              )}

              {/* Show Grid */}
              {onShowGridChange && (
                <div className="flex items-center justify-between">
                  <Label htmlFor="show-grid" className="text-sm">
                    Show Grid
                  </Label>
                  <Switch
                    id="show-grid"
                    checked={showGrid}
                    onCheckedChange={onShowGridChange}
                  />
                </div>
              )}

              {/* Show Tooltips */}
              {onShowTooltipsChange && (
                <div className="flex items-center justify-between">
                  <Label htmlFor="show-tooltips" className="text-sm">
                    Show Tooltips
                  </Label>
                  <Switch
                    id="show-tooltips"
                    checked={showTooltips}
                    onCheckedChange={onShowTooltipsChange}
                  />
                </div>
              )}

              <Separator />

              {/* Action Buttons */}
              <div className="flex gap-2">
                {onShare && (
                  <Button variant="outline" size="sm" onClick={onShare} className="flex-1">
                    <Share2 className="h-4 w-4 mr-2" />
                    Share
                  </Button>
                )}
                {onFullscreen && (
                  <Button variant="outline" size="sm" onClick={onFullscreen} className="flex-1">
                    <Maximize2 className="h-4 w-4 mr-2" />
                    Fullscreen
                  </Button>
                )}
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>
    </div>
  )
}

/**
 * Simple Chart Controls (for compact spaces)
 */
export interface SimpleChartControlsProps {
  timeRange: TimeRange
  onTimeRangeChange: (range: TimeRange) => void
  loading?: boolean
  className?: string
  size?: 'sm' | 'md'
}

export function SimpleChartControls({
  timeRange,
  onTimeRangeChange,
  loading,
  className,
  size = 'sm'
}: SimpleChartControlsProps) {
  const ranges: TimeRange[] = ['1D', '1W', '1M', '3M', '1Y', 'All']

  return (
    <div className={cn('flex gap-1', className)}>
      {ranges.map((range) => (
        <Button
          key={range}
          variant={timeRange === range ? 'default' : 'outline'}
          size={size}
          onClick={() => onTimeRangeChange(range)}
          disabled={loading}
          className={cn(
            'text-xs',
            size === 'sm' ? 'h-7 px-2' : 'h-8 px-3'
          )}
        >
          {range}
        </Button>
      ))}
    </div>
  )
}

/**
 * Chart Status Indicator
 */
export interface ChartStatusProps {
  loading?: boolean
  error?: string
  lastUpdated?: Date
  dataPoints?: number
  source?: string
  className?: string
}

export function ChartStatus({
  loading,
  error,
  lastUpdated,
  dataPoints,
  source,
  className
}: ChartStatusProps) {
  if (loading) {
    return (
      <div className={cn('flex items-center gap-2 text-xs text-muted-foreground', className)}>
        <RefreshCw className="h-3 w-3 animate-spin" />
        Loading chart data...
      </div>
    )
  }

  if (error) {
    return (
      <div className={cn('flex items-center gap-2 text-xs text-red-600', className)}>
        <span>⚠</span>
        {error}
      </div>
    )
  }

  return (
    <div className={cn('flex items-center gap-4 text-xs text-muted-foreground', className)}>
      {lastUpdated && (
        <span>
          Updated: {lastUpdated.toLocaleTimeString()}
        </span>
      )}
      {dataPoints && (
        <span>
          {dataPoints} data points
        </span>
      )}
      {source && (
        <Badge variant="outline" className="text-xs">
          {source}
        </Badge>
      )}
    </div>
  )
}

/**
 * Chart Comparison Controls
 */
export interface ChartComparisonProps {
  symbols: string[]
  selectedSymbols: string[]
  onSymbolToggle: (symbol: string) => void
  onAddSymbol?: (symbol: string) => void
  maxSymbols?: number
  className?: string
}

export function ChartComparison({
  symbols,
  selectedSymbols,
  onSymbolToggle,
  onAddSymbol,
  maxSymbols = 5,
  className
}: ChartComparisonProps) {
  return (
    <Card className={className}>
      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium">Compare Symbols</h4>
            <Badge variant="secondary">
              {selectedSymbols.length}/{maxSymbols}
            </Badge>
          </div>
          
          <div className="flex flex-wrap gap-2">
            {symbols.map((symbol) => (
              <Button
                key={symbol}
                variant={selectedSymbols.includes(symbol) ? 'default' : 'outline'}
                size="sm"
                onClick={() => onSymbolToggle(symbol)}
                disabled={
                  !selectedSymbols.includes(symbol) && 
                  selectedSymbols.length >= maxSymbols
                }
                className="h-7 px-2 text-xs"
              >
                {symbol}
              </Button>
            ))}
          </div>

          {onAddSymbol && (
            <div className="pt-2 border-t">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  const symbol = prompt('Enter symbol to add:')
                  if (symbol) onAddSymbol(symbol.toUpperCase())
                }}
                className="w-full text-xs"
                disabled={selectedSymbols.length >= maxSymbols}
              >
                + Add Symbol
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
