"use client"

import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Crown, Zap, Lock } from "lucide-react"
import { useSubscription } from "@/hooks/use-subscription"

interface PremiumGateProps {
  feature: string
  description?: string
  children: React.ReactNode
  showUpgrade?: boolean
}

export function PremiumGate({ 
  feature, 
  description, 
  children, 
  showUpgrade = true 
}: PremiumGateProps) {
  const router = useRouter()
  const subscription = useSubscription()

  // If user has active subscription, show the content
  if (subscription.hasActiveSubscription) {
    return <>{children}</>
  }

  // If showUpgrade is false, just show a simple locked message
  if (!showUpgrade) {
    return (
      <div className="flex items-center justify-center p-8 bg-slate-800/50 border border-slate-700 rounded-lg">
        <div className="text-center">
          <Lock className="h-8 w-8 text-slate-400 mx-auto mb-2" />
          <p className="text-slate-400">Premium feature locked</p>
        </div>
      </div>
    )
  }

  // Show upgrade prompt
  return (
    <Card className="bg-gradient-to-r from-yellow-500/10 to-emerald-500/10 border-yellow-500/30">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 bg-yellow-500/20 p-3 rounded-full w-16 h-16 flex items-center justify-center">
          <Crown className="h-8 w-8 text-yellow-400" />
        </div>
        <CardTitle className="flex items-center justify-center gap-2">
          <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
            Premium Feature
          </Badge>
        </CardTitle>
        <CardDescription className="text-lg">
          {feature}
        </CardDescription>
        {description && (
          <p className="text-slate-400 mt-2">{description}</p>
        )}
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <h4 className="font-medium text-white">What you'll get:</h4>
            <ul className="text-sm text-slate-300 space-y-1">
              <li>• Unlimited portfolio tracking</li>
              <li>• Advanced analytics & charts</li>
              <li>• Real-time market data</li>
              <li>• Custom alerts & automations</li>
            </ul>
          </div>
          <div className="space-y-2">
            <h4 className="font-medium text-white">Professional tools:</h4>
            <ul className="text-sm text-slate-300 space-y-1">
              <li>• Portfolio rebalancing</li>
              <li>• Research reports</li>
              <li>• Advanced screening</li>
              <li>• Priority support</li>
            </ul>
          </div>
        </div>
        
        <div className="text-center space-y-4 pt-4 border-t border-slate-700">
          <div>
            <p className="text-2xl font-bold text-white">
              $19.99<span className="text-sm text-slate-400">/month</span>
            </p>
            <p className="text-sm text-slate-400">14-day free trial • Cancel anytime</p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button
              onClick={() => router.push("/pricing")}
              className="bg-gradient-to-r from-yellow-500 to-emerald-500 hover:from-yellow-600 hover:to-emerald-600 text-white"
            >
              <Zap className="h-4 w-4 mr-2" />
              Start Free Trial
            </Button>
            <Button
              variant="outline"
              onClick={() => router.push("/pricing")}
              className="border-slate-600 text-slate-300 hover:bg-slate-700"
            >
              Learn More
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Simplified version for inline use
export function PremiumBadge({ 
  feature, 
  onClick 
}: { 
  feature: string
  onClick?: () => void 
}) {
  const router = useRouter()
  const subscription = useSubscription()

  if (subscription.hasActiveSubscription) {
    return null
  }

  return (
    <Button
      size="sm"
      variant="outline"
      onClick={onClick || (() => router.push("/pricing"))}
      className="border-yellow-500/30 text-yellow-400 hover:bg-yellow-500/10 bg-yellow-500/5"
    >
      <Crown className="h-3 w-3 mr-1" />
      Upgrade for {feature}
    </Button>
  )
}

// Hook for checking premium access
export function usePremiumAccess() {
  const subscription = useSubscription()
  
  return {
    hasAccess: subscription.hasActiveSubscription,
    isLoading: subscription.loading,
    showUpgrade: () => {
      // You can add analytics tracking here
      console.log("Premium feature accessed without subscription")
    }
  }
}
