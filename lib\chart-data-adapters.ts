/**
 * Chart Data Adapters
 * Converts different API responses into consistent chart data formats
 */

import { HistoricalData, HistoricalDataPoint } from './historical-data-service'
import { PortfolioPerformance, PortfolioPerformancePoint } from './portfolio-performance-calculator'

export interface ChartDataPoint {
  date: string
  timestamp: number
  value: number
  label?: string
  color?: string
  volume?: number
}

export interface CandlestickDataPoint {
  date: string
  timestamp: number
  open: number
  high: number
  low: number
  close: number
  volume: number
}

export interface ChartConfig {
  type: 'line' | 'area' | 'candlestick' | 'bar'
  color: string
  gradient?: {
    from: string
    to: string
  }
  showVolume?: boolean
  showGrid?: boolean
  showTooltip?: boolean
  height?: number
}

export class ChartDataAdapter {
  /**
   * Convert historical stock data to line chart format
   */
  static toLineChart(
    historicalData: HistoricalData,
    config: Partial<ChartConfig> = {}
  ): ChartDataPoint[] {
    return historicalData.data.map(point => ({
      date: point.date,
      timestamp: point.timestamp,
      value: point.close,
      volume: point.volume
    }))
  }

  /**
   * Convert historical stock data to area chart format
   */
  static toAreaChart(
    historicalData: HistoricalData,
    config: Partial<ChartConfig> = {}
  ): ChartDataPoint[] {
    return this.toLineChart(historicalData, config)
  }

  /**
   * Convert historical stock data to candlestick chart format
   */
  static toCandlestickChart(
    historicalData: HistoricalData
  ): CandlestickDataPoint[] {
    return historicalData.data.map(point => ({
      date: point.date,
      timestamp: point.timestamp,
      open: point.open,
      high: point.high,
      low: point.low,
      close: point.close,
      volume: point.volume
    }))
  }

  /**
   * Convert portfolio performance to line chart format
   */
  static portfolioToLineChart(
    portfolioPerformance: PortfolioPerformance,
    valueType: 'value' | 'totalReturn' | 'totalReturnPercent' = 'value'
  ): ChartDataPoint[] {
    return portfolioPerformance.data.map(point => ({
      date: point.date,
      timestamp: point.timestamp,
      value: point[valueType],
      label: this.formatValue(point[valueType], valueType)
    }))
  }

  /**
   * Convert portfolio performance to area chart format
   */
  static portfolioToAreaChart(
    portfolioPerformance: PortfolioPerformance,
    valueType: 'value' | 'totalReturn' | 'totalReturnPercent' = 'value'
  ): ChartDataPoint[] {
    const data = this.portfolioToLineChart(portfolioPerformance, valueType)
    
    // Add color coding based on performance
    return data.map((point, index) => {
      const isPositive = valueType === 'value' 
        ? point.value > (portfolioPerformance.data[0]?.value || 0)
        : point.value > 0

      return {
        ...point,
        color: isPositive ? '#10b981' : '#ef4444' // green for positive, red for negative
      }
    })
  }

  /**
   * Create comparison chart data (multiple series)
   */
  static createComparisonChart(
    datasets: Array<{
      name: string
      data: HistoricalData | PortfolioPerformance
      color: string
      type?: 'stock' | 'portfolio'
    }>
  ): Array<{
    name: string
    data: ChartDataPoint[]
    color: string
  }> {
    return datasets.map(dataset => {
      let chartData: ChartDataPoint[]

      if (dataset.type === 'portfolio' || 'data' in dataset.data) {
        // Portfolio performance data
        const portfolioData = dataset.data as PortfolioPerformance
        chartData = this.portfolioToLineChart(portfolioData, 'totalReturnPercent')
      } else {
        // Historical stock data
        const stockData = dataset.data as HistoricalData
        chartData = this.toLineChart(stockData)
        
        // Convert to percentage returns for comparison
        const firstValue = chartData[0]?.value || 1
        chartData = chartData.map(point => ({
          ...point,
          value: ((point.value - firstValue) / firstValue) * 100
        }))
      }

      return {
        name: dataset.name,
        data: chartData,
        color: dataset.color
      }
    })
  }

  /**
   * Create volume chart data
   */
  static toVolumeChart(
    historicalData: HistoricalData
  ): ChartDataPoint[] {
    return historicalData.data.map(point => ({
      date: point.date,
      timestamp: point.timestamp,
      value: point.volume,
      color: point.close > point.open ? '#10b981' : '#ef4444' // green for up days, red for down days
    }))
  }

  /**
   * Create moving average data
   */
  static addMovingAverage(
    data: ChartDataPoint[],
    period: number = 20,
    type: 'SMA' | 'EMA' = 'SMA'
  ): ChartDataPoint[] {
    const result: ChartDataPoint[] = []

    for (let i = 0; i < data.length; i++) {
      if (i < period - 1) {
        // Not enough data points for moving average
        result.push({ ...data[i], value: NaN })
        continue
      }

      let average: number

      if (type === 'SMA') {
        // Simple Moving Average
        const sum = data.slice(i - period + 1, i + 1)
          .reduce((acc, point) => acc + point.value, 0)
        average = sum / period
      } else {
        // Exponential Moving Average
        const multiplier = 2 / (period + 1)
        if (i === period - 1) {
          // First EMA value is SMA
          const sum = data.slice(0, period)
            .reduce((acc, point) => acc + point.value, 0)
          average = sum / period
        } else {
          const previousEMA = result[i - 1].value
          average = (data[i].value * multiplier) + (previousEMA * (1 - multiplier))
        }
      }

      result.push({
        ...data[i],
        value: average
      })
    }

    return result
  }

  /**
   * Format values for display
   */
  private static formatValue(value: number, type: string): string {
    switch (type) {
      case 'value':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        }).format(value)
      
      case 'totalReturn':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
          signDisplay: 'always'
        }).format(value)
      
      case 'totalReturnPercent':
        return new Intl.NumberFormat('en-US', {
          style: 'percent',
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
          signDisplay: 'always'
        }).format(value / 100)
      
      default:
        return value.toFixed(2)
    }
  }

  /**
   * Resample data for different time periods
   */
  static resampleData(
    data: ChartDataPoint[],
    targetPoints: number = 100
  ): ChartDataPoint[] {
    if (data.length <= targetPoints) {
      return data
    }

    const step = Math.floor(data.length / targetPoints)
    const result: ChartDataPoint[] = []

    for (let i = 0; i < data.length; i += step) {
      result.push(data[i])
    }

    // Always include the last point
    if (result[result.length - 1] !== data[data.length - 1]) {
      result.push(data[data.length - 1])
    }

    return result
  }

  /**
   * Calculate price change indicators
   */
  static addPriceChangeIndicators(
    data: ChartDataPoint[]
  ): Array<ChartDataPoint & { change: number; changePercent: number }> {
    return data.map((point, index) => {
      if (index === 0) {
        return {
          ...point,
          change: 0,
          changePercent: 0
        }
      }

      const previousValue = data[index - 1].value
      const change = point.value - previousValue
      const changePercent = previousValue !== 0 ? (change / previousValue) * 100 : 0

      return {
        ...point,
        change,
        changePercent
      }
    })
  }

  /**
   * Create chart configuration for different chart types
   */
  static getDefaultConfig(type: ChartConfig['type']): ChartConfig {
    const configs: Record<ChartConfig['type'], ChartConfig> = {
      line: {
        type: 'line',
        color: '#3b82f6',
        showGrid: true,
        showTooltip: true,
        height: 300
      },
      area: {
        type: 'area',
        color: '#3b82f6',
        gradient: {
          from: '#3b82f6',
          to: '#3b82f620'
        },
        showGrid: true,
        showTooltip: true,
        height: 300
      },
      candlestick: {
        type: 'candlestick',
        color: '#10b981',
        showVolume: true,
        showGrid: true,
        showTooltip: true,
        height: 400
      },
      bar: {
        type: 'bar',
        color: '#6366f1',
        showGrid: true,
        showTooltip: true,
        height: 200
      }
    }

    return configs[type]
  }

  /**
   * Merge multiple chart datasets with common time axis
   */
  static mergeDatasets(
    datasets: Array<{ name: string; data: ChartDataPoint[]; color: string }>
  ): Array<{ date: string; timestamp: number; [key: string]: any }> {
    // Get all unique dates
    const allDates = new Set<string>()
    datasets.forEach(dataset => {
      dataset.data.forEach(point => allDates.add(point.date))
    })

    const sortedDates = Array.from(allDates).sort()

    // Create merged data structure
    return sortedDates.map(date => {
      const mergedPoint: any = { date, timestamp: new Date(date).getTime() }

      datasets.forEach(dataset => {
        const dataPoint = dataset.data.find(point => point.date === date)
        mergedPoint[dataset.name] = dataPoint?.value || null
      })

      return mergedPoint
    })
  }
}

// Utility functions for common chart operations
export const ChartUtils = {
  /**
   * Format currency values
   */
  formatCurrency: (value: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value)
  },

  /**
   * Format percentage values
   */
  formatPercent: (value: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'percent',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
      signDisplay: 'always'
    }).format(value / 100)
  },

  /**
   * Get color based on value change
   */
  getChangeColor: (value: number): string => {
    return value >= 0 ? '#10b981' : '#ef4444'
  },

  /**
   * Calculate min and max values for chart scaling
   */
  getValueRange: (data: ChartDataPoint[]): { min: number; max: number } => {
    const values = data.map(point => point.value).filter(v => !isNaN(v))
    return {
      min: Math.min(...values),
      max: Math.max(...values)
    }
  }
}
