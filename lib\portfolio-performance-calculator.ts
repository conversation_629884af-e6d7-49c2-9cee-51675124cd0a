/**
 * Portfolio Performance Calculator
 * Calculates portfolio performance over time using historical data and allocation weights with centralized caching
 */

import { getHistoricalDataService, type HistoricalData, type TimePeriod, type TimeInterval } from './historical-data-service'
import { getCacheManager, createCacheKey } from './cache-manager'
import { getCacheTTL, getMarketAwareCacheTTL, getCacheTagsForDataType } from './cache-config'

export interface PortfolioAllocation {
  symbol: string
  allocation: number // Percentage (0-100)
  name?: string
}

export interface PortfolioPerformancePoint {
  date: string
  timestamp: number
  value: number
  totalReturn: number
  totalReturnPercent: number
  dailyReturn?: number
  dailyReturnPercent?: number
}

export interface PortfolioPerformance {
  data: PortfolioPerformancePoint[]
  summary: {
    totalValue: number
    totalReturn: number
    totalReturnPercent: number
    dailyChange: number
    dailyChangePercent: number
    bestDay: { date: string; return: number; returnPercent: number }
    worstDay: { date: string; return: number; returnPercent: number }
    volatility: number
    sharpeRatio: number
    maxDrawdown: number
  }
  period: TimePeriod
  interval: TimeInterval
  lastUpdated: string
  allocations: PortfolioAllocation[]
}

export class PortfolioPerformanceCalculator {
  private historicalService = getHistoricalDataService()
  private cacheManager = getCacheManager()

  /**
   * Calculate portfolio performance over time
   */
  async calculatePortfolioPerformance(
    allocations: PortfolioAllocation[],
    period: TimePeriod = '1M',
    interval: TimeInterval = '1d',
    initialValue: number = 10000
  ): Promise<PortfolioPerformance | null> {
    // Create a stable cache key based on allocations
    const allocationKey = allocations
      .sort((a, b) => a.symbol.localeCompare(b.symbol)) // Sort for consistent key
      .map(a => `${a.symbol}:${a.allocation}`)
      .join(',')
    const cacheKey = createCacheKey('portfolio', allocationKey, period, interval, initialValue.toString())

    // Check cache first with market-aware TTL
    const cached = this.cacheManager.get<PortfolioPerformance>(cacheKey)
    if (cached) return cached

    try {
      // Fetch historical data for all symbols
      const historicalDataPromises = allocations.map(allocation =>
        this.historicalService.getHistoricalData(allocation.symbol, period, interval)
      )

      const historicalDataResults = await Promise.all(historicalDataPromises)
      
      // Filter out null results
      const validHistoricalData: Array<{ allocation: PortfolioAllocation; data: HistoricalData }> = []
      
      for (let i = 0; i < historicalDataResults.length; i++) {
        const data = historicalDataResults[i]
        if (data && data.data.length > 0) {
          validHistoricalData.push({
            allocation: allocations[i],
            data
          })
        }
      }

      if (validHistoricalData.length === 0) {
        return null
      }

      // Find common date range (intersection of all data)
      const commonDates = this.findCommonDates(validHistoricalData.map(item => item.data))
      
      if (commonDates.length === 0) {
        return null
      }

      // Calculate portfolio value for each date
      const performanceData: PortfolioPerformancePoint[] = []
      let previousValue = initialValue

      for (const date of commonDates) {
        let portfolioValue = 0

        // Calculate weighted portfolio value for this date
        for (const { allocation, data } of validHistoricalData) {
          const dataPoint = data.data.find(d => d.date === date)
          if (dataPoint) {
            const weight = allocation.allocation / 100
            const stockValue = initialValue * weight
            const stockPrice = dataPoint.close
            const initialStockPrice = data.data[0]?.close || stockPrice
            
            // Calculate how this stock contributed to portfolio value
            const stockReturn = (stockPrice / initialStockPrice) - 1
            const stockContribution = stockValue * (1 + stockReturn)
            portfolioValue += stockContribution
          }
        }

        const totalReturn = portfolioValue - initialValue
        const totalReturnPercent = (totalReturn / initialValue) * 100
        const dailyReturn = portfolioValue - previousValue
        const dailyReturnPercent = previousValue > 0 ? (dailyReturn / previousValue) * 100 : 0

        performanceData.push({
          date,
          timestamp: new Date(date).getTime(),
          value: portfolioValue,
          totalReturn,
          totalReturnPercent,
          dailyReturn,
          dailyReturnPercent
        })

        previousValue = portfolioValue
      }

      // Calculate summary statistics
      const summary = this.calculateSummaryStatistics(performanceData, initialValue)

      const portfolioPerformance: PortfolioPerformance = {
        data: performanceData,
        summary,
        period,
        interval,
        lastUpdated: new Date().toISOString(),
        allocations
      }

      // Cache the result with smart TTL and tags
      const ttl = getMarketAwareCacheTTL('portfolioPerformance')
      const tags = getCacheTagsForDataType('portfolioPerformance')
      this.cacheManager.set(cacheKey, portfolioPerformance, ttl, tags)

      return portfolioPerformance
    } catch (error) {
      console.error('Portfolio performance calculation error:', error)
      return null
    }
  }

  /**
   * Calculate summary statistics for portfolio performance
   */
  private calculateSummaryStatistics(
    data: PortfolioPerformancePoint[], 
    initialValue: number
  ): PortfolioPerformance['summary'] {
    if (data.length === 0) {
      return {
        totalValue: initialValue,
        totalReturn: 0,
        totalReturnPercent: 0,
        dailyChange: 0,
        dailyChangePercent: 0,
        bestDay: { date: '', return: 0, returnPercent: 0 },
        worstDay: { date: '', return: 0, returnPercent: 0 },
        volatility: 0,
        sharpeRatio: 0,
        maxDrawdown: 0
      }
    }

    const lastPoint = data[data.length - 1]
    const previousPoint = data.length > 1 ? data[data.length - 2] : data[0]

    // Find best and worst days
    let bestDay = { date: '', return: -Infinity, returnPercent: -Infinity }
    let worstDay = { date: '', return: Infinity, returnPercent: Infinity }

    for (const point of data) {
      if (point.dailyReturn !== undefined && point.dailyReturnPercent !== undefined) {
        if (point.dailyReturnPercent > bestDay.returnPercent) {
          bestDay = {
            date: point.date,
            return: point.dailyReturn,
            returnPercent: point.dailyReturnPercent
          }
        }
        if (point.dailyReturnPercent < worstDay.returnPercent) {
          worstDay = {
            date: point.date,
            return: point.dailyReturn,
            returnPercent: point.dailyReturnPercent
          }
        }
      }
    }

    // Calculate volatility (standard deviation of daily returns)
    const dailyReturns = data
      .filter(point => point.dailyReturnPercent !== undefined)
      .map(point => point.dailyReturnPercent!)

    const avgDailyReturn = dailyReturns.reduce((sum, ret) => sum + ret, 0) / dailyReturns.length
    const variance = dailyReturns.reduce((sum, ret) => sum + Math.pow(ret - avgDailyReturn, 2), 0) / dailyReturns.length
    const volatility = Math.sqrt(variance) * Math.sqrt(252) // Annualized volatility

    // Calculate Sharpe ratio (assuming 2% risk-free rate)
    const riskFreeRate = 2
    const annualizedReturn = lastPoint.totalReturnPercent * (252 / data.length) // Rough annualization
    const sharpeRatio = volatility > 0 ? (annualizedReturn - riskFreeRate) / volatility : 0

    // Calculate maximum drawdown
    let maxDrawdown = 0
    let peak = initialValue

    for (const point of data) {
      if (point.value > peak) {
        peak = point.value
      }
      const drawdown = (peak - point.value) / peak * 100
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown
      }
    }

    return {
      totalValue: lastPoint.value,
      totalReturn: lastPoint.totalReturn,
      totalReturnPercent: lastPoint.totalReturnPercent,
      dailyChange: lastPoint.value - previousPoint.value,
      dailyChangePercent: previousPoint.value > 0 ? ((lastPoint.value - previousPoint.value) / previousPoint.value) * 100 : 0,
      bestDay,
      worstDay,
      volatility,
      sharpeRatio,
      maxDrawdown
    }
  }

  /**
   * Find common dates across all historical data sets
   */
  private findCommonDates(historicalDataSets: HistoricalData[]): string[] {
    if (historicalDataSets.length === 0) return []
    
    // Start with dates from first dataset
    let commonDates = new Set(historicalDataSets[0].data.map(d => d.date))
    
    // Find intersection with other datasets
    for (let i = 1; i < historicalDataSets.length; i++) {
      const currentDates = new Set(historicalDataSets[i].data.map(d => d.date))
      commonDates = new Set([...commonDates].filter(date => currentDates.has(date)))
    }
    
    // Sort dates chronologically
    return Array.from(commonDates).sort()
  }

  /**
   * Clear cache for specific allocations
   */
  clearCacheForAllocations(allocations: PortfolioAllocation[]): void {
    const allocationKey = allocations
      .sort((a, b) => a.symbol.localeCompare(b.symbol))
      .map(a => `${a.symbol}:${a.allocation}`)
      .join(',')

    // Clear all cached entries for these allocations
    const pattern = new RegExp(`portfolio:${allocationKey.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}:.*`)
    const keys = this.cacheManager.getKeys().filter(key => pattern.test(key))
    keys.forEach(key => this.cacheManager.delete(key))
  }

  /**
   * Clear all portfolio cache
   */
  clearCache(): void {
    this.cacheManager.clearByTags(['portfolio'])
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return this.cacheManager.getStats()
  }

  /**
   * Warm cache with common portfolio configurations
   */
  async warmCache(
    commonAllocations: PortfolioAllocation[][],
    periods: TimePeriod[] = ['1D', '1W', '1M', '3M', '1Y'],
    initialValue: number = 10000
  ): Promise<void> {
    const warmupPromises: Promise<any>[] = []

    for (const allocations of commonAllocations) {
      for (const period of periods) {
        warmupPromises.push(
          this.calculatePortfolioPerformance(allocations, period, '1d', initialValue)
            .catch(error => console.warn('Cache warming failed:', error))
        )
      }
    }

    await Promise.allSettled(warmupPromises)
  }
}

// Singleton instance
let portfolioCalculatorInstance: PortfolioPerformanceCalculator | null = null

export function getPortfolioPerformanceCalculator(): PortfolioPerformanceCalculator {
  if (!portfolioCalculatorInstance) {
    portfolioCalculatorInstance = new PortfolioPerformanceCalculator()
  }
  return portfolioCalculatorInstance
}
