/**
 * PromptTemplateBuilder - Converts survey data into structured LLM prompts
 */

import crypto from 'crypto'
import type { IPromptTemplateBuilder, PromptData } from './types'
import type { SurveyData } from '@/components/onboarding-survey'

export class PromptTemplateBuilder implements IPromptTemplateBuilder {
  
  /**
   * Build a comprehensive portfolio generation prompt from survey data
   */
  async buildPortfolioPrompt(data: PromptData): Promise<string> {
    const { surveyData, userId, userMajor, userMetadata, additionalContext } = data
    
    const prompt = `You are an expert financial advisor creating a personalized investment portfolio. Generate a detailed portfolio recommendation based on the following client profile:

## Client Profile
- **Academic Background**: ${this.sanitizeUserMajor(userMajor)}
- **Primary Investment Goal**: ${this.sanitizeText(surveyData.primaryGoal)}
- **Time Horizon**: ${this.sanitizeText(surveyData.timeHorizon)}
- **Risk Tolerance**: ${this.normalizeRiskTolerance(surveyData.riskTolerance)}
- **Experience Level**: ${this.sanitizeText(surveyData.experienceLevel)}
- **Investment Budget Range**: ${this.anonymizeInvestmentAmount(surveyData.monthlyInvestment)}
- **Interested Investment Themes**: ${this.sanitizeThemes(surveyData.interestedThemes)}

${userMetadata ? `## Additional Context
${Object.entries(userMetadata).map(([key, value]) => `- **${key}**: ${value}`).join('\n')}` : ''}

${additionalContext ? `## Special Instructions
${additionalContext}` : ''}

## Portfolio Requirements
Please create a portfolio with the following structure and return it as a valid JSON object:

\`\`\`json
{
  "allocations": [
    {
      "symbol": "STOCK_SYMBOL",
      "name": "Company/Fund Name",
      "allocation": 25.5,
      "category": "Growth Stocks|Value Stocks|ETFs|Bonds|REITs|International|Commodities",
      "rationale": "Specific reason for including this investment"
    }
  ],
  "riskLevel": "Conservative|Moderate|Aggressive",
  "expectedReturn": "X-Y%",
  "strategy": "Brief description of overall investment strategy",
  "rebalanceFrequency": "Monthly|Quarterly|Semi-annually|Annually",
  "rationale": "Comprehensive explanation of why this portfolio fits the client's profile"
}
\`\`\`

## Investment Guidelines
1. **Diversification**: Include 12-20 different investments across multiple asset classes
2. **Allocation Precision**: All allocations must sum to exactly 100%
3. **Risk Alignment**: Portfolio risk level must match client's risk tolerance
4. **Real Symbols**: Use only real, tradeable stock symbols and ETF tickers
5. **Category Distribution**: 
   - Conservative: 60%+ ETFs/Bonds, 40%- Individual stocks
   - Moderate: 50-70% ETFs/Stocks mix, 20-30% Bonds/REITs
   - Aggressive: 70%+ Growth stocks/ETFs, minimal bonds
6. **Major-Specific Considerations**: 
   - Tech majors: Include technology sector exposure
   - Business majors: Include financial sector and dividend stocks
   - Healthcare majors: Include healthcare and biotech exposure
   - Engineering majors: Include industrial and infrastructure investments

## Expected Return Guidelines
- Conservative: 6-8% annually
- Moderate: 8-12% annually  
- Aggressive: 12-16% annually

## Quality Standards
- Each allocation must have a clear, specific rationale
- Strategy should be coherent and match risk tolerance
- Rationale should address client's goals, timeline, and experience level
- All percentages must be realistic and sum to 100%

Generate the portfolio now as a properly formatted JSON object:`

    return prompt
  }

  /**
   * Generate a SHA-256 hash of the prompt for caching
   */
  generatePromptHash(prompt: string): string {
    return crypto.createHash('sha256').update(prompt).digest('hex')
  }

  /**
   * Build a simplified prompt for testing/fallback scenarios
   */
  async buildSimplePrompt(data: PromptData): Promise<string> {
    const { surveyData } = data
    
    return `Create a ${this.getRiskLevelText(surveyData.riskTolerance)} investment portfolio for:
- Goal: ${surveyData.primaryGoal}
- Timeline: ${surveyData.timeHorizon}
- Budget: $${surveyData.monthlyInvestment}/month
- Themes: ${surveyData.interestedThemes?.join(', ') || 'Diversified'}

Return as JSON with allocations array, riskLevel, expectedReturn, strategy, rebalanceFrequency, and rationale.`
  }

  /**
   * Build a prompt for portfolio validation
   */
  async buildValidationPrompt(portfolioJson: string): Promise<string> {
    return `Please validate this investment portfolio JSON and identify any issues:

${portfolioJson}

Check for:
1. Do allocations sum to exactly 100%?
2. Are all stock symbols real and tradeable?
3. Is the risk level appropriate for the allocations?
4. Are expected returns realistic?
5. Is the strategy coherent?

Return a JSON response:
{
  "isValid": true/false,
  "errors": ["list of specific issues found"],
  "suggestions": ["list of improvement suggestions"]
}`
  }

  /**
   * Build a prompt for portfolio optimization
   */
  async buildOptimizationPrompt(portfolioJson: string, feedback: string): Promise<string> {
    return `Please optimize this investment portfolio based on the feedback:

Current Portfolio:
${portfolioJson}

Feedback:
${feedback}

Return an improved portfolio as JSON with the same structure, addressing the feedback while maintaining proper diversification and risk alignment.`
  }

  /**
   * Helper method to convert risk tolerance number to text
   */
  private getRiskLevelText(riskTolerance: number): string {
    if (riskTolerance <= 2) return 'conservative'
    if (riskTolerance <= 3) return 'moderate'
    return 'aggressive'
  }

  /**
   * Helper method to get major-specific investment themes
   */
  private getMajorThemes(major?: string): string[] {
    const majorThemes: Record<string, string[]> = {
      'computer-science': ['Technology', 'AI/Robotics', 'Cybersecurity'],
      'business': ['Financial Services', 'Consumer Goods', 'Real Estate'],
      'engineering': ['Industrial', 'Infrastructure', 'Clean Energy'],
      'healthcare': ['Healthcare', 'Biotechnology', 'Pharmaceuticals'],
      'finance': ['Banking', 'Insurance', 'Fintech'],
      'marketing': ['Consumer Discretionary', 'Media', 'E-commerce'],
      'education': ['Education Technology', 'Publishing', 'Training Services']
    }

    return majorThemes[major || ''] || []
  }

  /**
   * Build a portfolio reweighting prompt
   */
  async buildReweightingPrompt(data: PromptData, currentPortfolio: any, reweightReason: string): Promise<string> {
    const { surveyData, userId, userMajor, userMetadata } = data

    const prompt = `You are an expert financial advisor performing portfolio rebalancing. A client wants to reweight their existing portfolio based on specific concerns or goals.

## Client Profile
- **Academic Background**: ${this.sanitizeUserMajor(userMajor)}
- **Primary Goal**: ${this.sanitizeText(surveyData.primaryGoal)}
- **Time Horizon**: ${this.sanitizeText(surveyData.timeHorizon)}
- **Risk Tolerance**: ${this.normalizeRiskTolerance(surveyData.riskTolerance)}
- **Experience Level**: ${this.sanitizeText(surveyData.experienceLevel)}
- **Investment Budget Range**: ${this.anonymizeInvestmentAmount(surveyData.monthlyInvestment)}

## Current Portfolio Analysis
- **Risk Level**: ${currentPortfolio.riskLevel}
- **Expected Return**: ${currentPortfolio.expectedReturn}
- **Current Strategy**: ${currentPortfolio.strategy}
- **Number of Holdings**: ${currentPortfolio.allocations.length}
- **Rebalance Frequency**: ${currentPortfolio.rebalanceFrequency}

## Current Allocations
${currentPortfolio.allocations.map((alloc: any) =>
  `- **${alloc.symbol}** (${alloc.name}): ${alloc.allocation}% - ${alloc.category}\n  Rationale: ${alloc.rationale}`
).join('\n')}

## Client's Reweighting Request
"${this.sanitizeReweightReason(reweightReason)}"

## Reweighting Instructions
Please analyze the current portfolio and create a NEW portfolio that addresses the client's specific reweighting request. Return as a valid JSON object:

\`\`\`json
{
  "allocations": [
    {
      "symbol": "STOCK_SYMBOL",
      "name": "Company/Fund Name",
      "allocation": 25.5,
      "category": "Growth Stocks|Value Stocks|ETFs|Bonds|REITs|International|Commodities",
      "rationale": "Specific reason for this allocation change or addition"
    }
  ],
  "riskLevel": "Conservative|Moderate|Aggressive",
  "expectedReturn": "X-Y%",
  "strategy": "Updated investment strategy reflecting the changes made",
  "rebalanceFrequency": "Monthly|Quarterly|Semi-annually|Annually",
  "rationale": "Comprehensive explanation of the reweighting decisions, addressing the client's specific request and explaining what changed and why"
}
\`\`\`

## Reweighting Guidelines
1. **Address the Request**: Directly respond to the client's specific reweighting reason
2. **Maintain Diversification**: Ensure the new portfolio remains well-diversified
3. **Preserve Risk Alignment**: Keep risk level appropriate unless client specifically requests change
4. **Explain Changes**: Clearly articulate what changed and why in the rationale
5. **Consider Current Holdings**: You may:
   - Adjust allocation percentages of existing holdings
   - Remove holdings that no longer fit the strategy
   - Add new holdings to address the client's concerns
   - Maintain holdings that still align with goals
6. **Market Awareness**: Consider current market conditions in your recommendations
7. **Allocation Precision**: All allocations must sum to exactly 100%
8. **Real Symbols**: Use only real, tradeable stock symbols and ETF tickers

## Quality Standards for Reweighting
- Each change should have a clear rationale tied to the client's request
- New strategy should reflect the portfolio changes
- Rationale should explain the reweighting logic and expected benefits
- All percentages must be realistic and sum to 100%
- Consider transaction costs and tax implications in major changes

Generate the reweighted portfolio now as a properly formatted JSON object:`

    return prompt
  }

  /**
   * Build a context-aware prompt with market conditions
   */
  async buildContextAwarePrompt(data: PromptData, marketContext?: string): Promise<string> {
    const basePrompt = await this.buildPortfolioPrompt(data)

    if (!marketContext) {
      return basePrompt
    }

    return `${basePrompt}

## Current Market Context
${marketContext}

Please consider these market conditions when making allocation decisions and adjust the portfolio accordingly while maintaining the client's risk profile.`
  }

  /**
   * Validate prompt data before building
   */
  private validatePromptData(data: PromptData): void {
    const { surveyData, userId } = data

    if (!userId) {
      throw new Error('User ID is required for prompt generation')
    }

    if (!surveyData) {
      throw new Error('Survey data is required for prompt generation')
    }

    if (!surveyData.primaryGoal) {
      throw new Error('Primary investment goal is required')
    }

    if (!surveyData.timeHorizon) {
      throw new Error('Time horizon is required')
    }

    if (typeof surveyData.riskTolerance !== 'number' || surveyData.riskTolerance < 1 || surveyData.riskTolerance > 5) {
      throw new Error('Risk tolerance must be a number between 1 and 5')
    }
  }

  /**
   * SECURITY: Sanitize text input to remove potential sensitive information
   */
  private sanitizeText(text: string): string {
    if (!text) return 'Not specified'

    // Remove potential PII patterns
    let sanitized = text
      // Remove email addresses
      .replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '[EMAIL_REMOVED]')
      // Remove phone numbers
      .replace(/\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g, '[PHONE_REMOVED]')
      // Remove SSN patterns
      .replace(/\b\d{3}-\d{2}-\d{4}\b/g, '[SSN_REMOVED]')
      // Remove potential account numbers (8+ consecutive digits)
      .replace(/\b\d{8,}\b/g, '[NUMBER_REMOVED]')
      // Remove potential names (common patterns)
      .replace(/\b(my name is|i am|i'm)\s+[A-Za-z]+\b/gi, '[NAME_REMOVED]')

    // Limit length to prevent prompt injection
    if (sanitized.length > 200) {
      sanitized = sanitized.substring(0, 200) + '...'
    }

    return sanitized
  }

  /**
   * SECURITY: Sanitize user major to generic categories
   */
  private sanitizeUserMajor(major?: string): string {
    if (!major) return 'General Studies'

    const majorCategories: Record<string, string> = {
      'computer-science': 'Technology/Engineering',
      'engineering': 'Technology/Engineering',
      'software-engineering': 'Technology/Engineering',
      'information-technology': 'Technology/Engineering',
      'business': 'Business/Finance',
      'finance': 'Business/Finance',
      'economics': 'Business/Finance',
      'accounting': 'Business/Finance',
      'marketing': 'Business/Finance',
      'healthcare': 'Healthcare/Life Sciences',
      'biology': 'Healthcare/Life Sciences',
      'medicine': 'Healthcare/Life Sciences',
      'nursing': 'Healthcare/Life Sciences',
      'education': 'Education/Liberal Arts',
      'psychology': 'Education/Liberal Arts',
      'english': 'Education/Liberal Arts',
      'history': 'Education/Liberal Arts',
      'art': 'Education/Liberal Arts'
    }

    const normalizedMajor = major.toLowerCase().replace(/[^a-z-]/g, '')
    return majorCategories[normalizedMajor] || 'Other'
  }

  /**
   * SECURITY: Normalize risk tolerance to descriptive text
   */
  private normalizeRiskTolerance(riskTolerance: number): string {
    if (riskTolerance <= 2) return 'Conservative (Low Risk)'
    if (riskTolerance <= 3) return 'Moderate (Medium Risk)'
    return 'Aggressive (High Risk)'
  }

  /**
   * SECURITY: Anonymize investment amounts to ranges
   */
  private anonymizeInvestmentAmount(amount?: number): string {
    if (!amount || amount <= 0) return 'Minimal ($0-$100/month)'

    if (amount <= 100) return 'Minimal ($0-$100/month)'
    if (amount <= 500) return 'Low ($100-$500/month)'
    if (amount <= 1000) return 'Moderate ($500-$1000/month)'
    if (amount <= 2000) return 'High ($1000-$2000/month)'
    return 'Very High ($2000+/month)'
  }

  /**
   * SECURITY: Sanitize investment themes
   */
  private sanitizeThemes(themes?: string[]): string {
    if (!themes || themes.length === 0) return 'Diversified approach'

    const allowedThemes = [
      'Technology', 'Healthcare', 'Finance', 'Energy', 'Real Estate',
      'International', 'Bonds', 'ETFs', 'Growth', 'Value', 'Dividend',
      'ESG', 'Small Cap', 'Large Cap', 'Emerging Markets'
    ]

    const sanitizedThemes = themes
      .filter(theme => allowedThemes.includes(theme))
      .slice(0, 5) // Limit to 5 themes

    return sanitizedThemes.length > 0 ? sanitizedThemes.join(', ') : 'Diversified approach'
  }

  /**
   * SECURITY: Sanitize reweight reason to prevent prompt injection
   */
  private sanitizeReweightReason(reason: string): string {
    if (!reason) return 'General portfolio optimization'

    // Remove potential prompt injection attempts
    let sanitized = reason
      // Remove common prompt injection patterns
      .replace(/\b(ignore|forget|disregard)\s+(previous|above|all)\s+(instructions?|prompts?|rules?)\b/gi, '[FILTERED]')
      .replace(/\b(act as|pretend to be|you are now)\b/gi, '[FILTERED]')
      .replace(/\b(system|admin|root|developer)\s+(prompt|instruction|command)\b/gi, '[FILTERED]')
      // Remove potential code injection
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '[SCRIPT_REMOVED]')
      .replace(/javascript:/gi, '[JS_REMOVED]')
      // Remove excessive special characters that might be injection attempts
      .replace(/[{}[\]\\`~!@#$%^&*()+=|;:"'<>?/]{5,}/g, '[SPECIAL_CHARS_REMOVED]')

    // Apply general text sanitization
    sanitized = this.sanitizeText(sanitized)

    // Additional length limit for reweight reasons
    if (sanitized.length > 500) {
      sanitized = sanitized.substring(0, 500) + '...'
    }

    return sanitized
  }
}
