/**
 * Stock Search API Route
 * GET /api/stocks/search?q=apple
 */

import { NextRequest, NextResponse } from 'next/server'
import { searchStocks } from '@/lib/supabase'
import { getStockDataService, POPULAR_STOCKS } from '@/lib/stock-config'

// Popular stock mappings for intelligent search
const SMART_SYMBOL_MAPPINGS: Record<string, string[]> = {
  // Common abbreviations that should prioritize major stocks
  'AA': ['AAPL', 'AA'], // Apple first, then Alcoa
  'JP': ['JPM'], // JPMorgan Chase
  'MS': ['MSFT', 'MS'], // Microsoft first, then Morgan Stanley
  'BA': ['BA'], // Boeing
  'C': ['C'], // Citigroup
  'F': ['F'], // Ford
  'T': ['T'], // AT&T
  'V': ['V'], // Visa
  'MA': ['MA'], // Mastercard
  'WM': ['WMT'], // Walmart
  'HD': ['HD'], // Home Depot
  'PG': ['PG'], // Procter & Gamble
  'JJ': ['JNJ'], // <PERSON> & Johnson
  'KO': ['KO'], // Coca-Cola
  'PEP': ['PEP'], // PepsiCo
  'MCD': ['MCD'], // McDonald's
  'NKE': ['NKE'], // Nike
  'UNH': ['UNH'], // UnitedHealth
  'CVX': ['CVX'], // Chevron
  'XOM': ['XOM'], // ExxonMobil

  // Tech stocks
  'APPLE': ['AAPL'],
  'MICROSOFT': ['MSFT'],
  'GOOGLE': ['GOOGL', 'GOOG'],
  'AMAZON': ['AMZN'],
  'TESLA': ['TSLA'],
  'META': ['META'],
  'FACEBOOK': ['META'],
  'NVIDIA': ['NVDA'],
  'NETFLIX': ['NFLX'],
  'AMD': ['AMD'],
  'INTEL': ['INTC'],
  'ORACLE': ['ORCL'],
  'SALESFORCE': ['CRM'],
  'ADOBE': ['ADBE'],
  'CISCO': ['CSCO'],

  // Financial
  'JPMORGAN': ['JPM'],
  'BANKOFAMERICA': ['BAC'],
  'WELLSFARGO': ['WFC'],
  'GOLDMANSACHS': ['GS'],
  'MORGENSTANLEY': ['MS'],
  'VISA': ['V'],
  'MASTERCARD': ['MA'],
  'AMERICANEXPRESS': ['AXP'],

  // Other major companies
  'DISNEY': ['DIS'],
  'WALMART': ['WMT'],
  'HOMEDEPOT': ['HD'],
  'COCACOLA': ['KO'],
  'PEPSI': ['PEP'],
  'MCDONALDS': ['MCD'],
  'NIKE': ['NKE'],
  'BOEING': ['BA'],
  'FORD': ['F'],
  'GENERALMOTORS': ['GM'],
  'ATT': ['T'],
  'VERIZON': ['VZ'],
  'EXXON': ['XOM'],
  'CHEVRON': ['CVX']
}

// Major stocks by market cap (prioritized)
const MAJOR_STOCKS = [
  'AAPL', 'MSFT', 'GOOGL', 'GOOG', 'AMZN', 'NVDA', 'META', 'TSLA', 'BRK.A', 'BRK.B',
  'UNH', 'JNJ', 'JPM', 'V', 'PG', 'XOM', 'HD', 'CVX', 'MA', 'ABBV',
  'PFE', 'AVGO', 'COST', 'DIS', 'KO', 'ADBE', 'WMT', 'BAC', 'CRM', 'TMO',
  'NFLX', 'ACN', 'LIN', 'AMD', 'CSCO', 'VZ', 'ABT', 'DHR', 'TXN', 'ORCL',
  'NKE', 'QCOM', 'PM', 'RTX', 'NEE', 'INTC', 'COP', 'T', 'UNP', 'LOW'
]

async function performSmartSearch(query: string, limit: number) {
  const queryUpper = query.toUpperCase()
  const stockService = getStockDataService()
  const results: any[] = []

  // 1. Check for smart symbol mappings first
  if (SMART_SYMBOL_MAPPINGS[queryUpper]) {
    for (const symbol of SMART_SYMBOL_MAPPINGS[queryUpper]) {
      try {
        const quote = await stockService.getStockQuote(symbol)
        if (quote) {
          results.push({
            symbol: quote.symbol,
            ticker: quote.symbol,
            name: quote.name,
            company_name: quote.name,
            price: quote.price,
            change: quote.change,
            changePercent: quote.changePercent,
            change_percent: quote.changePercent,
            volume: quote.volume,
            marketCap: quote.marketCap,
            market_cap: quote.marketCap,
            category: quote.sector || 'Unknown',
            sector: quote.sector,
            tags: [],
            lastUpdated: quote.lastUpdated,
            last_updated: quote.lastUpdated,
            source: quote.source,
            exchange: quote.exchange,
            priority: 1 // Highest priority
          })
        }
      } catch (error) {
        console.error(`Error fetching ${symbol}:`, error)
      }
    }
  }

  // 2. Try exact symbol match if not in mappings
  if (results.length === 0) {
    try {
      const quote = await stockService.getStockQuote(queryUpper)
      if (quote) {
        results.push({
          symbol: quote.symbol,
          ticker: quote.symbol,
          name: quote.name,
          company_name: quote.name,
          price: quote.price,
          change: quote.change,
          changePercent: quote.changePercent,
          change_percent: quote.changePercent,
          volume: quote.volume,
          marketCap: quote.marketCap,
          market_cap: quote.marketCap,
          category: quote.sector || 'Unknown',
          sector: quote.sector,
          tags: [],
          lastUpdated: quote.lastUpdated,
          last_updated: quote.lastUpdated,
          source: quote.source,
          exchange: quote.exchange,
          priority: 1
        })
      }
    } catch (error) {
      console.error(`Error fetching exact match ${queryUpper}:`, error)
    }
  }

  // 3. Search database with intelligent ranking
  try {
    const dbResults = await searchStocks(query, 50)

    // Rank database results
    const rankedDbResults = dbResults.map(stock => ({
      ...stock,
      priority: calculatePriority(stock, query)
    })).sort((a, b) => b.priority - a.priority)

    // Add top database results (avoid duplicates)
    const existingSymbols = new Set(results.map(r => r.symbol))
    for (const stock of rankedDbResults) {
      if (!existingSymbols.has(stock.symbol) && results.length < limit) {
        results.push(stock)
      }
    }
  } catch (error) {
    console.error('Database search error:', error)
  }

  // 4. If still no results, try partial matches on major stocks
  if (results.length === 0 && query.length >= 2) {
    const partialMatches = MAJOR_STOCKS.filter(symbol =>
      symbol.toLowerCase().includes(query.toLowerCase())
    ).slice(0, 5)

    for (const symbol of partialMatches) {
      try {
        const quote = await stockService.getStockQuote(symbol)
        if (quote) {
          results.push({
            symbol: quote.symbol,
            ticker: quote.symbol,
            name: quote.name,
            company_name: quote.name,
            price: quote.price,
            change: quote.change,
            changePercent: quote.changePercent,
            change_percent: quote.changePercent,
            volume: quote.volume,
            marketCap: quote.marketCap,
            market_cap: quote.marketCap,
            category: quote.sector || 'Unknown',
            sector: quote.sector,
            tags: [],
            lastUpdated: quote.lastUpdated,
            last_updated: quote.lastUpdated,
            source: quote.source,
            exchange: quote.exchange,
            priority: 0.5
          })
        }
      } catch (error) {
        console.error(`Error fetching partial match ${symbol}:`, error)
      }
    }
  }

  return results.slice(0, limit)
}

function calculatePriority(stock: any, query: string): number {
  let priority = 0
  const symbol = stock.symbol?.toUpperCase() || ''
  const name = stock.name?.toLowerCase() || ''
  const queryUpper = query.toUpperCase()
  const queryLower = query.toLowerCase()

  // Exact symbol match gets highest priority
  if (symbol === queryUpper) {
    priority += 100
  }

  // Symbol starts with query
  if (symbol.startsWith(queryUpper)) {
    priority += 50
  }

  // Major stock bonus
  if (MAJOR_STOCKS.includes(symbol)) {
    priority += 30
  }

  // Company name starts with query
  if (name.startsWith(queryLower)) {
    priority += 20
  }

  // Company name contains query
  if (name.includes(queryLower)) {
    priority += 10
  }

  // Symbol contains query
  if (symbol.includes(queryUpper)) {
    priority += 5
  }

  // Prefer stocks over ETFs for ambiguous queries
  if (stock.category !== 'ETF' && stock.sector !== 'ETF') {
    priority += 2
  }

  // Penalize very long symbols (usually obscure)
  if (symbol.length > 5) {
    priority -= 5
  }

  return priority
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')
    const limit = parseInt(searchParams.get('limit') || '10')

    if (!query) {
      return NextResponse.json(
        { error: 'Missing required parameter: q (query)' },
        { status: 400 }
      )
    }

    if (query.length < 1) {
      return NextResponse.json(
        { error: 'Query must be at least 1 character long' },
        { status: 400 }
      )
    }

    // Smart search with prioritization
    const results = await performSmartSearch(query, limit)

    if (results.length > 0) {
      return NextResponse.json({
        data: results,
        count: results.length,
        source: 'smart_search',
        timestamp: new Date().toISOString()
      })
    }

    // If no database results, try popular stocks as fallback
    const popularResults = POPULAR_STOCKS.filter(stock =>
      stock.toLowerCase().includes(query.toLowerCase())
    ).slice(0, Math.min(limit, 5))

    if (popularResults.length > 0) {
      // Try to get real-time data for popular stocks
      const stockService = getStockDataService()
      const quotes = await Promise.all(
        popularResults.map(async (symbol) => {
          try {
            return await stockService.getStockQuote(symbol)
          } catch {
            return null
          }
        })
      )

      const validQuotes = quotes.filter(Boolean)
      if (validQuotes.length > 0) {
        return NextResponse.json({
          data: validQuotes,
          count: validQuotes.length,
          source: 'api',
          timestamp: new Date().toISOString()
        })
      }
    }

    // If still no results, try to get real-time data for potential stock symbols
    const stockService = getStockDataService()

    // Try the query as-is first
    let quote = await stockService.getStockQuote(query.toUpperCase())

    // If that fails and query looks like it could be a symbol, try some variations
    if (!quote && query.length <= 6) {
      // Try common variations
      const variations = [
        query.toUpperCase(),
        query.toUpperCase().replace(/[^A-Z]/g, ''), // Remove non-letters
        query.toUpperCase() + '.US', // Some APIs need .US suffix
      ]

      for (const variation of variations) {
        quote = await stockService.getStockQuote(variation)
        if (quote) break
      }
    }

    if (quote) {
      // Convert to the format expected by the search page
      const searchResult = {
        symbol: quote.symbol,
        ticker: quote.symbol,
        name: quote.name,
        company_name: quote.name,
        price: quote.price,
        change: quote.change,
        changePercent: quote.changePercent,
        change_percent: quote.changePercent,
        volume: quote.volume,
        marketCap: quote.marketCap,
        market_cap: quote.marketCap,
        category: quote.sector || 'Unknown',
        sector: quote.sector,
        tags: [],
        lastUpdated: quote.lastUpdated,
        last_updated: quote.lastUpdated,
        source: quote.source,
        exchange: quote.exchange
      }

      return NextResponse.json({
        data: [searchResult],
        count: 1,
        source: 'api',
        timestamp: new Date().toISOString()
      })
    }

    return NextResponse.json({
      data: [],
      count: 0,
      source: 'none',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Stock search API error:', error)
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
