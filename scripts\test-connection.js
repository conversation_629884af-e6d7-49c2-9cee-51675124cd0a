// Test Supabase connection
// Run with: node scripts/test-connection.js

const { createClient } = require('@supabase/supabase-js')

// Direct values for testing (replace with your actual values)
const supabaseUrl = 'https://fiwhyrlkcojfeovdfrjn.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZpd2h5cmxrY29qZmVvdmRmcmpuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxMDI4ODQsImV4cCI6MjA2NzY3ODg4NH0.9JLGagCUl0URZwx6QA67JsG5fTQmg28QE7AQlroyuLg'

console.log('Testing Supabase connection...')
console.log('URL:', supabaseUrl)
console.log('Key:', supabaseKey ? `${supabaseKey.substring(0, 20)}...` : 'Not found')

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Environment variables not found!')
  console.log('Make sure .env.local contains:')
  console.log('NEXT_PUBLIC_SUPABASE_URL=your_url')
  console.log('NEXT_PUBLIC_SUPABASE_ANON_KEY=your_key')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function testConnection() {
  try {
    // Test basic connection
    const { data, error } = await supabase.from('user_profiles').select('count', { count: 'exact', head: true })
    
    if (error) {
      console.error('❌ Connection failed:', error.message)
      
      if (error.message.includes('relation "user_profiles" does not exist')) {
        console.log('\n💡 Solution: Run the database setup script:')
        console.log('1. Go to Supabase dashboard → SQL Editor')
        console.log('2. Copy contents of scripts/create-tables-supabase.sql')
        console.log('3. Paste and run the script')
      }
      
      return false
    }
    
    console.log('✅ Supabase connection successful!')
    console.log(`📊 Found ${data} user profiles in database`)
    return true
    
  } catch (error) {
    console.error('❌ Unexpected error:', error.message)
    return false
  }
}

testConnection().then(success => {
  if (success) {
    console.log('\n🎉 Your Supabase setup is working correctly!')
    console.log('You can now run: npm run dev')
  } else {
    console.log('\n🔧 Please fix the issues above and try again')
  }
  process.exit(success ? 0 : 1)
})
