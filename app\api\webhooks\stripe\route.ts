import { NextRequest, NextResponse } from 'next/server'
import <PERSON><PERSON> from 'stripe'
import { createClient } from '@supabase/supabase-js'

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2023-10-16',
})

// Initialize Supabase with service role key for admin operations
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''
)

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET || ''

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const signature = request.headers.get('stripe-signature')
    
    if (!signature) {
      console.error('No Stripe signature found')
      return NextResponse.json({ error: 'No signature' }, { status: 400 })
    }
    
    if (!webhookSecret) {
      console.error('Stripe webhook secret not configured')
      return NextResponse.json({ error: 'Webhook secret not configured' }, { status: 500 })
    }
    
    let event: Stripe.Event
    
    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret)
    } catch (err) {
      console.error('Webhook signature verification failed:', err)
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 })
    }
    
    console.log('Received Stripe webhook event:', event.type)
    
    // Handle the event
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(event.data.object as Stripe.Checkout.Session)
        break
        
      case 'payment_intent.succeeded':
        await handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent)
        break
        
      case 'payment_intent.payment_failed':
        await handlePaymentIntentFailed(event.data.object as Stripe.PaymentIntent)
        break
        
      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice)
        break
        
      case 'customer.subscription.created':
      case 'customer.subscription.updated':
      case 'customer.subscription.deleted':
        await handleSubscriptionChange(event.data.object as Stripe.Subscription, event.type)
        break
        
      default:
        console.log(`Unhandled event type: ${event.type}`)
    }
    
    return NextResponse.json({ received: true })
    
  } catch (error) {
    console.error('Webhook error:', error)
    return NextResponse.json({ error: 'Webhook handler failed' }, { status: 500 })
  }
}

async function handleCheckoutSessionCompleted(session: Stripe.Checkout.Session) {
  try {
    console.log('Processing checkout session completed:', session.id)
    
    const userId = session.metadata?.userId
    const amount = session.amount_total ? session.amount_total / 100 : 0
    
    if (!userId) {
      console.error('No user ID found in session metadata')
      return
    }
    
    // Check if payment already exists
    const { data: existingPayment } = await supabase
      .from('payments')
      .select('id')
      .eq('stripe_session_id', session.id)
      .single()
    
    if (existingPayment) {
      console.log('Payment already processed:', session.id)
      return
    }
    
    // Insert payment record
    const { error: paymentError } = await supabase
      .from('payments')
      .insert({
        user_id: userId,
        stripe_session_id: session.id,
        stripe_payment_intent_id: session.payment_intent,
        amount,
        currency: session.currency || 'usd',
        status: 'completed',
        payment_method: 'stripe',
        customer_email: session.customer_details?.email,
        created_at: new Date().toISOString()
      })
    
    if (paymentError) {
      console.error('Error inserting payment record:', paymentError)
      return
    }
    
    // Update user balance
    const { error: balanceError } = await supabase.rpc('update_user_balance', {
      p_user_id: userId,
      p_amount: amount
    })
    
    if (balanceError) {
      console.error('Error updating user balance:', balanceError)
    }
    
    console.log(`Successfully processed payment for user ${userId}: $${amount}`)
    
  } catch (error) {
    console.error('Error handling checkout session completed:', error)
  }
}

async function handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent) {
  try {
    console.log('Payment intent succeeded:', paymentIntent.id)
    
    // Update payment status if it exists
    const { error } = await supabase
      .from('payments')
      .update({ 
        status: 'completed',
        stripe_payment_intent_id: paymentIntent.id
      })
      .eq('stripe_payment_intent_id', paymentIntent.id)
    
    if (error) {
      console.error('Error updating payment status:', error)
    }
    
  } catch (error) {
    console.error('Error handling payment intent succeeded:', error)
  }
}

async function handlePaymentIntentFailed(paymentIntent: Stripe.PaymentIntent) {
  try {
    console.log('Payment intent failed:', paymentIntent.id)
    
    // Update payment status
    const { error } = await supabase
      .from('payments')
      .update({ 
        status: 'failed',
        failure_reason: paymentIntent.last_payment_error?.message
      })
      .eq('stripe_payment_intent_id', paymentIntent.id)
    
    if (error) {
      console.error('Error updating failed payment status:', error)
    }
    
  } catch (error) {
    console.error('Error handling payment intent failed:', error)
  }
}

async function handleInvoicePaymentSucceeded(invoice: Stripe.Invoice) {
  try {
    console.log('Invoice payment succeeded:', invoice.id)
    
    // Handle subscription payments
    if (invoice.subscription) {
      const subscription = await stripe.subscriptions.retrieve(invoice.subscription as string)
      
      // Update subscription status in database
      const { error } = await supabase
        .from('subscriptions')
        .update({
          status: 'active',
          current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
          current_period_end: new Date(subscription.current_period_end * 1000).toISOString()
        })
        .eq('stripe_subscription_id', subscription.id)
      
      if (error) {
        console.error('Error updating subscription:', error)
      }
    }
    
  } catch (error) {
    console.error('Error handling invoice payment succeeded:', error)
  }
}

async function handleSubscriptionChange(subscription: Stripe.Subscription, eventType: string) {
  try {
    console.log(`Subscription ${eventType}:`, subscription.id)
    
    const userId = subscription.metadata?.userId
    if (!userId) {
      console.error('No user ID found in subscription metadata')
      return
    }
    
    const subscriptionData = {
      user_id: userId,
      stripe_subscription_id: subscription.id,
      stripe_customer_id: subscription.customer as string,
      status: subscription.status,
      price_id: subscription.items.data[0]?.price.id,
      current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
      current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
      cancel_at_period_end: subscription.cancel_at_period_end
    }
    
    if (eventType === 'customer.subscription.created') {
      const { error } = await supabase
        .from('subscriptions')
        .insert(subscriptionData)
      
      if (error) {
        console.error('Error creating subscription record:', error)
      }
    } else {
      const { error } = await supabase
        .from('subscriptions')
        .upsert(subscriptionData)
        .eq('stripe_subscription_id', subscription.id)
      
      if (error) {
        console.error('Error updating subscription record:', error)
      }
    }
    
  } catch (error) {
    console.error('Error handling subscription change:', error)
  }
}
