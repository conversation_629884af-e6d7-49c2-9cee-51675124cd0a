/**
 * Supa<PERSON><PERSON><PERSON><PERSON> - Handles all database operations for LLM portfolio system
 */

import { createClient } from '@supabase/supabase-js'
import type { 
  ISupabaseLogger, 
  LLMRequest, 
  LLMResponse, 
  AuditLogEntry, 
  ApiUsageEntry,
  LLMPromptRecord,
  LLMResponseRecord,
  LLMGeneratedPortfolioRecord,
  PortfolioCacheRecord
} from './types'
import type { PersonalizedPortfolio } from '@/lib/portfolio-generator'

export class SupabaseLogger implements ISupabaseLogger {
  private supabase

  constructor() {
    // Use service role key for backend operations
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    
    this.supabase = createClient(supabaseUrl, serviceRoleKey)
  }

  /**
   * Log an LLM prompt to the database
   */
  async logPrompt(request: LLMRequest): Promise<string> {
    try {
      const promptRecord: Omit<LLMPromptRecord, 'id' | 'created_at'> = {
        user_id: request.userId,
        prompt_hash: request.promptHash,
        prompt_text: request.prompt,
        prompt_type: 'portfolio_generation',
        survey_data: request.promptData.surveyData,
        user_metadata: {
          major: request.promptData.userMajor,
          ...request.promptData.userMetadata,
          ...request.metadata
        }
      }

      const { data, error } = await this.supabase
        .from('llm_prompts')
        .insert(promptRecord)
        .select('id')
        .single()

      if (error) {
        console.error('Error logging prompt:', error)
        throw new Error(`Failed to log prompt: ${error.message}`)
      }

      return data.id
    } catch (error) {
      console.error('SupabaseLogger.logPrompt error:', error)
      throw error
    }
  }

  /**
   * Log an LLM response to the database
   */
  async logResponse(response: LLMResponse): Promise<void> {
    try {
      const responseRecord: Omit<LLMResponseRecord, 'id' | 'created_at'> = {
        prompt_id: response.promptId,
        user_id: response.id, // This should be userId, fixing the mapping
        response_text: response.responseText,
        response_tokens: response.tokensUsed,
        model_used: response.model,
        api_provider: response.provider,
        processing_time_ms: response.processingTimeMs,
        success: response.success,
        error_message: response.errorMessage
      }

      const { error } = await this.supabase
        .from('llm_responses')
        .insert(responseRecord)

      if (error) {
        console.error('Error logging response:', error)
        throw new Error(`Failed to log response: ${error.message}`)
      }
    } catch (error) {
      console.error('SupabaseLogger.logResponse error:', error)
      throw error
    }
  }

  /**
   * Log a generated portfolio to the database
   */
  async logPortfolio(portfolioId: string, portfolio: PersonalizedPortfolio, responseId: string): Promise<void> {
    try {
      // First get the response to find the user_id
      const { data: responseData, error: responseError } = await this.supabase
        .from('llm_responses')
        .select('user_id')
        .eq('id', responseId)
        .single()

      if (responseError) {
        throw new Error(`Failed to get response data: ${responseError.message}`)
      }

      const portfolioRecord: Omit<LLMGeneratedPortfolioRecord, 'id' | 'created_at' | 'updated_at'> = {
        response_id: responseId,
        user_id: responseData.user_id,
        portfolio_data: portfolio,
        risk_level: portfolio.riskLevel,
        expected_return: portfolio.expectedReturn,
        strategy: portfolio.strategy,
        rebalance_frequency: portfolio.rebalanceFrequency,
        rationale: portfolio.rationale,
        allocations: portfolio.allocations,
        validation_status: 'pending',
        is_active: true
      }

      const { error } = await this.supabase
        .from('llm_generated_portfolios')
        .insert(portfolioRecord)

      if (error) {
        console.error('Error logging portfolio:', error)
        throw new Error(`Failed to log portfolio: ${error.message}`)
      }
    } catch (error) {
      console.error('SupabaseLogger.logPortfolio error:', error)
      throw error
    }
  }

  /**
   * Log an audit entry
   */
  async logAudit(entry: Omit<AuditLogEntry, 'id' | 'createdAt'>): Promise<void> {
    try {
      const auditRecord = {
        user_id: entry.userId,
        action: entry.action,
        details: entry.details,
        ip_address: entry.ipAddress,
        user_agent: entry.userAgent,
        api_endpoint: entry.apiEndpoint,
        execution_time_ms: entry.executionTimeMs,
        success: entry.success,
        error_details: entry.errorDetails
      }

      const { error } = await this.supabase
        .from('llm_audit_logs')
        .insert(auditRecord)

      if (error) {
        console.error('Error logging audit entry:', error)
        // Don't throw here to avoid breaking the main flow
      }
    } catch (error) {
      console.error('SupabaseLogger.logAudit error:', error)
      // Don't throw here to avoid breaking the main flow
    }
  }

  /**
   * SECURITY: Log security-related events
   */
  async logSecurityEvent(event: {
    userId: string
    eventType: 'input_sanitization' | 'response_filtering' | 'prompt_injection_attempt' | 'suspicious_activity'
    severity: 'low' | 'medium' | 'high' | 'critical'
    details: Record<string, any>
    ipAddress?: string
    userAgent?: string
    endpoint?: string
  }): Promise<void> {
    try {
      await this.logAudit({
        userId: event.userId,
        action: `security_${event.eventType}`,
        details: {
          ...event.details,
          severity: event.severity,
          timestamp: new Date().toISOString(),
          securityEvent: true
        },
        ipAddress: event.ipAddress,
        userAgent: event.userAgent,
        apiEndpoint: event.endpoint,
        success: true
      })
    } catch (error) {
      console.error('Failed to log security event:', error)
    }
  }

  /**
   * SECURITY: Log prompt sanitization events
   */
  async logPromptSanitization(
    userId: string,
    originalPrompt: string,
    sanitizedPrompt: string,
    modificationsApplied: string[],
    riskLevel: 'low' | 'medium' | 'high'
  ): Promise<void> {
    await this.logSecurityEvent({
      userId,
      eventType: 'input_sanitization',
      severity: riskLevel === 'high' ? 'high' : riskLevel === 'medium' ? 'medium' : 'low',
      details: {
        originalLength: originalPrompt.length,
        sanitizedLength: sanitizedPrompt.length,
        modificationsApplied,
        riskLevel,
        changesDetected: originalPrompt !== sanitizedPrompt
      }
    })
  }

  /**
   * SECURITY: Log response filtering events
   */
  async logResponseFiltering(
    userId: string,
    originalResponse: string,
    filteredResponse: string,
    modificationsApplied: string[],
    containsSensitiveData: boolean
  ): Promise<void> {
    await this.logSecurityEvent({
      userId,
      eventType: 'response_filtering',
      severity: containsSensitiveData ? 'high' : modificationsApplied.length > 0 ? 'medium' : 'low',
      details: {
        originalLength: originalResponse.length,
        filteredLength: filteredResponse.length,
        modificationsApplied,
        containsSensitiveData,
        changesDetected: originalResponse !== filteredResponse
      }
    })
  }

  /**
   * SECURITY: Log suspicious activity
   */
  async logSuspiciousActivity(
    userId: string,
    activityType: string,
    details: Record<string, any>,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    await this.logSecurityEvent({
      userId,
      eventType: 'suspicious_activity',
      severity: 'high',
      details: {
        activityType,
        ...details,
        flaggedAt: new Date().toISOString()
      },
      ipAddress,
      userAgent
    })
  }

  /**
   * Log API usage for cost tracking
   */
  async logApiUsage(usage: Omit<ApiUsageEntry, 'id' | 'createdAt'>): Promise<void> {
    try {
      const usageRecord = {
        user_id: usage.userId,
        api_provider: usage.apiProvider,
        model_used: usage.model,
        tokens_used: usage.tokensUsed,
        cost_usd: usage.costUsd,
        request_type: usage.requestType,
        success: usage.success
      }

      const { error } = await this.supabase
        .from('llm_api_usage')
        .insert(usageRecord)

      if (error) {
        console.error('Error logging API usage:', error)
        // Don't throw here to avoid breaking the main flow
      }
    } catch (error) {
      console.error('SupabaseLogger.logApiUsage error:', error)
      // Don't throw here to avoid breaking the main flow
    }
  }

  /**
   * Get user's portfolio generation history
   */
  async getUserPortfolioHistory(userId: string, limit: number = 10): Promise<LLMGeneratedPortfolioRecord[]> {
    try {
      const { data, error } = await this.supabase
        .from('llm_generated_portfolios')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .order('created_at', { ascending: false })
        .limit(limit)

      if (error) {
        throw new Error(`Failed to get portfolio history: ${error.message}`)
      }

      return data || []
    } catch (error) {
      console.error('SupabaseLogger.getUserPortfolioHistory error:', error)
      throw error
    }
  }

  /**
   * Get user's generation statistics
   */
  async getUserStats(userId: string): Promise<{
    totalGenerations: number
    cacheHits: number
    fallbackUses: number
    avgProcessingTime: number
    lastGeneration: Date | null
  }> {
    try {
      const { data, error } = await this.supabase
        .rpc('get_user_portfolio_stats', { p_user_id: userId })

      if (error) {
        throw new Error(`Failed to get user stats: ${error.message}`)
      }

      const stats = data?.[0] || {
        total_generations: 0,
        cache_hits: 0,
        fallback_uses: 0,
        avg_processing_time: 0,
        last_generation: null
      }

      return {
        totalGenerations: stats.total_generations,
        cacheHits: stats.cache_hits,
        fallbackUses: stats.fallback_uses,
        avgProcessingTime: stats.avg_processing_time,
        lastGeneration: stats.last_generation ? new Date(stats.last_generation) : null
      }
    } catch (error) {
      console.error('SupabaseLogger.getUserStats error:', error)
      return {
        totalGenerations: 0,
        cacheHits: 0,
        fallbackUses: 0,
        avgProcessingTime: 0,
        lastGeneration: null
      }
    }
  }

  /**
   * Update portfolio validation status
   */
  async updatePortfolioValidation(portfolioId: string, isValid: boolean, errors?: string[]): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('llm_generated_portfolios')
        .update({
          validation_status: isValid ? 'valid' : 'invalid',
          validation_errors: errors ? { errors } : null,
          updated_at: new Date().toISOString()
        })
        .eq('id', portfolioId)

      if (error) {
        throw new Error(`Failed to update portfolio validation: ${error.message}`)
      }
    } catch (error) {
      console.error('SupabaseLogger.updatePortfolioValidation error:', error)
      throw error
    }
  }

  /**
   * Clean up old audit logs (keep last 30 days)
   */
  async cleanupOldLogs(): Promise<number> {
    try {
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

      const { data, error } = await this.supabase
        .from('llm_audit_logs')
        .delete()
        .lt('created_at', thirtyDaysAgo.toISOString())
        .select('id')

      if (error) {
        throw new Error(`Failed to cleanup old logs: ${error.message}`)
      }

      return data?.length || 0
    } catch (error) {
      console.error('SupabaseLogger.cleanupOldLogs error:', error)
      return 0
    }
  }

  /**
   * Get API usage summary for a user
   */
  async getApiUsageSummary(userId: string, days: number = 30): Promise<{
    totalRequests: number
    totalTokens: number
    totalCost: number
    byProvider: Record<string, { requests: number; tokens: number; cost: number }>
  }> {
    try {
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - days)

      const { data, error } = await this.supabase
        .from('llm_api_usage')
        .select('*')
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString())

      if (error) {
        throw new Error(`Failed to get API usage summary: ${error.message}`)
      }

      const summary = {
        totalRequests: 0,
        totalTokens: 0,
        totalCost: 0,
        byProvider: {} as Record<string, { requests: number; tokens: number; cost: number }>
      }

      data?.forEach(usage => {
        summary.totalRequests++
        summary.totalTokens += usage.tokens_used
        summary.totalCost += usage.cost_usd

        if (!summary.byProvider[usage.api_provider]) {
          summary.byProvider[usage.api_provider] = { requests: 0, tokens: 0, cost: 0 }
        }

        summary.byProvider[usage.api_provider].requests++
        summary.byProvider[usage.api_provider].tokens += usage.tokens_used
        summary.byProvider[usage.api_provider].cost += usage.cost_usd
      })

      return summary
    } catch (error) {
      console.error('SupabaseLogger.getApiUsageSummary error:', error)
      return {
        totalRequests: 0,
        totalTokens: 0,
        totalCost: 0,
        byProvider: {}
      }
    }
  }
}
