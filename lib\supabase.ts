import { createBrowserClient } from "@supabase/ssr"

// Use environment variables properly
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

// Check if Supabase is properly configured
export const isSupabaseConfigured = () => {
  return (
    supabaseUrl &&
    supabaseAnonKey &&
    supabaseUrl !== "your_supabase_url_here" &&
    supabaseAnonKey !== "your_supabase_anon_key_here" &&
    supabaseUrl.includes("supabase.co")
  )
}

export const createClient = () => {
  if (!isSupabaseConfigured()) {
    throw new Error(
      "Supabase environment variables not configured. Please set NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY in your .env.local file."
    )
  }

  return createBrowserClient(supabaseUrl!, supabaseAnonKey!)
}

// Create a singleton client instance
export const supabase = isSupabaseConfigured() ? createClient() : null

// Database Types
export interface Database {
  public: {
    Tables: {
      user_profiles: {
        Row: UserProfile
        Insert: Omit<UserProfile, 'created_at' | 'updated_at'>
        Update: Partial<Omit<UserProfile, 'id' | 'created_at' | 'updated_at'>>
      }
      portfolios: {
        Row: Portfolio
        Insert: Omit<Portfolio, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<Portfolio, 'id' | 'created_at' | 'updated_at'>>
      }
      portfolio_holdings: {
        Row: PortfolioHolding
        Insert: Omit<PortfolioHolding, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<PortfolioHolding, 'id' | 'created_at' | 'updated_at'>>
      }
      transactions: {
        Row: Transaction
        Insert: Omit<Transaction, 'id' | 'created_at'>
        Update: Partial<Omit<Transaction, 'id' | 'created_at'>>
      }
      learning_progress: {
        Row: LearningProgress
        Insert: Omit<LearningProgress, 'id' | 'created_at'>
        Update: Partial<Omit<LearningProgress, 'id' | 'created_at'>>
      }
      user_settings: {
        Row: UserSettings
        Insert: Omit<UserSettings, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<UserSettings, 'id' | 'created_at' | 'updated_at'>>
      }
      financial_goals: {
        Row: FinancialGoal
        Insert: Omit<FinancialGoal, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<FinancialGoal, 'id' | 'created_at' | 'updated_at'>>
      }
      notifications: {
        Row: Notification
        Insert: Omit<Notification, 'id' | 'created_at'>
        Update: Partial<Omit<Notification, 'id' | 'created_at'>>
      }
      notification_settings: {
        Row: NotificationSettings
        Insert: Omit<NotificationSettings, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<NotificationSettings, 'id' | 'created_at' | 'updated_at'>>
      }
      stocks: {
        Row: Stock
        Insert: Omit<Stock, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<Stock, 'id' | 'created_at' | 'updated_at'>>
      }
    }
  }
}

// User Profile Types
export interface UserProfile {
  id: string
  first_name: string | null
  last_name: string | null
  email: string | null
  major: string | null
  graduation_year: number | null
  university: string | null
  is_student: boolean
  risk_tolerance: 'conservative' | 'moderate' | 'aggressive' | null
  investment_goals: string[] | null
  monthly_budget: number | null
  onboarding_completed: boolean
  created_at: string
  updated_at: string
}

export interface Portfolio {
  id: string
  user_id: string
  name: string
  description: string | null
  total_value: number
  is_default: boolean
  created_at: string
  updated_at: string
}

export interface PortfolioHolding {
  id: string
  portfolio_id: string
  symbol: string
  shares: number
  average_cost: number
  current_price: number | null
  created_at: string
  updated_at: string
}

export interface Transaction {
  id: string
  user_id: string
  portfolio_id: string
  symbol: string
  transaction_type: 'buy' | 'sell'
  shares: number
  price: number
  total_amount: number
  fees: number
  transaction_date: string
  created_at: string
}

export interface LearningProgress {
  id: string
  user_id: string
  course_id: string
  lesson_id: string
  completed: boolean
  completion_date: string | null
  created_at: string
}

export interface UserSettings {
  id: string
  user_id: string
  theme: string
  notifications_enabled: boolean
  email_notifications: boolean
  push_notifications: boolean
  created_at: string
  updated_at: string
}

export interface FinancialGoal {
  id: string
  user_id: string
  title: string
  description: string | null
  target_amount: number
  current_amount: number
  target_date: string
  category: string
  priority: 'low' | 'medium' | 'high'
  is_completed: boolean
  created_at: string
  updated_at: string
}

export interface Notification {
  id: string
  user_id: string
  type: 'portfolio' | 'goal' | 'market' | 'educational' | 'system'
  title: string
  message: string
  is_read: boolean
  created_at: string
}

export interface NotificationSettings {
  id: string
  user_id: string
  portfolio_updates: boolean
  goal_reminders: boolean
  market_alerts: boolean
  educational_content: boolean
  system_notifications: boolean
  email_notifications: boolean
  created_at: string
  updated_at: string
}

// Stock Types (existing)
export interface Stock {
  id: number
  ticker: string
  company_name: string
  description: string | null
  sector: string | null
  exchange: string | null
  market_cap: string | null
  asset_class: string | null
  assets: any
  stock_type: string | null
  embedding: any
  cluster_id: number | null
  metadata: any
  created_at: string
  updated_at: string
}

export interface StockWithTags extends Stock {
  tags: any // JSON field
}

export interface StockTag {
  stock_id: number
  tag_id: number
  confidence: number | null
  created_at: string
}

// Transform database stock to UI format
export function transformStockData(stock: Stock | StockWithTags): any {
  return {
    id: stock.id,
    symbol: stock.ticker,
    name: stock.company_name,
    price: Math.random() * 500 + 50, // Mock price - replace with real data
    change: (Math.random() - 0.5) * 20, // Mock change
    changePercent: (Math.random() - 0.5) * 5, // Mock percentage
    category: stock.sector || "Unknown",
    sector: stock.sector || "Unknown",
    market_cap: stock.market_cap || "N/A",
    tags: "tags" in stock && stock.tags ? (Array.isArray(stock.tags) ? stock.tags : []) : [],
    description: stock.description || "",
    volume: Math.floor(Math.random() * 10000000) + 1000000, // Mock volume
    ticker: stock.ticker,
    company_name: stock.company_name,
    exchange: stock.exchange || "N/A",
    asset_class: stock.asset_class || "Equity",
    stock_type: stock.stock_type || "Common Stock",
  }
}

// Search stocks by query
export async function searchStocks(query: string, limit = 20) {
  try {
    if (!isSupabaseConfigured() || !supabase) {
      console.warn("Supabase not configured - cannot search stocks")
      return []
    }

    // First try stocks_with_tags
    const { data: stocksWithTags, error: tagsError } = await supabase
      .from("stocks_with_tags")
      .select("*")
      .or(`ticker.ilike.%${query}%,company_name.ilike.%${query}%,sector.ilike.%${query}%`)
      .limit(limit)

    if (stocksWithTags && stocksWithTags.length > 0) {
      return stocksWithTags.map(transformStockData)
    }

    // Fallback to regular stocks table
    const { data: stocks, error } = await supabase
      .from("stocks")
      .select("*")
      .or(`ticker.ilike.%${query}%,company_name.ilike.%${query}%,sector.ilike.%${query}%`)
      .limit(limit)

    if (error) throw error

    return (stocks || []).map(transformStockData)
  } catch (error) {
    console.error("Error searching stocks:", error)
    return []
  }
}

// Get stocks by sector
export async function getStocksBySector(sector: string, limit = 50) {
  try {
    if (!isSupabaseConfigured() || !supabase) {
      console.warn("Supabase not configured - cannot get stocks by sector")
      return []
    }

    // First try stocks_with_tags
    const { data: stocksWithTags, error: tagsError } = await supabase
      .from("stocks_with_tags")
      .select("*")
      .ilike("sector", `%${sector}%`)
      .limit(limit)

    if (stocksWithTags && stocksWithTags.length > 0) {
      return stocksWithTags.map(transformStockData)
    }

    // Fallback to regular stocks table
    const { data: stocks, error } = await supabase
      .from("stocks")
      .select("*")
      .ilike("sector", `%${sector}%`)
      .limit(limit)

    if (error) throw error

    return (stocks || []).map(transformStockData)
  } catch (error) {
    console.error("Error getting stocks by sector:", error)
    return []
  }
}

// Get stock by symbol
export async function getStockBySymbol(symbol: string) {
  try {
    if (!isSupabaseConfigured() || !supabase) {
      console.warn("Supabase not configured - cannot get stock by symbol")
      return null
    }

    // First try stocks_with_tags
    const { data: stockWithTags, error: tagsError } = await supabase
      .from("stocks_with_tags")
      .select("*")
      .eq("ticker", symbol.toUpperCase())
      .single()

    if (stockWithTags) {
      return transformStockData(stockWithTags)
    }

    // Fallback to regular stocks table
    const { data: stock, error } = await supabase.from("stocks").select("*").eq("ticker", symbol.toUpperCase()).single()

    if (error) throw error

    return stock ? transformStockData(stock) : null
  } catch (error) {
    console.error("Error getting stock by symbol:", error)
    return null
  }
}

// Get all stocks
export async function getAllStocks(limit = 100) {
  try {
    if (!isSupabaseConfigured() || !supabase) {
      console.warn("Supabase not configured - cannot get all stocks")
      return []
    }

    // First try stocks_with_tags
    const { data: stocksWithTags, error: tagsError } = await supabase.from("stocks_with_tags").select("*").limit(limit)

    if (stocksWithTags && stocksWithTags.length > 0) {
      return stocksWithTags.map(transformStockData)
    }

    // Fallback to regular stocks table
    const { data: stocks, error } = await supabase.from("stocks").select("*").limit(limit)

    if (error) throw error

    return (stocks || []).map(transformStockData)
  } catch (error) {
    console.error("Error getting all stocks:", error)
    return []
  }
}

// Get stock count
export async function getStockCount(): Promise<number> {
  try {
    if (!isSupabaseConfigured() || !supabase) {
      console.warn("Supabase not configured - cannot get stock count")
      return 0
    }

    // First try stocks_with_tags table
    const { count: tagsCount, error: tagsError } = await supabase
      .from("stocks_with_tags")
      .select("*", { count: "exact", head: true })

    if (!tagsError && tagsCount !== null && tagsCount > 0) {
      return tagsCount
    }

    // Fallback to regular stocks table
    const { count, error } = await supabase.from("stocks").select("*", { count: "exact", head: true })

    if (error) throw error

    return count || 0
  } catch (error) {
    console.error("Error getting stock count:", error)
    return 0
  }
}


