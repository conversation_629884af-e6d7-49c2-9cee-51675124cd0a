import { NextResponse } from 'next/server'

// Temporary mock implementation until Plaid package is installed
// TODO: Replace with actual Plaid implementation after running npm install

export async function POST(request: Request) {
  try {
    const { userId } = await request.json()

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    // Mock implementation for demo purposes
    // TODO: Replace with actual Plaid API calls after npm install
    const mockLinkToken = 'link-sandbox-' + Math.random().toString(36).substr(2, 9)
    const mockExpiration = new Date(Date.now() + 4 * 60 * 60 * 1000).toISOString() // 4 hours from now

    return NextResponse.json({
      link_token: mockLinkToken,
      expiration: mockExpiration,
    })

  } catch (error) {
    console.error('Error creating Plaid link token:', error)
    return NextResponse.json(
      { error: 'Failed to create link token' },
      { status: 500 }
    )
  }
}
