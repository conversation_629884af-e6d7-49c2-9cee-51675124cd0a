/**
 * Data Flow Integration
 * Ensures seamless data flow from Supabase and market data providers to frontend
 */

import { createClient } from './supabase'
import { getStockHistoryService } from './stock-history-service'
import { getStockDataService } from './stock-config'
import { getUserCapitalResolver } from './user-capital-resolver'
import { getPortfolioStockIntegration } from './portfolio-stock-integration'

export interface DataFlowResult<T> {
  success: boolean
  data?: T
  error?: string
  source: string
  cached: boolean
  timestamp: string
}

export interface HealthCheckResult {
  service: string
  status: 'healthy' | 'degraded' | 'unhealthy'
  responseTime: number
  error?: string
  lastCheck: string
}

export interface SystemHealthStatus {
  overall: 'healthy' | 'degraded' | 'unhealthy'
  services: HealthCheckResult[]
  timestamp: string
}

export class DataFlowIntegration {
  private supabase = createClient()
  private stockHistoryService = getStockHistoryService()
  private stockDataService = getStockDataService()
  private capitalResolver = getUserCapitalResolver()
  private portfolioIntegration = getPortfolioStockIntegration()

  /**
   * Comprehensive health check for all services
   */
  async performHealthCheck(): Promise<SystemHealthStatus> {
    const services: HealthCheckResult[] = []
    
    // Check Supabase connection
    services.push(await this.checkSupabaseHealth())
    
    // Check stock data services
    services.push(await this.checkStockDataHealth())
    
    // Check stock history service
    services.push(await this.checkStockHistoryHealth())
    
    // Check portfolio integration
    services.push(await this.checkPortfolioIntegrationHealth())

    // Determine overall health
    const healthyCount = services.filter(s => s.status === 'healthy').length
    const degradedCount = services.filter(s => s.status === 'degraded').length
    
    let overall: 'healthy' | 'degraded' | 'unhealthy'
    if (healthyCount === services.length) {
      overall = 'healthy'
    } else if (healthyCount + degradedCount >= services.length * 0.7) {
      overall = 'degraded'
    } else {
      overall = 'unhealthy'
    }

    return {
      overall,
      services,
      timestamp: new Date().toISOString()
    }
  }

  /**
   * Test complete data flow for a user
   */
  async testUserDataFlow(userId: string): Promise<DataFlowResult<any>> {
    const startTime = Date.now()
    
    try {
      // Test 1: User capital data
      const capitalSummary = await this.capitalResolver.getUserCapitalSummary(userId)
      if (!capitalSummary) {
        return {
          success: false,
          error: 'Failed to retrieve user capital data',
          source: 'user_capital',
          cached: false,
          timestamp: new Date().toISOString()
        }
      }

      // Test 2: Portfolio data
      const portfolioData = await this.portfolioIntegration.calculatePortfolioPerformance(userId, '1M')
      if (!portfolioData) {
        return {
          success: false,
          error: 'Failed to calculate portfolio performance',
          source: 'portfolio_integration',
          cached: false,
          timestamp: new Date().toISOString()
        }
      }

      // Test 3: Stock data (if user has holdings)
      let stockDataTest = null
      if (portfolioData.holdings.length > 0) {
        const testSymbol = portfolioData.holdings[0].symbol
        try {
          stockDataTest = await this.stockHistoryService.getHistoricalData(testSymbol, '1W')
        } catch (error) {
          console.warn('Stock data test failed:', error)
        }
      }

      const responseTime = Date.now() - startTime

      return {
        success: true,
        data: {
          userId,
          capitalSummary,
          portfolioData: {
            totalValue: portfolioData.currentMetrics.totalValue,
            holdingsCount: portfolioData.currentMetrics.holdingsCount,
            timeSeriesPoints: portfolioData.timeSeries.length
          },
          stockDataTest: stockDataTest ? {
            symbol: stockDataTest.symbol,
            dataPoints: stockDataTest.dataPoints,
            source: stockDataTest.source,
            cached: stockDataTest.cached
          } : null,
          responseTime
        },
        source: 'complete_flow',
        cached: false,
        timestamp: new Date().toISOString()
      }

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        source: 'complete_flow',
        cached: false,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Validate data consistency across services
   */
  async validateDataConsistency(userId: string): Promise<DataFlowResult<any>> {
    try {
      // Get data from multiple sources
      const [capitalSummary, portfolioData] = await Promise.all([
        this.capitalResolver.getUserCapitalSummary(userId),
        this.portfolioIntegration.calculatePortfolioPerformance(userId, '1M')
      ])

      if (!capitalSummary || !portfolioData) {
        return {
          success: false,
          error: 'Missing data from one or more sources',
          source: 'consistency_check',
          cached: false,
          timestamp: new Date().toISOString()
        }
      }

      // Check consistency between capital and portfolio data
      const capitalTotal = capitalSummary.totalCash + capitalSummary.totalInvested
      const portfolioTotal = portfolioData.currentMetrics.totalValue
      const difference = Math.abs(capitalTotal - portfolioTotal)
      const tolerance = Math.max(capitalTotal * 0.01, 1) // 1% tolerance or $1

      const isConsistent = difference <= tolerance

      return {
        success: true,
        data: {
          consistent: isConsistent,
          capitalTotal,
          portfolioTotal,
          difference,
          tolerance,
          details: {
            capitalBreakdown: {
              cash: capitalSummary.totalCash,
              invested: capitalSummary.totalInvested
            },
            portfolioBreakdown: {
              cash: portfolioData.currentMetrics.totalCash,
              invested: portfolioData.currentMetrics.totalInvested
            }
          }
        },
        source: 'consistency_check',
        cached: false,
        timestamp: new Date().toISOString()
      }

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Consistency check failed',
        source: 'consistency_check',
        cached: false,
        timestamp: new Date().toISOString()
      }
    }
  }

  // Private health check methods
  private async checkSupabaseHealth(): Promise<HealthCheckResult> {
    const startTime = Date.now()
    
    try {
      const { data, error } = await this.supabase
        .from('portfolios')
        .select('count')
        .limit(1)

      const responseTime = Date.now() - startTime

      if (error) {
        return {
          service: 'supabase',
          status: 'unhealthy',
          responseTime,
          error: error.message,
          lastCheck: new Date().toISOString()
        }
      }

      return {
        service: 'supabase',
        status: responseTime < 1000 ? 'healthy' : 'degraded',
        responseTime,
        lastCheck: new Date().toISOString()
      }

    } catch (error) {
      return {
        service: 'supabase',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Connection failed',
        lastCheck: new Date().toISOString()
      }
    }
  }

  private async checkStockDataHealth(): Promise<HealthCheckResult> {
    const startTime = Date.now()
    
    try {
      const quote = await this.stockDataService.getStockQuote('AAPL')
      const responseTime = Date.now() - startTime

      if (!quote || !quote.price) {
        return {
          service: 'stock_data',
          status: 'degraded',
          responseTime,
          error: 'No price data returned',
          lastCheck: new Date().toISOString()
        }
      }

      return {
        service: 'stock_data',
        status: responseTime < 2000 ? 'healthy' : 'degraded',
        responseTime,
        lastCheck: new Date().toISOString()
      }

    } catch (error) {
      return {
        service: 'stock_data',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Service unavailable',
        lastCheck: new Date().toISOString()
      }
    }
  }

  private async checkStockHistoryHealth(): Promise<HealthCheckResult> {
    const startTime = Date.now()
    
    try {
      const history = await this.stockHistoryService.getHistoricalData('AAPL', '1W')
      const responseTime = Date.now() - startTime

      if (!history || !history.data || history.data.length === 0) {
        return {
          service: 'stock_history',
          status: 'degraded',
          responseTime,
          error: 'No historical data returned',
          lastCheck: new Date().toISOString()
        }
      }

      return {
        service: 'stock_history',
        status: responseTime < 3000 ? 'healthy' : 'degraded',
        responseTime,
        lastCheck: new Date().toISOString()
      }

    } catch (error) {
      return {
        service: 'stock_history',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Service unavailable',
        lastCheck: new Date().toISOString()
      }
    }
  }

  private async checkPortfolioIntegrationHealth(): Promise<HealthCheckResult> {
    const startTime = Date.now()
    
    try {
      // Test with a dummy user ID (this should handle gracefully)
      const testUserId = '00000000-0000-0000-0000-000000000000'
      const result = await this.portfolioIntegration.calculatePortfolioPerformance(testUserId, '1M')
      const responseTime = Date.now() - startTime

      // For test user, we expect null result but no errors
      return {
        service: 'portfolio_integration',
        status: responseTime < 2000 ? 'healthy' : 'degraded',
        responseTime,
        lastCheck: new Date().toISOString()
      }

    } catch (error) {
      return {
        service: 'portfolio_integration',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Service unavailable',
        lastCheck: new Date().toISOString()
      }
    }
  }
}

// Singleton instance
let dataFlowIntegration: DataFlowIntegration | null = null

export function getDataFlowIntegration(): DataFlowIntegration {
  if (!dataFlowIntegration) {
    dataFlowIntegration = new DataFlowIntegration()
  }
  return dataFlowIntegration
}
