// Test the stock data fixes
// Run with: node scripts/test-fixes.js

const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://fiwhyrlkcojfeovdfrjn.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZpd2h5cmxrY29qZmVvdmRmcmpuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxMDI4ODQsImV4cCI6MjA2NzY3ODg4NH0.9JLGagCUl0URZwx6QA67JsG5fTQmg28QE7AQlroyuLg'

const supabase = createClient(supabaseUrl, supabaseKey)

async function testFixes() {
  console.log('🔍 Testing stock data fixes...\n')

  try {
    // Test 1: Check if AAPL exists and has proper data
    console.log('📊 Test 1: AAPL Data Quality')
    const { data: aapl, error: aaplError } = await supabase
      .from('stocks_with_tags')
      .select('*')
      .eq('ticker', 'AAPL')
      .single()

    if (aaplError) {
      console.log('❌ AAPL not found or error:', aaplError.message)
    } else if (aapl) {
      console.log('✅ AAPL found successfully')
      console.log(`📋 Company: ${aapl.company_name}`)
      console.log(`📋 Sector: ${aapl.sector || 'NULL'}`)
      console.log(`📋 Exchange: ${aapl.exchange || 'NULL'}`)
      console.log(`📋 Market Cap: ${aapl.market_cap || 'NULL'}`)
      console.log(`📋 Stock Type: ${aapl.stock_type || 'NULL'}`)
      console.log(`📋 Asset Class: ${aapl.asset_class || 'NULL'}`)
    }

    console.log('\n' + '='.repeat(50) + '\n')

    // Test 2: Check sector distribution
    console.log('📊 Test 2: Sector Distribution')
    const { data: sectors, error: sectorError } = await supabase
      .from('stocks')
      .select('sector')
      .not('sector', 'is', null)

    if (sectorError) {
      console.log('❌ Error getting sectors:', sectorError.message)
    } else {
      const sectorCounts = {}
      sectors.forEach(stock => {
        sectorCounts[stock.sector] = (sectorCounts[stock.sector] || 0) + 1
      })
      
      console.log('✅ Sector distribution:')
      Object.entries(sectorCounts)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 10)
        .forEach(([sector, count]) => {
          console.log(`📋 ${sector}: ${count} stocks`)
        })
    }

    console.log('\n' + '='.repeat(50) + '\n')

    // Test 3: Check for null values
    console.log('📊 Test 3: Data Completeness Check')
    
    const { count: nullSectors } = await supabase
      .from('stocks')
      .select('*', { count: 'exact', head: true })
      .is('sector', null)

    const { count: nullExchanges } = await supabase
      .from('stocks')
      .select('*', { count: 'exact', head: true })
      .is('exchange', null)

    const { count: nullMarketCaps } = await supabase
      .from('stocks')
      .select('*', { count: 'exact', head: true })
      .is('market_cap', null)

    console.log(`📊 Null sectors: ${nullSectors}`)
    console.log(`📊 Null exchanges: ${nullExchanges}`)
    console.log(`📊 Null market caps: ${nullMarketCaps}`)

    console.log('\n' + '='.repeat(50) + '\n')

    // Test 4: Test search functionality
    console.log('📊 Test 4: Search Functionality')
    
    const { data: techStocks, error: searchError } = await supabase
      .from('stocks_with_tags')
      .select('ticker, company_name, sector')
      .eq('sector', 'Technology')
      .limit(5)

    if (searchError) {
      console.log('❌ Error searching tech stocks:', searchError.message)
    } else {
      console.log('✅ Technology stocks found:')
      techStocks.forEach(stock => {
        console.log(`📋 ${stock.ticker} - ${stock.company_name}`)
      })
    }

    console.log('\n' + '='.repeat(50) + '\n')

    // Test 5: Test the app's search query
    console.log('📊 Test 5: App Search Query Test')
    
    const { data: searchResults, error: appSearchError } = await supabase
      .from('stocks_with_tags')
      .select('ticker, company_name, sector, market_cap')
      .or('ticker.ilike.%AAPL%,company_name.ilike.%Apple%,sector.ilike.%Technology%')
      .limit(5)

    if (appSearchError) {
      console.log('❌ Error with app search:', appSearchError.message)
    } else {
      console.log('✅ App search results:')
      searchResults.forEach(stock => {
        console.log(`📋 ${stock.ticker} - ${stock.company_name} (${stock.sector}) - ${stock.market_cap}`)
      })
    }

    console.log('\n' + '='.repeat(50) + '\n')

    // Test 6: Check tags structure
    console.log('📊 Test 6: Tags Structure')
    
    const { data: stockWithTags, error: tagsError } = await supabase
      .from('stocks_with_tags')
      .select('ticker, company_name, tags')
      .not('tags', 'is', null)
      .limit(3)

    if (tagsError) {
      console.log('❌ Error getting tags:', tagsError.message)
    } else {
      console.log('✅ Sample stocks with tags:')
      stockWithTags.forEach(stock => {
        console.log(`📋 ${stock.ticker} - ${stock.company_name}`)
        console.log(`   Tags: ${JSON.stringify(stock.tags, null, 2)}`)
      })
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error.message)
  }
}

testFixes().then(() => {
  console.log('\n🎉 Stock data fixes test completed!')
  process.exit(0)
}).catch(error => {
  console.error('💥 Test failed:', error)
  process.exit(1)
})
