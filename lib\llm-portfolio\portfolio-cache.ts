/**
 * PortfolioCache - Intelligent caching system for LLM portfolio results
 */

import { createClient } from '@supabase/supabase-js'
import type { 
  IPortfolioCache, 
  CacheEntry, 
  CacheConfig,
  PortfolioCacheRecord 
} from './types'
import type { PersonalizedPortfolio } from '@/lib/portfolio-generator'

export class Port<PERSON><PERSON><PERSON><PERSON> implements IPortfolioCache {
  private supabase
  private config: CacheConfig
  private memoryCache: Map<string, CacheEntry> = new Map()
  private cleanupInterval?: NodeJS.Timeout

  constructor(config: CacheConfig) {
    this.config = config
    
    // Use service role key for backend operations
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    
    this.supabase = createClient(supabaseUrl, serviceRoleKey)
    
    // Start cleanup interval
    this.startCleanupInterval()
  }

  /**
   * Get a cached portfolio entry
   */
  async get(cacheKey: string): Promise<CacheEntry | null> {
    try {
      // First check memory cache
      const memoryEntry = this.memoryCache.get(cacheKey)
      if (memoryEntry && memoryEntry.expiresAt > new Date()) {
        // Update hit count and last accessed
        await this.updateHitCount(cacheKey)
        return memoryEntry
      }

      // Check database cache
      const { data, error } = await this.supabase
        .from('portfolio_cache')
        .select(`
          *,
          llm_generated_portfolios!inner(portfolio_data)
        `)
        .eq('cache_key', cacheKey)
        .gt('expires_at', new Date().toISOString())
        .single()

      if (error || !data) {
        return null
      }

      // Convert database record to CacheEntry
      const cacheEntry: CacheEntry = {
        id: data.id,
        cacheKey: data.cache_key,
        userId: data.user_id,
        promptHash: data.prompt_hash,
        portfolioData: data.llm_generated_portfolios.portfolio_data,
        hitCount: data.hit_count,
        lastAccessed: new Date(data.last_accessed),
        expiresAt: new Date(data.expires_at),
        createdAt: new Date(data.created_at)
      }

      // Store in memory cache for faster subsequent access
      this.memoryCache.set(cacheKey, cacheEntry)

      // Update hit count
      await this.updateHitCount(cacheKey)

      return cacheEntry
    } catch (error) {
      console.error('PortfolioCache.get error:', error)
      return null
    }
  }

  /**
   * Set a portfolio in the cache
   */
  async set(
    cacheKey: string, 
    portfolio: PersonalizedPortfolio, 
    userId: string, 
    promptHash: string
  ): Promise<void> {
    try {
      const expiresAt = new Date()
      expiresAt.setHours(expiresAt.getHours() + this.config.ttlHours)

      // First, create the portfolio record
      const { data: portfolioData, error: portfolioError } = await this.supabase
        .from('llm_generated_portfolios')
        .insert({
          response_id: null, // Will be updated when response is logged
          user_id: userId,
          portfolio_data: portfolio,
          risk_level: portfolio.riskLevel,
          expected_return: portfolio.expectedReturn,
          strategy: portfolio.strategy,
          rebalance_frequency: portfolio.rebalanceFrequency,
          rationale: portfolio.rationale,
          allocations: portfolio.allocations,
          validation_status: 'valid',
          is_active: true
        })
        .select('id')
        .single()

      if (portfolioError) {
        throw new Error(`Failed to create portfolio record: ${portfolioError.message}`)
      }

      // Then create the cache entry
      const cacheRecord: Omit<PortfolioCacheRecord, 'id' | 'created_at'> = {
        cache_key: cacheKey,
        user_id: userId,
        prompt_hash: promptHash,
        portfolio_id: portfolioData.id,
        hit_count: 0,
        last_accessed: new Date().toISOString(),
        expires_at: expiresAt.toISOString()
      }

      const { error: cacheError } = await this.supabase
        .from('portfolio_cache')
        .upsert(cacheRecord, { onConflict: 'cache_key' })

      if (cacheError) {
        throw new Error(`Failed to create cache entry: ${cacheError.message}`)
      }

      // Store in memory cache
      const cacheEntry: CacheEntry = {
        id: portfolioData.id,
        cacheKey,
        userId,
        promptHash,
        portfolioData: portfolio,
        hitCount: 0,
        lastAccessed: new Date(),
        expiresAt,
        createdAt: new Date()
      }

      this.memoryCache.set(cacheKey, cacheEntry)

      // Enforce memory cache size limit
      this.enforceMemoryCacheLimit()
    } catch (error) {
      console.error('PortfolioCache.set error:', error)
      throw error
    }
  }

  /**
   * Invalidate all cache entries for a user
   */
  async invalidate(userId: string): Promise<void> {
    try {
      // Remove from database
      const { error } = await this.supabase
        .from('portfolio_cache')
        .delete()
        .eq('user_id', userId)

      if (error) {
        throw new Error(`Failed to invalidate cache: ${error.message}`)
      }

      // Remove from memory cache
      for (const [key, entry] of this.memoryCache.entries()) {
        if (entry.userId === userId) {
          this.memoryCache.delete(key)
        }
      }
    } catch (error) {
      console.error('PortfolioCache.invalidate error:', error)
      throw error
    }
  }

  /**
   * Clean up expired cache entries
   */
  async cleanup(): Promise<number> {
    try {
      // Clean up database
      const { data, error } = await this.supabase
        .rpc('cleanup_expired_cache')

      if (error) {
        throw new Error(`Failed to cleanup cache: ${error.message}`)
      }

      const deletedCount = data || 0

      // Clean up memory cache
      const now = new Date()
      for (const [key, entry] of this.memoryCache.entries()) {
        if (entry.expiresAt <= now) {
          this.memoryCache.delete(key)
        }
      }

      return deletedCount
    } catch (error) {
      console.error('PortfolioCache.cleanup error:', error)
      return 0
    }
  }

  /**
   * Get cache statistics for a user
   */
  async getStats(userId: string): Promise<{ hits: number; misses: number; size: number }> {
    try {
      const { data, error } = await this.supabase
        .from('portfolio_cache')
        .select('hit_count')
        .eq('user_id', userId)

      if (error) {
        throw new Error(`Failed to get cache stats: ${error.message}`)
      }

      const totalHits = data?.reduce((sum, entry) => sum + entry.hit_count, 0) || 0
      const cacheSize = data?.length || 0

      // For misses, we'd need to track this separately in audit logs
      // For now, return basic stats
      return {
        hits: totalHits,
        misses: 0, // Would need separate tracking
        size: cacheSize
      }
    } catch (error) {
      console.error('PortfolioCache.getStats error:', error)
      return { hits: 0, misses: 0, size: 0 }
    }
  }

  /**
   * Generate cache key from user ID and prompt hash
   */
  static generateCacheKey(userId: string, promptHash: string): string {
    return `${userId}:${promptHash}`
  }

  /**
   * Update hit count for a cache entry
   */
  private async updateHitCount(cacheKey: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('portfolio_cache')
        .update({
          hit_count: this.supabase.raw('hit_count + 1'),
          last_accessed: new Date().toISOString()
        })
        .eq('cache_key', cacheKey)

      if (error) {
        console.error('Failed to update hit count:', error)
      }

      // Update memory cache hit count
      const memoryEntry = this.memoryCache.get(cacheKey)
      if (memoryEntry) {
        memoryEntry.hitCount++
        memoryEntry.lastAccessed = new Date()
      }
    } catch (error) {
      console.error('PortfolioCache.updateHitCount error:', error)
    }
  }

  /**
   * Enforce memory cache size limit using LRU eviction
   */
  private enforceMemoryCacheLimit(): void {
    if (this.memoryCache.size <= this.config.maxEntries) {
      return
    }

    // Convert to array and sort by last accessed (oldest first)
    const entries = Array.from(this.memoryCache.entries())
      .sort(([, a], [, b]) => a.lastAccessed.getTime() - b.lastAccessed.getTime())

    // Remove oldest entries until we're under the limit
    const toRemove = this.memoryCache.size - this.config.maxEntries
    for (let i = 0; i < toRemove; i++) {
      this.memoryCache.delete(entries[i][0])
    }
  }

  /**
   * Start the cleanup interval
   */
  private startCleanupInterval(): void {
    this.cleanupInterval = setInterval(async () => {
      try {
        await this.cleanup()
      } catch (error) {
        console.error('Cache cleanup interval error:', error)
      }
    }, this.config.cleanupIntervalMs)
  }

  /**
   * Stop the cleanup interval
   */
  public stopCleanupInterval(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = undefined
    }
  }

  /**
   * Get memory cache size
   */
  public getMemoryCacheSize(): number {
    return this.memoryCache.size
  }

  /**
   * Clear all caches (memory and database)
   */
  async clearAll(): Promise<void> {
    try {
      // Clear database cache
      const { error } = await this.supabase
        .from('portfolio_cache')
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000') // Delete all

      if (error) {
        throw new Error(`Failed to clear database cache: ${error.message}`)
      }

      // Clear memory cache
      this.memoryCache.clear()
    } catch (error) {
      console.error('PortfolioCache.clearAll error:', error)
      throw error
    }
  }
}
