/**
 * Portfolio Reweighting API Endpoint
 * POST /api/portfolio/reweight
 */

import { NextRequest, NextResponse } from 'next/server'
import { withAuth, addSecurityHeaders, addCorsHeaders, validateInput } from '@/lib/llm-portfolio/auth-middleware'
import { getLLMPortfolioEngine } from '@/lib/llm-portfolio'
import { PromptTemplateBuilder } from '@/lib/llm-portfolio/prompt-template-builder'
import { InputSanitizer } from '@/lib/llm-portfolio/input-sanitizer'
import type { AuthContext } from '@/lib/llm-portfolio/auth-middleware'
import type { PersonalizedPortfolio } from '@/lib/portfolio-generator'

// Input validation schema
const REWEIGHT_PORTFOLIO_SCHEMA = {
  required: ['currentPortfolio', 'reweightReason', 'surveyData'],
  properties: {
    currentPortfolio: {
      type: 'object',
      required: ['allocations', 'riskLevel', 'expectedReturn', 'strategy']
    },
    reweightReason: { type: 'string', minLength: 10 },
    surveyData: {
      type: 'object',
      required: ['primaryGoal', 'timeHorizon', 'riskTolerance', 'experienceLevel']
    },
    userMajor: { type: 'string' }
  }
}

async function reweightPortfolioHandler(
  request: NextRequest,
  context: AuthContext
): Promise<NextResponse> {
  const startTime = Date.now()

  try {
    // Parse request body
    const body = await request.json()
    
    // Validate input
    const validation = validateInput(body, REWEIGHT_PORTFOLIO_SCHEMA)
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          error: 'Invalid input data',
          details: validation.errors
        },
        { status: 400 }
      )
    }

    const { currentPortfolio, reweightReason, surveyData, userMajor } = body

    // Validate current portfolio structure
    if (!isValidPortfolio(currentPortfolio)) {
      return NextResponse.json(
        { error: 'Invalid current portfolio structure' },
        { status: 400 }
      )
    }

    // Validate reweight reason
    if (!reweightReason || reweightReason.trim().length < 10) {
      return NextResponse.json(
        { error: 'Reweight reason must be at least 10 characters long' },
        { status: 400 }
      )
    }

    // SECURITY: Check for security risks in input
    const inputText = JSON.stringify({ currentPortfolio, reweightReason, surveyData, userMajor })
    if (InputSanitizer.containsSecurityRisks(inputText)) {
      return NextResponse.json(
        { error: 'Input contains potentially unsafe content' },
        { status: 400 }
      )
    }

    // SECURITY: Sanitize reweight reason
    const sanitizedReason = InputSanitizer.sanitizeUserInput(reweightReason, {
      maxLength: 500,
      strictMode: true
    })

    // Get LLM portfolio engine
    const engine = getLLMPortfolioEngine()
    const promptBuilder = new PromptTemplateBuilder()

    // Build reweighting prompt
    const prompt = await promptBuilder.buildReweightingPrompt(
      {
        surveyData,
        userId: context.userId,
        userMajor
      },
      currentPortfolio,
      reweightReason
    )

    const promptHash = promptBuilder.generatePromptHash(prompt)

    // Create LLM request for reweighting
    const llmRequest = {
      prompt,
      promptHash,
      userId: context.userId,
      promptData: {
        surveyData,
        userId: context.userId,
        userMajor,
        additionalContext: `Portfolio reweighting: ${reweightReason}`
      },
      metadata: {
        type: 'reweighting',
        originalPortfolio: currentPortfolio,
        reweightReason,
        timestamp: new Date().toISOString()
      }
    }

    // Generate reweighted portfolio
    const result = await engine.generatePortfolio({
      surveyData: {
        ...surveyData,
        primaryGoal: "Portfolio rebalancing"
      },
      userId: context.userId,
      userMajor,
      additionalContext: `PORTFOLIO REWEIGHTING REQUEST:

Current Portfolio Analysis:
- Current Risk Level: ${currentPortfolio.riskLevel}
- Current Expected Return: ${currentPortfolio.expectedReturn}
- Current Strategy: ${currentPortfolio.strategy}
- Number of Holdings: ${currentPortfolio.allocations.length}

Current Allocations:
${currentPortfolio.allocations.map((alloc: any) => 
  `• ${alloc.symbol} (${alloc.name}): ${alloc.allocation}% - ${alloc.category}`
).join('\n')}

User's Reweighting Request:
"${reweightReason}"

INSTRUCTIONS:
1. Analyze the current portfolio and the user's specific reweighting request
2. Provide a NEW portfolio that addresses their concerns while maintaining diversification
3. Explain changes made and rationale for each adjustment
4. Ensure the new portfolio still aligns with their risk tolerance
5. Consider market conditions and current performance of existing holdings
6. You may add new holdings, remove existing ones, or adjust allocations as needed
7. Maintain professional investment principles while addressing user feedback

Please generate a reweighted portfolio that specifically addresses: "${reweightReason}"`
    })

    const processingTime = Date.now() - startTime

    // Prepare response with comparison data
    const response = NextResponse.json({
      success: true,
      data: {
        newPortfolio: result.portfolio,
        originalPortfolio: currentPortfolio,
        reweightReason,
        comparison: generatePortfolioComparison(currentPortfolio, result.portfolio),
        metadata: {
          source: result.source,
          confidence: result.confidence,
          processingTimeMs: processingTime,
          cacheHit: result.cacheHit || false,
          fallbackReason: result.fallbackReason,
          validationErrors: result.validationErrors,
          type: 'reweighting'
        }
      },
      rateLimitRemaining: context.rateLimitRemaining
    })

    // Add security headers
    addSecurityHeaders(response)
    addCorsHeaders(response, request.headers.get('origin') || undefined)

    return response

  } catch (error) {
    console.error('Portfolio reweighting error:', error)

    const processingTime = Date.now() - startTime
    
    // Handle specific error types
    if (error.name === 'LLMError') {
      return NextResponse.json(
        {
          error: error.message,
          code: error.code,
          processingTimeMs: processingTime
        },
        { status: error.code === 'RATE_LIMIT_EXCEEDED' ? 429 : 500 }
      )
    }

    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          error: 'Portfolio reweighting validation failed',
          details: error.errors,
          processingTimeMs: processingTime
        },
        { status: 422 }
      )
    }

    // Generic error response
    return NextResponse.json(
      {
        error: 'Portfolio reweighting failed',
        message: error.message,
        processingTimeMs: processingTime
      },
      { status: 500 }
    )
  }
}

/**
 * Validate portfolio structure
 */
function isValidPortfolio(portfolio: any): portfolio is PersonalizedPortfolio {
  if (!portfolio || typeof portfolio !== 'object') {
    return false
  }

  const required = ['allocations', 'riskLevel', 'expectedReturn', 'strategy', 'rationale']
  for (const field of required) {
    if (!portfolio[field]) {
      return false
    }
  }

  if (!Array.isArray(portfolio.allocations) || portfolio.allocations.length === 0) {
    return false
  }

  // Validate allocations
  for (const alloc of portfolio.allocations) {
    if (!alloc.symbol || !alloc.name || typeof alloc.allocation !== 'number' || 
        !alloc.category || !alloc.rationale) {
      return false
    }
  }

  // Check allocations sum to approximately 100%
  const totalAllocation = portfolio.allocations.reduce((sum: number, alloc: any) => sum + alloc.allocation, 0)
  if (Math.abs(totalAllocation - 100) > 1) { // Allow 1% tolerance
    return false
  }

  return true
}

/**
 * Generate portfolio comparison data
 */
function generatePortfolioComparison(original: PersonalizedPortfolio, reweighted: PersonalizedPortfolio) {
  const changes: Array<{
    symbol: string
    name: string
    oldAllocation: number
    newAllocation: number
    change: number
    changeType: 'increased' | 'decreased' | 'added' | 'removed'
  }> = []

  // Track changes in existing holdings
  original.allocations.forEach(originalAlloc => {
    const newAlloc = reweighted.allocations.find(a => a.symbol === originalAlloc.symbol)
    if (newAlloc) {
      const change = newAlloc.allocation - originalAlloc.allocation
      if (Math.abs(change) > 0.1) { // Only show changes > 0.1%
        changes.push({
          symbol: originalAlloc.symbol,
          name: originalAlloc.name,
          oldAllocation: originalAlloc.allocation,
          newAllocation: newAlloc.allocation,
          change,
          changeType: change > 0 ? 'increased' : 'decreased'
        })
      }
    } else {
      // Holding was removed
      changes.push({
        symbol: originalAlloc.symbol,
        name: originalAlloc.name,
        oldAllocation: originalAlloc.allocation,
        newAllocation: 0,
        change: -originalAlloc.allocation,
        changeType: 'removed'
      })
    }
  })

  // Track new holdings
  reweighted.allocations.forEach(newAlloc => {
    const originalAlloc = original.allocations.find(a => a.symbol === newAlloc.symbol)
    if (!originalAlloc) {
      changes.push({
        symbol: newAlloc.symbol,
        name: newAlloc.name,
        oldAllocation: 0,
        newAllocation: newAlloc.allocation,
        change: newAlloc.allocation,
        changeType: 'added'
      })
    }
  })

  return {
    changes: changes.sort((a, b) => Math.abs(b.change) - Math.abs(a.change)),
    summary: {
      holdingsAdded: changes.filter(c => c.changeType === 'added').length,
      holdingsRemoved: changes.filter(c => c.changeType === 'removed').length,
      holdingsModified: changes.filter(c => c.changeType === 'increased' || c.changeType === 'decreased').length,
      riskLevelChanged: original.riskLevel !== reweighted.riskLevel,
      expectedReturnChanged: original.expectedReturn !== reweighted.expectedReturn
    }
  }
}

// Export the handler with authentication middleware
export const POST = withAuth(reweightPortfolioHandler, {
  requireAuth: true,
  rateLimitPerHour: 5 // Lower limit for reweighting since it's more resource intensive
})

// Handle OPTIONS for CORS
export async function OPTIONS(request: NextRequest) {
  const response = new NextResponse(null, { status: 200 })
  addCorsHeaders(response, request.headers.get('origin') || undefined)
  return response
}

// Handle GET for endpoint info
export async function GET() {
  const response = NextResponse.json({
    endpoint: '/api/portfolio/reweight',
    method: 'POST',
    description: 'Reweight existing portfolio using AI analysis',
    authentication: 'Bearer token required',
    rateLimit: '5 requests per hour per user',
    requestBody: {
      currentPortfolio: 'PersonalizedPortfolio (required)',
      reweightReason: 'string (required, min 10 chars)',
      surveyData: 'SurveyData (required)',
      userMajor: 'string (optional)'
    },
    responseFormat: {
      success: 'boolean',
      data: {
        newPortfolio: 'PersonalizedPortfolio',
        originalPortfolio: 'PersonalizedPortfolio',
        reweightReason: 'string',
        comparison: {
          changes: 'Array<PortfolioChange>',
          summary: 'ComparisonSummary'
        },
        metadata: 'GenerationMetadata'
      }
    }
  })

  addSecurityHeaders(response)
  return response
}
