/**
 * LLM Portfolio Generation System - Main Entry Point
 */

// Export all types
export type * from './types'

// Export main classes
export { LLMPortfolioEngine } from './llm-portfolio-engine'
export { PromptTemplateBuilder } from './prompt-template-builder'
export { PortfolioCache } from './portfolio-cache'
export { PortfolioFallback } from './portfolio-fallback'
export { SupabaseLogger } from './supabase-logger'
export { LLMClient } from './llm-client'

// Export configuration
export { 
  getLLMConfig, 
  validateLLMConfig, 
  getTestLLMConfig,
  isLLMConfigured,
  getConfigStatus,
  createLLMConfig,
  LLM_CONFIG_CONSTANTS,
  ENV_VARS
} from './config'

// Export error classes
export { LLMError, CacheError, ValidationError } from './types'

import { LLMPortfolioEngine } from './llm-portfolio-engine'
import { getLLMConfig, validateLLMConfig } from './config'
import type { LLMSystemConfig } from './types'

// Singleton instance
let portfolioEngine: LLMPortfolioEngine | null = null

/**
 * Get or create the LLM Portfolio Engine singleton
 */
export function getLLMPortfolioEngine(config?: LLMSystemConfig): LLMPortfolioEngine {
  if (!portfolioEngine) {
    const systemConfig = config || getLLMConfig()
    
    // Validate configuration
    const validation = validateLLMConfig(systemConfig)
    if (!validation.isValid) {
      throw new Error(`Invalid LLM configuration: ${validation.errors.join(', ')}`)
    }
    
    portfolioEngine = new LLMPortfolioEngine(systemConfig)
  }
  
  return portfolioEngine
}

/**
 * Reset the singleton instance (useful for testing)
 */
export function resetLLMPortfolioEngine(): void {
  if (portfolioEngine) {
    portfolioEngine.cleanup()
    portfolioEngine = null
  }
}

/**
 * Quick portfolio generation function
 */
export async function generateLLMPortfolio(
  surveyData: any,
  userId: string,
  userMajor?: string,
  additionalContext?: string
) {
  const engine = getLLMPortfolioEngine()
  
  return await engine.generatePortfolio({
    surveyData,
    userId,
    userMajor,
    additionalContext
  })
}

/**
 * Health check for the LLM system
 */
export async function checkLLMSystemHealth(): Promise<{
  status: 'healthy' | 'degraded' | 'unhealthy'
  checks: {
    configuration: boolean
    database: boolean
    llmApi: boolean
    cache: boolean
  }
  errors: string[]
}> {
  const errors: string[] = []
  const checks = {
    configuration: false,
    database: false,
    llmApi: false,
    cache: false
  }

  try {
    // Check configuration
    const config = getLLMConfig()
    const validation = validateLLMConfig(config)
    checks.configuration = validation.isValid
    if (!validation.isValid) {
      errors.push(...validation.errors)
    }

    // Check database connection
    try {
      const logger = new (await import('./supabase-logger')).SupabaseLogger()
      await logger.getUserStats('health-check-user')
      checks.database = true
    } catch (error) {
      checks.database = false
      errors.push(`Database check failed: ${error.message}`)
    }

    // Check LLM API
    if (checks.configuration) {
      try {
        const client = new (await import('./llm-client')).LLMClient(config.llm)
        checks.llmApi = await client.testConnection()
        if (!checks.llmApi) {
          errors.push('LLM API connection test failed')
        }
      } catch (error) {
        checks.llmApi = false
        errors.push(`LLM API check failed: ${error.message}`)
      }
    }

    // Check cache
    try {
      const cache = new (await import('./portfolio-cache')).PortfolioCache(config.cache)
      await cache.getStats('health-check-user')
      checks.cache = true
    } catch (error) {
      checks.cache = false
      errors.push(`Cache check failed: ${error.message}`)
    }

    // Determine overall status
    const healthyChecks = Object.values(checks).filter(Boolean).length
    let status: 'healthy' | 'degraded' | 'unhealthy'
    
    if (healthyChecks === 4) {
      status = 'healthy'
    } else if (healthyChecks >= 2) {
      status = 'degraded'
    } else {
      status = 'unhealthy'
    }

    return {
      status,
      checks,
      errors
    }

  } catch (error) {
    return {
      status: 'unhealthy',
      checks,
      errors: [...errors, `Health check failed: ${error.message}`]
    }
  }
}

/**
 * Get system statistics
 */
export async function getLLMSystemStats(): Promise<{
  totalUsers: number
  totalGenerations: number
  cacheHitRate: number
  avgProcessingTime: number
  apiUsage: {
    totalRequests: number
    totalTokens: number
    totalCost: number
  }
}> {
  try {
    const logger = new (await import('./supabase-logger')).SupabaseLogger()
    
    // This would need to be implemented in SupabaseLogger
    // For now, return placeholder data
    return {
      totalUsers: 0,
      totalGenerations: 0,
      cacheHitRate: 0,
      avgProcessingTime: 0,
      apiUsage: {
        totalRequests: 0,
        totalTokens: 0,
        totalCost: 0
      }
    }
  } catch (error) {
    console.error('Failed to get system stats:', error)
    return {
      totalUsers: 0,
      totalGenerations: 0,
      cacheHitRate: 0,
      avgProcessingTime: 0,
      apiUsage: {
        totalRequests: 0,
        totalTokens: 0,
        totalCost: 0
      }
    }
  }
}

/**
 * Utility function to clean up resources
 */
export async function cleanupLLMSystem(): Promise<void> {
  if (portfolioEngine) {
    await portfolioEngine.cleanup()
  }
}

// Default export for convenience
export default {
  getLLMPortfolioEngine,
  generateLLMPortfolio,
  checkLLMSystemHealth,
  getLLMSystemStats,
  cleanupLLMSystem,
  resetLLMPortfolioEngine
}
