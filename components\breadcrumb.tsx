/**
 * Breadcrumb Navigation Component
 * Provides navigation context and back functionality
 */

'use client'

import React from 'react'
import Link from 'next/link'
import { useRouter, usePathname } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { ChevronLeft, ChevronRight, Home } from 'lucide-react'
import { cn } from '@/lib/utils'

export interface BreadcrumbItem {
  label: string
  href?: string
  icon?: React.ComponentType<{ className?: string }>
}

export interface BreadcrumbProps {
  items?: BreadcrumbItem[]
  showBackButton?: boolean
  showHomeButton?: boolean
  className?: string
}

export function Breadcrumb({
  items = [],
  showBackButton = true,
  showHomeButton = true,
  className
}: BreadcrumbProps) {
  const router = useRouter()
  const pathname = usePathname()

  // Auto-generate breadcrumbs from pathname if no items provided
  const breadcrumbItems = items.length > 0 ? items : generateBreadcrumbsFromPath(pathname)

  const handleBack = () => {
    if (window.history.length > 1) {
      router.back()
    } else {
      router.push('/dashboard')
    }
  }

  return (
    <nav className={cn('flex items-center space-x-2 text-sm', className)}>
      {/* Back Button */}
      {showBackButton && (
        <Button
          variant="ghost"
          size="sm"
          onClick={handleBack}
          className="h-8 px-2 text-muted-foreground hover:text-foreground"
        >
          <ChevronLeft className="h-4 w-4" />
          <span className="sr-only">Go back</span>
        </Button>
      )}

      {/* Home Button */}
      {showHomeButton && (
        <Link href="/dashboard">
          <Button
            variant="ghost"
            size="sm"
            className="h-8 px-2 text-muted-foreground hover:text-foreground"
          >
            <Home className="h-4 w-4" />
            <span className="sr-only">Home</span>
          </Button>
        </Link>
      )}

      {/* Breadcrumb Items */}
      {breadcrumbItems.map((item, index) => (
        <React.Fragment key={index}>
          {/* Separator */}
          {(showHomeButton || showBackButton || index > 0) && (
            <ChevronRight className="h-4 w-4 text-muted-foreground" />
          )}

          {/* Breadcrumb Item */}
          {item.href && index < breadcrumbItems.length - 1 ? (
            <Link href={item.href}>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 px-2 text-muted-foreground hover:text-foreground"
              >
                {item.icon && <item.icon className="h-4 w-4 mr-1" />}
                {item.label}
              </Button>
            </Link>
          ) : (
            <span className="flex items-center px-2 py-1 text-foreground font-medium">
              {item.icon && <item.icon className="h-4 w-4 mr-1" />}
              {item.label}
            </span>
          )}
        </React.Fragment>
      ))}
    </nav>
  )
}

/**
 * Generate breadcrumbs from pathname
 */
function generateBreadcrumbsFromPath(pathname: string): BreadcrumbItem[] {
  const segments = pathname.split('/').filter(Boolean)
  const breadcrumbs: BreadcrumbItem[] = []

  // Route mappings for better labels
  const routeLabels: Record<string, string> = {
    dashboard: 'Dashboard',
    investments: 'Investments',
    charts: 'Charts',
    'test-charts': 'Chart Testing',
    learn: 'Learn',
    profile: 'Profile',
    settings: 'Settings',
    portfolio: 'Portfolio',
    history: 'History',
    notifications: 'Notifications',
    help: 'Help'
  }

  segments.forEach((segment, index) => {
    const isLast = index === segments.length - 1
    const href = isLast ? undefined : '/' + segments.slice(0, index + 1).join('/')
    const label = routeLabels[segment] || segment.charAt(0).toUpperCase() + segment.slice(1)

    breadcrumbs.push({
      label,
      href
    })
  })

  return breadcrumbs
}

/**
 * Page Header with Breadcrumb
 */
export interface PageHeaderProps {
  title: string
  description?: string
  breadcrumbItems?: BreadcrumbItem[]
  showBackButton?: boolean
  actions?: React.ReactNode
  className?: string
}

export function PageHeader({
  title,
  description,
  breadcrumbItems,
  showBackButton = true,
  actions,
  className
}: PageHeaderProps) {
  return (
    <div className={cn('space-y-4', className)}>
      {/* Breadcrumb */}
      <Breadcrumb
        items={breadcrumbItems}
        showBackButton={showBackButton}
        showHomeButton={true}
      />

      {/* Header Content */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="space-y-1">
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">{title}</h1>
          {description && (
            <p className="text-muted-foreground">{description}</p>
          )}
        </div>
        {actions && (
          <div className="flex items-center gap-2">
            {actions}
          </div>
        )}
      </div>
    </div>
  )
}

/**
 * Quick Navigation Component
 */
export interface QuickNavProps {
  items: Array<{
    label: string
    href: string
    icon?: React.ComponentType<{ className?: string }>
    description?: string
  }>
  className?: string
}

export function QuickNav({ items, className }: QuickNavProps) {
  return (
    <div className={cn('grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3', className)}>
      {items.map((item, index) => (
        <Link key={index} href={item.href}>
          <Button
            variant="outline"
            className="h-auto p-4 flex flex-col items-center gap-2 hover:bg-muted/50"
          >
            {item.icon && <item.icon className="h-5 w-5" />}
            <div className="text-center">
              <div className="font-medium text-sm">{item.label}</div>
              {item.description && (
                <div className="text-xs text-muted-foreground mt-1">
                  {item.description}
                </div>
              )}
            </div>
          </Button>
        </Link>
      ))}
    </div>
  )
}

/**
 * Navigation Context Hook
 */
export function useNavigation() {
  const router = useRouter()
  const pathname = usePathname()

  const goBack = () => {
    if (window.history.length > 1) {
      router.back()
    } else {
      router.push('/dashboard')
    }
  }

  const goHome = () => {
    router.push('/dashboard')
  }

  const navigateTo = (path: string) => {
    router.push(path)
  }

  return {
    pathname,
    goBack,
    goHome,
    navigateTo,
    router
  }
}
