/**
 * Portfolio Performance API Route
 * GET /api/portfolio/performance
 * Requires user authentication
 */

import { NextRequest, NextResponse } from 'next/server'
import { withAuthAndPermissions } from '@/lib/auth-middleware'
import { getPortfolioStockIntegration } from '@/lib/portfolio-stock-integration'
import { getUserCapitalResolver } from '@/lib/user-capital-resolver'
import type { AuthenticatedUser } from '@/lib/auth-middleware'
import type { Timeframe } from '@/lib/types/stock-history'

interface PortfolioPerformanceResponse {
  totalInvested: number
  currentValue: number
  netReturn: {
    amount: number
    percentage: number
  }
  valueTimeline: Array<{
    timestamp: number
    value: number
  }>
}

async function portfolioPerformanceHandler(
  request: NextRequest,
  user: AuthenticatedUser
): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url)

    // Get query parameters
    const portfolioId = searchParams.get('portfolio_id')
    const timeframe = (searchParams.get('timeframe') || searchParams.get('period') || '1M').toUpperCase() as Timeframe
    const includeDetails = searchParams.get('details') === 'true'

    // Validate timeframe
    const validTimeframes: Timeframe[] = ['1D', '1W', '1M', '3M', '1Y', 'All']
    if (!validTimeframes.includes(timeframe)) {
      return NextResponse.json(
        {
          error: `Invalid timeframe. Valid options: ${validTimeframes.join(', ')}`,
          code: 'INVALID_TIMEFRAME'
        },
        { status: 400 }
      )
    }

    // Get portfolio performance data
    const portfolioIntegration = getPortfolioStockIntegration()
    const performanceData = await portfolioIntegration.calculatePortfolioPerformance(
      user.id,
      timeframe,
      portfolioId || undefined
    )

    if (!performanceData) {
      // Return empty portfolio data
      const emptyResponse: PortfolioPerformanceResponse = {
        totalInvested: 0,
        currentValue: 0,
        netReturn: {
          amount: 0,
          percentage: 0
        },
        valueTimeline: []
      }

      return NextResponse.json(emptyResponse, {
        headers: {
          'Cache-Control': 'public, max-age=60',
          'X-Portfolio-Status': 'empty'
        }
      })
    }

    // Get user capital summary for additional context
    const capitalResolver = getUserCapitalResolver()
    const capitalSummary = await capitalResolver.getUserCapitalSummary(user.id)

    // Calculate metrics
    const totalInvested = performanceData.currentMetrics.totalInvested
    const totalCash = performanceData.currentMetrics.totalCash
    const currentValue = totalInvested + totalCash
    const netReturnAmount = performanceData.currentMetrics.totalReturn
    const netReturnPercentage = performanceData.currentMetrics.totalReturnPercent

    // Build value timeline from time series data
    const valueTimeline = performanceData.timeSeries.map(point => ({
      timestamp: point.timestamp,
      value: point.totalValue
    }))

    // Build main response
    const response: PortfolioPerformanceResponse = {
      totalInvested,
      currentValue,
      netReturn: {
        amount: netReturnAmount,
        percentage: netReturnPercentage
      },
      valueTimeline
    }

    // Add detailed information if requested
    let detailedResponse: any = response
    if (includeDetails) {
      detailedResponse = {
        ...response,
        details: {
          portfolioId: performanceData.portfolioId,
          timeframe: performanceData.timeframe,
          totalCash,
          holdingsCount: performanceData.currentMetrics.holdingsCount,
          holdings: performanceData.holdings.map(holding => ({
            symbol: holding.symbol,
            shares: holding.shares,
            currentPrice: holding.currentPrice,
            totalValue: holding.totalValue,
            gainLoss: holding.gainLoss,
            gainLossPercent: holding.gainLossPercent
          })),
          performance: {
            bestPerformer: performanceData.holdings.length > 0
              ? performanceData.holdings.reduce((best, current) =>
                  current.gainLossPercent > best.gainLossPercent ? current : best
                ).symbol
              : null,
            worstPerformer: performanceData.holdings.length > 0
              ? performanceData.holdings.reduce((worst, current) =>
                  current.gainLossPercent < worst.gainLossPercent ? current : worst
                ).symbol
              : null
          },
          lastUpdated: performanceData.lastUpdated
        }
      }
    }

    // Set cache headers based on data freshness
    const cacheMaxAge = performanceData.timeSeries.length > 0 ? 300 : 60 // 5 min for data, 1 min for empty

    return NextResponse.json(detailedResponse, {
      headers: {
        'Cache-Control': `public, max-age=${cacheMaxAge}`,
        'X-Portfolio-ID': performanceData.portfolioId,
        'X-Data-Points': performanceData.timeSeries.length.toString(),
        'X-Holdings-Count': performanceData.currentMetrics.holdingsCount.toString(),
        'X-Last-Updated': performanceData.lastUpdated
      }
    })

  } catch (error) {
    console.error('Portfolio performance API error:', error)

    return NextResponse.json(
      {
        error: 'Failed to calculate portfolio performance',
        code: 'CALCULATION_ERROR',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// Export the authenticated route handler
export const GET = withAuthAndPermissions(portfolioPerformanceHandler, {
  requiredPermissions: ['portfolio:read'],
  rateLimit: { maxRequests: 60, windowMs: 60 * 1000 }, // 60 requests per minute
  validatePortfolio: true
})

/**
 * POST endpoint for initializing portfolio
 */
async function portfolioInitHandler(
  request: NextRequest,
  user: AuthenticatedUser
): Promise<NextResponse> {
  try {
    const body = await request.json()
    const { action, initialBalance } = body

    if (action === 'initialize') {
      // Initialize user capital system
      const capitalResolver = getUserCapitalResolver()
      const initialized = await capitalResolver.initializeUserCapital(
        user.id,
        initialBalance || 10000
      )

      if (!initialized) {
        return NextResponse.json(
          { error: 'Failed to initialize user capital system' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        message: 'Portfolio system initialized successfully',
        userId: user.id,
        initialBalance: initialBalance || 10000
      })
    }

    return NextResponse.json(
      { error: 'Invalid action. Supported actions: initialize' },
      { status: 400 }
    )

  } catch (error) {
    console.error('Portfolio init error:', error)

    return NextResponse.json(
      {
        error: 'Failed to process portfolio request',
        code: 'INIT_ERROR',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export const POST = withAuthAndPermissions(portfolioInitHandler, {
  requiredPermissions: ['portfolio:write'],
  rateLimit: { maxRequests: 10, windowMs: 60 * 1000 } // 10 requests per minute for init
})
