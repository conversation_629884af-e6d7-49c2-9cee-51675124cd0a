/**
 * Twelve Data API Integration
 * Mid-tier service for 80+ technical indicators and international markets
 */

import { StockQuote, MarketMover } from './stock-data-service'

export interface TechnicalIndicator {
  indicator: string
  value: number
  signal?: 'BUY' | 'SELL' | 'NEUTRAL'
  timestamp: string
}

export interface TwelveDataQuote extends StockQuote {
  technicalIndicators?: TechnicalIndicator[]
  fundamentals?: {
    peRatio?: number
    eps?: number
    dividendYield?: number
    marketCap?: number
  }
}

export class TwelveDataService {
  private apiKey: string
  private baseUrl = 'https://api.twelvedata.com'
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>()

  constructor(apiKey: string) {
    this.apiKey = apiKey
  }

  /**
   * Get enhanced quote with technical indicators
   */
  async getEnhancedQuote(symbol: string, indicators?: string[]): Promise<TwelveDataQuote | null> {
    const cacheKey = `twelve-quote:${symbol}:${indicators?.join(',') || 'basic'}`
    
    const cached = this.getFromCache<TwelveDataQuote>(cacheKey)
    if (cached) return cached

    try {
      // Get basic quote
      const quote = await this.getBasicQuote(symbol)
      if (!quote) return null

      // Get technical indicators if requested
      let technicalIndicators: TechnicalIndicator[] = []
      if (indicators && indicators.length > 0) {
        technicalIndicators = await this.getTechnicalIndicators(symbol, indicators)
      }

      // Get fundamentals
      const fundamentals = await this.getFundamentals(symbol)

      const enhancedQuote: TwelveDataQuote = {
        ...quote,
        technicalIndicators,
        fundamentals
      }

      this.setCache(cacheKey, enhancedQuote, 60 * 1000) // 1 minute cache
      return enhancedQuote
    } catch (error) {
      console.error('Twelve Data enhanced quote error:', error)
      return null
    }
  }

  /**
   * Get basic stock quote
   */
  async getBasicQuote(symbol: string): Promise<StockQuote | null> {
    try {
      const response = await fetch(
        `${this.baseUrl}/quote?symbol=${symbol}&apikey=${this.apiKey}`
      )

      if (!response.ok) return null

      const data = await response.json()
      if (data.status === 'error') return null

      return {
        symbol,
        name: data.name || symbol,
        price: parseFloat(data.close),
        change: parseFloat(data.change),
        changePercent: parseFloat(data.percent_change),
        volume: parseInt(data.volume) || 0,
        high: parseFloat(data.high),
        low: parseFloat(data.low),
        open: parseFloat(data.open),
        previousClose: parseFloat(data.previous_close),
        exchange: data.exchange || 'UNKNOWN',
        sector: data.sector,
        lastUpdated: new Date().toISOString(),
        source: 'twelvedata'
      }
    } catch (error) {
      console.error('Twelve Data quote error:', error)
      return null
    }
  }

  /**
   * Get technical indicators
   */
  async getTechnicalIndicators(symbol: string, indicators: string[]): Promise<TechnicalIndicator[]> {
    const results: TechnicalIndicator[] = []

    for (const indicator of indicators) {
      try {
        const response = await fetch(
          `${this.baseUrl}/${indicator.toLowerCase()}?symbol=${symbol}&interval=1day&apikey=${this.apiKey}`
        )

        if (!response.ok) continue

        const data = await response.json()
        if (data.status === 'error') continue

        // Handle different indicator response formats
        let value: number
        let signal: 'BUY' | 'SELL' | 'NEUTRAL' | undefined

        if (indicator === 'RSI') {
          value = parseFloat(data.values?.[0]?.rsi || data.rsi || 0)
          signal = value > 70 ? 'SELL' : value < 30 ? 'BUY' : 'NEUTRAL'
        } else if (indicator === 'MACD') {
          value = parseFloat(data.values?.[0]?.macd || data.macd || 0)
          const signal_line = parseFloat(data.values?.[0]?.macd_signal || data.macd_signal || 0)
          signal = value > signal_line ? 'BUY' : 'SELL'
        } else if (indicator === 'SMA' || indicator === 'EMA') {
          value = parseFloat(data.values?.[0]?.[indicator.toLowerCase()] || data[indicator.toLowerCase()] || 0)
        } else {
          // Generic handling for other indicators
          value = parseFloat(data.values?.[0]?.value || data.value || 0)
        }

        results.push({
          indicator,
          value,
          signal,
          timestamp: new Date().toISOString()
        })
      } catch (error) {
        console.warn(`Failed to get ${indicator} for ${symbol}:`, error)
      }
    }

    return results
  }

  /**
   * Get fundamental data
   */
  async getFundamentals(symbol: string): Promise<TwelveDataQuote['fundamentals']> {
    try {
      const response = await fetch(
        `${this.baseUrl}/statistics?symbol=${symbol}&apikey=${this.apiKey}`
      )

      if (!response.ok) return undefined

      const data = await response.json()
      if (data.status === 'error') return undefined

      return {
        peRatio: parseFloat(data.valuations?.pe_ratio) || undefined,
        eps: parseFloat(data.valuations?.earnings_per_share) || undefined,
        dividendYield: parseFloat(data.dividends?.dividend_yield) || undefined,
        marketCap: parseFloat(data.valuations?.market_capitalization) || undefined
      }
    } catch (error) {
      console.warn(`Failed to get fundamentals for ${symbol}:`, error)
      return undefined
    }
  }

  /**
   * Get market movers
   */
  async getMarketMovers(): Promise<{ gainers: MarketMover[]; losers: MarketMover[]; mostActive: MarketMover[] } | null> {
    try {
      // Twelve Data doesn't have direct market movers endpoint
      // We'll use a list of popular stocks and sort them
      const popularSymbols = [
        'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'NFLX',
        'JPM', 'BAC', 'V', 'MA', 'JNJ', 'PFE', 'KO', 'PEP', 'WMT', 'HD'
      ]

      const quotes = await Promise.all(
        popularSymbols.map(symbol => this.getBasicQuote(symbol))
      )

      const validQuotes = quotes.filter(q => q !== null) as StockQuote[]
      
      const movers: MarketMover[] = validQuotes.map(quote => ({
        symbol: quote.symbol,
        name: quote.name,
        price: quote.price,
        change: quote.change,
        changePercent: quote.changePercent,
        volume: quote.volume,
        sector: quote.sector,
        lastUpdated: quote.lastUpdated,
        source: 'twelvedata'
      }))

      const gainers = movers
        .filter(m => m.changePercent > 0)
        .sort((a, b) => b.changePercent - a.changePercent)
        .slice(0, 10)

      const losers = movers
        .filter(m => m.changePercent < 0)
        .sort((a, b) => a.changePercent - b.changePercent)
        .slice(0, 10)

      const mostActive = movers
        .filter(m => m.volume > 0)
        .sort((a, b) => b.volume - a.volume)
        .slice(0, 10)

      return { gainers, losers, mostActive }
    } catch (error) {
      console.error('Twelve Data market movers error:', error)
      return null
    }
  }

  /**
   * Get international markets data
   */
  async getInternationalQuote(symbol: string, exchange: string): Promise<StockQuote | null> {
    try {
      const response = await fetch(
        `${this.baseUrl}/quote?symbol=${symbol}&exchange=${exchange}&apikey=${this.apiKey}`
      )

      if (!response.ok) return null

      const data = await response.json()
      if (data.status === 'error') return null

      return {
        symbol: `${symbol}:${exchange}`,
        name: data.name || symbol,
        price: parseFloat(data.close),
        change: parseFloat(data.change),
        changePercent: parseFloat(data.percent_change),
        volume: parseInt(data.volume) || 0,
        high: parseFloat(data.high),
        low: parseFloat(data.low),
        open: parseFloat(data.open),
        previousClose: parseFloat(data.previous_close),
        exchange: exchange,
        sector: data.sector,
        lastUpdated: new Date().toISOString(),
        source: 'twelvedata'
      }
    } catch (error) {
      console.error('Twelve Data international quote error:', error)
      return null
    }
  }

  /**
   * Cache management
   */
  private getFromCache<T>(key: string): T | null {
    const cached = this.cache.get(key)
    if (!cached) return null

    if (Date.now() > cached.timestamp + cached.ttl) {
      this.cache.delete(key)
      return null
    }

    return cached.data as T
  }

  private setCache(key: string, data: any, ttl: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }
}

// Available technical indicators
export const AVAILABLE_INDICATORS = [
  'RSI', 'MACD', 'SMA', 'EMA', 'BBANDS', 'STOCH', 'ADX', 'CCI', 'WILLIAMS',
  'MOMENTUM', 'ROC', 'TRIX', 'ULTOSC', 'WILLR', 'AROON', 'AROONOSC',
  'MFI', 'BOP', 'CMO', 'MINUS_DI', 'PLUS_DI', 'MINUS_DM', 'PLUS_DM',
  'MIDPOINT', 'MIDPRICE', 'SAR', 'TRANGE', 'ATR', 'NATR', 'AD', 'ADOSC',
  'OBV', 'HT_TRENDLINE', 'HT_SINE', 'HT_TRENDMODE', 'HT_DCPERIOD',
  'HT_DCPHASE', 'HT_PHASOR', 'AVGPRICE', 'MEDPRICE', 'TYPPRICE', 'WCLPRICE'
]

// Singleton instance
let twelveDataServiceInstance: TwelveDataService | null = null

export function getTwelveDataService(): TwelveDataService | null {
  const apiKey = process.env.TWELVE_DATA_API_KEY
  if (!apiKey) return null

  if (!twelveDataServiceInstance) {
    twelveDataServiceInstance = new TwelveDataService(apiKey)
  }
  return twelveDataServiceInstance
}
