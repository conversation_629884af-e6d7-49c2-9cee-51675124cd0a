# Enhanced Stock Retrieval System

This document describes the enhanced stock retrieval system that provides semi-realtime stock data using free APIs.

## 🚀 Features

- **Real-time Stock Quotes**: Get current stock prices with 1-minute refresh
- **Multiple API Sources**: Alpha Vantage, Finnhub, and Yahoo Finance fallback
- **Intelligent Caching**: Smart caching to maximize API efficiency
- **Rate Limit Management**: Automatic handling of API rate limits
- **Fallback Chain**: Graceful degradation when APIs are unavailable
- **Database Integration**: Enhanced Supabase schema for price tracking
- **React Components**: Ready-to-use components for stock display
- **TypeScript Support**: Full type safety throughout

## 📊 API Sources & Limits

### Primary APIs
1. **Finnhub** (Real-time quotes)
   - Free: 60 requests/minute
   - Best for: Real-time prices, company profiles
   - Signup: https://finnhub.io/register

2. **Alpha Vantage** (Fundamentals)
   - Free: 25 requests/day
   - Best for: Company data, historical prices
   - Signup: https://www.alphavantage.co/support/#api-key

3. **Yahoo Finance** (Fallback)
   - Unofficial but free
   - Best for: Basic quotes when other APIs fail

## 🛠️ Setup Instructions

### 1. Get API Keys
Follow the detailed guide in `scripts/setup-stock-apis.md`

### 2. Environment Configuration
```bash
# Copy the example file
cp .env.example .env.local

# Add your API keys
ALPHA_VANTAGE_API_KEY=your_key_here
FINNHUB_API_KEY=your_key_here
```

### 3. Database Migration
Run the enhanced schema in Supabase:
```sql
-- Run scripts/enhance-stocks-schema.sql in Supabase SQL Editor
```

### 4. Install Dependencies
```bash
npm install
```

### 5. Start Development
```bash
npm run dev
```

## 🏗️ Architecture

### Core Components

1. **StockDataService** (`lib/stock-data-service.ts`)
   - Main service class handling all API interactions
   - Implements caching, rate limiting, and fallback logic

2. **API Routes** (`app/api/stocks/`)
   - `/api/stocks/quote` - Get single or multiple quotes
   - `/api/stocks/search` - Search for stocks
   - `/api/stocks/status` - API status and usage

3. **React Hooks** (`hooks/use-stock-data.ts`)
   - `useStockQuote` - Single stock with auto-refresh
   - `useMultipleStockQuotes` - Multiple stocks efficiently
   - `useStockSearch` - Search functionality

4. **UI Components** (`components/`)
   - `RealTimeStockQuote` - Individual stock display
   - `StockWatchlist` - Multiple stocks with management

### Data Flow

```
Frontend Component
    ↓
React Hook
    ↓
API Route (/api/stocks/*)
    ↓
StockDataService
    ↓
Cache Check → API Call → Database Fallback
    ↓
Response with Source Attribution
```

## 📱 Usage Examples

### Basic Stock Quote
```tsx
import { RealTimeStockQuote } from '@/components/real-time-stock-quote'

export function MyComponent() {
  return (
    <RealTimeStockQuote 
      symbol="AAPL" 
      autoRefresh={true}
      showDetails={true}
    />
  )
}
```

### Stock Watchlist
```tsx
import { StockWatchlist } from '@/components/stock-watchlist'

export function Dashboard() {
  return (
    <StockWatchlist 
      initialSymbols={['AAPL', 'MSFT', 'GOOGL']}
      autoRefresh={true}
      maxSymbols={20}
    />
  )
}
```

### Custom Hook Usage
```tsx
import { useStockQuote } from '@/hooks/use-stock-data'

export function CustomStockDisplay() {
  const { data, loading, error, refetch } = useStockQuote('AAPL', true)
  
  if (loading) return <div>Loading...</div>
  if (error) return <div>Error: {error}</div>
  if (!data) return <div>No data</div>
  
  return (
    <div>
      <h2>{data.symbol}: ${data.price}</h2>
      <p>Change: {data.change} ({data.changePercent}%)</p>
    </div>
  )
}
```

## 🔧 API Endpoints

### GET /api/stocks/quote
Get stock quote(s)

**Single Stock:**
```
GET /api/stocks/quote?symbol=AAPL
```

**Multiple Stocks:**
```
GET /api/stocks/quote?symbols=AAPL,MSFT,GOOGL
```

**Response:**
```json
{
  "data": {
    "symbol": "AAPL",
    "name": "Apple Inc.",
    "price": 175.00,
    "change": 2.50,
    "changePercent": 1.45,
    "volume": 50000000,
    "high": 177.00,
    "low": 172.50,
    "open": 173.00,
    "previousClose": 172.50,
    "exchange": "NASDAQ",
    "sector": "Technology",
    "lastUpdated": "2024-01-15T10:30:00Z",
    "source": "finnhub"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### GET /api/stocks/search
Search for stocks

```
GET /api/stocks/search?q=apple&limit=10
```

### GET /api/stocks/status
Get API status and usage

```json
{
  "market": {
    "isOpen": true,
    "status": "open",
    "timezone": "America/New_York"
  },
  "apis": {
    "alphavantage": {
      "configured": true,
      "usage": { "used": 5, "limit": 25, "resetTime": "Daily" }
    },
    "finnhub": {
      "configured": true,
      "usage": { "used": 12, "limit": 60, "resetTime": "Per minute" }
    }
  }
}
```

## 💾 Database Schema

### Enhanced Tables

1. **stocks** (enhanced)
   - Added real-time price fields
   - API source tracking
   - Last update timestamps

2. **stock_quotes** (new)
   - Current market data
   - Multiple source support
   - Real-time updates

3. **stock_price_history** (new)
   - Historical price tracking
   - Daily OHLCV data

4. **api_usage_log** (new)
   - API usage monitoring
   - Error tracking

### Key Functions

- `update_stock_price()` - Update stock prices
- `log_api_usage()` - Track API usage
- `cleanup_old_stock_data()` - Data maintenance

## 🔄 Caching Strategy

### Cache Levels
1. **In-Memory Cache** (StockDataService)
   - Real-time quotes: 1 minute TTL
   - Company profiles: 24 hours TTL
   - Search results: 30 minutes TTL

2. **Database Cache** (stock_quotes table)
   - Persistent storage for quotes
   - Source attribution
   - Historical tracking

3. **Browser Cache** (React hooks)
   - Component-level caching
   - Auto-refresh management

## 📈 Performance Optimization

### Rate Limit Management
- Intelligent API selection based on limits
- Automatic fallback when limits exceeded
- Usage tracking and alerts

### Batch Operations
- Multiple stock quotes in single requests
- Efficient database updates
- Reduced API calls

### Smart Caching
- Different TTL for different data types
- Cache invalidation strategies
- Fallback to stale data when needed

## 🚨 Error Handling

### Graceful Degradation
1. **API Failure**: Fall back to next API in chain
2. **Rate Limits**: Use cached data or alternative source
3. **Network Issues**: Return last known good data
4. **Invalid Symbols**: Clear error messages

### Error Types
- `API_LIMIT_EXCEEDED`
- `NETWORK_ERROR`
- `INVALID_SYMBOL`
- `NO_DATA_AVAILABLE`
- `SERVICE_UNAVAILABLE`

## 🔍 Monitoring

### API Usage Tracking
- Request counts per API
- Success/error rates
- Rate limit monitoring

### Performance Metrics
- Cache hit rates
- Response times
- Error frequencies

### Alerts
- API limit approaching
- High error rates
- Service unavailability

## 🚀 Production Deployment

### Environment Variables
```env
ALPHA_VANTAGE_API_KEY=prod_key
FINNHUB_API_KEY=prod_key
NODE_ENV=production
```

### Scaling Considerations
- Consider Redis for distributed caching
- API key rotation strategy
- Rate limit monitoring
- Database connection pooling

### Monitoring Setup
- API usage dashboards
- Error rate alerts
- Performance monitoring
- Uptime checks

## 🤝 Contributing

1. Follow TypeScript best practices
2. Add tests for new features
3. Update documentation
4. Consider API rate limits in design
5. Implement proper error handling

## 📝 License

This enhanced stock system is part of the Investry application.
