/**
 * Stock History API Route
 * GET /api/stock/:ticker/history?timeframe=1M
 */

import { NextRequest, NextResponse } from 'next/server'
import { getStockHistoryService } from '@/lib/stock-history-service'
import { Timeframe, StockHistoryError } from '@/lib/types/stock-history'

// Valid timeframes
const VALID_TIMEFRAMES: Timeframe[] = ['1D', '1W', '1M', '3M', '1Y', 'All']

export async function GET(
  request: NextRequest,
  { params }: { params: { ticker: string } }
) {
  try {
    const { ticker } = params
    const { searchParams } = new URL(request.url)
    
    // Validate ticker
    if (!ticker || ticker.length === 0) {
      return NextResponse.json(
        { 
          error: 'Missing ticker symbol',
          code: 'MISSING_TICKER'
        },
        { status: 400 }
      )
    }

    // Get and validate timeframe
    const timeframeParam = searchParams.get('timeframe') || '1M'
    const timeframe = timeframeParam.toUpperCase() as Timeframe
    
    if (!VALID_TIMEFRAMES.includes(timeframe)) {
      return NextResponse.json(
        { 
          error: `Invalid timeframe. Valid options: ${VALID_TIMEFRAMES.join(', ')}`,
          code: 'INVALID_TIMEFRAME',
          validTimeframes: VALID_TIMEFRAMES
        },
        { status: 400 }
      )
    }

    // Optional parameters
    const forceRefresh = searchParams.get('refresh') === 'true'
    const includeMetadata = searchParams.get('metadata') === 'true'

    // Get stock history service
    const historyService = getStockHistoryService()

    // Force refresh if requested
    let result
    if (forceRefresh) {
      result = await historyService.forceRefresh(ticker.toUpperCase(), timeframe)
    } else {
      result = await historyService.getHistoricalData(ticker.toUpperCase(), timeframe)
    }

    // Format data to match required format: { timestamp, close }[]
    const formattedData = result.data.map(point => ({
      timestamp: point.timestamp,
      close: point.close
    }))

    // Build response
    const response: any = {
      symbol: result.symbol,
      timeframe: result.timeframe,
      data: formattedData,
      dataPoints: result.dataPoints,
      source: result.source,
      lastUpdated: result.lastUpdated,
      cached: result.cached
    }

    // Add metadata if requested
    if (includeMetadata) {
      response.metadata = {
        cacheInfo: historyService.getCacheInfo(ticker.toUpperCase()),
        apiUsage: historyService.getApiUsage(),
        cacheMetrics: historyService.getCacheMetrics()
      }
    }

    // Set appropriate cache headers
    const cacheMaxAge = result.cached ? 60 : 300 // 1 min for cached, 5 min for fresh
    
    return NextResponse.json(response, {
      headers: {
        'Cache-Control': `public, max-age=${cacheMaxAge}`,
        'X-Data-Source': result.source,
        'X-Cache-Status': result.cached ? 'HIT' : 'MISS'
      }
    })

  } catch (error) {
    console.error('Stock history API error:', error)

    // Handle specific stock history errors
    if (error instanceof StockHistoryError) {
      const statusCode = getErrorStatusCode(error.code)
      
      return NextResponse.json(
        {
          error: error.message,
          code: error.code,
          provider: error.provider,
          retryable: error.retryable
        },
        { status: statusCode }
      )
    }

    // Handle generic errors
    return NextResponse.json(
      {
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    )
  }
}

// Additional endpoints for batch requests and cache management

/**
 * POST /api/stock/:ticker/history/batch
 * Get multiple timeframes at once
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { ticker: string } }
) {
  try {
    const { ticker } = params
    const body = await request.json()
    
    // Validate request body
    if (!body.timeframes || !Array.isArray(body.timeframes)) {
      return NextResponse.json(
        { 
          error: 'Missing or invalid timeframes array',
          code: 'INVALID_REQUEST_BODY'
        },
        { status: 400 }
      )
    }

    // Validate timeframes
    const timeframes = body.timeframes as string[]
    const invalidTimeframes = timeframes.filter(tf => 
      !VALID_TIMEFRAMES.includes(tf.toUpperCase() as Timeframe)
    )

    if (invalidTimeframes.length > 0) {
      return NextResponse.json(
        { 
          error: `Invalid timeframes: ${invalidTimeframes.join(', ')}`,
          code: 'INVALID_TIMEFRAMES',
          validTimeframes: VALID_TIMEFRAMES
        },
        { status: 400 }
      )
    }

    // Get stock history service
    const historyService = getStockHistoryService()
    
    // Fetch multiple timeframes
    const validTimeframes = timeframes.map(tf => tf.toUpperCase() as Timeframe)
    const results = await historyService.getMultipleTimeframes(
      ticker.toUpperCase(), 
      validTimeframes
    )

    return NextResponse.json({
      symbol: ticker.toUpperCase(),
      results,
      requestedTimeframes: validTimeframes,
      successfulTimeframes: Object.keys(results),
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Stock history batch API error:', error)
    
    return NextResponse.json(
      {
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/stock/:ticker/history
 * Clear cache for a ticker
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { ticker: string } }
) {
  try {
    const { ticker } = params
    const { searchParams } = new URL(request.url)
    const timeframe = searchParams.get('timeframe')?.toUpperCase() as Timeframe

    const historyService = getStockHistoryService()
    
    if (timeframe && VALID_TIMEFRAMES.includes(timeframe)) {
      historyService.invalidateCache(ticker.toUpperCase(), timeframe)
    } else {
      historyService.invalidateCache(ticker.toUpperCase())
    }

    return NextResponse.json({
      message: 'Cache cleared successfully',
      symbol: ticker.toUpperCase(),
      timeframe: timeframe || 'all'
    })

  } catch (error) {
    console.error('Stock history cache clear error:', error)
    
    return NextResponse.json(
      {
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    )
  }
}

// Helper function to map error codes to HTTP status codes
function getErrorStatusCode(errorCode: string): number {
  switch (errorCode) {
    case 'API_KEY_MISSING':
    case 'UNKNOWN_PROVIDER':
      return 503 // Service Unavailable
    case 'RATE_LIMIT':
      return 429 // Too Many Requests
    case 'API_ERROR':
      return 502 // Bad Gateway
    case 'NO_PROVIDERS_AVAILABLE':
      return 503 // Service Unavailable
    case 'FETCH_ERROR':
      return 502 // Bad Gateway
    case 'NOT_IMPLEMENTED':
      return 501 // Not Implemented
    default:
      return 500 // Internal Server Error
  }
}
