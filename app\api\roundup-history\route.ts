import { NextResponse } from 'next/server'
import { createAdminClient, createUserClient, isAdminConfigured } from '@/lib/supabase-clients'

// Initialize Supabase with proper client selection
const supabase = isAdminConfigured() ? createAdminClient() : createUserClient()

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id')
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = parseInt(searchParams.get('offset') || '0')
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }
    
    // Get user's roundup history with transaction details
    const { data: transactions, error } = await supabase
      .from('roundups')
      .select(`
        id,
        original_amount,
        roundup_amount,
        status,
        processed_at,
        created_at,
        transactions (
          merchant_name,
          category,
          date
        ),
        linked_accounts (
          account_name,
          account_mask,
          institution_name
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)
    
    if (error) {
      console.error('Error fetching roundup history:', error)
      return NextResponse.json(
        { error: 'Failed to fetch roundup history' },
        { status: 500 }
      )
    }
    
    // Get total count for pagination
    const { count, error: countError } = await supabase
      .from('roundups')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
    
    if (countError) {
      console.error('Error fetching roundup count:', countError)
    }
    
    // Get summary statistics
    const { data: stats } = await supabase
      .from('roundups')
      .select('roundup_amount, status')
      .eq('user_id', userId)
    
    const totalRoundups = stats?.reduce((sum, r) => sum + (r.status === 'processed' ? r.roundup_amount : 0), 0) || 0
    const pendingRoundups = stats?.reduce((sum, r) => sum + (r.status === 'pending' ? r.roundup_amount : 0), 0) || 0
    const roundupCount = stats?.filter(r => r.status === 'processed').length || 0
    
    return NextResponse.json({
      transactions: transactions || [],
      total: count || 0,
      limit,
      offset,
      summary: {
        totalRoundups: Number(totalRoundups.toFixed(2)),
        pendingRoundups: Number(pendingRoundups.toFixed(2)),
        roundupCount
      }
    })
    
  } catch (error) {
    console.error('Roundup history API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
