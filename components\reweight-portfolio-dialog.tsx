/**
 * Reweight Portfolio Dialog - AI-powered portfolio rebalancing
 */

"use client"

import { useState } from "react"
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Loader2, TrendingUp, AlertTriangle, CheckCircle, RefreshCw } from "lucide-react"
import { useAuth } from "@/components/auth-provider"
import { createClient } from "@/lib/supabase"
import { useNotifications } from "@/components/notification-system"
import type { PersonalizedPortfolio, PortfolioAllocation } from "@/lib/portfolio-generator"

interface ReweightPortfolioDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentPortfolio: PersonalizedPortfolio | null
  onPortfolioUpdated: (newPortfolio: PersonalizedPortfolio) => void
}

export function ReweightPortfolioDialog({
  open,
  onOpenChange,
  currentPortfolio,
  onPortfolioUpdated
}: ReweightPortfolioDialogProps) {
  const { user } = useAuth()
  const { addNotification } = useNotifications()
  const [reweightReason, setReweightReason] = useState("")
  const [isGenerating, setIsGenerating] = useState(false)
  const [newPortfolio, setNewPortfolio] = useState<PersonalizedPortfolio | null>(null)
  const [step, setStep] = useState<'input' | 'generating' | 'review'>('input')

  const handleReweight = async () => {
    if (!currentPortfolio || !user) {
      addNotification({
        type: "error",
        title: "Error",
        message: "No portfolio or user found"
      })
      return
    }

    if (!reweightReason.trim()) {
      addNotification({
        type: "error",
        title: "Reason Required",
        message: "Please provide a reason for reweighting your portfolio"
      })
      return
    }

    setIsGenerating(true)
    setStep('generating')

    try {
      // Get the current session to access the token
      const supabase = createClient()
      const { data: { session } } = await supabase.auth.getSession()

      const response = await fetch('/api/portfolio/reweight', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session?.access_token || 'demo-token'}`
        },
        body: JSON.stringify({
          currentPortfolio,
          reweightReason,
          surveyData: {
            // Use existing portfolio data to infer preferences
            primaryGoal: "Portfolio rebalancing",
            timeHorizon: "5–10 years", // Default
            riskTolerance: getRiskToleranceFromLevel(currentPortfolio.riskLevel),
            experienceLevel: "Intermediate", // Assume intermediate for reweighting
            interestedThemes: extractThemesFromPortfolio(currentPortfolio),
            monthlyInvestment: 1000 // Default
          },
          userMajor: user.user_metadata?.major || "general"
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to generate reweighted portfolio')
      }

      const result = await response.json()

      if (result.success && result.data?.newPortfolio) {
        setNewPortfolio(result.data.newPortfolio)
        setStep('review')

        addNotification({
          type: "success",
          title: "Portfolio Reweighted",
          message: `New portfolio generated using ${result.data.metadata.source === 'llm' ? 'AI analysis' : 'our algorithm'}`
        })
      } else {
        throw new Error('Invalid response format')
      }

    } catch (error) {
      console.error('Portfolio reweighting failed:', error)
      addNotification({
        type: "error",
        title: "Reweighting Failed",
        message: error instanceof Error ? error.message : "Failed to reweight portfolio. Please try again."
      })
      setStep('input')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleAcceptPortfolio = () => {
    if (newPortfolio) {
      // Save to localStorage
      localStorage.setItem("demo-portfolio", JSON.stringify(newPortfolio))
      localStorage.setItem("user-portfolio", JSON.stringify(newPortfolio))
      
      // Update parent component
      onPortfolioUpdated(newPortfolio)
      
      addNotification({
        type: "success",
        title: "Portfolio Updated",
        message: "Your portfolio has been successfully reweighted"
      })
      
      // Reset and close
      setStep('input')
      setReweightReason("")
      setNewPortfolio(null)
      onOpenChange(false)
    }
  }

  const handleReject = () => {
    setStep('input')
    setNewPortfolio(null)
  }

  const getRiskToleranceFromLevel = (riskLevel: string): number => {
    switch (riskLevel.toLowerCase()) {
      case 'conservative': return 2
      case 'moderate': return 3
      case 'aggressive': return 4
      default: return 3
    }
  }

  const extractThemesFromPortfolio = (portfolio: PersonalizedPortfolio): string[] => {
    const categories = [...new Set(portfolio.allocations.map(alloc => alloc.category))]
    const themeMap: Record<string, string> = {
      'Growth Stocks': 'Technology',
      'Technology': 'Technology',
      'Healthcare': 'Healthcare',
      'ETFs / Index Funds': 'ETFs / Index Funds',
      'Bonds': 'Bonds',
      'REITs': 'Real Estate',
      'International': 'International',
      'Value Stocks': 'Value Investing'
    }
    
    return categories.map(cat => themeMap[cat] || cat).slice(0, 3)
  }

  const compareAllocations = (current: PortfolioAllocation[], new_: PortfolioAllocation[]) => {
    const changes: Array<{symbol: string, oldAllocation: number, newAllocation: number, change: number}> = []
    
    // Track changes in existing holdings
    current.forEach(currentAlloc => {
      const newAlloc = new_.find(a => a.symbol === currentAlloc.symbol)
      if (newAlloc) {
        const change = newAlloc.allocation - currentAlloc.allocation
        if (Math.abs(change) > 0.1) { // Only show changes > 0.1%
          changes.push({
            symbol: currentAlloc.symbol,
            oldAllocation: currentAlloc.allocation,
            newAllocation: newAlloc.allocation,
            change
          })
        }
      } else {
        // Holding was removed
        changes.push({
          symbol: currentAlloc.symbol,
          oldAllocation: currentAlloc.allocation,
          newAllocation: 0,
          change: -currentAlloc.allocation
        })
      }
    })
    
    // Track new holdings
    new_.forEach(newAlloc => {
      const currentAlloc = current.find(a => a.symbol === newAlloc.symbol)
      if (!currentAlloc) {
        changes.push({
          symbol: newAlloc.symbol,
          oldAllocation: 0,
          newAllocation: newAlloc.allocation,
          change: newAlloc.allocation
        })
      }
    })
    
    return changes.sort((a, b) => Math.abs(b.change) - Math.abs(a.change))
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-slate-800 border-slate-700 text-white max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <RefreshCw className="h-5 w-5 text-emerald-400" />
            Reweight Portfolio with AI
          </DialogTitle>
          <DialogDescription className="text-slate-400">
            Use AI to rebalance your portfolio based on your specific needs and market conditions
          </DialogDescription>
        </DialogHeader>

        {step === 'input' && (
          <div className="space-y-6">
            <div className="space-y-3">
              <Label htmlFor="reweight-reason" className="text-sm font-medium">
                Why do you want to reweight your portfolio?
              </Label>
              <Textarea
                id="reweight-reason"
                placeholder="e.g., 'I want to reduce risk and increase bond allocation', 'Add more technology exposure', 'Rebalance based on recent market changes', 'I'm approaching retirement and need more conservative investments'..."
                value={reweightReason}
                onChange={(e) => setReweightReason(e.target.value)}
                className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 min-h-[100px]"
              />
              <p className="text-xs text-slate-500">
                Be specific about your goals, risk tolerance changes, or market concerns
              </p>
            </div>

            {currentPortfolio && (
              <Card className="bg-slate-700/50 border-slate-600">
                <CardHeader>
                  <CardTitle className="text-sm">Current Portfolio Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-slate-400">Risk Level:</span>
                      <span className="ml-2 font-medium">{currentPortfolio.riskLevel}</span>
                    </div>
                    <div>
                      <span className="text-slate-400">Expected Return:</span>
                      <span className="ml-2 font-medium">{currentPortfolio.expectedReturn}</span>
                    </div>
                    <div>
                      <span className="text-slate-400">Holdings:</span>
                      <span className="ml-2 font-medium">{currentPortfolio.allocations.length} investments</span>
                    </div>
                    <div>
                      <span className="text-slate-400">Strategy:</span>
                      <span className="ml-2 font-medium">{currentPortfolio.strategy.substring(0, 30)}...</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            <div className="flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => onOpenChange(false)}
                className="border-slate-600 text-slate-300 hover:bg-slate-700"
              >
                Cancel
              </Button>
              <Button
                onClick={handleReweight}
                disabled={!reweightReason.trim() || isGenerating}
                className="bg-emerald-600 hover:bg-emerald-700"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <TrendingUp className="h-4 w-4 mr-2" />
                    Reweight Portfolio
                  </>
                )}
              </Button>
            </div>
          </div>
        )}

        {step === 'generating' && (
          <div className="flex flex-col items-center justify-center py-12 space-y-4">
            <Loader2 className="h-12 w-12 animate-spin text-emerald-400" />
            <div className="text-center space-y-2">
              <h3 className="text-lg font-medium">Analyzing Your Portfolio</h3>
              <p className="text-slate-400">AI is reweighting your investments based on your requirements...</p>
            </div>
          </div>
        )}

        {step === 'review' && newPortfolio && currentPortfolio && (
          <div className="space-y-6">
            <div className="flex items-center gap-2 text-emerald-400">
              <CheckCircle className="h-5 w-5" />
              <span className="font-medium">New Portfolio Generated</span>
            </div>

            {/* Portfolio Comparison */}
            <div className="grid md:grid-cols-2 gap-6">
              <Card className="bg-slate-700/50 border-slate-600">
                <CardHeader>
                  <CardTitle className="text-sm">Current Portfolio</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2 text-sm">
                  <div><span className="text-slate-400">Risk:</span> {currentPortfolio.riskLevel}</div>
                  <div><span className="text-slate-400">Return:</span> {currentPortfolio.expectedReturn}</div>
                  <div><span className="text-slate-400">Holdings:</span> {currentPortfolio.allocations.length}</div>
                </CardContent>
              </Card>

              <Card className="bg-emerald-900/20 border-emerald-600/50">
                <CardHeader>
                  <CardTitle className="text-sm text-emerald-400">New Portfolio</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2 text-sm">
                  <div><span className="text-slate-400">Risk:</span> {newPortfolio.riskLevel}</div>
                  <div><span className="text-slate-400">Return:</span> {newPortfolio.expectedReturn}</div>
                  <div><span className="text-slate-400">Holdings:</span> {newPortfolio.allocations.length}</div>
                </CardContent>
              </Card>
            </div>

            {/* Key Changes */}
            <Card className="bg-slate-700/50 border-slate-600">
              <CardHeader>
                <CardTitle className="text-sm">Key Changes</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {compareAllocations(currentPortfolio.allocations, newPortfolio.allocations)
                    .slice(0, 5)
                    .map((change, index) => (
                      <div key={index} className="flex items-center justify-between text-sm">
                        <span>{change.symbol}</span>
                        <div className="flex items-center gap-2">
                          <span className="text-slate-400">{change.oldAllocation.toFixed(1)}%</span>
                          <span>→</span>
                          <span className="font-medium">{change.newAllocation.toFixed(1)}%</span>
                          <Badge 
                            variant={change.change > 0 ? "default" : "destructive"}
                            className="text-xs"
                          >
                            {change.change > 0 ? '+' : ''}{change.change.toFixed(1)}%
                          </Badge>
                        </div>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>

            {/* New Strategy */}
            <Card className="bg-slate-700/50 border-slate-600">
              <CardHeader>
                <CardTitle className="text-sm">Updated Strategy</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-slate-300">{newPortfolio.strategy}</p>
              </CardContent>
            </Card>

            {/* Rationale */}
            <Card className="bg-slate-700/50 border-slate-600">
              <CardHeader>
                <CardTitle className="text-sm">AI Rationale</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-slate-300">{newPortfolio.rationale}</p>
              </CardContent>
            </Card>

            <div className="flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={handleReject}
                className="border-slate-600 text-slate-300 hover:bg-slate-700"
              >
                <AlertTriangle className="h-4 w-4 mr-2" />
                Try Again
              </Button>
              <Button
                onClick={handleAcceptPortfolio}
                className="bg-emerald-600 hover:bg-emerald-700"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Accept New Portfolio
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
