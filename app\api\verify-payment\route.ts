import { NextResponse } from 'next/server'
import Stripe from 'stripe'
import { createClient } from '@supabase/supabase-js'

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2023-10-16',
})

// Initialize Supabase
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''
)

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('session_id')
    
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      )
    }
    
    // Retrieve the checkout session from Stripe
    const session = await stripe.checkout.sessions.retrieve(sessionId)
    
    if (!session) {
      return NextResponse.json(
        { error: 'Invalid session ID' },
        { status: 404 }
      )
    }
    
    // Check if payment was successful
    if (session.payment_status !== 'paid') {
      return NextResponse.json(
        { error: 'Payment not completed' },
        { status: 400 }
      )
    }
    
    const userId = session.metadata?.userId
    const amount = session.amount_total ? session.amount_total / 100 : 0 // Convert from cents
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID not found in session metadata' },
        { status: 400 }
      )
    }
    
    // Record the payment in your database
    try {
      // First, check if this payment has already been processed
      const { data: existingPayment } = await supabase
        .from('payments')
        .select('id')
        .eq('stripe_session_id', sessionId)
        .single()
      
      if (existingPayment) {
        // Payment already processed
        return NextResponse.json({
          success: true,
          amount,
          message: 'Payment already processed',
          alreadyProcessed: true
        })
      }
      
      // Insert payment record
      const { error: paymentError } = await supabase
        .from('payments')
        .insert({
          user_id: userId,
          stripe_session_id: sessionId,
          stripe_payment_intent_id: session.payment_intent,
          amount,
          currency: session.currency || 'usd',
          status: 'completed',
          payment_method: 'stripe',
          created_at: new Date().toISOString()
        })
      
      if (paymentError) {
        console.error('Error recording payment:', paymentError)
        // Don't fail the verification if we can't record it
        // The webhook will handle this as backup
      }
      
      // Update user balance (if you have a user_balances table)
      const { error: balanceError } = await supabase.rpc('update_user_balance', {
        p_user_id: userId,
        p_amount: amount
      })
      
      if (balanceError) {
        console.error('Error updating user balance:', balanceError)
        // Continue anyway - webhook will handle this
      }
      
    } catch (dbError) {
      console.error('Database error during payment verification:', dbError)
      // Continue with success response - webhook will handle database updates
    }
    
    return NextResponse.json({
      success: true,
      amount,
      currency: session.currency || 'usd',
      paymentIntent: session.payment_intent,
      customerEmail: session.customer_details?.email
    })
    
  } catch (error) {
    console.error('Payment verification error:', error)
    return NextResponse.json(
      { error: 'Failed to verify payment' },
      { status: 500 }
    )
  }
}
