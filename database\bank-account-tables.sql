-- Bank account and round-up tables for Investry
-- Run these in your Supabase SQL editor after the payment tables

-- <PERSON><PERSON> function to update updated_at timestamp (if it doesn't exist)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 1. Linked bank accounts table
CREATE TABLE IF NOT EXISTS linked_accounts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    plaid_account_id VARCHAR(255) NOT NULL,
    plaid_item_id VARCHAR(255) NOT NULL,
    plaid_access_token TEXT NOT NULL, -- Encrypted in production
    account_name VARCHAR(255) NOT NULL,
    account_mask VARCHAR(10) NOT NULL,
    account_type VARCHAR(50) NOT NULL, -- checking, savings, etc.
    account_subtype VARCHAR(50),
    institution_name VARCHAR(255) NOT NULL,
    institution_id VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    roundups_enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, plaid_account_id)
);

-- 2. Transactions table (for round-up processing)
CREATE TABLE IF NOT EXISTS transactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    linked_account_id UUID, -- Will add foreign key constraint after linked_accounts table is created
    plaid_transaction_id VARCHAR(255) UNIQUE NOT NULL,
    amount DECIMAL(12, 2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    merchant_name VARCHAR(255),
    category JSONB,
    date DATE NOT NULL,
    pending BOOLEAN DEFAULT false,
    roundup_amount DECIMAL(12, 2),
    roundup_processed BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Round-ups table
CREATE TABLE IF NOT EXISTS roundups (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    transaction_id UUID, -- Will add foreign key constraint after transactions table is created
    linked_account_id UUID, -- Will add foreign key constraint after linked_accounts table is created
    original_amount DECIMAL(12, 2) NOT NULL,
    roundup_amount DECIMAL(12, 2) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending' NOT NULL, -- pending, processed, failed
    ach_transfer_id VARCHAR(255), -- Reference to ACH transfer
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. ACH transfers table
CREATE TABLE IF NOT EXISTS ach_transfers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    linked_account_id UUID, -- Will add foreign key constraint after linked_accounts table is created
    external_transfer_id VARCHAR(255) UNIQUE, -- Dwolla/Stripe transfer ID
    amount DECIMAL(12, 2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    status VARCHAR(50) DEFAULT 'pending' NOT NULL, -- pending, processing, completed, failed, cancelled
    transfer_type VARCHAR(50) DEFAULT 'roundup' NOT NULL, -- roundup, manual, deposit
    failure_reason TEXT,
    external_status VARCHAR(100),
    fees DECIMAL(12, 2) DEFAULT 0.00,
    estimated_completion TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Round-up settings table
CREATE TABLE IF NOT EXISTS roundup_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    multiplier DECIMAL(3, 1) DEFAULT 1.0, -- 1.0 = normal, 2.0 = double roundups, etc.
    minimum_roundup DECIMAL(12, 2) DEFAULT 0.01,
    maximum_roundup DECIMAL(12, 2) DEFAULT 5.00,
    batch_threshold DECIMAL(12, 2) DEFAULT 5.00, -- Minimum amount before processing
    auto_invest BOOLEAN DEFAULT true,
    categories_enabled JSONB DEFAULT '[]'::jsonb, -- Empty = all categories
    categories_disabled JSONB DEFAULT '[]'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Add foreign key constraints after all tables are created
ALTER TABLE transactions
ADD CONSTRAINT fk_transactions_linked_account
FOREIGN KEY (linked_account_id) REFERENCES linked_accounts(id) ON DELETE CASCADE;

ALTER TABLE roundups
ADD CONSTRAINT fk_roundups_transaction
FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE;

ALTER TABLE roundups
ADD CONSTRAINT fk_roundups_linked_account
FOREIGN KEY (linked_account_id) REFERENCES linked_accounts(id) ON DELETE CASCADE;

ALTER TABLE ach_transfers
ADD CONSTRAINT fk_ach_transfers_linked_account
FOREIGN KEY (linked_account_id) REFERENCES linked_accounts(id) ON DELETE CASCADE;

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_linked_accounts_user_id ON linked_accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_linked_accounts_plaid_item_id ON linked_accounts(plaid_item_id);
CREATE INDEX IF NOT EXISTS idx_linked_accounts_active ON linked_accounts(is_active);

CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_transactions_account_id ON transactions(linked_account_id);
CREATE INDEX IF NOT EXISTS idx_transactions_plaid_id ON transactions(plaid_transaction_id);
CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(date);
CREATE INDEX IF NOT EXISTS idx_transactions_roundup_processed ON transactions(roundup_processed);

CREATE INDEX IF NOT EXISTS idx_roundups_user_id ON roundups(user_id);
CREATE INDEX IF NOT EXISTS idx_roundups_status ON roundups(status);
CREATE INDEX IF NOT EXISTS idx_roundups_created_at ON roundups(created_at);

CREATE INDEX IF NOT EXISTS idx_ach_transfers_user_id ON ach_transfers(user_id);
CREATE INDEX IF NOT EXISTS idx_ach_transfers_status ON ach_transfers(status);
CREATE INDEX IF NOT EXISTS idx_ach_transfers_external_id ON ach_transfers(external_transfer_id);

-- RLS (Row Level Security) policies
ALTER TABLE linked_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE roundups ENABLE ROW LEVEL SECURITY;
ALTER TABLE ach_transfers ENABLE ROW LEVEL SECURITY;
ALTER TABLE roundup_settings ENABLE ROW LEVEL SECURITY;

-- Linked accounts policies
CREATE POLICY "Users can view their own linked accounts" ON linked_accounts
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own linked accounts" ON linked_accounts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own linked accounts" ON linked_accounts
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own linked accounts" ON linked_accounts
    FOR DELETE USING (auth.uid() = user_id);

-- Transactions policies
CREATE POLICY "Users can view their own transactions" ON transactions
    FOR SELECT USING (auth.uid() = user_id);

-- Roundups policies
CREATE POLICY "Users can view their own roundups" ON roundups
    FOR SELECT USING (auth.uid() = user_id);

-- ACH transfers policies
CREATE POLICY "Users can view their own ACH transfers" ON ach_transfers
    FOR SELECT USING (auth.uid() = user_id);

-- Roundup settings policies
CREATE POLICY "Users can view their own roundup settings" ON roundup_settings
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own roundup settings" ON roundup_settings
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own roundup settings" ON roundup_settings
    FOR UPDATE USING (auth.uid() = user_id);

-- Functions for round-up calculations
CREATE OR REPLACE FUNCTION calculate_roundup(amount DECIMAL)
RETURNS DECIMAL AS $$
BEGIN
    -- Round up to nearest dollar
    RETURN CEIL(amount) - amount;
END;
$$ LANGUAGE plpgsql;

-- Function to get user's total roundups
CREATE OR REPLACE FUNCTION get_user_total_roundups(p_user_id UUID)
RETURNS DECIMAL AS $$
DECLARE
    total_roundups DECIMAL;
BEGIN
    SELECT COALESCE(SUM(roundup_amount), 0.00) INTO total_roundups
    FROM roundups
    WHERE user_id = p_user_id AND status = 'processed';
    
    RETURN total_roundups;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get pending roundups for batch processing
CREATE OR REPLACE FUNCTION get_pending_roundups_for_user(p_user_id UUID, p_threshold DECIMAL DEFAULT 5.00)
RETURNS TABLE(
    total_amount DECIMAL,
    roundup_count INTEGER,
    linked_account_id UUID
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        SUM(r.roundup_amount) as total_amount,
        COUNT(*)::INTEGER as roundup_count,
        r.linked_account_id
    FROM roundups r
    WHERE r.user_id = p_user_id 
        AND r.status = 'pending'
    GROUP BY r.linked_account_id
    HAVING SUM(r.roundup_amount) >= p_threshold;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update updated_at timestamp
CREATE TRIGGER update_linked_accounts_updated_at BEFORE UPDATE ON linked_accounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_transactions_updated_at BEFORE UPDATE ON transactions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_roundups_updated_at BEFORE UPDATE ON roundups
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ach_transfers_updated_at BEFORE UPDATE ON ach_transfers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_roundup_settings_updated_at BEFORE UPDATE ON roundup_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
