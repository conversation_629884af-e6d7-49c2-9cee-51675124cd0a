"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { TopNav } from "@/components/top-nav"
import {
  User,
  Mail,
  GraduationCap,
  Calendar,
  Edit2,
  Check,
  X,
  LogOut,
  Settings,
  Shield,
  Bell,
  CreditCard,
  HelpCircle,
  Target,
  DollarSign,
  Wallet,
  Crown,
  Zap,
} from "lucide-react"
import { useNotifications } from "@/components/notification-system"
import { useAuth } from "@/components/auth-provider"
import { useSubscription } from "@/hooks/use-subscription"
import { supabase } from "@/lib/supabase"

export default function ProfilePage() {
  const router = useRouter()
  const { addNotification } = useNotifications()
  const { user, loading: authLoading } = useAuth()
  const subscription = useSubscription()
  const [loading, setLoading] = useState(true)
  const [isEditingName, setIsEditingName] = useState(false)
  const [editedName, setEditedName] = useState("")
  const [isEditingCollege, setIsEditingCollege] = useState(false)
  const [editedCollege, setEditedCollege] = useState({
    university: "",
    major: "",
    graduationYear: "",
    isStudent: false
  })
  const [showSignOutConfirm, setShowSignOutConfirm] = useState(false)

  // Helper functions to get user data from Supabase user object
  const getUserName = () => {
    const firstName = user?.user_metadata?.first_name || ""
    const lastName = user?.user_metadata?.last_name || ""
    const fullName = `${firstName} ${lastName}`.trim()
    return fullName || editedName || user?.email?.split('@')[0] || "User"
  }

  const getUserEmail = () => {
    return user?.email || ""
  }

  const getUserSchool = () => {
    return user?.user_metadata?.university || null
  }

  const getUserMajor = () => {
    return user?.user_metadata?.major || null
  }

  const getUserGraduationYear = () => {
    return user?.user_metadata?.graduation_year || null
  }

  const getIsStudent = () => {
    return user?.user_metadata?.is_student || false
  }

  useEffect(() => {
    // Wait for auth to load
    if (authLoading) return

    // Check if user is authenticated with Supabase
    if (!user) {
      router.push("/auth/signin")
      return
    }

    // Set user data from Supabase auth
    setEditedName(user.user_metadata?.full_name || user.email?.split('@')[0] || "")
    setEditedCollege({
      university: user.user_metadata?.university || "",
      major: user.user_metadata?.major || "",
      graduationYear: user.user_metadata?.graduation_year?.toString() || "",
      isStudent: user.user_metadata?.is_student || false
    })
    setLoading(false)
  }, [user, authLoading, router])

  const handleSaveName = () => {
    if (editedName.trim()) {
      // In a real app, you would update the user profile in Supabase here
      // For now, we'll just update the local state and show a notification
      setIsEditingName(false)

      addNotification({
        type: "success",
        title: "Profile updated",
        message: "Your name has been updated successfully.",
      })
    }
  }

  const handleSaveCollege = () => {
    // In a real app, you would update the user profile in Supabase here
    // For now, we'll just update the local state and show a notification
    setIsEditingCollege(false)

    addNotification({
      type: "success",
      title: "College information updated",
      message: "Your college information has been updated successfully.",
    })
  }

  const handleCollegeInputChange = (field: string, value: string | boolean) => {
    setEditedCollege(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSignOut = async () => {
    try {
      if (supabase) {
        await supabase.auth.signOut()
      }

      addNotification({
        type: "info",
        title: "Signed out",
        message: "You've been successfully signed out.",
      })

      router.push("/")
    } catch (error) {
      console.error("Error signing out:", error)
      addNotification({
        type: "error",
        title: "Sign out failed",
        message: "There was an error signing out. Please try again.",
      })
    }
  }

  if (authLoading || loading || !user) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-white">Loading...</div>
      </div>
    )
  }

  const profileSections = [
    {
      title: "Account Settings",
      icon: Settings,
      items: [
        { label: "Personal Information", action: () => {} },
        { label: "Password & Security", action: () => {} },
        { label: "Email Preferences", action: () => {} },
      ],
    },
    {
      title: "Investment Profile",
      icon: CreditCard,
      items: [
        { label: "Risk Tolerance", action: () => {} },
        { label: "Investment Goals", action: () => {} },
        { label: "Portfolio Preferences", action: () => {} },
      ],
    },
    {
      title: "Notifications",
      icon: Bell,
      items: [
        { label: "Price Alerts", action: () => {} },
        { label: "Market Updates", action: () => {} },
        { label: "Educational Content", action: () => {} },
      ],
    },
    {
      title: "Support",
      icon: HelpCircle,
      items: [
        { label: "Help Center", action: () => {} },
        { label: "Contact Support", action: () => {} },
        { label: "Feedback", action: () => {} },
      ],
    },
  ]

  return (
    <div className="min-h-screen bg-slate-900 text-white">
      <TopNav currentPage="profile" />

      <div className="container mx-auto px-4 py-8 space-y-8">
        {/* Profile Header */}
        <Card className="bg-slate-800/50 border-slate-700">
          <CardContent className="p-8">
            <div className="flex items-start gap-6">
              <div className="w-24 h-24 bg-emerald-500/20 rounded-full flex items-center justify-center">
                <User className="h-12 w-12 text-emerald-400" />
              </div>

              <div className="flex-1 space-y-6">
                <div className="flex items-center gap-3">
                  {isEditingName ? (
                    <div className="flex items-center gap-2">
                      <Input
                        value={editedName}
                        onChange={(e) => setEditedName(e.target.value)}
                        className="bg-slate-700 border-slate-600 text-white max-w-xs"
                        placeholder="Enter your name"
                      />
                      <Button size="sm" onClick={handleSaveName} className="bg-emerald-500 hover:bg-emerald-600">
                        <Check className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          setIsEditingName(false)
                          setEditedName(getUserName())
                        }}
                        className="border-slate-600"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ) : (
                    <div className="flex items-center gap-3">
                      <h2 className="text-3xl font-bold">{getUserName()}</h2>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => setIsEditingName(true)}
                        className="text-slate-400 hover:text-white"
                      >
                        <Edit2 className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </div>

                <div className="grid md:grid-cols-2 gap-6">
                  <div className="flex items-center gap-3 text-slate-300">
                    <Mail className="h-5 w-5" />
                    <span>{getUserEmail()}</span>
                  </div>

                  {getUserSchool() && (
                    <div className="flex items-center gap-3 text-slate-300">
                      <GraduationCap className="h-5 w-5" />
                      <span>{getUserSchool()}</span>
                    </div>
                  )}

                  {getUserMajor() && (
                    <div className="flex items-center gap-3">
                      <Badge variant="outline" className="border-emerald-400/30 text-emerald-400">
                        {getUserMajor()}
                      </Badge>
                    </div>
                  )}

                  {getUserGraduationYear() && (
                    <div className="flex items-center gap-3 text-slate-300">
                      <Calendar className="h-5 w-5" />
                      <span>Class of {getUserGraduationYear()}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Upgrade to Premium Section */}
        {!subscription.hasActiveSubscription && (
          <Card className="bg-gradient-to-r from-yellow-500/10 to-emerald-500/10 border-yellow-500/30">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Crown className="h-5 w-5 text-yellow-400" />
                Upgrade to investry Premium
              </CardTitle>
              <CardDescription>
                Unlock advanced features and professional investment tools
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <h4 className="font-medium text-white">Analytics</h4>
                  <ul className="text-sm text-slate-300 space-y-1">
                    <li>• Advanced charts</li>
                    <li>• Real-time data</li>
                    <li>• Custom indicators</li>
                  </ul>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium text-white">Portfolio Tools</h4>
                  <ul className="text-sm text-slate-300 space-y-1">
                    <li>• Auto-rebalancing</li>
                    <li>• Risk analysis</li>
                    <li>• Performance tracking</li>
                  </ul>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium text-white">Research</h4>
                  <ul className="text-sm text-slate-300 space-y-1">
                    <li>• Professional reports</li>
                    <li>• Market insights</li>
                    <li>• Screening tools</li>
                  </ul>
                </div>
              </div>
              <div className="flex items-center justify-between pt-4 border-t border-slate-700">
                <div>
                  <p className="text-2xl font-bold text-white">$29.99<span className="text-sm text-slate-400">/month</span></p>
                  <p className="text-sm text-slate-400">or $304.99/year • 14-day free trial • Cancel anytime</p>
                </div>
                <Button
                  onClick={() => router.push("/pricing")}
                  className="bg-gradient-to-r from-yellow-500 to-emerald-500 hover:from-yellow-600 hover:to-emerald-600 text-white"
                >
                  <Zap className="h-4 w-4 mr-2" />
                  Start Free Trial
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* College Information */}
        {getIsStudent() && (
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <GraduationCap className="h-5 w-5 text-emerald-400" />
                    College Information
                  </CardTitle>
                  <CardDescription>Your academic details and student status</CardDescription>
                </div>
                {!isEditingCollege && (
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setIsEditingCollege(true)}
                    className="text-slate-400 hover:text-white"
                  >
                    <Edit2 className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {isEditingCollege ? (
                <div className="space-y-4">
                  {/* Student Status */}
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="isStudent"
                      checked={editedCollege.isStudent}
                      onCheckedChange={(checked) => handleCollegeInputChange("isStudent", checked as boolean)}
                    />
                    <Label htmlFor="isStudent" className="text-slate-300">
                      I'm currently a college student
                    </Label>
                  </div>

                  {/* University */}
                  <div className="space-y-2">
                    <Label htmlFor="university" className="text-slate-300">
                      University/College
                    </Label>
                    <Input
                      id="university"
                      value={editedCollege.university}
                      onChange={(e) => handleCollegeInputChange("university", e.target.value)}
                      placeholder="University of California, Berkeley"
                      className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400"
                    />
                  </div>

                  {/* Major */}
                  <div className="space-y-2">
                    <Label htmlFor="major" className="text-slate-300">
                      Major
                    </Label>
                    <Input
                      id="major"
                      value={editedCollege.major}
                      onChange={(e) => handleCollegeInputChange("major", e.target.value)}
                      placeholder="Computer Science"
                      className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400"
                    />
                  </div>

                  {/* Graduation Year */}
                  <div className="space-y-2">
                    <Label htmlFor="graduationYear" className="text-slate-300">
                      Expected Graduation Year
                    </Label>
                    <Input
                      id="graduationYear"
                      value={editedCollege.graduationYear}
                      onChange={(e) => handleCollegeInputChange("graduationYear", e.target.value)}
                      placeholder="2025"
                      className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400"
                    />
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2 pt-2">
                    <Button
                      onClick={handleSaveCollege}
                      className="bg-emerald-500 hover:bg-emerald-600"
                    >
                      <Check className="h-4 w-4 mr-2" />
                      Save Changes
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setIsEditingCollege(false)
                        setEditedCollege({
                          university: getUserSchool() || "",
                          major: getUserMajor() || "",
                          graduationYear: getUserGraduationYear()?.toString() || "",
                          isStudent: getIsStudent()
                        })
                      }}
                      className="border-slate-600"
                    >
                      <X className="h-4 w-4 mr-2" />
                      Cancel
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="flex items-center gap-3 text-slate-300">
                      <GraduationCap className="h-5 w-5" />
                      <span>{getUserSchool() || "No university specified"}</span>
                    </div>

                    <div className="flex items-center gap-3">
                      <Badge variant="outline" className="border-emerald-400/30 text-emerald-400">
                        {getUserMajor() || "No major specified"}
                      </Badge>
                    </div>

                    <div className="flex items-center gap-3 text-slate-300">
                      <Calendar className="h-5 w-5" />
                      <span>Class of {getUserGraduationYear() || "Not specified"}</span>
                    </div>

                    <div className="flex items-center gap-3 text-slate-300">
                      <User className="h-5 w-5" />
                      <span>Student Status: Active</span>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Add College Information for Non-Students */}
        {!getIsStudent() && (
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <GraduationCap className="h-5 w-5 text-emerald-400" />
                College Information
              </CardTitle>
              <CardDescription>
                Are you a college student? Add your academic information to get personalized investment advice.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-6">
                <GraduationCap className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-white mb-2">Add College Information</h3>
                <p className="text-slate-400 mb-4">
                  Let us know if you're a student to get tailored investment recommendations for your situation.
                </p>
                <Button
                  onClick={() => {
                    setEditedCollege({
                      university: "",
                      major: "",
                      graduationYear: "",
                      isStudent: true
                    })
                    setIsEditingCollege(true)
                  }}
                  className="bg-emerald-500 hover:bg-emerald-600"
                >
                  <GraduationCap className="h-4 w-4 mr-2" />
                  I'm a College Student
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Investment Profile */}
        <Card className="bg-slate-800/50 border-slate-700">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5 text-emerald-400" />
                  Investment Profile
                </CardTitle>
                <CardDescription>Your investment preferences and risk profile</CardDescription>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push("/onboarding")}
                className="border-slate-600 text-slate-300 hover:bg-slate-700"
              >
                <Edit2 className="h-4 w-4 mr-2" />
                Edit
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {(() => {
              const userSurveyKey = `investry_survey-${user?.id}`
              const surveyData = localStorage.getItem(userSurveyKey)
              if (surveyData) {
                const survey = JSON.parse(surveyData)
                return (
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label className="text-slate-400 flex items-center gap-2">
                        <Target className="h-4 w-4" />
                        Primary Goal
                      </Label>
                      <p className="text-white">{survey.primaryGoal}</p>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-slate-400">Risk Tolerance</Label>
                      <div className="flex items-center gap-2">
                        <Badge
                          variant="secondary"
                          className={
                            survey.riskTolerance <= 2
                              ? "bg-blue-600"
                              : survey.riskTolerance <= 3
                                ? "bg-yellow-600"
                                : "bg-red-600"
                          }
                        >
                          {survey.riskTolerance <= 2
                            ? "Conservative"
                            : survey.riskTolerance <= 3
                              ? "Moderate"
                              : "Aggressive"}
                        </Badge>
                        <span className="text-slate-400 text-sm">({survey.riskTolerance}/5)</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-slate-400">Investment Timeline</Label>
                      <p className="text-white">{survey.timeHorizon}</p>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-slate-400 flex items-center gap-2">
                        <DollarSign className="h-4 w-4" />
                        Monthly Investment
                      </Label>
                      <p className="text-white">{survey.monthlyInvestment}</p>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-slate-400">Experience Level</Label>
                      <p className="text-white">{survey.experienceLevel}</p>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-slate-400">Investment Themes</Label>
                      <div className="flex flex-wrap gap-2">
                        {survey.interestedThemes?.slice(0, 3).map((theme: string, index: number) => (
                          <Badge key={index} variant="outline" className="border-slate-600 text-slate-300">
                            {theme}
                          </Badge>
                        ))}
                        {survey.interestedThemes?.length > 3 && (
                          <Badge variant="outline" className="border-slate-600 text-slate-300">
                            +{survey.interestedThemes.length - 3} more
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                )
              }
              return (
                <div className="text-center py-8">
                  <Shield className="h-16 w-16 text-slate-400 mx-auto mb-4" />
                  <p className="text-slate-400 mb-4">
                    Complete your investment profile to get personalized recommendations
                  </p>
                  <Button onClick={() => router.push("/onboarding")} className="bg-emerald-500 hover:bg-emerald-600">
                    Complete Profile
                  </Button>
                </div>
              )
            })()}
          </CardContent>
        </Card>

        {/* Settings Sections */}
        <div className="grid md:grid-cols-2 gap-6">
          {profileSections.map((section, index) => (
            <Card key={index} className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <section.icon className="h-5 w-5 text-emerald-400" />
                  {section.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-1">
                  {section.items.map((item, itemIndex) => (
                    <div key={itemIndex}>
                      <Button
                        variant="ghost"
                        className="w-full justify-start text-slate-300 hover:text-white hover:bg-slate-700/50"
                        onClick={item.action}
                      >
                        {item.label}
                      </Button>
                      {itemIndex < section.items.length - 1 && <Separator className="bg-slate-700" />}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Sign Out Button */}
        <div className="flex justify-center">
          <Button
            variant="outline"
            onClick={() => setShowSignOutConfirm(true)}
            className="border-red-600 text-red-400 hover:bg-red-600/10"
          >
            <LogOut className="h-4 w-4 mr-2" />
            Sign Out
          </Button>
        </div>
      </div>

      {/* Sign Out Confirmation */}
      {showSignOutConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="bg-slate-800 border-slate-700 max-w-md w-full">
            <CardHeader>
              <CardTitle className="text-white">Sign Out</CardTitle>
              <CardDescription>Are you sure you want to sign out of your account?</CardDescription>
            </CardHeader>
            <CardContent className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => setShowSignOutConfirm(false)}
                className="flex-1 border-slate-600"
              >
                Cancel
              </Button>
              <Button onClick={handleSignOut} className="flex-1 bg-red-600 hover:bg-red-700">
                Sign Out
              </Button>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Footer */}
      <footer className="relative z-10 border-t border-slate-700/50 bg-slate-800/30 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center gap-2 mb-4 md:mb-0">
              <Wallet className="h-6 w-6 text-emerald-400" />
              <span className="text-white font-semibold text-lg">investry</span>
            </div>
            <div className="flex items-center gap-6 text-sm text-slate-400">
              <span>Empowering investors of all backgrounds</span>
              <div className="flex items-center gap-4">
                <a href="#" className="hover:text-white transition-colors">
                  Privacy
                </a>
                <a href="#" className="hover:text-white transition-colors">
                  Terms
                </a>
                <a href="#" className="hover:text-white transition-colors">
                  Support
                </a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
