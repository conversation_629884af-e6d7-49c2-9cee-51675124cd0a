"use client"

import { useState } from 'react'
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { RefreshCw, Plus, X, TrendingUp, TrendingDown, Search } from 'lucide-react'
import { useMultipleStockQuotes, useStockSearch } from '@/hooks/use-stock-data'
import { cn } from '@/lib/utils'

interface StockWatchlistProps {
  initialSymbols?: string[]
  autoRefresh?: boolean
  maxSymbols?: number
  className?: string
}

export function StockWatchlist({ 
  initialSymbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA'],
  autoRefresh = true,
  maxSymbols = 20,
  className 
}: StockWatchlistProps) {
  const [symbols, setSymbols] = useState<string[]>(initialSymbols)
  const [searchQuery, setSearchQuery] = useState('')
  const [showSearch, setShowSearch] = useState(false)
  
  const { data: quotes, loading, error, refetch, lastUpdated } = useMultipleStockQuotes(symbols, autoRefresh)
  const { data: searchResults, loading: searchLoading, search } = useStockSearch()

  const handleAddSymbol = (symbol: string) => {
    const upperSymbol = symbol.toUpperCase()
    if (!symbols.includes(upperSymbol) && symbols.length < maxSymbols) {
      setSymbols([...symbols, upperSymbol])
      setSearchQuery('')
      setShowSearch(false)
    }
  }

  const handleRemoveSymbol = (symbol: string) => {
    setSymbols(symbols.filter(s => s !== symbol))
  }

  const handleSearch = async (query: string) => {
    setSearchQuery(query)
    if (query.length > 0) {
      await search(query)
    }
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(price)
  }

  const formatChange = (change: number, changePercent: number) => {
    const changeStr = change >= 0 ? `+${change.toFixed(2)}` : change.toFixed(2)
    const percentStr = changePercent >= 0 ? `+${changePercent.toFixed(2)}%` : `${changePercent.toFixed(2)}%`
    return `${changeStr} (${percentStr})`
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-xl font-semibold">Stock Watchlist</CardTitle>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSearch(!showSearch)}
            >
              <Plus className="h-4 w-4" />
              Add Stock
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={refetch}
              disabled={loading}
            >
              <RefreshCw className={cn("h-4 w-4", loading && "animate-spin")} />
            </Button>
          </div>
        </div>

        {/* Search Interface */}
        {showSearch && (
          <div className="space-y-3 border-t pt-4">
            <div className="flex space-x-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search stocks (e.g., AAPL, Apple, Technology)"
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button
                variant="outline"
                onClick={() => setShowSearch(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Search Results */}
            {searchQuery && (
              <div className="max-h-48 overflow-y-auto space-y-1">
                {searchLoading ? (
                  <div className="flex items-center justify-center py-4">
                    <RefreshCw className="h-4 w-4 animate-spin" />
                    <span className="ml-2 text-sm">Searching...</span>
                  </div>
                ) : searchResults.length > 0 ? (
                  searchResults.map((result) => (
                    <div
                      key={result.symbol}
                      className="flex items-center justify-between p-2 hover:bg-muted rounded cursor-pointer"
                      onClick={() => handleAddSymbol(result.symbol)}
                    >
                      <div>
                        <div className="font-medium">{result.symbol}</div>
                        <div className="text-sm text-muted-foreground truncate">
                          {result.name}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">{formatPrice(result.price)}</div>
                        <div className={cn(
                          "text-sm",
                          result.change >= 0 ? "text-green-600" : "text-red-600"
                        )}>
                          {result.changePercent.toFixed(2)}%
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4 text-muted-foreground">
                    No results found
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </CardHeader>

      <CardContent>
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {loading && quotes.length === 0 ? (
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin" />
            <span className="ml-2">Loading watchlist...</span>
          </div>
        ) : (
          <div className="space-y-3">
            {quotes.map((quote) => {
              const isPositive = quote.change >= 0
              const changeColor = isPositive ? 'text-green-600' : 'text-red-600'
              const TrendIcon = isPositive ? TrendingUp : TrendingDown

              return (
                <div
                  key={quote.symbol}
                  className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveSymbol(quote.symbol)}
                      className="h-6 w-6 p-0 text-muted-foreground hover:text-red-500"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                    <div>
                      <div className="font-semibold">{quote.symbol}</div>
                      <div className="text-sm text-muted-foreground truncate max-w-[200px]">
                        {quote.name}
                      </div>
                    </div>
                  </div>

                  <div className="text-right">
                    <div className="font-semibold text-lg">
                      {formatPrice(quote.price)}
                    </div>
                    <div className={cn("flex items-center justify-end space-x-1 text-sm", changeColor)}>
                      <TrendIcon className="h-3 w-3" />
                      <span>{formatChange(quote.change, quote.changePercent)}</span>
                    </div>
                  </div>

                  <div className="flex flex-col items-end space-y-1">
                    {quote.source && (
                      <Badge variant="secondary" className="text-xs">
                        {quote.source}
                      </Badge>
                    )}
                    {quote.exchange && (
                      <span className="text-xs text-muted-foreground">
                        {quote.exchange}
                      </span>
                    )}
                  </div>
                </div>
              )
            })}

            {symbols.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <p>No stocks in your watchlist</p>
                <Button
                  variant="outline"
                  className="mt-2"
                  onClick={() => setShowSearch(true)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add your first stock
                </Button>
              </div>
            )}
          </div>
        )}

        {/* Footer */}
        <div className="flex items-center justify-between text-xs text-muted-foreground mt-4 pt-4 border-t">
          <span>
            {symbols.length} of {maxSymbols} stocks
          </span>
          <span>
            {lastUpdated ? `Updated: ${lastUpdated.toLocaleTimeString()}` : 'Not updated'}
          </span>
          <span>
            {autoRefresh ? 'Auto-refresh: ON' : 'Auto-refresh: OFF'}
          </span>
        </div>
      </CardContent>
    </Card>
  )
}
