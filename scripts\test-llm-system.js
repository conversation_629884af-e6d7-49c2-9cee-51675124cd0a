/**
 * Manual Test Script for LLM Portfolio System
 * Run with: node scripts/test-llm-system.js
 */

const { generateEnhancedPortfolio } = require('../lib/enhanced-portfolio-generator')
const { checkLLMSystemHealth, getConfigStatus } = require('../lib/llm-portfolio')

// Mock survey data
const testSurveyData = {
  primaryGoal: "Long-term wealth building",
  timeHorizon: "10+ years",
  riskTolerance: 3,
  experienceLevel: "Beginner",
  interestedThemes: ["Technology", "Healthcare", "Clean Energy"],
  monthlyInvestment: 1000,
  major: "computer-science"
}

async function testLLMSystem() {
  console.log('🚀 Testing LLM Portfolio Generation System\n')

  try {
    // Test 1: Configuration Status
    console.log('1️⃣ Checking Configuration...')
    const configStatus = getConfigStatus()
    console.log('Configuration Status:', {
      configured: configStatus.configured,
      errors: configStatus.errors.length > 0 ? configStatus.errors : 'None'
    })
    console.log('')

    // Test 2: System Health Check
    console.log('2️⃣ Performing Health Check...')
    const health = await checkLLMSystemHealth()
    console.log('System Health:', {
      status: health.status,
      checks: health.checks,
      errors: health.errors.length > 0 ? health.errors : 'None'
    })
    console.log('')

    // Test 3: Portfolio Generation (Fallback)
    console.log('3️⃣ Testing Portfolio Generation (Fallback Mode)...')
    const startTime = Date.now()
    
    const result = await generateEnhancedPortfolio(
      testSurveyData,
      'test-user-123',
      'computer-science',
      {
        useLLM: false, // Force fallback for testing
        fallbackToRules: true
      }
    )

    const endTime = Date.now()
    
    console.log('Portfolio Generation Result:')
    console.log('- Source:', result.source)
    console.log('- Confidence:', result.confidence)
    console.log('- Processing Time:', result.processingTimeMs, 'ms')
    console.log('- Actual Time:', endTime - startTime, 'ms')
    console.log('- Allocations Count:', result.portfolio.allocations.length)
    console.log('- Risk Level:', result.portfolio.riskLevel)
    console.log('- Expected Return:', result.portfolio.expectedReturn)
    
    // Validate allocations sum to 100%
    const totalAllocation = result.portfolio.allocations.reduce(
      (sum, alloc) => sum + alloc.allocation, 
      0
    )
    console.log('- Total Allocation:', totalAllocation.toFixed(2) + '%')
    console.log('- Allocation Valid:', Math.abs(totalAllocation - 100) < 0.01 ? '✅' : '❌')
    console.log('')

    // Test 4: Portfolio Generation (LLM Mode if configured)
    if (configStatus.configured) {
      console.log('4️⃣ Testing Portfolio Generation (LLM Mode)...')
      try {
        const llmStartTime = Date.now()
        
        const llmResult = await generateEnhancedPortfolio(
          testSurveyData,
          'test-user-456',
          'computer-science',
          {
            useLLM: true,
            fallbackToRules: true,
            additionalContext: 'This is a test generation for system validation.'
          }
        )

        const llmEndTime = Date.now()
        
        console.log('LLM Portfolio Generation Result:')
        console.log('- Source:', llmResult.source)
        console.log('- Confidence:', llmResult.confidence)
        console.log('- Processing Time:', llmResult.processingTimeMs, 'ms')
        console.log('- Actual Time:', llmEndTime - llmStartTime, 'ms')
        console.log('- LLM Used:', llmResult.metadata?.llmUsed ? '✅' : '❌')
        console.log('- Cache Hit:', llmResult.metadata?.cacheHit ? '✅' : '❌')
        
        if (llmResult.metadata?.fallbackReason) {
          console.log('- Fallback Reason:', llmResult.metadata.fallbackReason)
        }
        
        console.log('')
      } catch (llmError) {
        console.log('❌ LLM Generation Failed:', llmError.message)
        console.log('')
      }
    } else {
      console.log('4️⃣ Skipping LLM Mode Test (Not Configured)')
      console.log('To test LLM mode, set OPENAI_API_KEY in your .env.local file')
      console.log('')
    }

    // Test 5: Display Sample Portfolio
    console.log('5️⃣ Sample Portfolio Allocations:')
    result.portfolio.allocations.slice(0, 5).forEach((alloc, index) => {
      console.log(`${index + 1}. ${alloc.symbol} (${alloc.name}) - ${alloc.allocation}%`)
      console.log(`   Category: ${alloc.category}`)
      console.log(`   Rationale: ${alloc.rationale.substring(0, 80)}...`)
      console.log('')
    })

    console.log('✅ All tests completed successfully!')
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
    console.error('Stack trace:', error.stack)
    process.exit(1)
  }
}

// Run the test
if (require.main === module) {
  testLLMSystem()
    .then(() => {
      console.log('\n🎉 LLM Portfolio System test completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('\n💥 Test suite failed:', error)
      process.exit(1)
    })
}

module.exports = { testLLMSystem }
