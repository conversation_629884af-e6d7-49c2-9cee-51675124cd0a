/**
 * Stock API Status Route
 * GET /api/stocks/status
 */

import { NextResponse } from 'next/server'
import { getStockDataService, getApiStatus, isMarketOpen, getMarketStatus } from '@/lib/stock-config'

export async function GET() {
  try {
    const stockService = getStockDataService()
    const apiStatus = getApiStatus()
    const apiUsage = stockService.getApiUsage()
    const marketOpen = isMarketOpen()
    const marketStatus = getMarketStatus()

    return NextResponse.json({
      market: {
        isOpen: marketOpen,
        status: marketStatus,
        timezone: 'America/New_York'
      },
      apis: {
        alphavantage: {
          configured: apiStatus.alphavantage.configured,
          usage: apiUsage.alphavantage,
          status: apiStatus.alphavantage.configured ? 'available' : 'not_configured'
        },
        finnhub: {
          configured: apiStatus.finnhub.configured,
          usage: apiUsage.finnhub,
          status: apiStatus.finnhub.configured ? 'available' : 'not_configured'
        },
        yahoo: {
          configured: true, // Always available as fallback
          status: 'available'
        }
      },
      cache: {
        size: 'N/A', // Could implement cache size tracking
        status: 'active'
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Stock status API error:', error)
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
