# Stock API Setup Guide

This guide will help you set up free stock market APIs for real-time data in your Investry application.

## Required API Keys

### 1. Alpha Vantage (Primary for fundamentals)
- **Free Tier**: 25 requests per day
- **Best For**: Company fundamentals, historical data, earnings
- **Sign Up**: https://www.alphavantage.co/support/#api-key

**Steps:**
1. Visit https://www.alphavantage.co/support/#api-key
2. Enter your email address
3. Click "GET FREE API KEY"
4. Copy the API key and add to your `.env.local` file:
   ```
   ALPHA_VANTAGE_API_KEY=your_api_key_here
   ```

### 2. Finnhub (Primary for real-time quotes)
- **Free Tier**: 60 requests per minute
- **Best For**: Real-time stock quotes, company profiles
- **Sign Up**: https://finnhub.io/register

**Steps:**
1. Visit https://finnhub.io/register
2. Create an account with your email
3. Verify your email address
4. Go to your dashboard to find your API key
5. Add to your `.env.local` file:
   ```
   FINNHUB_API_KEY=your_api_key_here
   ```

## Environment Setup

1. Copy the example environment file:
   ```bash
   cp .env.example .env.local
   ```

2. Edit `.env.local` and add your API keys:
   ```env
   # Supabase (existing)
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   
   # Stock APIs (new)
   ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key
   FINNHUB_API_KEY=your_finnhub_key
   ```

## API Usage Strategy

### Rate Limiting Strategy
- **Finnhub**: Used for real-time quotes (60/minute)
- **Alpha Vantage**: Used for company data (25/day)
- **Yahoo Finance**: Fallback for basic quotes (unlimited but unofficial)

### Caching Strategy
- **Real-time quotes**: 1 minute cache
- **Company profiles**: 24 hour cache
- **Historical data**: 1 hour cache

### Fallback Chain
1. **Cache** (if data is fresh)
2. **Finnhub** (real-time quotes)
3. **Alpha Vantage** (if Finnhub fails)
4. **Yahoo Finance** (unofficial fallback)
5. **Database/Mock data** (last resort)

## Testing Your Setup

1. Start your development server:
   ```bash
   npm run dev
   ```

2. Test the stock API endpoints:
   ```bash
   # Get a single quote
   curl http://localhost:3000/api/stocks/quote?symbol=AAPL
   
   # Get multiple quotes
   curl http://localhost:3000/api/stocks/quote?symbols=AAPL,MSFT,GOOGL
   
   # Check API status
   curl http://localhost:3000/api/stocks/status
   
   # Search stocks
   curl http://localhost:3000/api/stocks/search?q=apple
   ```

3. Check the browser console for any API errors

## Monitoring Usage

Visit `/api/stocks/status` to monitor:
- API usage limits
- Market status
- Cache performance
- API availability

## Troubleshooting

### Common Issues

1. **"API key not configured"**
   - Check your `.env.local` file
   - Restart your development server
   - Verify API key format

2. **"Rate limit exceeded"**
   - Alpha Vantage: Wait until next day (resets at midnight UTC)
   - Finnhub: Wait 1 minute
   - Consider upgrading to paid plans for higher limits

3. **"No data available"**
   - Check if the stock symbol is valid
   - Verify market hours (US markets: 9:30 AM - 4:00 PM ET)
   - Check API status at provider websites

4. **Network errors**
   - Check internet connection
   - Verify API endpoints are accessible
   - Check for firewall/proxy issues

### API Provider Status Pages
- Alpha Vantage: https://status.alphavantage.co/
- Finnhub: https://status.finnhub.io/

## Upgrading to Paid Plans

If you need higher limits:

### Alpha Vantage Premium
- **Basic**: $49.99/month (1,200 requests/day)
- **Standard**: $149.99/month (15,000 requests/day)
- **Professional**: $499.99/month (75,000 requests/day)

### Finnhub Premium
- **Basic**: $7.99/month (300 requests/minute)
- **Professional**: $24.99/month (600 requests/minute)
- **Enterprise**: Custom pricing

## Security Notes

- Never commit API keys to version control
- Use environment variables for all sensitive data
- Consider using API key rotation for production
- Monitor API usage to detect unusual activity

## Next Steps

After setting up the APIs:
1. Test the stock search and quote functionality
2. Monitor API usage in the status endpoint
3. Consider implementing Redis for better caching in production
4. Set up monitoring alerts for API limits
5. Plan for scaling with paid API tiers if needed
