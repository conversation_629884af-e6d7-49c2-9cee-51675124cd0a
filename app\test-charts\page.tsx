/**
 * Test Charts Page
 * Simple page to test chart functionality
 */

'use client'

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  StockPriceChart,
  PortfolioPerformanceChart,
  CompactStockPriceChart,
  CompactPort<PERSON>lio<PERSON>hart
} from '@/components/charts'
import { PageHeader } from '@/components/breadcrumb'
import { TopNav } from '@/components/top-nav'
import { BottomNav } from '@/components/bottom-nav'

export default function TestChartsPage() {
  return (
    <div className="min-h-screen bg-background">
      <TopNav />
      
      <div className="container mx-auto p-6 space-y-8">
        <PageHeader
          title="Chart Testing"
          description="Testing chart components with mock data fallbacks"
          showBackButton={true}
        />

        {/* Stock Price Chart Test */}
        <Card>
          <CardHeader>
            <CardTitle>Stock Price Chart - AAPL</CardTitle>
          </CardHeader>
          <CardContent>
            <StockPriceChart
              symbol="AAPL"
              height={300}
              showControls={true}
              showPerformance={true}
              initialTimeRange="1M"
              initialValueType="absolute"
            />
          </CardContent>
        </Card>

        {/* Portfolio Performance Chart Test */}
        <Card>
          <CardHeader>
            <CardTitle>Portfolio Performance Chart</CardTitle>
          </CardHeader>
          <CardContent>
            <PortfolioPerformanceChart
              height={300}
              showControls={true}
              showPerformance={true}
              showDeposits={true}
              chartType="area"
              initialTimeRange="1M"
              initialValueType="absolute"
            />
          </CardContent>
        </Card>

        {/* Compact Charts Test */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Compact Stock Chart</CardTitle>
            </CardHeader>
            <CardContent>
              <CompactStockPriceChart
                symbol="MSFT"
                timeRange="1D"
                height={100}
                showChange={true}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Compact Portfolio Chart</CardTitle>
            </CardHeader>
            <CardContent>
              <CompactPortfolioChart
                timeRange="1M"
                height={100}
                showValue={true}
              />
            </CardContent>
          </Card>
        </div>

        {/* Multiple Stock Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {['GOOGL', 'TSLA'].map((symbol) => (
            <Card key={symbol}>
              <CardHeader>
                <CardTitle>{symbol} Stock Chart</CardTitle>
              </CardHeader>
              <CardContent>
                <StockPriceChart
                  symbol={symbol}
                  height={250}
                  showControls={false}
                  showPerformance={true}
                  initialTimeRange="1W"
                  initialValueType="absolute"
                />
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Chart Status Info */}
        <Card>
          <CardHeader>
            <CardTitle>Chart Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <p>✅ Charts are using mock data fallbacks when APIs are unavailable</p>
              <p>✅ Authentication is handled gracefully with demo data for logged-out users</p>
              <p>✅ Real-time updates are disabled in test mode</p>
              <p>✅ All chart types are responsive and interactive</p>
            </div>
          </CardContent>
        </Card>
      </div>
      
      <BottomNav />
    </div>
  )
}
