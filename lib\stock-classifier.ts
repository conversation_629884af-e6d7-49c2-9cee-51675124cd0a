import { searchStocks, getStocksBySector, getAllStocks } from "@/lib/supabase"
import { STOCK_SECTORS } from "@/lib/stock-config"

export interface StockQuote {
  id: number
  symbol: string
  name: string
  price: number
  change: number
  changePercent: number
  category: string
  sector: string
  market_cap: string
  tags: string[]
  description: string
  volume: number
  ticker: string
  company_name: string
  exchange: string
  asset_class: string
  stock_type: string
  classification?: {
    name: string
    industry: string
    type: string
    categories: string[]
  }
}

export interface StockClassification {
  category: string
  confidence: number
  tags: string[]
}

export interface ClassifiedStock {
  symbol: string
  name: string
  price: number
  change: number
  changePercent: number
  volume: number
  marketCap: string
  sector: string
  classification: StockClassification
}

export interface StockData {
  symbol: string
  name: string
  price: number
  change: number
  changePercent: number
  volume: number
  marketCap: number
  sector: string
  tags: string[]
}

// Comprehensive stock database with detailed information
const STOCK_DATABASE: StockQuote[] = [
  // Technology Stocks
  {
    id: 1,
    symbol: "AAPL",
    name: "Apple Inc.",
    price: 185.64,
    change: 2.34,
    changePercent: 1.28,
    volume: 45234567,
    market_cap: "2.89T",
    category: "Technology",
    sector: "Consumer Electronics",
    tags: ["Large Cap", "Consumer Electronics", "Innovation", "Dividend"],
    description:
      "Designs, manufactures, and markets smartphones, personal computers, tablets, wearables, and accessories worldwide.",
    ticker: "AAPL",
    company_name: "Apple Inc.",
    exchange: "NASDAQ",
    asset_class: "Equity",
    stock_type: "Common Stock",
    classification: {
      name: "Apple Inc.",
      industry: "Consumer Electronics",
      type: "Large Cap Stock",
      categories: ["Technology", "Consumer Electronics", "Innovation", "Dividend"],
    },
  },
  {
    id: 2,
    symbol: "MSFT",
    name: "Microsoft Corporation",
    price: 378.85,
    change: -1.23,
    changePercent: -0.32,
    volume: 23456789,
    market_cap: "2.81T",
    category: "Technology",
    sector: "Software",
    tags: ["Large Cap", "Cloud Computing", "AI", "Enterprise Software", "Dividend"],
    description: "Develops, licenses, and supports software, services, devices, and solutions worldwide.",
    ticker: "MSFT",
    company_name: "Microsoft Corporation",
    exchange: "NASDAQ",
    asset_class: "Equity",
    stock_type: "Common Stock",
    classification: {
      name: "Microsoft Corporation",
      industry: "Software",
      type: "Large Cap Stock",
      categories: ["Technology", "Cloud Computing", "AI", "Enterprise Software"],
    },
  },
  {
    id: 3,
    symbol: "GOOGL",
    name: "Alphabet Inc.",
    price: 142.56,
    change: 3.45,
    changePercent: 2.48,
    volume: 34567890,
    market_cap: "1.78T",
    category: "Technology",
    sector: "Internet Services",
    tags: ["Large Cap", "Search Engine", "AI", "Cloud Computing", "Advertising"],
    description:
      "Provides online advertising services in the United States, Europe, the Middle East, Africa, the Asia-Pacific, Canada, and Latin America.",
    ticker: "GOOGL",
    company_name: "Alphabet Inc.",
    exchange: "NASDAQ",
    asset_class: "Equity",
    stock_type: "Common Stock",
    classification: {
      name: "Alphabet Inc.",
      industry: "Internet Services",
      type: "Large Cap Stock",
      categories: ["Technology", "Search Engine", "AI", "Cloud Computing"],
    },
  },
  {
    id: 4,
    symbol: "NVDA",
    name: "NVIDIA Corporation",
    price: 875.28,
    change: 15.67,
    changePercent: 1.82,
    volume: 56789012,
    market_cap: "2.16T",
    category: "AI & Semiconductors",
    sector: "Semiconductors",
    tags: ["AI", "Gaming", "Data Center", "Autonomous Vehicles", "Growth"],
    description:
      "Designs graphics processing units for the gaming and professional markets, as well as system on a chip units for the mobile computing and automotive market.",
    ticker: "NVDA",
    company_name: "NVIDIA Corporation",
    exchange: "NASDAQ",
    asset_class: "Equity",
    stock_type: "Common Stock",
    classification: {
      name: "NVIDIA Corporation",
      industry: "Semiconductors",
      type: "Growth Stock",
      categories: ["AI", "Gaming", "Data Center", "Semiconductors"],
    },
  },
  {
    id: 5,
    symbol: "TSLA",
    name: "Tesla, Inc.",
    price: 248.42,
    change: -8.34,
    changePercent: -3.25,
    volume: 78901234,
    market_cap: "789B",
    category: "Electric Vehicles",
    sector: "Automotive",
    tags: ["Electric Vehicles", "Clean Energy", "Innovation", "Growth", "Volatile"],
    description:
      "Designs, develops, manufactures, leases, and sells electric vehicles, and energy generation and storage systems in the United States, China, and internationally.",
    ticker: "TSLA",
    company_name: "Tesla, Inc.",
    exchange: "NASDAQ",
    asset_class: "Equity",
    stock_type: "Common Stock",
    classification: {
      name: "Tesla, Inc.",
      industry: "Automotive",
      type: "Growth Stock",
      categories: ["Electric Vehicles", "Clean Energy", "Innovation", "Growth"],
    },
  },

  // Healthcare & Biotechnology
  {
    id: 6,
    symbol: "JNJ",
    name: "Johnson & Johnson",
    price: 156.78,
    change: 0.89,
    changePercent: 0.57,
    volume: 12345678,
    market_cap: "415B",
    category: "Healthcare",
    sector: "Pharmaceuticals",
    tags: ["Large Cap", "Pharmaceuticals", "Medical Devices", "Dividend", "Stable"],
    description:
      "Researches and develops, manufactures, and sells various products in the health care field worldwide.",
    ticker: "JNJ",
    company_name: "Johnson & Johnson",
    exchange: "NYSE",
    asset_class: "Equity",
    stock_type: "Common Stock",
    classification: {
      name: "Johnson & Johnson",
      industry: "Pharmaceuticals",
      type: "Dividend Stock",
      categories: ["Healthcare", "Pharmaceuticals", "Medical Devices", "Dividend"],
    },
  },
  {
    id: 7,
    symbol: "PFE",
    name: "Pfizer Inc.",
    price: 28.45,
    change: -0.34,
    changePercent: -1.18,
    volume: 23456789,
    market_cap: "160B",
    category: "Healthcare",
    sector: "Pharmaceuticals",
    tags: ["Pharmaceuticals", "Vaccines", "Dividend", "Large Cap"],
    description:
      "Discovers, develops, manufactures, markets, distributes, and sells biopharmaceutical products worldwide.",
    ticker: "PFE",
    company_name: "Pfizer Inc.",
    exchange: "NYSE",
    asset_class: "Equity",
    stock_type: "Common Stock",
    classification: {
      name: "Pfizer Inc.",
      industry: "Pharmaceuticals",
      type: "Dividend Stock",
      categories: ["Healthcare", "Pharmaceuticals", "Vaccines", "Large Cap"],
    },
  },
  {
    id: 8,
    symbol: "MRNA",
    name: "Moderna, Inc.",
    price: 89.23,
    change: 4.56,
    changePercent: 5.38,
    volume: 34567890,
    market_cap: "33B",
    category: "Biotechnology",
    sector: "Biotechnology",
    tags: ["mRNA Technology", "Vaccines", "Innovation", "Growth", "Volatile"],
    description:
      "Develops therapeutics and vaccines based on messenger ribonucleic acid for the treatment of infectious diseases, immuno-oncology, rare diseases, and cardiovascular diseases.",
    ticker: "MRNA",
    company_name: "Moderna, Inc.",
    exchange: "NYSE",
    asset_class: "Equity",
    stock_type: "Common Stock",
    classification: {
      name: "Moderna, Inc.",
      industry: "Biotechnology",
      type: "Growth Stock",
      categories: ["Biotechnology", "mRNA Technology", "Vaccines", "Innovation"],
    },
  },

  // Finance
  {
    id: 9,
    symbol: "JPM",
    name: "JPMorgan Chase & Co.",
    price: 178.92,
    change: 2.15,
    changePercent: 1.22,
    volume: ********,
    market_cap: "525B",
    category: "Finance",
    sector: "Banking",
    tags: ["Banking", "Large Cap", "Dividend", "Financial Services"],
    description:
      "Provides financial services to consumers, small businesses and commercial clients, and large corporate, institutional, and government clients worldwide.",
    ticker: "JPM",
    company_name: "JPMorgan Chase & Co.",
    exchange: "NYSE",
    asset_class: "Equity",
    stock_type: "Common Stock",
    classification: {
      name: "JPMorgan Chase & Co.",
      industry: "Banking",
      type: "Dividend Stock",
      categories: ["Finance", "Banking", "Financial Services", "Large Cap"],
    },
  },
  {
    id: 10,
    symbol: "V",
    name: "Visa Inc.",
    price: 267.34,
    change: 1.78,
    changePercent: 0.67,
    volume: 9876543,
    market_cap: "565B",
    category: "Finance",
    sector: "Payment Processing",
    tags: ["Payment Processing", "Large Cap", "Growth", "Digital Payments"],
    description: "Operates as a payments technology company worldwide.",
    ticker: "V",
    company_name: "Visa Inc.",
    exchange: "NYSE",
    asset_class: "Equity",
    stock_type: "Common Stock",
    classification: {
      name: "Visa Inc.",
      industry: "Payment Processing",
      type: "Growth Stock",
      categories: ["Finance", "Payment Processing", "Digital Payments", "Large Cap"],
    },
  },

  // Energy
  {
    id: 11,
    symbol: "XOM",
    name: "Exxon Mobil Corporation",
    price: 102.45,
    change: -1.23,
    changePercent: -1.19,
    volume: ********,
    market_cap: "425B",
    category: "Energy",
    sector: "Oil & Gas",
    tags: ["Oil & Gas", "Large Cap", "Dividend", "Energy"],
    description: "Explores for and produces crude oil and natural gas in the United States and internationally.",
    ticker: "XOM",
    company_name: "Exxon Mobil Corporation",
    exchange: "NYSE",
    asset_class: "Equity",
    stock_type: "Common Stock",
    classification: {
      name: "Exxon Mobil Corporation",
      industry: "Oil & Gas",
      type: "Dividend Stock",
      categories: ["Energy", "Oil & Gas", "Large Cap", "Dividend"],
    },
  },

  // Consumer Goods
  {
    id: 12,
    symbol: "KO",
    name: "The Coca-Cola Company",
    price: 58.67,
    change: 0.45,
    changePercent: 0.77,
    volume: 13579246,
    market_cap: "254B",
    category: "Consumer Goods",
    sector: "Beverages",
    tags: ["Consumer Staples", "Dividend", "Large Cap", "Global Brand"],
    description: "Manufactures, markets, and sells various nonalcoholic beverages worldwide.",
    ticker: "KO",
    company_name: "The Coca-Cola Company",
    exchange: "NYSE",
    asset_class: "Equity",
    stock_type: "Common Stock",
    classification: {
      name: "The Coca-Cola Company",
      industry: "Beverages",
      type: "Dividend Stock",
      categories: ["Consumer Goods", "Consumer Staples", "Dividend", "Global Brand"],
    },
  },
  {
    id: 13,
    symbol: "WMT",
    name: "Walmart Inc.",
    price: 165.23,
    change: 1.89,
    changePercent: 1.16,
    volume: 8642097,
    market_cap: "535B",
    category: "Consumer Goods",
    sector: "Retail",
    tags: ["Retail", "Large Cap", "Dividend", "Consumer Staples"],
    description: "Engages in the operation of retail, wholesale, and other units worldwide.",
    ticker: "WMT",
    company_name: "Walmart Inc.",
    exchange: "NYSE",
    asset_class: "Equity",
    stock_type: "Common Stock",
    classification: {
      name: "Walmart Inc.",
      industry: "Retail",
      type: "Dividend Stock",
      categories: ["Consumer Goods", "Retail", "Consumer Staples", "Large Cap"],
    },
  },

  // Entertainment & Media
  {
    id: 14,
    symbol: "DIS",
    name: "The Walt Disney Company",
    price: 96.78,
    change: -2.34,
    changePercent: -2.36,
    volume: 19753086,
    market_cap: "177B",
    category: "Entertainment",
    sector: "Entertainment",
    tags: ["Entertainment", "Streaming", "Theme Parks", "Media", "Large Cap"],
    description: "Operates as an entertainment company worldwide.",
    ticker: "DIS",
    company_name: "The Walt Disney Company",
    exchange: "NYSE",
    asset_class: "Equity",
    stock_type: "Common Stock",
    classification: {
      name: "The Walt Disney Company",
      industry: "Entertainment",
      type: "Large Cap Stock",
      categories: ["Entertainment", "Streaming", "Theme Parks", "Media"],
    },
  },
  {
    id: 15,
    symbol: "NFLX",
    name: "Netflix, Inc.",
    price: 445.67,
    change: 8.92,
    changePercent: 2.04,
    volume: 5432109,
    market_cap: "198B",
    category: "Entertainment",
    sector: "Streaming",
    tags: ["Streaming", "Content Creation", "Growth", "Technology"],
    description: "Provides entertainment services in the United States and internationally.",
    ticker: "NFLX",
    company_name: "Netflix, Inc.",
    exchange: "NASDAQ",
    asset_class: "Equity",
    stock_type: "Common Stock",
    classification: {
      name: "Netflix, Inc.",
      industry: "Streaming",
      type: "Growth Stock",
      categories: ["Entertainment", "Streaming", "Content Creation", "Technology"],
    },
  },

  // E-commerce
  {
    id: 16,
    symbol: "AMZN",
    name: "Amazon.com, Inc.",
    price: 155.89,
    change: 3.45,
    changePercent: 2.26,
    volume: 42086531,
    market_cap: "1.62T",
    category: "E-commerce",
    sector: "E-commerce",
    tags: ["E-commerce", "Cloud Computing", "Large Cap", "Growth", "Innovation"],
    description:
      "Engages in the retail sale of consumer products and subscriptions in North America and internationally.",
    ticker: "AMZN",
    company_name: "Amazon.com, Inc.",
    exchange: "NASDAQ",
    asset_class: "Equity",
    stock_type: "Common Stock",
    classification: {
      name: "Amazon.com, Inc.",
      industry: "E-commerce",
      type: "Growth Stock",
      categories: ["E-commerce", "Cloud Computing", "Large Cap", "Innovation"],
    },
  },

  // ETFs
  {
    id: 17,
    symbol: "SPY",
    name: "SPDR S&P 500 ETF Trust",
    price: 456.78,
    change: 2.34,
    changePercent: 0.51,
    volume: 87654321,
    market_cap: "425B",
    category: "ETF",
    sector: "Index Fund",
    tags: ["ETF", "S&P 500", "Index Fund", "Diversified", "Low Cost"],
    description:
      "Seeks to provide investment results that correspond generally to the price and yield performance of the S&P 500 Index.",
    ticker: "SPY",
    company_name: "SPDR S&P 500 ETF Trust",
    exchange: "NYSE",
    asset_class: "ETF",
    stock_type: "ETF",
    classification: {
      name: "SPDR S&P 500 ETF Trust",
      industry: "Index Fund",
      type: "ETF",
      categories: ["ETF", "S&P 500", "Index Fund", "Diversified"],
    },
  },
  {
    id: 18,
    symbol: "QQQ",
    name: "Invesco QQQ Trust",
    price: 389.45,
    change: 4.67,
    changePercent: 1.21,
    volume: 65432109,
    market_cap: "198B",
    category: "ETF",
    sector: "Technology Index",
    tags: ["ETF", "NASDAQ", "Technology", "Growth", "Index Fund"],
    description:
      "Tracks the NASDAQ-100 Index, which includes 100 of the largest domestic and international non-financial companies listed on the NASDAQ Stock Market.",
    ticker: "QQQ",
    company_name: "Invesco QQQ Trust",
    exchange: "NYSE",
    asset_class: "ETF",
    stock_type: "ETF",
    classification: {
      name: "Invesco QQQ Trust",
      industry: "Technology Index",
      type: "ETF",
      categories: ["ETF", "NASDAQ", "Technology", "Growth"],
    },
  },
  {
    id: 19,
    symbol: "VTI",
    name: "Vanguard Total Stock Market ETF",
    price: 234.56,
    change: 1.89,
    changePercent: 0.81,
    volume: 43210987,
    market_cap: "312B",
    category: "ETF",
    sector: "Total Market Index",
    tags: ["ETF", "Total Market", "Diversified", "Low Cost", "Index Fund"],
    description:
      "Seeks to track the performance of the CRSP US Total Market Index, which represents approximately 100% of the investable U.S. stock market.",
    ticker: "VTI",
    company_name: "Vanguard Total Stock Market ETF",
    exchange: "NYSE",
    asset_class: "ETF",
    stock_type: "ETF",
    classification: {
      name: "Vanguard Total Stock Market ETF",
      industry: "Total Market Index",
      type: "ETF",
      categories: ["ETF", "Total Market", "Diversified", "Low Cost"],
    },
  },

  // Additional stocks for variety
  {
    id: 20,
    symbol: "AMD",
    name: "Advanced Micro Devices, Inc.",
    price: 142.78,
    change: 5.23,
    changePercent: 3.8,
    volume: 34567890,
    market_cap: "231B",
    category: "AI & Semiconductors",
    sector: "Semiconductors",
    tags: ["Semiconductors", "AI", "Gaming", "Data Center", "Growth"],
    description:
      "Designs and integrates technology for laptops, desktops, workstations, servers, tablets, handheld devices, and embedded system applications.",
    ticker: "AMD",
    company_name: "Advanced Micro Devices, Inc.",
    exchange: "NYSE",
    asset_class: "Equity",
    stock_type: "Common Stock",
    classification: {
      name: "Advanced Micro Devices, Inc.",
      industry: "Semiconductors",
      type: "Growth Stock",
      categories: ["AI & Semiconductors", "AI", "Gaming", "Data Center"],
    },
  },
  {
    id: 21,
    symbol: "BABA",
    name: "Alibaba Group Holding Limited",
    price: 78.45,
    change: -1.23,
    changePercent: -1.54,
    volume: 23456789,
    market_cap: "189B",
    category: "E-commerce",
    sector: "E-commerce",
    tags: ["E-commerce", "China", "Cloud Computing", "Digital Payments"],
    description:
      "Provides technology infrastructure and marketing reach to merchants, brands, retailers, and other businesses to engage with their users and customers in China and internationally.",
    ticker: "BABA",
    company_name: "Alibaba Group Holding Limited",
    exchange: "NYSE",
    asset_class: "Equity",
    stock_type: "Common Stock",
    classification: {
      name: "Alibaba Group Holding Limited",
      industry: "E-commerce",
      type: "Growth Stock",
      categories: ["E-commerce", "China", "Cloud Computing", "Digital Payments"],
    },
  },
]

// Import stock data service for real-time data
import { getStockDataService } from './stock-config'

// Helper functions for stock classification and search
export async function getStockBySymbol(symbol: string): Promise<StockData | undefined> {
  try {
    const stockService = getStockDataService()
    const quote = await stockService.getStockQuote(symbol.toUpperCase())

    if (!quote) return undefined

    return {
      symbol: quote.symbol,
      name: quote.name,
      price: quote.price,
      change: quote.change,
      changePercent: quote.changePercent,
      volume: quote.volume,
      marketCap: 0, // Not available in quote
      sector: quote.sector || 'Unknown',
      tags: [] // Would need to be derived from sector/classification
    }
  } catch (error) {
    console.error('Error getting stock by symbol:', error)
    return undefined
  }
}

export async function searchStocksByKeyword(query: string, limit = 20): Promise<StockQuote[]> {
  try {
    const results = await searchStocks(query, limit)
    return results
  } catch (error) {
    console.error("Error searching stocks by keyword:", error)
    // Fallback to local search
    const searchTerm = query.toLowerCase()
    return STOCK_DATABASE.filter(
      (stock) =>
        stock.symbol.toLowerCase().includes(searchTerm) ||
        stock.name.toLowerCase().includes(searchTerm) ||
        stock.category.toLowerCase().includes(searchTerm) ||
        stock.sector.toLowerCase().includes(searchTerm) ||
        stock.tags.some((tag) => tag.toLowerCase().includes(searchTerm)) ||
        stock.description.toLowerCase().includes(searchTerm),
    ).slice(0, limit)
  }
}

export async function getStocksByCategory(category: string, limit = 50): Promise<StockQuote[]> {
  try {
    const results = await getStocksBySector(category, limit)
    return results
  } catch (error) {
    console.error("Error getting stocks by category:", error)
    // Fallback to local search
    return STOCK_DATABASE.filter((stock) => stock.category.toLowerCase() === category.toLowerCase()).slice(0, limit)
  }
}

export async function getStocksByTag(tag: string): Promise<StockData[]> {
  try {
    // Use database search to find stocks by sector/category that matches the tag
    const results = await searchStocks(tag, 50)
    return results.map(stock => ({
      symbol: stock.symbol,
      name: stock.name,
      price: stock.price,
      change: stock.change,
      changePercent: stock.changePercent,
      volume: stock.volume,
      marketCap: 0,
      sector: stock.sector,
      tags: stock.tags || []
    }))
  } catch (error) {
    console.error('Error getting stocks by tag:', error)
    return []
  }
}

export function getTrendingStocks(): StockQuote[] {
  // Return stocks with highest volume (simulating trending)
  return STOCK_DATABASE.sort((a, b) => b.volume - a.volume).slice(0, 10)
}

export async function getTopGainers(limit = 10): Promise<StockData[]> {
  try {
    const stockService = getStockDataService()
    const gainers = await stockService.getTopGainers(limit)
    return gainers.map(stock => ({
      symbol: stock.symbol,
      name: stock.name,
      price: stock.price,
      change: stock.change,
      changePercent: stock.changePercent,
      volume: stock.volume,
      marketCap: 0,
      sector: stock.sector || 'Unknown',
      tags: []
    }))
  } catch (error) {
    console.error('Error getting top gainers:', error)
    return []
  }
}

export async function getTopLosers(limit = 10): Promise<StockData[]> {
  try {
    const stockService = getStockDataService()
    const losers = await stockService.getTopLosers(limit)
    return losers.map(stock => ({
      symbol: stock.symbol,
      name: stock.name,
      price: stock.price,
      change: stock.change,
      changePercent: stock.changePercent,
      volume: stock.volume,
      marketCap: 0,
      sector: stock.sector || 'Unknown',
      tags: []
    }))
  } catch (error) {
    console.error('Error getting top losers:', error)
    return []
  }
}

export async function getMostActive(limit = 10): Promise<StockData[]> {
  try {
    const stockService = getStockDataService()
    const mostActive = await stockService.getMostActive(limit)
    return mostActive.map(stock => ({
      symbol: stock.symbol,
      name: stock.name,
      price: stock.price,
      change: stock.change,
      changePercent: stock.changePercent,
      volume: stock.volume,
      marketCap: 0,
      sector: stock.sector || 'Unknown',
      tags: []
    }))
  } catch (error) {
    console.error('Error getting most active:', error)
    return []
  }
}

export function getCategories(): string[] {
  const categories = new Set(STOCK_DATABASE.map((stock) => stock.category))
  return Array.from(categories).sort()
}

export function getSectors(): string[] {
  const sectors = new Set(STOCK_DATABASE.map((stock) => stock.sector))
  return Array.from(sectors).sort()
}

export function getAllTags(): string[] {
  const tags = new Set(STOCK_DATABASE.flatMap((stock) => stock.tags))
  return Array.from(tags).sort()
}

export function getRelatedTags(category: string): string[] {
  const categoryStocks = STOCK_DATABASE.filter((stock) => stock.category.toLowerCase() === category.toLowerCase())
  const tags = new Set(categoryStocks.flatMap((stock) => stock.tags))
  return Array.from(tags).sort()
}

export function getRandomStocks(count = 5): StockQuote[] {
  const shuffled = [...STOCK_DATABASE].sort(() => 0.5 - Math.random())
  return shuffled.slice(0, count)
}

// Market data simulation
export function getMarketSummary() {
  const totalStocks = STOCK_DATABASE.length
  const gainers = STOCK_DATABASE.filter((stock) => stock.changePercent > 0).length
  const losers = STOCK_DATABASE.filter((stock) => stock.changePercent < 0).length
  const unchanged = totalStocks - gainers - losers

  const avgChange = STOCK_DATABASE.reduce((sum, stock) => sum + stock.changePercent, 0) / totalStocks

  return {
    totalStocks,
    gainers,
    losers,
    unchanged,
    avgChange: Number(avgChange.toFixed(2)),
  }
}

export async function initializeStockData(limit = 100): Promise<StockQuote[]> {
  try {
    const results = await getAllStocks(limit)
    return results
  } catch (error) {
    console.error("Error initializing stock data:", error)
    return STOCK_DATABASE.slice(0, limit)
  }
}

export function classifyStock(symbol: string, name: string, sector?: string): StockClassification {
  // Basic classification logic based on symbol and sector
  const symbolUpper = symbol.toUpperCase()
  const nameUpper = name.toUpperCase()
  const sectorUpper = (sector || "").toUpperCase()

  // Technology stocks
  if (sectorUpper.includes("TECHNOLOGY") || sectorUpper.includes("SOFTWARE")) {
    return {
      category: "Technology",
      confidence: 0.8,
      tags: ["technology", "software"],
    }
  }

  // Healthcare/Biotech
  if (sectorUpper.includes("HEALTHCARE") || sectorUpper.includes("BIOTECH") || sectorUpper.includes("PHARMACEUTICAL")) {
    return {
      category: "Healthcare & Biotech",
      confidence: 0.8,
      tags: ["healthcare", "biotech"],
    }
  }

  // Financial
  if (sectorUpper.includes("FINANCIAL") || sectorUpper.includes("BANK")) {
    return {
      category: "Financial Services",
      confidence: 0.8,
      tags: ["financial", "banking"],
    }
  }

  // Energy
  if (sectorUpper.includes("ENERGY") || sectorUpper.includes("OIL")) {
    return {
      category: "Energy",
      confidence: 0.8,
      tags: ["energy", "oil"],
    }
  }

  // Default classification
  return {
    category: "General",
    confidence: 0.5,
    tags: ["general"],
  }
}

// These functions now delegate to the async versions above
export async function getMostActiveClassified(limit = 10): Promise<StockData[]> {
  return getMostActive(limit)
}

export async function getTopGainersClassified(limit = 10): Promise<StockData[]> {
  return getTopGainers(limit)
}

export async function getTopLosersClassified(limit = 10): Promise<StockData[]> {
  return getTopLosers(limit)
}

export async function getStocksByCategoryClassified(category: string): Promise<StockData[]> {
  try {
    const results = await getStocksBySector(category, 50)
    return results.map(stock => ({
      symbol: stock.symbol,
      name: stock.name,
      price: stock.price,
      change: stock.change,
      changePercent: stock.changePercent,
      volume: stock.volume,
      marketCap: 0,
      sector: stock.sector,
      tags: stock.tags || []
    }))
  } catch (error) {
    console.error('Error getting stocks by category:', error)
    return []
  }
}

export async function searchClassifiedStocks(query: string): Promise<StockData[]> {
  try {
    const results = await searchStocks(query, 20)
    return results.map(stock => ({
      symbol: stock.symbol,
      name: stock.name,
      price: stock.price,
      change: stock.change,
      changePercent: stock.changePercent,
      volume: stock.volume,
      marketCap: 0,
      sector: stock.sector,
      tags: stock.tags || []
    }))
  } catch (error) {
    console.error('Error searching classified stocks:', error)
    return []
  }
}

export function filterStocks(
  stocks: any[],
  filters: {
    category?: string
    priceRange?: [number, number]
    marketCap?: number
    sector?: string
  },
) {
  return stocks.filter((stock) => {
    if (filters.category && classifyStock(stock.symbol, stock.name, stock.sector).category !== filters.category) {
      return false
    }

    if (filters.priceRange) {
      const [min, max] = filters.priceRange
      if (stock.price < min || stock.price > max) {
        return false
      }
    }

    if (filters.marketCap) {
      if (stock.marketCap < filters.marketCap) {
        return false
      }
    }

    if (filters.sector && stock.sector !== filters.sector) {
      return false
    }

    return true
  })
}

// Stock categories for classification
export const STOCK_CATEGORIES = [
  "Technology",
  "Healthcare",
  "Financial Services",
  "Consumer Discretionary",
  "Consumer Staples",
  "Energy",
  "Utilities",
  "Real Estate",
  "Materials",
  "Industrials",
  "Communication Services",
  "ETF",
  "Cryptocurrency",
] as const

export type StockCategory = (typeof STOCK_CATEGORIES)[number]

// Get all available categories
export function getAllCategories(): string[] {
  return [...STOCK_CATEGORIES]
}

// Popular company tags for search suggestions
export const POPULAR_COMPANY_TAGS = [
  { name: "Apple", count: 1250000, category: "Technology" },
  { name: "Microsoft", count: 980000, category: "Technology" },
  { name: "Tesla", count: 850000, category: "Automotive" },
  { name: "Amazon", count: 720000, category: "E-commerce" },
  { name: "Google", count: 690000, category: "Technology" },
  { name: "Meta", count: 520000, category: "Social Media" },
  { name: "Netflix", count: 480000, category: "Entertainment" },
  { name: "NVIDIA", count: 450000, category: "Semiconductors" },
  { name: "PayPal", count: 380000, category: "Fintech" },
  { name: "Spotify", count: 320000, category: "Music Streaming" },
  { name: "Uber", count: 290000, category: "Transportation" },
  { name: "Airbnb", count: 260000, category: "Travel" },
] as const
