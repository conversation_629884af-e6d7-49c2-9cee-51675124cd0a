"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { useAuth } from "@/components/auth-provider"
import { useNotifications } from "@/components/notification-system"
import { createClient, isSupabaseConfigured } from "@/lib/supabase"
import { Target, Plus, Edit, Trash2, <PERSON><PERSON><PERSON><PERSON>, <PERSON>R<PERSON> } from "lucide-react"

export interface FinancialGoal {
  id: string
  user_id: string
  title: string
  description?: string
  target_amount: number
  current_amount: number
  target_date: string
  category: string
  priority: "low" | "medium" | "high"
  is_completed: boolean
  created_at: string
  updated_at: string
}

interface GoalFormData {
  title: string
  description: string
  target_amount: string
  target_date: string
  category: string
  priority: "low" | "medium" | "high"
}

interface GoalTrackerProps {
  showAddButton?: boolean
}

export function GoalTracker({ showAddButton = false }: GoalTrackerProps) {
  const { user } = useAuth()
  const { addNotification } = useNotifications()
  const router = useRouter()
  const [goals, setGoals] = useState<FinancialGoal[]>([])
  const [loading, setLoading] = useState(true)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingGoal, setEditingGoal] = useState<FinancialGoal | null>(null)
  const [formData, setFormData] = useState<GoalFormData>({
    title: "",
    description: "",
    target_amount: "",
    target_date: "",
    category: "",
    priority: "medium",
  })
  const supabase = createClient()
  const configured = isSupabaseConfigured()

  useEffect(() => {
    if (user) {
      fetchGoals()
    }
  }, [user])

  const fetchGoals = async () => {
    if (!user) return

    try {
      if (!configured) {
        // If Supabase not configured, show message instead of using localStorage
        addNotification({
          type: "info",
          title: "Database Not Configured",
          message: "Please configure Supabase to save your financial goals permanently.",
        })
        setLoading(false)
        return
      }

      const { data, error } = await supabase
        .from("financial_goals")
        .select("*")
        .eq("user_id", user.id)
        .order("created_at", { ascending: false })

      if (error) throw error
      setGoals(data || [])
    } catch (error) {
      console.error("Error fetching goals:", error)
      addNotification({
        type: "error",
        title: "Error",
        message: "Failed to load your goals. Please try again.",
      })
    } finally {
      setLoading(false)
    }
  }

  // Remove localStorage fallback - require Supabase for data persistence
  const saveGoalsToStorage = (updatedGoals: FinancialGoal[]) => {
    // No longer using localStorage - all data must be saved to Supabase
    if (!configured) {
      console.warn("Cannot save goals: Supabase not configured")
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return

    try {
      const goalData = {
        id: editingGoal?.id || `goal-${Date.now()}`,
        user_id: user.id,
        title: formData.title,
        description: formData.description,
        target_amount: Number.parseFloat(formData.target_amount),
        current_amount: editingGoal?.current_amount || 0,
        target_date: formData.target_date,
        category: formData.category,
        priority: formData.priority,
        is_completed: false,
        created_at: editingGoal?.created_at || new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }

      if (!configured) {
        // Demo mode
        let updatedGoals: FinancialGoal[]
        if (editingGoal) {
          updatedGoals = goals.map((goal) => (goal.id === editingGoal.id ? goalData : goal))
        } else {
          updatedGoals = [goalData, ...goals]
        }
        setGoals(updatedGoals)
        saveGoalsToStorage(updatedGoals)

        addNotification({
          type: "success",
          title: editingGoal ? "Goal Updated" : "Goal Created",
          message: `Your financial goal has been ${editingGoal ? "updated" : "created"} successfully!`,
        })
      } else {
        // Real Supabase
        if (editingGoal) {
          const { error } = await supabase.from("financial_goals").update(goalData).eq("id", editingGoal.id)
          if (error) throw error
        } else {
          const { error } = await supabase.from("financial_goals").insert(goalData)
          if (error) throw error
        }

        addNotification({
          type: "success",
          title: editingGoal ? "Goal Updated" : "Goal Created",
          message: `Your financial goal has been ${editingGoal ? "updated" : "created"} successfully!`,
        })

        fetchGoals()
      }

      setIsDialogOpen(false)
      setEditingGoal(null)
      resetForm()
    } catch (error) {
      console.error("Error saving goal:", error)
      addNotification({
        type: "error",
        title: "Error",
        message: "Failed to save your goal. Please try again.",
      })
    }
  }

  const handleEdit = (goal: FinancialGoal) => {
    setEditingGoal(goal)
    setFormData({
      title: goal.title,
      description: goal.description || "",
      target_amount: goal.target_amount.toString(),
      target_date: goal.target_date,
      category: goal.category,
      priority: goal.priority,
    })
    setIsDialogOpen(true)
  }

  const handleDelete = async (goalId: string) => {
    if (!confirm("Are you sure you want to delete this goal?")) return

    try {
      if (!configured) {
        // Demo mode
        const updatedGoals = goals.filter((goal) => goal.id !== goalId)
        setGoals(updatedGoals)
        saveGoalsToStorage(updatedGoals)
      } else {
        const { error } = await supabase.from("financial_goals").delete().eq("id", goalId)
        if (error) throw error
        fetchGoals()
      }

      addNotification({
        type: "success",
        title: "Goal Deleted",
        message: "Your financial goal has been deleted.",
      })
    } catch (error) {
      console.error("Error deleting goal:", error)
      addNotification({
        type: "error",
        title: "Error",
        message: "Failed to delete your goal. Please try again.",
      })
    }
  }

  const updateGoalProgress = async (goalId: string, newAmount: number) => {
    try {
      if (!configured) {
        // Demo mode
        const updatedGoals = goals.map((goal) =>
          goal.id === goalId ? { ...goal, current_amount: newAmount, updated_at: new Date().toISOString() } : goal,
        )
        setGoals(updatedGoals)
        saveGoalsToStorage(updatedGoals)
      } else {
        const { error } = await supabase.from("financial_goals").update({ current_amount: newAmount }).eq("id", goalId)
        if (error) throw error
        fetchGoals()
      }

      addNotification({
        type: "success",
        title: "Progress Updated",
        message: "Your goal progress has been updated!",
      })
    } catch (error) {
      console.error("Error updating progress:", error)
      addNotification({
        type: "error",
        title: "Error",
        message: "Failed to update progress. Please try again.",
      })
    }
  }

  const resetForm = () => {
    setFormData({
      title: "",
      description: "",
      target_amount: "",
      target_date: "",
      category: "",
      priority: "medium",
    })
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-500/20 text-red-400 border-red-500/30"
      case "medium":
        return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30"
      case "low":
        return "bg-green-500/20 text-green-400 border-green-500/30"
      default:
        return "bg-gray-500/20 text-gray-400 border-gray-500/30"
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "Emergency Fund":
        return "🛡️"
      case "Vacation":
        return "✈️"
      case "House Down Payment":
        return "🏠"
      case "Car":
        return "🚗"
      case "Education":
        return "🎓"
      case "Retirement":
        return "🏖️"
      default:
        return "🎯"
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-foreground">Financial Goals</h2>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="bg-card border-border">
              <CardContent className="p-6">
                <div className="animate-pulse space-y-4">
                  <div className="h-4 bg-muted rounded w-3/4"></div>
                  <div className="h-2 bg-muted rounded"></div>
                  <div className="h-4 bg-muted rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-foreground">Financial Goals</h2>
          <p className="text-muted-foreground">Track your progress toward important financial milestones</p>
        </div>
        {showAddButton ? (
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button
                onClick={() => {
                  resetForm()
                  setEditingGoal(null)
                }}
                className="bg-emerald-500 hover:bg-emerald-600"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Goal
              </Button>
            </DialogTrigger>
          </Dialog>
        ) : (
          <Button
            onClick={() => router.push("/dashboard/goals")}
            className="bg-emerald-500 hover:bg-emerald-600"
          >
            View All Goals
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        )}

        {showAddButton && (
          <>
            <DialogContent className="bg-card border-border">
            <DialogHeader>
              <DialogTitle className="text-foreground">{editingGoal ? "Edit Goal" : "Create New Goal"}</DialogTitle>
              <DialogDescription className="text-muted-foreground">
                Set a financial target and track your progress toward achieving it.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">Goal Title</Label>
                <Input
                  id="title"
                  placeholder="e.g., Emergency Fund"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description (Optional)</Label>
                <Textarea
                  id="description"
                  placeholder="Describe your goal..."
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="target_amount">Target Amount ($)</Label>
                  <Input
                    id="target_amount"
                    type="number"
                    placeholder="5000"
                    value={formData.target_amount}
                    onChange={(e) => setFormData({ ...formData, target_amount: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="target_date">Target Date</Label>
                  <Input
                    id="target_date"
                    type="date"
                    value={formData.target_date}
                    onChange={(e) => setFormData({ ...formData, target_date: e.target.value })}
                    required
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  <Select
                    value={formData.category}
                    onValueChange={(value) => setFormData({ ...formData, category: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Emergency Fund">Emergency Fund</SelectItem>
                      <SelectItem value="Vacation">Vacation</SelectItem>
                      <SelectItem value="House Down Payment">House Down Payment</SelectItem>
                      <SelectItem value="Car">Car</SelectItem>
                      <SelectItem value="Education">Education</SelectItem>
                      <SelectItem value="Retirement">Retirement</SelectItem>
                      <SelectItem value="Other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="priority">Priority</Label>
                  <Select
                    value={formData.priority}
                    onValueChange={(value: "low" | "medium" | "high") => setFormData({ ...formData, priority: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="low">Low</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" className="bg-emerald-500 hover:bg-emerald-600">
                  {editingGoal ? "Update Goal" : "Create Goal"}
                </Button>
              </DialogFooter>
            </form>
            </DialogContent>
          </>
        )}
      </div>

      {goals.length === 0 ? (
        <Card className="bg-card border-border">
          <CardContent className="p-12 text-center">
            <Target className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-foreground mb-2">No Goals Yet</h3>
            <p className="text-muted-foreground mb-4">
              Start by creating your first financial goal to track your progress.
            </p>
            <Button onClick={() => setIsDialogOpen(true)} className="bg-emerald-500 hover:bg-emerald-600">
              <Plus className="h-4 w-4 mr-2" />
              Create Your First Goal
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {goals.map((goal) => {
            const progress = (goal.current_amount / goal.target_amount) * 100
            const isCompleted = progress >= 100
            const daysLeft = Math.ceil(
              (new Date(goal.target_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24),
            )

            return (
              <Card
                key={goal.id}
                className={`bg-card border-border hover-lift ${isCompleted ? "ring-2 ring-emerald-500" : ""}`}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-2xl">{getCategoryIcon(goal.category)}</span>
                      <div>
                        <CardTitle className="text-lg text-foreground">{goal.title}</CardTitle>
                        <CardDescription className="text-muted-foreground">{goal.category}</CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getPriorityColor(goal.priority)}>{goal.priority}</Badge>
                      {isCompleted && <CheckCircle className="h-5 w-5 text-emerald-500" />}
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {goal.description && <p className="text-sm text-muted-foreground">{goal.description}</p>}

                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Progress</span>
                      <span className="font-medium text-foreground">
                        ${goal.current_amount.toLocaleString()} / ${goal.target_amount.toLocaleString()}
                      </span>
                    </div>
                    <Progress value={Math.min(progress, 100)} className="h-2" />
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>{progress.toFixed(1)}% complete</span>
                      <span>{daysLeft > 0 ? `${daysLeft} days left` : "Overdue"}</span>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Input
                      type="number"
                      placeholder="Add amount"
                      className="flex-1 h-8 text-sm"
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          const input = e.target as HTMLInputElement
                          const amount = Number.parseFloat(input.value)
                          if (amount > 0) {
                            updateGoalProgress(goal.id, goal.current_amount + amount)
                            input.value = ""
                          }
                        }
                      }}
                    />
                    <Button size="sm" variant="outline" onClick={() => handleEdit(goal)} className="h-8 w-8 p-0">
                      <Edit className="h-3 w-3" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDelete(goal.id)}
                      className="h-8 w-8 p-0 text-red-400 hover:text-red-300"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      )}
    </div>
  )
}
