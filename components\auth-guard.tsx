"use client"

import { useAuth } from "@/components/auth-provider"
import { useRouter, usePathname } from "next/navigation"
import { useEffect } from "react"

interface AuthGuardProps {
  children: React.ReactNode
}

const protectedRoutes = [
  '/dashboard',
  '/profile',
  '/portfolio',
  '/investments',
  '/settings',
  '/onboarding',
  '/learn',
  '/history',
  '/notifications',
  '/help'
]

export function AuthGuard({ children }: AuthGuardProps) {
  const { user, loading } = useAuth()
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    // Don't do anything while loading
    if (loading) return

    // Check if current path is protected
    const isProtectedRoute = protectedRoutes.some(route =>
      pathname.startsWith(route)
    )

    // If accessing a protected route without authentication, redirect to signin
    if (isProtectedRoute && !user) {
      console.log('AuthGuard: Redirecting unauthenticated user from', pathname, 'to signin')
      const redirectUrl = `/auth/signin?redirectTo=${encodeURIComponent(pathname)}`
      router.push(redirectUrl)
      return
    }

    // If user is authenticated and on auth pages, redirect to dashboard
    if (user && (pathname.startsWith('/auth/signin') || pathname.startsWith('/auth/signup'))) {
      console.log('AuthGuard: Redirecting authenticated user from', pathname, 'to dashboard')
      router.push('/dashboard')
      return
    }
  }, [user, loading, pathname, router])

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-white text-lg">Loading...</div>
      </div>
    )
  }

  // For protected routes, don't render children until user is authenticated
  const isProtectedRoute = protectedRoutes.some(route =>
    pathname.startsWith(route)
  )

  if (isProtectedRoute && !user) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-white text-lg">Redirecting to sign in...</div>
      </div>
    )
  }

  return <>{children}</>
}
