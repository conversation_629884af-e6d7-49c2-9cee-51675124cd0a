import type { SurveyData } from "@/components/onboarding-survey"

export interface PortfolioAllocation {
  symbol: string
  name: string
  allocation: number
  category: string
  rationale: string
}

export interface PersonalizedPortfolio {
  allocations: PortfolioAllocation[]
  riskLevel: string
  expectedReturn: string
  strategy: string
  rebalanceFrequency: string
  rationale: string
}

// Stock universe organized by themes and risk levels
const STOCK_UNIVERSE = {
  "Tech & AI": {
    conservative: [
      { symbol: "MSFT", name: "Microsoft Corp", category: "Large Cap Tech" },
      { symbol: "AAPL", name: "Apple Inc", category: "Large Cap Tech" },
      { symbol: "GOOGL", name: "Alphabet Inc", category: "Large Cap Tech" },
    ],
    moderate: [
      { symbol: "NVDA", name: "NVIDIA Corp", category: "AI/Semiconductors" },
      { symbol: "AMD", name: "Advanced Micro Devices", category: "Semiconductors" },
      { symbol: "CRM", name: "Salesforce Inc", category: "Cloud Software" },
    ],
    aggressive: [
      { symbol: "PLTR", name: "Palantir Technologies", category: "AI/Data Analytics" },
      { symbol: "SNOW", name: "Snowflake Inc", category: "Cloud Data" },
      { symbol: "NET", name: "Cloudflare Inc", category: "Edge Computing" },
    ],
  },
  "Green Energy": {
    conservative: [
      { symbol: "NEE", name: "NextEra Energy", category: "Renewable Utilities" },
      { symbol: "DUK", name: "Duke Energy", category: "Utilities" },
    ],
    moderate: [
      { symbol: "ENPH", name: "Enphase Energy", category: "Solar Technology" },
      { symbol: "FSLR", name: "First Solar", category: "Solar Panels" },
    ],
    aggressive: [
      { symbol: "TSLA", name: "Tesla Inc", category: "Electric Vehicles" },
      { symbol: "PLUG", name: "Plug Power", category: "Hydrogen Fuel Cells" },
    ],
  },
  "Healthcare & Biotech": {
    conservative: [
      { symbol: "JNJ", name: "Johnson & Johnson", category: "Healthcare" },
      { symbol: "PFE", name: "Pfizer Inc", category: "Pharmaceuticals" },
    ],
    moderate: [
      { symbol: "GILD", name: "Gilead Sciences", category: "Biotechnology" },
      { symbol: "AMGN", name: "Amgen Inc", category: "Biotechnology" },
    ],
    aggressive: [
      { symbol: "MRNA", name: "Moderna Inc", category: "mRNA Technology" },
      { symbol: "CRISPR", name: "CRISPR Therapeutics", category: "Gene Editing" },
    ],
  },
  "Real Estate": {
    conservative: [
      { symbol: "VNQ", name: "Vanguard Real Estate ETF", category: "REIT ETF" },
      { symbol: "SCHH", name: "Schwab US REIT ETF", category: "REIT ETF" },
    ],
    moderate: [
      { symbol: "PLD", name: "Prologis Inc", category: "Industrial REITs" },
      { symbol: "AMT", name: "American Tower Corp", category: "Cell Tower REITs" },
    ],
    aggressive: [
      { symbol: "EQIX", name: "Equinix Inc", category: "Data Center REITs" },
      { symbol: "SPG", name: "Simon Property Group", category: "Retail REITs" },
    ],
  },
  "ETFs / Index Funds": {
    conservative: [
      { symbol: "VTI", name: "Vanguard Total Stock Market", category: "Broad Market ETF" },
      { symbol: "VOO", name: "Vanguard S&P 500 ETF", category: "Large Cap ETF" },
    ],
    moderate: [
      { symbol: "VGT", name: "Vanguard Technology ETF", category: "Technology ETF" },
      { symbol: "VHT", name: "Vanguard Health Care ETF", category: "Healthcare ETF" },
    ],
    aggressive: [
      { symbol: "ARKK", name: "ARK Innovation ETF", category: "Innovation ETF" },
      { symbol: "ICLN", name: "iShares Clean Energy ETF", category: "Clean Energy ETF" },
    ],
  },
  "Blue-chip Stocks": {
    conservative: [
      { symbol: "KO", name: "Coca-Cola Co", category: "Consumer Staples" },
      { symbol: "PG", name: "Procter & Gamble", category: "Consumer Goods" },
      { symbol: "WMT", name: "Walmart Inc", category: "Retail" },
    ],
    moderate: [
      { symbol: "JPM", name: "JPMorgan Chase", category: "Banking" },
      { symbol: "V", name: "Visa Inc", category: "Financial Services" },
    ],
    aggressive: [
      { symbol: "BRK.B", name: "Berkshire Hathaway", category: "Conglomerate" },
      { symbol: "BA", name: "Boeing Co", category: "Aerospace" },
    ],
  },
  "Dividend Income": {
    conservative: [
      { symbol: "VYM", name: "Vanguard High Dividend Yield", category: "Dividend ETF" },
      { symbol: "SCHD", name: "Schwab US Dividend Equity", category: "Dividend ETF" },
    ],
    moderate: [
      { symbol: "T", name: "AT&T Inc", category: "Telecommunications" },
      { symbol: "VZ", name: "Verizon Communications", category: "Telecommunications" },
    ],
    aggressive: [
      { symbol: "XOM", name: "Exxon Mobil Corp", category: "Energy" },
      { symbol: "CVX", name: "Chevron Corp", category: "Energy" },
    ],
  },
}

// Major-specific theme adjustments
const MAJOR_THEME_ADJUSTMENTS = {
  "computer-science": {
    "Tech & AI": 1.4,
    "Green Energy": 1.1,
    "ETFs / Index Funds": 1.0,
  },
  engineering: {
    "Tech & AI": 1.3,
    "Green Energy": 1.3,
    "Real Estate": 1.1,
  },
  business: {
    "Blue-chip Stocks": 1.3,
    "ETFs / Index Funds": 1.2,
    "Dividend Income": 1.1,
  },
  finance: {
    "Blue-chip Stocks": 1.4,
    "ETFs / Index Funds": 1.3,
    "Dividend Income": 1.2,
  },
  biology: {
    "Healthcare & Biotech": 1.4,
    "Green Energy": 1.1,
    "ETFs / Index Funds": 1.0,
  },
  chemistry: {
    "Healthcare & Biotech": 1.3,
    "Green Energy": 1.2,
    "ETFs / Index Funds": 1.0,
  },
  economics: {
    "Blue-chip Stocks": 1.2,
    "ETFs / Index Funds": 1.3,
    "Dividend Income": 1.1,
  },
  marketing: {
    "Tech & AI": 1.2,
    "Blue-chip Stocks": 1.1,
    "ETFs / Index Funds": 1.0,
  },
  psychology: {
    "Healthcare & Biotech": 1.2,
    "ETFs / Index Funds": 1.2,
    "Blue-chip Stocks": 1.0,
  },
}

function getRiskCategory(riskTolerance: number): "conservative" | "moderate" | "aggressive" {
  if (riskTolerance <= 2) return "conservative"
  if (riskTolerance <= 3) return "moderate"
  return "aggressive"
}

function getBaseAllocation(
  themes: string[],
  riskTolerance: number,
  timeHorizon: string,
  experienceLevel: string,
  major?: string,
): Record<string, number> {
  // Validate themes input
  if (!themes || !Array.isArray(themes) || themes.length === 0) {
    console.warn("No themes provided, using default themes")
    themes = ["ETFs / Index Funds", "Blue-chip Stocks"]
  }

  const baseAllocations: Record<string, number> = {}

  // Start with equal weighting across selected themes
  const baseWeight = 100 / themes.length

  themes.forEach((theme) => {
    baseAllocations[theme] = baseWeight
  })

  // Apply major-specific adjustments
  if (major && MAJOR_THEME_ADJUSTMENTS[major as keyof typeof MAJOR_THEME_ADJUSTMENTS]) {
    const adjustments = MAJOR_THEME_ADJUSTMENTS[major as keyof typeof MAJOR_THEME_ADJUSTMENTS]
    Object.entries(adjustments).forEach(([theme, multiplier]) => {
      if (baseAllocations[theme]) {
        baseAllocations[theme] *= multiplier
      }
    })
  }

  // Adjust based on risk tolerance
  if (riskTolerance <= 2) {
    // Conservative: favor ETFs and blue-chip stocks
    if (baseAllocations["ETFs / Index Funds"]) baseAllocations["ETFs / Index Funds"] *= 1.3
    if (baseAllocations["Blue-chip Stocks"]) baseAllocations["Blue-chip Stocks"] *= 1.2
    if (baseAllocations["Dividend Income"]) baseAllocations["Dividend Income"] *= 1.2
  } else if (riskTolerance >= 4) {
    // Aggressive: favor growth themes
    if (baseAllocations["Tech & AI"]) baseAllocations["Tech & AI"] *= 1.3
    if (baseAllocations["Green Energy"]) baseAllocations["Green Energy"] *= 1.2
    if (baseAllocations["Healthcare & Biotech"]) baseAllocations["Healthcare & Biotech"] *= 1.1
  }

  // Adjust based on time horizon
  if (timeHorizon === "Less than 1 year" || timeHorizon === "1–3 years") {
    // Short-term: more conservative
    if (baseAllocations["ETFs / Index Funds"]) baseAllocations["ETFs / Index Funds"] *= 1.4
    if (baseAllocations["Dividend Income"]) baseAllocations["Dividend Income"] *= 1.3
  } else if (timeHorizon === "10+ years") {
    // Long-term: can be more aggressive
    if (baseAllocations["Tech & AI"]) baseAllocations["Tech & AI"] *= 1.2
    if (baseAllocations["Green Energy"]) baseAllocations["Green Energy"] *= 1.1
  }

  // Normalize to 100%
  const total = Object.values(baseAllocations).reduce((sum, val) => sum + val, 0)
  if (total > 0) {
    Object.keys(baseAllocations).forEach((key) => {
      baseAllocations[key] = (baseAllocations[key] / total) * 100
    })
  }

  return baseAllocations
}

function selectStocksFromTheme(
  theme: string,
  allocation: number,
  riskCategory: "conservative" | "moderate" | "aggressive",
  userId: string,
): PortfolioAllocation[] {
  const themeStocks = STOCK_UNIVERSE[theme as keyof typeof STOCK_UNIVERSE]
  if (!themeStocks) return []

  // Create a deterministic but unique selection based on user ID and theme
  const seed = hashString(userId + theme)
  const rng = createSeededRandom(seed)

  const availableStocks = [
    ...themeStocks.conservative,
    ...(riskCategory !== "conservative" ? themeStocks.moderate : []),
    ...(riskCategory === "aggressive" ? themeStocks.aggressive : []),
  ]

  // Select 2-4 stocks from the theme based on allocation size
  const numStocks = allocation > 25 ? 4 : allocation > 15 ? 3 : 2
  const selectedStocks = shuffleArray([...availableStocks], rng).slice(0, numStocks)

  // Distribute allocation among selected stocks with some randomization
  const allocations: PortfolioAllocation[] = []
  let remainingAllocation = allocation

  selectedStocks.forEach((stock, index) => {
    let stockAllocation: number
    if (index === selectedStocks.length - 1) {
      // Last stock gets remaining allocation
      stockAllocation = remainingAllocation
    } else {
      // Random allocation between 20-60% of remaining, but ensure minimum 5%
      const maxAllocation = Math.min(
        remainingAllocation * 0.6,
        remainingAllocation - (selectedStocks.length - index - 1) * 5,
      )
      const minAllocation = Math.max(5, remainingAllocation * 0.2)
      stockAllocation = minAllocation + rng() * (maxAllocation - minAllocation)
      remainingAllocation -= stockAllocation
    }

    allocations.push({
      symbol: stock.symbol,
      name: stock.name,
      allocation: Math.round(stockAllocation * 100) / 100,
      category: stock.category,
      rationale: `Selected for ${theme} exposure based on your risk profile and investment goals`,
    })
  })

  return allocations
}

// Simple hash function for string
function hashString(str: string): number {
  let hash = 0
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = (hash << 5) - hash + char
    hash = hash & hash // Convert to 32-bit integer
  }
  return Math.abs(hash)
}

// Seeded random number generator
function createSeededRandom(seed: number) {
  return () => {
    seed = (seed * 9301 + 49297) % 233280
    return seed / 233280
  }
}

// Fisher-Yates shuffle with seeded random
function shuffleArray<T>(array: T[], rng: () => number): T[] {
  const shuffled = [...array]
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(rng() * (i + 1))
    ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
  }
  return shuffled
}

export function generatePersonalizedPortfolio(
  surveyData: SurveyData,
  userId: string,
  major?: string,
): PersonalizedPortfolio {
  // Validate input data
  if (!surveyData) {
    throw new Error("Survey data is required")
  }

  // Ensure we have valid themes
  const validThemes =
    surveyData.interestedThemes && Array.isArray(surveyData.interestedThemes)
      ? surveyData.interestedThemes.filter((theme) => theme && typeof theme === "string")
      : []

  if (validThemes.length === 0) {
    console.warn("No valid themes found, using defaults")
    validThemes.push("ETFs / Index Funds", "Blue-chip Stocks")
  }

  const riskCategory = getRiskCategory(surveyData.riskTolerance || 3)
  const themeAllocations = getBaseAllocation(
    validThemes,
    surveyData.riskTolerance || 3,
    surveyData.timeHorizon || "5–10 years",
    surveyData.experienceLevel || "Beginner",
    major,
  )

  const portfolioAllocations: PortfolioAllocation[] = []

  // Generate stock selections for each theme
  Object.entries(themeAllocations).forEach(([theme, allocation]) => {
    const themeStocks = selectStocksFromTheme(theme, allocation, riskCategory, userId)
    portfolioAllocations.push(...themeStocks)
  })

  // Determine portfolio characteristics
  const riskLevel = riskCategory.charAt(0).toUpperCase() + riskCategory.slice(1)
  const expectedReturn = {
    conservative: "6-8%",
    moderate: "8-12%",
    aggressive: "12-16%",
  }[riskCategory]

  const strategy = generateStrategyDescription(surveyData, riskCategory, major)
  const rebalanceFrequency = surveyData.experienceLevel === "Beginner" ? "Quarterly" : "Monthly"

  return {
    allocations: portfolioAllocations,
    riskLevel,
    expectedReturn,
    strategy,
    rebalanceFrequency,
    rationale: generatePortfolioRationale(surveyData, riskCategory, major),
  }
}

function generateStrategyDescription(surveyData: SurveyData, riskCategory: string, major?: string): string {
  const strategies = {
    conservative: "Focus on stable, dividend-paying stocks and broad market ETFs",
    moderate: "Balanced mix of growth and value stocks across multiple sectors",
    aggressive: "Growth-oriented portfolio with emphasis on emerging technologies",
  }

  let strategy = strategies[riskCategory as keyof typeof strategies]

  if (major) {
    const majorStrategies = {
      "computer-science": " with emphasis on technology and AI companies",
      engineering: " focusing on innovative technology and clean energy solutions",
      business: " emphasizing established companies with strong fundamentals",
      finance: " with focus on financial services and dividend-paying stocks",
      biology: " with significant healthcare and biotechnology exposure",
      chemistry: " including pharmaceutical and materials companies",
      economics: " based on economic fundamentals and market analysis",
      marketing: " including consumer-focused and technology companies",
      psychology: " with consideration for behavioral finance principles",
    }

    if (majorStrategies[major as keyof typeof majorStrategies]) {
      strategy += majorStrategies[major as keyof typeof majorStrategies]
    }
  }

  const timeHorizon = surveyData.timeHorizon || "5–10 years"
  if (timeHorizon === "10+ years") {
    strategy += " with long-term growth potential"
  } else if (timeHorizon === "Less than 1 year") {
    strategy += " with capital preservation focus"
  }

  return strategy
}

function generatePortfolioRationale(surveyData: SurveyData, riskCategory: string, major?: string): string {
  const primaryGoal = surveyData.primaryGoal || "Long-term wealth building"
  const experienceLevel = surveyData.experienceLevel || "Beginner"
  const timeHorizon = surveyData.timeHorizon || "5–10 years"
  const interestedThemes = surveyData.interestedThemes || ["ETFs / Index Funds"]

  let rationale = `This portfolio is designed for your ${primaryGoal.toLowerCase()} goal with a ${riskCategory} risk approach. `

  rationale += `Given your ${experienceLevel.toLowerCase()} experience level and ${timeHorizon.toLowerCase()} time horizon, `

  if (major) {
    const majorRationales = {
      "computer-science":
        "and your computer science background, we've increased allocation to technology and AI companies that align with your field of study. ",
      engineering:
        "and your engineering background, we've emphasized technology and clean energy companies that match your technical expertise. ",
      business: "and your business studies, we've focused on established companies with strong business fundamentals. ",
      finance: "and your finance background, we've included more financial services and dividend-focused investments. ",
      biology: "and your biology studies, we've significantly increased healthcare and biotechnology exposure. ",
      chemistry: "and your chemistry background, we've included pharmaceutical and materials companies. ",
      economics: "and your economics studies, we've applied economic principles to stock selection. ",
      marketing: "and your marketing background, we've included consumer-focused companies. ",
      psychology: "and your psychology studies, we've considered behavioral finance in our selections. ",
    }

    if (majorRationales[major as keyof typeof majorRationales]) {
      rationale += majorRationales[major as keyof typeof majorRationales]
    }
  }

  if (riskCategory === "conservative") {
    rationale +=
      "we've emphasized stable, established companies and diversified ETFs to protect your capital while providing steady growth."
  } else if (riskCategory === "moderate") {
    rationale +=
      "we've balanced growth potential with stability, selecting a mix of established leaders and promising growth companies."
  } else {
    rationale +=
      "we've focused on high-growth potential investments that align with your risk tolerance and long-term outlook."
  }

  rationale += ` The portfolio reflects your interest in ${interestedThemes.join(", ").toLowerCase()}.`

  return rationale
}
