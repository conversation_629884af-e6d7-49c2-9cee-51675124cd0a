/**
 * Utility functions for onboarding flow management
 */

/**
 * Check if a user has completed the onboarding process
 * @param userId - The user's ID
 * @returns boolean indicating if onboarding is complete
 */
export function hasCompletedOnboarding(userId: string): boolean {
  if (!userId) return false
  
  try {
    const userSurveyKey = `investry_survey-${userId}`
    const userPortfolioKey = `demo-portfolio-${userId}`
    
    const completedSurvey = localStorage.getItem(userSurveyKey)
    const hasPortfolio = localStorage.getItem(userPortfolioKey)
    
    return !!(completedSurvey && hasPortfolio)
  } catch (error) {
    console.error('Error checking onboarding status:', error)
    return false
  }
}

/**
 * Get the appropriate redirect path for a user based on their onboarding status
 * @param userId - The user's ID
 * @returns string path to redirect to
 */
export function getRedirectPath(userId: string): string {
  // Default to dashboard for existing users (onboarding is optional)
  return '/dashboard'
}

/**
 * Clear user-specific onboarding data from localStorage
 * @param userId - The user's ID
 */
export function clearOnboardingData(userId: string): void {
  if (!userId) return
  
  try {
    const keysToRemove = [
      `investry_survey-${userId}`,
      `demo-portfolio-${userId}`,
      `user-portfolio-${userId}`,
      `portfolio-metadata-${userId}`
    ]
    
    keysToRemove.forEach(key => {
      localStorage.removeItem(key)
    })
    
    console.log(`Cleared onboarding data for user: ${userId}`)
  } catch (error) {
    console.error('Error clearing onboarding data:', error)
  }
}
