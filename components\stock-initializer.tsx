"use client"

import { useEffect, useState } from "react"
import { getAllStocks, getStockCount } from "@/lib/supabase"

interface StockData {
  id: number
  symbol: string
  name: string
  price: number
  change: number
  changePercent: number
  category: string
  sector: string
  market_cap: string
  tags: string[]
  description: string
  volume: number
  ticker: string
  company_name: string
  exchange: string
  asset_class: string
  stock_type: string
}

export function StockInitializer() {
  const [isLoading, setIsLoading] = useState(true)
  const [isInitialized, setIsInitialized] = useState(false)
  const [stockCount, setStockCount] = useState(0)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const initializeStocks = async () => {
      try {
        setIsLoading(true)
        setError(null)

        // Check if we already have stocks in localStorage
        const existingStocks = localStorage.getItem("stocks-data")
        const timestamp = localStorage.getItem("stocks-last-updated")
        const oneHour = 60 * 60 * 1000 // 1 hour in milliseconds

        // Use cached data if it's less than 1 hour old
        if (existingStocks && timestamp && Date.now() - new Date(timestamp).getTime() < oneHour) {
          const stocks = JSON.parse(existingStocks)
          setStockCount(stocks.length)
          setIsLoading(false)
          setIsInitialized(true)
          console.log(`Using cached stocks: ${stocks.length} items`)
          return
        }

        console.log("Fetching fresh stock data from Supabase...")

        // Fetch stocks from Supabase
        const [stocks, count] = await Promise.all([
          getAllStocks(1000), // Get first 1000 stocks
          getStockCount(),
        ])

        if (stocks && stocks.length > 0) {
          // Store in localStorage for quick access
          localStorage.setItem("stocks-data", JSON.stringify(stocks))
          localStorage.setItem("stocks-count", count.toString())
          localStorage.setItem("stocks-last-updated", new Date().toISOString())

          setStockCount(stocks.length)
          setIsInitialized(true)
          console.log(`Successfully loaded ${stocks.length} stocks from Supabase (${count} total available)`)

          // Dispatch event for other components
          window.dispatchEvent(
            new CustomEvent("stocksLoaded", {
              detail: { stocks, count },
            }),
          )
        } else {
          console.warn("No stocks returned from Supabase")
          setStockCount(count || 0)
          setError("No stock data available")
        }
      } catch (err) {
        console.error("Error initializing stocks:", err)
        setError(err instanceof Error ? err.message : "Failed to load stocks")

        // Try to use any existing cached data
        const existingStocks = localStorage.getItem("stocks-data")
        if (existingStocks) {
          try {
            const stocks = JSON.parse(existingStocks)
            setStockCount(stocks.length)
            setIsInitialized(true)
            console.log(`Falling back to cached stocks: ${stocks.length} items`)
          } catch (parseError) {
            console.error("Error parsing cached stocks:", parseError)
          }
        }
      } finally {
        setIsLoading(false)
      }
    }

    initializeStocks()
  }, [])

  // This component doesn't render anything visible
  return null
}

// Helper function to get stocks from localStorage
export function getStoredStocks(): StockData[] {
  if (typeof window === "undefined") return []

  try {
    const stored = localStorage.getItem("stocks-data")
    if (stored) {
      return JSON.parse(stored)
    }
  } catch (error) {
    console.error("Error reading stored stocks:", error)
  }
  return []
}

// Helper function to get a specific stock by symbol
export function getStockBySymbol(symbol: string): StockData | null {
  try {
    const stocks = getStoredStocks()
    return (
      stocks.find(
        (stock) =>
          stock.symbol.toLowerCase() === symbol.toLowerCase() || stock.ticker.toLowerCase() === symbol.toLowerCase(),
      ) || null
    )
  } catch (error) {
    console.error("Error finding stock by symbol:", error)
    return null
  }
}

// Helper function to search stocks
export function searchStoredStocks(query: string): StockData[] {
  try {
    const stocks = getStoredStocks()
    const searchTerm = query.toLowerCase()

    return stocks.filter(
      (stock) =>
        stock.symbol.toLowerCase().includes(searchTerm) ||
        stock.name.toLowerCase().includes(searchTerm) ||
        stock.category.toLowerCase().includes(searchTerm) ||
        stock.sector.toLowerCase().includes(searchTerm) ||
        stock.description.toLowerCase().includes(searchTerm) ||
        (Array.isArray(stock.tags) &&
          stock.tags.some((tag) => typeof tag === "string" && tag.toLowerCase().includes(searchTerm))),
    )
  } catch (error) {
    console.error("Error searching stored stocks:", error)
    return []
  }
}

// Helper function to get stocks by category
export function getStocksByCategory(category: string): StockData[] {
  try {
    const stocks = getStoredStocks()
    return stocks.filter((stock) => stock.category && stock.category.toLowerCase() === category.toLowerCase())
  } catch (error) {
    console.error("Error getting stocks by category:", error)
    return []
  }
}

// Helper function to get related tags for a category
export function getRelatedTags(category: string): string[] {
  try {
    const stocks = getStocksByCategory(category)
    const allTags = new Set<string>()

    stocks.forEach((stock) => {
      if (Array.isArray(stock.tags)) {
        stock.tags.forEach((tag) => {
          if (typeof tag === "string") {
            allTags.add(tag)
          }
        })
      }
    })

    return Array.from(allTags).slice(0, 10) // Return top 10 related tags
  } catch (error) {
    console.error("Error getting related tags:", error)
    return []
  }
}

export default StockInitializer
