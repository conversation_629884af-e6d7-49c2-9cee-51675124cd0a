/**
 * React Hook for Stock Data
 * Provides real-time stock data with caching and error handling
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { getHistoricalDataService, type HistoricalData, type TimePeriod, type TimeInterval } from '@/lib/historical-data-service'
import { type PortfolioPerformance, type PortfolioAllocation } from '@/lib/portfolio-performance-calculator'
import { getCacheManager } from '@/lib/cache-manager'
import { getBrowserCache } from '@/lib/browser-cache'

export interface StockQuote {
  symbol: string
  name: string
  price: number
  change: number
  changePercent: number
  volume: number
  high: number
  low: number
  open: number
  previousClose: number
  exchange: string
  sector?: string
  lastUpdated: string
  source: string
}

export interface UseStockQuoteResult {
  data: StockQuote | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  lastUpdated: Date | null
}

export interface UseMultipleStockQuotesResult {
  data: StockQuote[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  lastUpdated: Date | null
}

export interface UseStockSearchResult {
  data: StockQuote[]
  loading: boolean
  error: string | null
  search: (query: string) => Promise<void>
  lastUpdated: Date | null
}

export interface StockFinancials {
  symbol: string
  // Valuation metrics
  peRatio?: number
  pegRatio?: number
  priceToBook?: number
  priceToSales?: number
  enterpriseValue?: number
  evToRevenue?: number
  evToEbitda?: number

  // Profitability metrics
  eps?: number
  epsGrowth?: number
  revenueGrowth?: number
  grossMargin?: number
  operatingMargin?: number
  netMargin?: number
  roe?: number
  roa?: number

  // Dividend metrics
  dividendYield?: number
  dividendPerShare?: number
  payoutRatio?: number

  // Financial strength
  debtToEquity?: number
  currentRatio?: number
  quickRatio?: number

  // Market data
  marketCap?: number
  sharesOutstanding?: number
  beta?: number
  week52High?: number
  week52Low?: number

  lastUpdated: string
  source: string
}

export interface NewsArticle {
  id: string
  headline: string
  summary: string
  url: string
  source: string
  publishedAt: string
  category: string
  sentiment?: 'positive' | 'negative' | 'neutral'
  image?: string
  relatedSymbols: string[]
}

export interface StockNews {
  symbol: string
  articles: NewsArticle[]
  lastUpdated: string
  source: string
}

export interface UseStockFinancialsResult {
  data: StockFinancials | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  lastUpdated: Date | null
}

export interface UseStockNewsResult {
  data: StockNews | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  lastUpdated: Date | null
}

export interface MarketMover {
  symbol: string
  name: string
  price: number
  change: number
  changePercent: number
  volume: number
  marketCap?: number
  sector?: string
  lastUpdated: string
  source: string
}

export interface UseMarketMoversResult {
  gainers: MarketMover[]
  losers: MarketMover[]
  mostActive: MarketMover[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  lastUpdated: Date | null
}

/**
 * Hook for fetching a single stock quote
 */
export function useStockQuote(symbol: string, autoRefresh = false): UseStockQuoteResult {
  const [data, setData] = useState<StockQuote | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  const fetchQuote = useCallback(async () => {
    if (!symbol) return

    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/stocks/quote?symbol=${encodeURIComponent(symbol)}`)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (result.error) {
        throw new Error(result.error)
      }

      setData(result.data)
      setLastUpdated(new Date())
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch stock quote'
      setError(errorMessage)
      console.error('Stock quote fetch error:', err)
    } finally {
      setLoading(false)
    }
  }, [symbol])

  // Initial fetch
  useEffect(() => {
    if (symbol) {
      fetchQuote()
    }
  }, [symbol, fetchQuote])

  // Auto-refresh setup
  useEffect(() => {
    if (autoRefresh && symbol) {
      intervalRef.current = setInterval(fetchQuote, 60000) // Refresh every minute
      
      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current)
        }
      }
    }
  }, [autoRefresh, symbol, fetchQuote])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  return {
    data,
    loading,
    error,
    refetch: fetchQuote,
    lastUpdated
  }
}

/**
 * Hook for fetching multiple stock quotes
 */
export function useMultipleStockQuotes(symbols: string[], autoRefresh = false): UseMultipleStockQuotesResult {
  const [data, setData] = useState<StockQuote[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  const fetchQuotes = useCallback(async () => {
    if (!symbols.length) return

    setLoading(true)
    setError(null)

    try {
      const symbolsParam = symbols.join(',')
      const response = await fetch(`/api/stocks/quote?symbols=${encodeURIComponent(symbolsParam)}`)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (result.error) {
        throw new Error(result.error)
      }

      setData(result.data || [])
      setLastUpdated(new Date())
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch stock quotes'
      setError(errorMessage)
      console.error('Multiple stock quotes fetch error:', err)
    } finally {
      setLoading(false)
    }
  }, [symbols])

  // Initial fetch
  useEffect(() => {
    if (symbols.length > 0) {
      fetchQuotes()
    }
  }, [symbols, fetchQuotes])

  // Auto-refresh setup
  useEffect(() => {
    if (autoRefresh && symbols.length > 0) {
      intervalRef.current = setInterval(fetchQuotes, 60000) // Refresh every minute
      
      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current)
        }
      }
    }
  }, [autoRefresh, symbols, fetchQuotes])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  return {
    data,
    loading,
    error,
    refetch: fetchQuotes,
    lastUpdated
  }
}

/**
 * Hook for searching stocks
 */
export function useStockSearch(): UseStockSearchResult {
  const [data, setData] = useState<StockQuote[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)

  const search = useCallback(async (query: string) => {
    if (!query.trim()) {
      setData([])
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/stocks/search?q=${encodeURIComponent(query)}`)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (result.error) {
        throw new Error(result.error)
      }

      setData(result.data || [])
      setLastUpdated(new Date())
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to search stocks'
      setError(errorMessage)
      console.error('Stock search error:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  return {
    data,
    loading,
    error,
    search,
    lastUpdated
  }
}

/**
 * Hook for getting API status
 */
export function useStockApiStatus() {
  const [status, setStatus] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchStatus = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/stocks/status')
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      setStatus(result)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch API status'
      setError(errorMessage)
      console.error('API status fetch error:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchStatus()
  }, [fetchStatus])

  return {
    status,
    loading,
    error,
    refetch: fetchStatus
  }
}

/**
 * Hook for fetching stock financial data
 */
export function useStockFinancials(symbol: string): UseStockFinancialsResult {
  const [data, setData] = useState<StockFinancials | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const abortControllerRef = useRef<AbortController | null>(null)

  const fetchFinancials = useCallback(async () => {
    if (!symbol) return

    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    abortControllerRef.current = new AbortController()
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/stocks/financials?symbol=${encodeURIComponent(symbol)}`, {
        signal: abortControllerRef.current.signal
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      setData(result.data)
      setLastUpdated(new Date())
    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        return // Request was cancelled
      }

      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch financial data'
      setError(errorMessage)
      console.error('Financial data fetch error:', err)
    } finally {
      setLoading(false)
    }
  }, [symbol])

  useEffect(() => {
    fetchFinancials()

    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [fetchFinancials])

  return {
    data,
    loading,
    error,
    refetch: fetchFinancials,
    lastUpdated
  }
}

/**
 * Hook for fetching stock news
 */
export function useStockNews(symbol: string, limit: number = 10): UseStockNewsResult {
  const [data, setData] = useState<StockNews | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const abortControllerRef = useRef<AbortController | null>(null)

  const fetchNews = useCallback(async () => {
    if (!symbol) return

    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    abortControllerRef.current = new AbortController()
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/stocks/news?symbol=${encodeURIComponent(symbol)}&limit=${limit}`, {
        signal: abortControllerRef.current.signal
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      setData(result.data)
      setLastUpdated(new Date())
    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        return // Request was cancelled
      }

      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch news data'
      setError(errorMessage)
      console.error('News data fetch error:', err)
    } finally {
      setLoading(false)
    }
  }, [symbol, limit])

  useEffect(() => {
    fetchNews()

    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [fetchNews])

  return {
    data,
    loading,
    error,
    refetch: fetchNews,
    lastUpdated
  }
}

/**
 * Hook for fetching market movers (gainers, losers, most active)
 */
export function useMarketMovers(limit: number = 10): UseMarketMoversResult {
  const [gainers, setGainers] = useState<MarketMover[]>([])
  const [losers, setLosers] = useState<MarketMover[]>([])
  const [mostActive, setMostActive] = useState<MarketMover[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const abortControllerRef = useRef<AbortController | null>(null)

  const fetchMarketMovers = useCallback(async () => {
    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    abortControllerRef.current = new AbortController()
    setLoading(true)
    setError(null)

    try {
      // Fetch all three types of market movers in parallel
      const [gainersResponse, losersResponse, mostActiveResponse] = await Promise.all([
        fetch(`/api/stocks/gainers?limit=${limit}`, {
          signal: abortControllerRef.current.signal
        }),
        fetch(`/api/stocks/losers?limit=${limit}`, {
          signal: abortControllerRef.current.signal
        }),
        fetch(`/api/stocks/most-active?limit=${limit}`, {
          signal: abortControllerRef.current.signal
        })
      ])

      // Check if all requests were successful
      if (!gainersResponse.ok || !losersResponse.ok || !mostActiveResponse.ok) {
        throw new Error('Failed to fetch market movers data')
      }

      const [gainersResult, losersResult, mostActiveResult] = await Promise.all([
        gainersResponse.json(),
        losersResponse.json(),
        mostActiveResponse.json()
      ])

      setGainers(gainersResult.data || [])
      setLosers(losersResult.data || [])
      setMostActive(mostActiveResult.data || [])
      setLastUpdated(new Date())
    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        return // Request was cancelled
      }

      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch market movers'
      setError(errorMessage)
      console.error('Market movers fetch error:', err)
    } finally {
      setLoading(false)
    }
  }, [limit])

  useEffect(() => {
    fetchMarketMovers()

    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [fetchMarketMovers])

  return {
    gainers,
    losers,
    mostActive,
    loading,
    error,
    refetch: fetchMarketMovers,
    lastUpdated
  }
}

/**
 * Hook for fetching historical stock data for charts
 */
export function useHistoricalData(
  symbol: string,
  period: TimePeriod = '1M',
  interval: TimeInterval = '1d'
): {
  data: HistoricalData | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  lastUpdated: Date | null
} {
  const [data, setData] = useState<HistoricalData | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const abortControllerRef = useRef<AbortController | null>(null)

  const fetchHistoricalData = useCallback(async () => {
    if (!symbol) return

    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    abortControllerRef.current = new AbortController()
    setLoading(true)
    setError(null)

    try {
      const historicalService = getHistoricalDataService()
      const historicalData = await historicalService.getHistoricalData(symbol, period, interval)

      if (historicalData) {
        setData(historicalData)
        setLastUpdated(new Date())
      } else {
        setError('No historical data available for this symbol')
      }
    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        return // Request was cancelled
      }

      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch historical data'
      setError(errorMessage)
      console.error('Historical data fetch error:', err)
    } finally {
      setLoading(false)
    }
  }, [symbol, period, interval])

  useEffect(() => {
    fetchHistoricalData()

    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [fetchHistoricalData])

  return {
    data,
    loading,
    error,
    refetch: fetchHistoricalData,
    lastUpdated
  }
}

/**
 * Hook for fetching portfolio performance data for charts
 */
export function usePortfolioPerformance(
  allocations: PortfolioAllocation[],
  period: TimePeriod = '1M',
  interval: TimeInterval = '1d',
  _initialValue: number = 10000 // Prefixed with underscore to indicate unused
): {
  data: PortfolioPerformance | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  lastUpdated: Date | null
} {
  const [data, setData] = useState<PortfolioPerformance | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const abortControllerRef = useRef<AbortController | null>(null)

  const fetchPortfolioPerformance = useCallback(async () => {
    // Temporarily disable to prevent endless loops
    setLoading(false)
    setError(null)

    // Return mock data for now
    const mockPerformance = {
      data: [
        {
          date: '2024-01-01',
          timestamp: new Date('2024-01-01').getTime(),
          value: 10000,
          totalReturn: 0,
          totalReturnPercent: 0,
          dailyReturn: 0,
          dailyReturnPercent: 0
        },
        {
          date: new Date().toISOString().split('T')[0],
          timestamp: Date.now(),
          value: 10342.18,
          totalReturn: 342.18,
          totalReturnPercent: 3.42,
          dailyReturn: 136.80,
          dailyReturnPercent: 1.37
        }
      ],
      summary: {
        totalValue: 10342.18,
        totalReturn: 342.18,
        totalReturnPercent: 3.42,
        dailyChange: 136.80,
        dailyChangePercent: 1.37,
        bestDay: { date: new Date().toISOString().split('T')[0], return: 136.80, returnPercent: 1.37 },
        worstDay: { date: '2024-01-01', return: 0, returnPercent: 0 },
        volatility: 0,
        sharpeRatio: 0,
        maxDrawdown: 0
      },
      period,
      interval,
      lastUpdated: new Date().toISOString(),
      allocations
    }

    setData(mockPerformance)
    setLastUpdated(new Date())
  }, [period, allocations, interval])

  useEffect(() => {
    // Only fetch if we have allocations to prevent endless loops
    if (allocations && allocations.length > 0) {
      fetchPortfolioPerformance()
    }

    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [period]) // Only depend on period, not the entire callback

  return {
    data,
    loading,
    error,
    refetch: fetchPortfolioPerformance,
    lastUpdated
  }
}

/**
 * Hook for cache management and statistics
 */
export function useCacheManager() {
  const cacheManager = getCacheManager()
  const browserCache = getBrowserCache()
  const [stats, setStats] = useState({
    memory: cacheManager.getStats(),
    browser: browserCache.getStats()
  })

  const refreshStats = useCallback(() => {
    setStats({
      memory: cacheManager.getStats(),
      browser: browserCache.getStats()
    })
  }, [cacheManager, browserCache])

  const clearAllCache = useCallback(() => {
    cacheManager.clear()
    browserCache.clear()
    refreshStats()
  }, [cacheManager, browserCache, refreshStats])

  const clearCacheByTags = useCallback((tags: string[]) => {
    const memoryCleared = cacheManager.clearByTags(tags)
    const browserCleared = browserCache.clearByTags(tags)
    refreshStats()
    return { memoryCleared, browserCleared }
  }, [cacheManager, browserCache, refreshStats])

  const cleanupExpiredCache = useCallback(() => {
    const browserCleaned = browserCache.cleanup()
    refreshStats()
    return { browserCleaned }
  }, [browserCache, refreshStats])

  // Auto-refresh stats every 30 seconds
  useEffect(() => {
    const interval = setInterval(refreshStats, 30000)
    return () => clearInterval(interval)
  }, [refreshStats])

  return {
    stats,
    refreshStats,
    clearAllCache,
    clearCacheByTags,
    cleanupExpiredCache,
    cacheManager,
    browserCache
  }
}
