"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { OnboardingSurvey, type SurveyData } from "@/components/onboarding-survey"
import { PortfolioResults } from "@/components/portfolio-results"
import { generateEnhancedPortfolio, type EnhancedPortfolioResult } from "@/lib/enhanced-portfolio-generator"
import type { PersonalizedPortfolio } from "@/lib/portfolio-generator"
import { useNotifications } from "@/components/notification-system"
import { useAuth } from "@/components/auth-provider"
import { createClient } from "@/lib/supabase"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Building2, DollarSign, CheckCircle, ArrowRight } from "lucide-react"
import { PlaidLink } from "@/components/plaid-link"

type OnboardingStep = 'bank-linking' | 'deposit-funds' | 'survey' | 'portfolio'

export default function OnboardingPage() {
  const router = useRouter()
  const { user } = useAuth()
  const { addNotification } = useNotifications()
  const [currentStep, setCurrentStep] = useState<OnboardingStep>('bank-linking')
  const [hasLinkedAccount, setHasLinkedAccount] = useState(false)
  const [userBalance, setUserBalance] = useState(0)
  const [showPortfolio, setShowPortfolio] = useState(false)
  const [generatedPortfolio, setGeneratedPortfolio] = useState<PersonalizedPortfolio | null>(null)

  // Check user's current status on component mount
  useEffect(() => {
    const checkUserStatus = async () => {
      if (!user?.id) return

      try {
        // Check if user has linked accounts
        const accountsResponse = await fetch(`/api/plaid/accounts?user_id=${user.id}`)
        if (accountsResponse.ok) {
          const accountsData = await accountsResponse.json()
          if (accountsData.accounts && accountsData.accounts.length > 0) {
            setHasLinkedAccount(true)
            setCurrentStep('deposit-funds')
          }
        }

        // Check user balance
        const balanceResponse = await fetch(`/api/user-balance?user_id=${user.id}`)
        if (balanceResponse.ok) {
          const balanceData = await balanceResponse.json()
          const balance = balanceData.balance || 0
          setUserBalance(balance)

          if (balance > 0) {
            setCurrentStep('survey')
          }
        }

        // Check if user has completed onboarding
        const profileResponse = await fetch(`/api/user-profile?user_id=${user.id}`)
        if (profileResponse.ok) {
          const profileData = await profileResponse.json()
          if (profileData.onboarding_completed) {
            router.push('/dashboard')
            return
          }
        }
      } catch (error) {
        console.error('Error checking user status:', error)
      }
    }

    checkUserStatus()
  }, [user?.id, router])

  const handlePlaidSuccess = async (accounts: any[]) => {
    try {
      // For demo purposes, we'll just mark the account as linked
      // In production, this would handle the actual Plaid token exchange
      setHasLinkedAccount(true)
      setCurrentStep('deposit-funds')
      addNotification({
        type: "success",
        title: "Bank Account Connected!",
        message: "Your bank account has been successfully linked.",
      })
    } catch (error) {
      console.error('Error handling Plaid success:', error)
      addNotification({
        type: "error",
        title: "Connection Failed",
        message: "Failed to connect your bank account. Please try again.",
      })
    }
  }

  const handlePlaidError = (error: any) => {
    console.error('Plaid error:', error)
    addNotification({
      type: "error",
      title: "Connection Error",
      message: "There was an error connecting your bank account. Please try again.",
    })
  }

  const handleSurveyComplete = async (data: SurveyData) => {
    try {
      console.log("Survey data received:", data)

      // Validate the survey data
      if (!data || !data.interestedThemes || data.interestedThemes.length === 0) {
        console.error("Invalid survey data:", data)
        addNotification({
          type: "error",
          title: "Survey Error",
          message: "Please complete all survey questions before proceeding.",
        })
        return
      }

      // Generate personalized portfolio using enhanced LLM system
      const userId = user?.id || `demo-${Date.now()}`
      const userMajor = user?.user_metadata?.major || data.major || "computer-science"

      console.log("Generating enhanced portfolio for user:", userId, "with major:", userMajor)

      try {
        const result = await generateEnhancedPortfolio(data, userId, userMajor, {
          useLLM: true,
          fallbackToRules: true,
          additionalContext: `User is a ${userMajor} student interested in building their first investment portfolio.`
        })

        console.log("Generated portfolio:", result)
        console.log("Setting portfolio state and transitioning to portfolio step")

        // Save survey data and portfolio to user-specific localStorage
        const userSurveyKey = `investry_survey-${userId}`
        const userPortfolioKey = `demo-portfolio-${userId}`
        const userPortfolioKey2 = `user-portfolio-${userId}`
        const userMetadataKey = `portfolio-metadata-${userId}`

        localStorage.setItem(userSurveyKey, JSON.stringify(data))
        localStorage.setItem(userPortfolioKey, JSON.stringify(result.portfolio))
        localStorage.setItem(userPortfolioKey2, JSON.stringify(result.portfolio))

        // Save generation metadata
        localStorage.setItem(userMetadataKey, JSON.stringify({
          source: result.source,
          confidence: result.confidence,
          processingTimeMs: result.processingTimeMs,
          metadata: result.metadata
        }))

        setGeneratedPortfolio(result.portfolio)
        setShowPortfolio(true)
        setCurrentStep('portfolio') // Move to portfolio step

        // Show success notification with source info
        addNotification({
          type: "success",
          title: "Portfolio Generated Successfully",
          message: `Your personalized portfolio was generated using ${result.source === 'llm' ? 'AI analysis' : result.source === 'cache' ? 'cached results' : 'our proven algorithm'} in ${result.processingTimeMs}ms`
        })

      } catch (error) {
        console.error("Portfolio generation failed:", error)
        addNotification({
          type: "error",
          title: "Portfolio Generation Failed",
          message: "We encountered an issue generating your portfolio. Please try again."
        })
      }

      addNotification({
        type: "success",
        title: "Portfolio Generated!",
        message: "Your personalized investment portfolio has been created.",
      })
    } catch (error) {
      console.error("Error generating portfolio:", error)
      addNotification({
        type: "error",
        title: "Generation Error",
        message: "There was an error generating your portfolio. Please try again.",
      })
    }
  }

  const handlePortfolioAccept = async () => {
    try {
      // Get the current session to access the token
      const supabase = createClient()
      const { data: { session } } = await supabase.auth.getSession()

      if (!session?.access_token) {
        addNotification({
          type: "error",
          title: "Authentication Error",
          message: "Please sign in again to continue.",
        })
        return
      }

      // Save the portfolio to the database FIRST
      const response = await fetch('/api/portfolio/accept', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          portfolio: generatedPortfolio,
          surveyData: JSON.parse(localStorage.getItem(`investry_survey-${user?.id || `demo-${Date.now()}`}`) || '{}')
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to save portfolio')
      }

      const result = await response.json()
      console.log('Portfolio saved successfully:', result)

      // Portfolio is now saved! Show success message
      addNotification({
        type: "success",
        title: "Portfolio Accepted!",
        message: "Your personalized portfolio has been saved successfully.",
      })

      // Check user balance to determine next step
      const balanceResponse = await fetch(`/api/user-balance?user_id=${user?.id}`)

      if (balanceResponse.ok) {
        const balanceData = await balanceResponse.json()
        const userBalance = balanceData.balance || 0

        if (userBalance <= 0) {
          // User has no funds, redirect to payments
          addNotification({
            type: "info",
            title: "Add Funds to Start Investing",
            message: "Connect your bank account to start investing with your portfolio.",
          })
          router.push("/payments")
          return
        }
      }

      // User has funds, proceed to dashboard
      addNotification({
        type: "success",
        title: "Welcome to Investry!",
        message: "Your investment journey begins now.",
      })

      // Redirect to dashboard
      router.push("/dashboard")
    } catch (error) {
      console.error('Error accepting portfolio:', error)
      addNotification({
        type: "error",
        title: "Error Saving Portfolio",
        message: error instanceof Error ? error.message : "Failed to save portfolio. Please try again.",
      })
    }
  }

  // Render different steps based on current step
  console.log("Current onboarding step:", currentStep, "showPortfolio:", showPortfolio, "hasPortfolio:", !!generatedPortfolio)

  if (currentStep === 'bank-linking') {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl bg-slate-800 border-slate-700">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl text-white flex items-center justify-center gap-2">
              <Building2 className="h-6 w-6 text-emerald-400" />
              Connect Your Bank Account
            </CardTitle>
            <p className="text-slate-400 mt-2">
              First, let's securely connect your bank account to enable deposits and round-ups.
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="bg-slate-700/50 p-4 rounded-lg border border-slate-600">
              <h3 className="font-medium text-white mb-2">Why connect your bank?</h3>
              <ul className="text-sm text-slate-300 space-y-1">
                <li>• Secure deposits to fund your investments</li>
                <li>• Automatic round-ups on purchases</li>
                <li>• Real-time balance tracking</li>
                <li>• Bank-level security with Plaid</li>
              </ul>
            </div>

            <div className="text-center">
              <PlaidLink
                userId={user?.id || ''}
                onSuccess={handlePlaidSuccess}
                onError={handlePlaidError}
              />
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (currentStep === 'deposit-funds') {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl bg-slate-800 border-slate-700">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl text-white flex items-center justify-center gap-2">
              <DollarSign className="h-6 w-6 text-emerald-400" />
              Add Funds to Start Investing
            </CardTitle>
            <p className="text-slate-400 mt-2">
              Deposit money to begin your investment journey. Your funds will be used to build your personalized portfolio.
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="bg-slate-700/50 p-4 rounded-lg border border-slate-600">
              <div className="flex items-center justify-between">
                <span className="text-slate-300">Current Balance:</span>
                <span className="text-2xl font-bold text-emerald-400">${userBalance.toFixed(2)}</span>
              </div>
            </div>

            {userBalance > 0 ? (
              <div className="text-center space-y-4">
                <div className="flex items-center justify-center gap-2 text-emerald-400">
                  <CheckCircle className="h-5 w-5" />
                  <span>Funds available! Ready to create your portfolio.</span>
                </div>
                <Button
                  onClick={() => setCurrentStep('survey')}
                  className="bg-emerald-600 hover:bg-emerald-700 text-white"
                >
                  Continue to Investment Survey
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </div>
            ) : (
              <div className="text-center space-y-4">
                <p className="text-slate-400">Add funds to continue with portfolio creation.</p>
                <Button
                  onClick={() => router.push('/payments?from=onboarding')}
                  className="bg-emerald-600 hover:bg-emerald-700 text-white"
                >
                  Add Funds Now
                  <DollarSign className="h-4 w-4 ml-2" />
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    )
  }

  if (currentStep === 'survey') {
    return <OnboardingSurvey onComplete={handleSurveyComplete} />
  }

  if (currentStep === 'portfolio' && generatedPortfolio) {
    return <PortfolioResults portfolio={generatedPortfolio} onAccept={handlePortfolioAccept} />
  }

  // Default fallback - should not reach here normally
  return <OnboardingSurvey onComplete={handleSurveyComplete} />
}
