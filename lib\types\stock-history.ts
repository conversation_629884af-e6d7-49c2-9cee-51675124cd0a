/**
 * Stock History Service Types
 * Type definitions for historical stock data and related interfaces
 */

export type Timeframe = '1D' | '1W' | '1M' | '3M' | '1Y' | 'All'

export interface HistoricalDataPoint {
  timestamp: number // Unix timestamp in milliseconds
  close: number
  open?: number
  high?: number
  low?: number
  volume?: number
}

export interface StockHistoryResponse {
  symbol: string
  timeframe: Timeframe
  data: HistoricalDataPoint[]
  source: string
  lastUpdated: string
  cached: boolean
  dataPoints: number
}

export interface HistoryApiResponse {
  success: boolean
  data?: HistoricalDataPoint[]
  error?: string
  source: string
  rateLimit?: {
    remaining: number
    resetTime: number
  }
}

// API-specific response types
export interface PolygonHistoryResponse {
  ticker: string
  status: string
  results?: Array<{
    t: number // timestamp
    c: number // close
    o: number // open
    h: number // high
    l: number // low
    v: number // volume
  }>
  resultsCount: number
  adjusted: boolean
}

export interface TwelveDataHistoryResponse {
  meta: {
    symbol: string
    interval: string
    currency: string
    exchange_timezone: string
    exchange: string
    mic_code: string
    type: string
  }
  values: Array<{
    datetime: string
    open: string
    high: string
    low: string
    close: string
    volume: string
  }>
  status: string
}

export interface AlphaVantageHistoryResponse {
  'Meta Data': {
    '1. Information': string
    '2. Symbol': string
    '3. Last Refreshed': string
    '4. Output Size': string
    '5. Time Zone': string
  }
  'Time Series (Daily)': {
    [date: string]: {
      '1. open': string
      '2. high': string
      '3. low': string
      '4. close': string
      '5. volume': string
    }
  }
}

export interface FinnhubHistoryResponse {
  c: number[] // close prices
  h: number[] // high prices
  l: number[] // low prices
  o: number[] // open prices
  s: string   // status
  t: number[] // timestamps
  v: number[] // volumes
}

// Cache configuration for different timeframes
export interface TimeframeCacheConfig {
  ttl: number // Time to live in milliseconds
  maxAge: number // Maximum age before forced refresh
  priority: number // Cache priority (higher = more important)
}

export const TIMEFRAME_CACHE_CONFIG: Record<Timeframe, TimeframeCacheConfig> = {
  '1D': {
    ttl: 5 * 60 * 1000, // 5 minutes
    maxAge: 15 * 60 * 1000, // 15 minutes max
    priority: 10
  },
  '1W': {
    ttl: 15 * 60 * 1000, // 15 minutes
    maxAge: 60 * 60 * 1000, // 1 hour max
    priority: 8
  },
  '1M': {
    ttl: 60 * 60 * 1000, // 1 hour
    maxAge: 4 * 60 * 60 * 1000, // 4 hours max
    priority: 6
  },
  '3M': {
    ttl: 4 * 60 * 60 * 1000, // 4 hours
    maxAge: 24 * 60 * 60 * 1000, // 24 hours max
    priority: 4
  },
  '1Y': {
    ttl: 24 * 60 * 60 * 1000, // 24 hours
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days max
    priority: 2
  },
  'All': {
    ttl: 7 * 24 * 60 * 60 * 1000, // 7 days
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days max
    priority: 1
  }
}

// API provider configuration
export interface HistoryApiProvider {
  name: string
  priority: number
  rateLimit: {
    requests: number
    period: number // in milliseconds
  }
  supports: {
    timeframes: Timeframe[]
    maxDataPoints: number
    realtime: boolean
  }
}

export const API_PROVIDERS: Record<string, HistoryApiProvider> = {
  polygon: {
    name: 'Polygon.io',
    priority: 1, // Highest priority
    rateLimit: {
      requests: 1000,
      period: 60 * 1000 // per minute
    },
    supports: {
      timeframes: ['1D', '1W', '1M', '3M', '1Y', 'All'],
      maxDataPoints: 50000,
      realtime: true
    }
  },
  twelvedata: {
    name: 'Twelve Data',
    priority: 2,
    rateLimit: {
      requests: 800,
      period: 60 * 1000
    },
    supports: {
      timeframes: ['1D', '1W', '1M', '3M', '1Y', 'All'],
      maxDataPoints: 5000,
      realtime: true
    }
  },
  alphavantage: {
    name: 'Alpha Vantage',
    priority: 3,
    rateLimit: {
      requests: 25,
      period: 24 * 60 * 60 * 1000 // per day
    },
    supports: {
      timeframes: ['1D', '1W', '1M', '3M', '1Y', 'All'],
      maxDataPoints: 100,
      realtime: false
    }
  },
  finnhub: {
    name: 'Finnhub',
    priority: 4,
    rateLimit: {
      requests: 60,
      period: 60 * 1000
    },
    supports: {
      timeframes: ['1D', '1W', '1M', '3M', '1Y'],
      maxDataPoints: 1000,
      realtime: true
    }
  }
}

// Error types
export class StockHistoryError extends Error {
  constructor(
    message: string,
    public code: string,
    public provider?: string,
    public retryable: boolean = false
  ) {
    super(message)
    this.name = 'StockHistoryError'
  }
}

export interface HistoryServiceConfig {
  enableFallback: boolean
  maxRetries: number
  retryDelay: number
  preferredProvider?: string
  enableCaching: boolean
  cachePrefix: string
}

export const DEFAULT_HISTORY_CONFIG: HistoryServiceConfig = {
  enableFallback: true,
  maxRetries: 3,
  retryDelay: 1000,
  enableCaching: true,
  cachePrefix: 'stock_history'
}
