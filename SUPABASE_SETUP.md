# Supabase Setup Guide for Investry

## Step 1: Create a Supabase Project

1. Go to [https://supabase.com](https://supabase.com)
2. Sign up or sign in to your account
3. Click "New Project"
4. Choose your organization
5. Fill in project details:
   - **Name**: investry-production (or your preferred name)
   - **Database Password**: Create a strong password (save this!)
   - **Region**: Choose closest to your users
6. Click "Create new project"
7. Wait for the project to be created (takes 1-2 minutes)

## Step 2: Get Your Project Credentials

1. In your Supabase dashboard, go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (looks like: `https://your-project-id.supabase.co`)
   - **anon/public key** (starts with `eyJ...`)

## Step 3: Configure Environment Variables

1. Open your `.env.local` file in the project root
2. Replace the placeholder values:

```env
# Replace these with your actual Supabase values
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Step 4: Set Up Database Tables

1. In your Supabase dashboard, go to **SQL Editor**
2. Copy the contents of `scripts/create-tables-supabase.sql`
3. Paste it into the SQL Editor
4. Click "Run" to create all the necessary tables

## Step 5: Configure Authentication

1. In your Supabase dashboard, go to **Authentication** → **Settings**
2. Under **Site URL**, add your domain:
   - For development: `http://localhost:3000`
   - For production: `https://your-vercel-domain.vercel.app`
3. Under **Redirect URLs**, add:
   - `http://localhost:3000/auth/callback` (for development)
   - `https://your-vercel-domain.vercel.app/auth/callback` (for production)

## Step 6: Enable Row Level Security (RLS)

The SQL script automatically enables RLS, but verify in your dashboard:
1. Go to **Database** → **Tables**
2. For each table, ensure RLS is enabled (green shield icon)

## Step 7: Test the Setup

1. Restart your development server: `npm run dev`
2. Try creating a new account at `/auth/signup`
3. Check your Supabase dashboard under **Authentication** → **Users** to see if the user was created

## Step 8: Configure Email (Optional)

For production, set up email authentication:
1. Go to **Authentication** → **Settings** → **SMTP Settings**
2. Configure your email provider (SendGrid, Mailgun, etc.)
3. This enables email verification and password reset

## Security Checklist

✅ **Environment Variables**: Never commit `.env.local` to version control
✅ **RLS Policies**: All tables have proper Row Level Security policies
✅ **API Keys**: Only use the `anon/public` key in frontend code
✅ **HTTPS**: Always use HTTPS in production
✅ **Email Verification**: Enable for production use

## Troubleshooting

### "Supabase not configured" Error
- Check that your environment variables are set correctly
- Restart your development server after changing `.env.local`
- Ensure the Supabase URL includes `supabase.co`

### Authentication Not Working
- Verify your Site URL and Redirect URLs in Supabase settings
- Check browser console for detailed error messages
- Ensure RLS policies are properly configured

### Database Connection Issues
- Verify your project is active in the Supabase dashboard
- Check that all tables were created successfully
- Ensure your database password is correct

## Next Steps

Once Supabase is configured:
1. Your app will automatically switch from demo mode to production mode
2. User data will be securely stored in your Supabase database
3. Authentication will use secure JWT tokens instead of localStorage
4. All API calls will be protected by Row Level Security

## Support

If you encounter issues:
1. Check the Supabase documentation: [https://supabase.com/docs](https://supabase.com/docs)
2. Review the browser console for error messages
3. Verify all environment variables are correctly set
