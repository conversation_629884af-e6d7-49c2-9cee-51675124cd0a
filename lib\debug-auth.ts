/**
 * Debug utilities for authentication and localStorage issues
 */

/**
 * Clear all investry-related localStorage data
 */
export function clearAllInvestryData(): void {
  console.log('🧹 Clearing all investry localStorage data...')
  
  // Get all localStorage keys
  const keys = Object.keys(localStorage)
  
  // Filter for investry-related keys
  const investryKeys = keys.filter(key => 
    key.includes('investry') || 
    key.includes('demo-portfolio') || 
    key.includes('user-portfolio') || 
    key.includes('portfolio-metadata') ||
    key.includes('supabase') ||
    key.includes('sb-')
  )
  
  console.log('Found investry-related keys:', investryKeys)
  
  // Remove all investry-related keys
  investryKeys.forEach(key => {
    console.log(`Removing: ${key}`)
    localStorage.removeItem(key)
  })
  
  // Also clear session storage
  const sessionKeys = Object.keys(sessionStorage)
  const investrySessionKeys = sessionKeys.filter(key => 
    key.includes('investry') || 
    key.includes('demo-portfolio') || 
    key.includes('user-portfolio') || 
    key.includes('portfolio-metadata') ||
    key.includes('supabase') ||
    key.includes('sb-')
  )
  
  investrySessionKeys.forEach(key => {
    console.log(`Removing from session: ${key}`)
    sessionStorage.removeItem(key)
  })
  
  console.log('✅ All investry data cleared!')
}

/**
 * Debug current authentication and localStorage state
 */
export function debugAuthState(userId?: string): void {
  console.log('🔍 DEBUG: Current Auth State')
  console.log('========================')
  
  if (userId) {
    console.log(`User ID: ${userId}`)
    
    // Check user-specific keys
    const userSurveyKey = `investry_survey-${userId}`
    const userPortfolioKey = `demo-portfolio-${userId}`
    const userPortfolioKey2 = `user-portfolio-${userId}`
    const userMetadataKey = `portfolio-metadata-${userId}`
    
    console.log(`Survey data (${userSurveyKey}):`, localStorage.getItem(userSurveyKey))
    console.log(`Portfolio data (${userPortfolioKey}):`, localStorage.getItem(userPortfolioKey))
    console.log(`Portfolio data 2 (${userPortfolioKey2}):`, localStorage.getItem(userPortfolioKey2))
    console.log(`Metadata (${userMetadataKey}):`, localStorage.getItem(userMetadataKey))
  }
  
  // Check old non-user-specific keys
  console.log('\nOld non-user-specific keys:')
  console.log('Survey data (investry_survey):', localStorage.getItem('investry_survey'))
  console.log('Portfolio data (demo-portfolio):', localStorage.getItem('demo-portfolio'))
  console.log('Portfolio data (user-portfolio):', localStorage.getItem('user-portfolio'))
  console.log('Metadata (portfolio-metadata):', localStorage.getItem('portfolio-metadata'))
  
  // Check all localStorage keys
  console.log('\nAll localStorage keys:')
  Object.keys(localStorage).forEach(key => {
    if (key.includes('investry') || key.includes('portfolio') || key.includes('supabase') || key.includes('sb-')) {
      console.log(`${key}: ${localStorage.getItem(key)?.substring(0, 100)}...`)
    }
  })
  
  console.log('========================')
}

/**
 * Force clear authentication session and 2FA remnants
 */
export function forceSignOut(): void {
  console.log('🚪 Force signing out and clearing 2FA remnants...')

  // Clear all investry data
  clearAllInvestryData()

  // Clear any auth tokens and 2FA data
  const authKeys = Object.keys(localStorage).filter(key =>
    key.includes('supabase') ||
    key.includes('sb-') ||
    key.includes('auth') ||
    key.includes('token') ||
    key.includes('2fa') ||
    key.includes('phone') ||
    key.includes('verification') ||
    key.includes('remember')
  )

  authKeys.forEach(key => {
    console.log(`Removing auth key: ${key}`)
    localStorage.removeItem(key)
  })

  // Also clear from session storage
  const sessionAuthKeys = Object.keys(sessionStorage).filter(key =>
    key.includes('supabase') ||
    key.includes('sb-') ||
    key.includes('auth') ||
    key.includes('token') ||
    key.includes('2fa') ||
    key.includes('phone') ||
    key.includes('verification') ||
    key.includes('remember')
  )

  sessionAuthKeys.forEach(key => {
    console.log(`Removing session auth key: ${key}`)
    sessionStorage.removeItem(key)
  })

  // Clear specific 2FA keys that might be lingering
  const twoFAKeys = [
    'investry_last_2fa_verification',
    'investry_remember_device',
    'investry_phone_verified',
    'investry_auth_flow'
  ]

  twoFAKeys.forEach(key => {
    localStorage.removeItem(key)
    sessionStorage.removeItem(key)
  })

  console.log('✅ Force sign out and 2FA cleanup complete!')
  console.log('Please refresh the page to complete the process.')
}

// Make these available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).debugInvestry = {
    clearAllData: clearAllInvestryData,
    debugAuth: debugAuthState,
    forceSignOut: forceSignOut
  }
  
  console.log('🛠️ Debug utilities available at window.debugInvestry')
  console.log('Available methods:')
  console.log('- window.debugInvestry.clearAllData()')
  console.log('- window.debugInvestry.debugAuth(userId?)')
  console.log('- window.debugInvestry.forceSignOut()')
}
