/**
 * Most Active Stocks API Route
 * GET /api/stocks/most-active?limit=10
 */

import { NextRequest, NextResponse } from 'next/server'
import { getStockDataService } from '@/lib/stock-config'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const limitParam = searchParams.get('limit')
    const limit = limitParam ? parseInt(limitParam, 10) : 10

    // Validate limit
    if (isNaN(limit) || limit < 1 || limit > 50) {
      return NextResponse.json(
        { error: 'Limit must be a number between 1 and 50' },
        { status: 400 }
      )
    }

    const stockService = getStockDataService()
    const mostActive = await stockService.getMostActive(limit)

    return NextResponse.json({
      data: mostActive,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Most active API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
