"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { RefreshCw, TrendingUp, TrendingDown, Clock, Wifi, WifiOff } from 'lucide-react'
import { useStockQuote } from '@/hooks/use-stock-data'
import { cn } from '@/lib/utils'

interface RealTimeStockQuoteProps {
  symbol: string
  autoRefresh?: boolean
  showDetails?: boolean
  className?: string
}

export function RealTimeStockQuote({ 
  symbol, 
  autoRefresh = true, 
  showDetails = true,
  className 
}: RealTimeStockQuoteProps) {
  const { data, loading, error, refetch, lastUpdated } = useStockQuote(symbol, autoRefresh)
  const [isRefreshing, setIsRefreshing] = useState(false)

  const handleRefresh = async () => {
    setIsRefreshing(true)
    await refetch()
    setIsRefreshing(false)
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(price)
  }

  const formatChange = (change: number, changePercent: number) => {
    const changeStr = change >= 0 ? `+${change.toFixed(2)}` : change.toFixed(2)
    const percentStr = changePercent >= 0 ? `+${changePercent.toFixed(2)}%` : `${changePercent.toFixed(2)}%`
    return `${changeStr} (${percentStr})`
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  const getSourceBadgeColor = (source: string) => {
    switch (source) {
      case 'finnhub': return 'bg-green-500'
      case 'alphavantage': return 'bg-blue-500'
      case 'yahoo': return 'bg-yellow-500'
      case 'cache': return 'bg-gray-500'
      default: return 'bg-gray-500'
    }
  }

  if (error) {
    return (
      <Card className={cn("w-full", className)}>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <WifiOff className="h-4 w-4 text-red-500" />
              <span className="text-sm text-red-500">Error loading {symbol}</span>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw className={cn("h-4 w-4", isRefreshing && "animate-spin")} />
            </Button>
          </div>
          <p className="text-xs text-muted-foreground mt-2">{error}</p>
        </CardContent>
      </Card>
    )
  }

  if (loading && !data) {
    return (
      <Card className={cn("w-full", className)}>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <RefreshCw className="h-4 w-4 animate-spin" />
              <span className="text-sm">Loading {symbol}...</span>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!data) {
    return (
      <Card className={cn("w-full", className)}>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            No data available for {symbol}
          </div>
        </CardContent>
      </Card>
    )
  }

  const isPositive = data.change >= 0
  const changeColor = isPositive ? 'text-green-600' : 'text-red-600'
  const TrendIcon = isPositive ? TrendingUp : TrendingDown

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg font-semibold">{data.symbol}</CardTitle>
            <p className="text-sm text-muted-foreground truncate">{data.name}</p>
          </div>
          <div className="flex items-center space-x-2">
            {data.source && (
              <Badge 
                variant="secondary" 
                className={cn("text-xs", getSourceBadgeColor(data.source))}
              >
                {data.source}
              </Badge>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing || loading}
            >
              <RefreshCw className={cn("h-4 w-4", (isRefreshing || loading) && "animate-spin")} />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="space-y-4">
          {/* Price and Change */}
          <div className="flex items-center justify-between">
            <div className="text-2xl font-bold">
              {formatPrice(data.price)}
            </div>
            <div className={cn("flex items-center space-x-1", changeColor)}>
              <TrendIcon className="h-4 w-4" />
              <span className="font-medium">
                {formatChange(data.change, data.changePercent)}
              </span>
            </div>
          </div>

          {/* Additional Details */}
          {showDetails && (
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Open:</span>
                <span className="ml-2 font-medium">{formatPrice(data.open)}</span>
              </div>
              <div>
                <span className="text-muted-foreground">High:</span>
                <span className="ml-2 font-medium">{formatPrice(data.high)}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Low:</span>
                <span className="ml-2 font-medium">{formatPrice(data.low)}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Prev Close:</span>
                <span className="ml-2 font-medium">{formatPrice(data.previousClose)}</span>
              </div>
              {data.volume > 0 && (
                <div className="col-span-2">
                  <span className="text-muted-foreground">Volume:</span>
                  <span className="ml-2 font-medium">
                    {data.volume.toLocaleString()}
                  </span>
                </div>
              )}
              {data.exchange && (
                <div>
                  <span className="text-muted-foreground">Exchange:</span>
                  <span className="ml-2 font-medium">{data.exchange}</span>
                </div>
              )}
              {data.sector && (
                <div>
                  <span className="text-muted-foreground">Sector:</span>
                  <span className="ml-2 font-medium">{data.sector}</span>
                </div>
              )}
            </div>
          )}

          {/* Last Updated */}
          <div className="flex items-center justify-between text-xs text-muted-foreground border-t pt-3">
            <div className="flex items-center space-x-1">
              <Clock className="h-3 w-3" />
              <span>
                Last updated: {lastUpdated ? formatTime(lastUpdated) : 'Never'}
              </span>
            </div>
            <div className="flex items-center space-x-1">
              <Wifi className="h-3 w-3" />
              <span>
                {autoRefresh ? 'Auto-refresh on' : 'Manual refresh'}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
