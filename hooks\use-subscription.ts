"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/auth-provider'

interface SubscriptionStatus {
  hasActiveSubscription: boolean
  plan: string
  planName: string
  status: string
  isTrialing?: boolean
  trialEnd?: string
  daysUntilRenewal?: number
  loading: boolean
  error: string | null
}

export function useSubscription(): SubscriptionStatus {
  const { user } = useAuth()
  const [subscription, setSubscription] = useState<SubscriptionStatus>({
    hasActiveSubscription: false,
    plan: 'free',
    planName: 'Investry Free',
    status: 'free',
    loading: true,
    error: null
  })

  useEffect(() => {
    if (!user) {
      setSubscription(prev => ({
        ...prev,
        loading: false,
        hasActiveSubscription: false,
        plan: 'free',
        planName: 'Investry Free',
        status: 'free'
      }))
      return
    }

    fetchSubscriptionStatus()
  }, [user])

  const fetchSubscriptionStatus = async () => {
    try {
      setSubscription(prev => ({ ...prev, loading: true, error: null }))
      
      const response = await fetch(`/api/subscription-status?user_id=${user?.id}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch subscription status')
      }
      
      const data = await response.json()
      
      setSubscription({
        hasActiveSubscription: data.hasActiveSubscription,
        plan: data.plan,
        planName: data.planName,
        status: data.status,
        isTrialing: data.isTrialing,
        trialEnd: data.trialEnd,
        daysUntilRenewal: data.daysUntilRenewal,
        loading: false,
        error: null
      })
      
    } catch (error) {
      console.error('Error fetching subscription status:', error)
      setSubscription(prev => ({
        ...prev,
        loading: false,
        error: 'Failed to load subscription status'
      }))
    }
  }

  return subscription
}
