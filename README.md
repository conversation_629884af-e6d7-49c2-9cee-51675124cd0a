# Investry.ai - AI-Powered Investment Platform

A modern investment platform with AI-powered portfolio generation, real-time stock data, and comprehensive financial tools.

## 🚀 New: LLM Portfolio Generation System

**Now featuring AI-powered portfolio recommendations!** Our new LLM Portfolio Generation System provides intelligent, personalized investment advice using advanced language models while maintaining backward compatibility with our proven rule-based system.

### Key Features
- 🤖 **AI-Powered Analysis**: GPT-4 integration for sophisticated portfolio recommendations
- 🔄 **Portfolio Reweighting**: AI-powered portfolio rebalancing based on user feedback
- 🚀 **Intelligent Caching**: Multi-layer caching system for optimal performance
- 🛡️ **Secure & Reliable**: JWT authentication, rate limiting, and automatic fallback
- 📊 **Comprehensive Monitoring**: Real-time health checks and usage analytics
- 🔄 **Seamless Integration**: Drop-in replacement for existing portfolio generation

## Features

### 💼 Portfolio Management
- **AI-Powered Portfolio Generation**: Advanced LLM analysis for personalized recommendations
- **Rule-Based Fallback**: Proven algorithm ensures reliability when AI is unavailable
- **Real-time Stock Data**: Live market data integration with multiple providers
- **Risk Assessment**: Intelligent risk profiling based on user preferences
- **Diversification Analysis**: Automated portfolio optimization and rebalancing

### 📈 Investment Tools
- **Stock Search & Analysis**: Comprehensive stock research with financial metrics
- **Market Data**: Real-time quotes, charts, and market indicators
- **News Integration**: Relevant financial news and market updates
- **Performance Tracking**: Portfolio performance monitoring and analytics

### 💳 Payment & Banking
- **Stripe Integration**: Secure payment processing for subscriptions
- **Plaid Integration**: Bank account linking for ACH transfers
- **Round-up Investments**: Automatic spare change investing
- **Subscription Management**: Flexible premium tier management

### 🎓 Educational Content
- **Investment Learning**: Comprehensive educational resources
- **Risk Education**: Understanding investment risks and strategies
- **Market Insights**: Regular market analysis and commentary

## Quick Start

### 1. Clone and Install
```bash
git clone <repository-url>
cd investry-final
npm install --legacy-peer-deps
```

### 2. Environment Setup
Copy `.env.local.example` to `.env.local` and configure:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_key

# OpenAI Configuration (for LLM Portfolio Generation)
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4

# Stock API Keys
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key
FINNHUB_API_KEY=your_finnhub_key

# Payment Processing
STRIPE_SECRET_KEY=your_stripe_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_public_key

# Banking Integration
PLAID_CLIENT_ID=your_plaid_client_id
PLAID_SECRET=your_plaid_secret
```

### 3. Database Setup
```bash
# Set up Supabase tables
# Run scripts/create-tables-supabase.sql in Supabase SQL Editor

# Set up LLM system tables
# Run scripts/create-llm-tables-supabase.sql in Supabase SQL Editor
```

### 4. Test LLM System
```bash
# Test the AI portfolio generation system
node scripts/test-llm-system.js
```

### 5. Start Development
```bash
npm run dev
```

Visit `http://localhost:3000` to see the application.

## LLM Portfolio System

### Quick Setup
See [docs/QUICK_START.md](docs/QUICK_START.md) for a 5-minute setup guide.

### Full Documentation
See [docs/LLM_PORTFOLIO_SYSTEM.md](docs/LLM_PORTFOLIO_SYSTEM.md) for comprehensive documentation.

### API Endpoints
- `POST /api/portfolio/generate-llm` - Generate AI-powered portfolio
- `POST /api/portfolio/reweight` - Reweight existing portfolio with AI
- `GET /api/portfolio/llm-status` - System health and status
- `GET /api/portfolio/user-stats` - User generation statistics

### How It Works
1. User completes investment survey
2. AI analyzes preferences and generates personalized portfolio
3. System caches results for performance
4. Automatic fallback to rule-based system if AI unavailable
5. All interactions logged for monitoring and optimization

## Architecture

### Frontend
- **Next.js 14**: React framework with App Router
- **TypeScript**: Type-safe development
- **Tailwind CSS**: Utility-first styling
- **Shadcn/ui**: Modern component library

### Backend
- **Next.js API Routes**: Serverless API endpoints
- **Supabase**: PostgreSQL database with real-time features
- **OpenAI API**: LLM integration for portfolio generation
- **Stripe API**: Payment processing
- **Plaid API**: Banking integration

### External Services
- **Alpha Vantage**: Stock market data
- **Finnhub**: Real-time financial data
- **Yahoo Finance**: Fallback stock data

## Project Structure

```
├── app/                    # Next.js App Router pages
│   ├── api/               # API routes
│   │   └── portfolio/     # Portfolio-related endpoints
│   ├── onboarding/        # User onboarding flow
│   └── dashboard/         # Main application dashboard
├── components/            # React components
├── lib/                   # Utility libraries
│   ├── llm-portfolio/     # LLM Portfolio Generation System
│   ├── portfolio-generator.ts  # Original rule-based generator
│   ├── enhanced-portfolio-generator.ts  # New LLM-enhanced generator
│   └── supabase.ts        # Database client
├── docs/                  # Documentation
├── scripts/               # Database and utility scripts
└── hooks/                 # Custom React hooks
```

## Development

### Running Tests
```bash
# Run LLM system tests
npm test lib/llm-portfolio/__tests__/

# Run manual system test
node scripts/test-llm-system.js
```

### Database Management
```bash
# Create new migration
supabase migration new migration_name

# Apply migrations
supabase db push
```

### Monitoring
- Health Check: `GET /api/portfolio/llm-status`
- User Stats: `GET /api/portfolio/user-stats`
- Supabase Dashboard: Monitor database and usage

## Deployment

### Vercel (Recommended)
```bash
# Deploy to Vercel
vercel --prod

# Set environment variables in Vercel dashboard
```

### Environment Variables
Ensure all required environment variables are set in your deployment platform:
- Database credentials
- API keys for external services
- OpenAI API key for LLM features
- Payment processing keys

## Security

- 🔐 **Authentication**: Supabase Auth with JWT tokens
- 🛡️ **Authorization**: Row-level security policies
- 🚦 **Rate Limiting**: API endpoint protection
- 📝 **Audit Logging**: Comprehensive activity tracking
- 🔒 **Data Encryption**: All data encrypted in transit and at rest

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## Support

- 📖 **Documentation**: See `/docs` directory
- 🧪 **Testing**: Run test scripts for diagnostics
- 🔍 **Monitoring**: Use health check endpoints
- 📊 **Analytics**: Check Supabase dashboard for insights

## License

This project is proprietary software. All rights reserved.

---

**Built with ❤️ for the future of investing**
