/**
 * Chart Performance Optimizer
 * Optimizes chart rendering performance through data resampling, caching, and smart updates
 */

import { ChartDataPoint, CandlestickDataPoint } from './chart-data-adapters'
import { getCacheManager, createCacheKey } from './cache-manager'
import { getCacheTTL } from './cache-config'

export interface ChartOptimizationConfig {
  maxDataPoints: number
  enableResampling: boolean
  enableCaching: boolean
  enableLazyLoading: boolean
  resamplingStrategy: 'uniform' | 'adaptive' | 'importance'
  cacheStrategy: 'memory' | 'browser' | 'both'
  updateThreshold: number // Minimum change to trigger update
}

export interface OptimizedChartData {
  data: ChartDataPoint[]
  originalLength: number
  resampledLength: number
  compressionRatio: number
  lastUpdated: number
  cacheKey: string
}

export class ChartPerformanceOptimizer {
  private config: ChartOptimizationConfig
  private cacheManager = getCacheManager()
  private updateQueue = new Map<string, NodeJS.Timeout>()

  constructor(config: Partial<ChartOptimizationConfig> = {}) {
    this.config = {
      maxDataPoints: 200,
      enableResampling: true,
      enableCaching: true,
      enableLazyLoading: true,
      resamplingStrategy: 'adaptive',
      cacheStrategy: 'both',
      updateThreshold: 0.01, // 1% change threshold
      ...config
    }
  }

  /**
   * Optimize chart data for rendering performance
   */
  optimizeChartData(
    data: ChartDataPoint[],
    chartType: 'line' | 'area' | 'bar',
    symbol?: string,
    period?: string
  ): OptimizedChartData {
    const cacheKey = createCacheKey('chart-optimized', symbol || 'unknown', period || 'default', chartType)
    
    // Check cache first
    if (this.config.enableCaching) {
      const cached = this.cacheManager.get<OptimizedChartData>(cacheKey)
      if (cached && this.isCacheValid(cached, data)) {
        return cached
      }
    }

    let optimizedData = [...data]
    const originalLength = data.length

    // Apply resampling if needed
    if (this.config.enableResampling && data.length > this.config.maxDataPoints) {
      optimizedData = this.resampleData(data, this.config.maxDataPoints)
    }

    // Apply smoothing for better visual appearance
    optimizedData = this.applySmoothingFilter(optimizedData)

    // Remove outliers that might cause rendering issues
    optimizedData = this.removeOutliers(optimizedData)

    const result: OptimizedChartData = {
      data: optimizedData,
      originalLength,
      resampledLength: optimizedData.length,
      compressionRatio: originalLength > 0 ? optimizedData.length / originalLength : 1,
      lastUpdated: Date.now(),
      cacheKey
    }

    // Cache the result
    if (this.config.enableCaching) {
      const ttl = getCacheTTL('chartData')
      this.cacheManager.set(cacheKey, result, ttl, ['charts', 'optimized'])
    }

    return result
  }

  /**
   * Resample data using different strategies
   */
  private resampleData(data: ChartDataPoint[], targetPoints: number): ChartDataPoint[] {
    if (data.length <= targetPoints) return data

    switch (this.config.resamplingStrategy) {
      case 'uniform':
        return this.uniformResampling(data, targetPoints)
      case 'adaptive':
        return this.adaptiveResampling(data, targetPoints)
      case 'importance':
        return this.importanceBasedResampling(data, targetPoints)
      default:
        return this.uniformResampling(data, targetPoints)
    }
  }

  /**
   * Uniform resampling - evenly spaced points
   */
  private uniformResampling(data: ChartDataPoint[], targetPoints: number): ChartDataPoint[] {
    const step = data.length / targetPoints
    const result: ChartDataPoint[] = []

    for (let i = 0; i < targetPoints; i++) {
      const index = Math.floor(i * step)
      if (index < data.length) {
        result.push(data[index])
      }
    }

    // Always include the last point
    if (result[result.length - 1] !== data[data.length - 1]) {
      result[result.length - 1] = data[data.length - 1]
    }

    return result
  }

  /**
   * Adaptive resampling - more points where data changes rapidly
   */
  private adaptiveResampling(data: ChartDataPoint[], targetPoints: number): ChartDataPoint[] {
    if (data.length <= targetPoints) return data

    // Calculate rate of change for each point
    const changes = data.map((point, index) => {
      if (index === 0) return 0
      const prevPoint = data[index - 1]
      return Math.abs(point.value - prevPoint.value) / prevPoint.value
    })

    // Create importance scores
    const importance = changes.map((change, index) => ({
      index,
      change,
      importance: change + (index === 0 || index === data.length - 1 ? 1 : 0) // Always include first and last
    }))

    // Sort by importance and take top points
    importance.sort((a, b) => b.importance - a.importance)
    const selectedIndices = importance
      .slice(0, targetPoints)
      .map(item => item.index)
      .sort((a, b) => a - b)

    return selectedIndices.map(index => data[index])
  }

  /**
   * Importance-based resampling - preserve significant points
   */
  private importanceBasedResampling(data: ChartDataPoint[], targetPoints: number): ChartDataPoint[] {
    if (data.length <= targetPoints) return data

    const result: ChartDataPoint[] = []
    const step = data.length / targetPoints

    // Always include first point
    result.push(data[0])

    // Find local extrema and significant changes
    for (let i = 1; i < targetPoints - 1; i++) {
      const startIndex = Math.floor(i * step)
      const endIndex = Math.min(Math.floor((i + 1) * step), data.length - 1)
      
      // Find the most significant point in this range
      let maxChange = 0
      let selectedIndex = startIndex

      for (let j = startIndex; j <= endIndex; j++) {
        if (j > 0 && j < data.length - 1) {
          const change = Math.abs(data[j].value - data[j - 1].value) + 
                        Math.abs(data[j + 1].value - data[j].value)
          if (change > maxChange) {
            maxChange = change
            selectedIndex = j
          }
        }
      }

      result.push(data[selectedIndex])
    }

    // Always include last point
    result.push(data[data.length - 1])

    return result
  }

  /**
   * Apply smoothing filter to reduce noise
   */
  private applySmoothingFilter(data: ChartDataPoint[]): ChartDataPoint[] {
    if (data.length < 3) return data

    const smoothed = [...data]
    const windowSize = Math.min(3, Math.floor(data.length / 10))

    for (let i = windowSize; i < data.length - windowSize; i++) {
      let sum = 0
      for (let j = i - windowSize; j <= i + windowSize; j++) {
        sum += data[j].value
      }
      smoothed[i] = {
        ...data[i],
        value: sum / (2 * windowSize + 1)
      }
    }

    return smoothed
  }

  /**
   * Remove statistical outliers that might cause rendering issues
   */
  private removeOutliers(data: ChartDataPoint[]): ChartDataPoint[] {
    if (data.length < 10) return data

    const values = data.map(d => d.value)
    const q1 = this.percentile(values, 25)
    const q3 = this.percentile(values, 75)
    const iqr = q3 - q1
    const lowerBound = q1 - 1.5 * iqr
    const upperBound = q3 + 1.5 * iqr

    return data.filter(point => 
      point.value >= lowerBound && point.value <= upperBound
    )
  }

  /**
   * Calculate percentile
   */
  private percentile(values: number[], percentile: number): number {
    const sorted = [...values].sort((a, b) => a - b)
    const index = (percentile / 100) * (sorted.length - 1)
    const lower = Math.floor(index)
    const upper = Math.ceil(index)
    
    if (lower === upper) {
      return sorted[lower]
    }
    
    return sorted[lower] * (upper - index) + sorted[upper] * (index - lower)
  }

  /**
   * Check if cached data is still valid
   */
  private isCacheValid(cached: OptimizedChartData, newData: ChartDataPoint[]): boolean {
    // Check if data length changed significantly
    if (Math.abs(cached.originalLength - newData.length) > cached.originalLength * 0.1) {
      return false
    }

    // Check if latest values changed significantly
    if (newData.length > 0 && cached.data.length > 0) {
      const latestNew = newData[newData.length - 1].value
      const latestCached = cached.data[cached.data.length - 1].value
      const change = Math.abs(latestNew - latestCached) / latestCached
      
      if (change > this.config.updateThreshold) {
        return false
      }
    }

    return true
  }

  /**
   * Debounced chart updates to prevent excessive re-renders
   */
  debouncedUpdate(
    key: string,
    updateFunction: () => void,
    delay: number = 100
  ): void {
    // Clear existing timeout
    if (this.updateQueue.has(key)) {
      clearTimeout(this.updateQueue.get(key)!)
    }

    // Set new timeout
    const timeout = setTimeout(() => {
      updateFunction()
      this.updateQueue.delete(key)
    }, delay)

    this.updateQueue.set(key, timeout)
  }

  /**
   * Optimize candlestick data
   */
  optimizeCandlestickData(
    data: CandlestickDataPoint[],
    targetPoints: number = 200
  ): CandlestickDataPoint[] {
    if (data.length <= targetPoints) return data

    const step = data.length / targetPoints
    const result: CandlestickDataPoint[] = []

    for (let i = 0; i < targetPoints; i++) {
      const startIndex = Math.floor(i * step)
      const endIndex = Math.min(Math.floor((i + 1) * step), data.length - 1)
      
      // Aggregate OHLC data for the range
      const rangeData = data.slice(startIndex, endIndex + 1)
      const aggregated: CandlestickDataPoint = {
        date: rangeData[0].date,
        timestamp: rangeData[0].timestamp,
        open: rangeData[0].open,
        close: rangeData[rangeData.length - 1].close,
        high: Math.max(...rangeData.map(d => d.high)),
        low: Math.min(...rangeData.map(d => d.low)),
        volume: rangeData.reduce((sum, d) => sum + d.volume, 0)
      }

      result.push(aggregated)
    }

    return result
  }

  /**
   * Get optimization statistics
   */
  getOptimizationStats(): {
    cacheHitRate: number
    averageCompressionRatio: number
    totalOptimizedCharts: number
  } {
    const stats = this.cacheManager.getStats()
    
    return {
      cacheHitRate: stats.hitRate,
      averageCompressionRatio: 0.5, // Would need to track this
      totalOptimizedCharts: stats.totalEntries
    }
  }

  /**
   * Clear optimization cache
   */
  clearCache(): void {
    this.cacheManager.clearByTags(['charts', 'optimized'])
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<ChartOptimizationConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * Cleanup on destroy
   */
  destroy(): void {
    // Clear all pending updates
    this.updateQueue.forEach(timeout => clearTimeout(timeout))
    this.updateQueue.clear()
  }
}

// Singleton instance
let chartOptimizerInstance: ChartPerformanceOptimizer | null = null

export function getChartOptimizer(): ChartPerformanceOptimizer {
  if (!chartOptimizerInstance) {
    chartOptimizerInstance = new ChartPerformanceOptimizer()
  }
  return chartOptimizerInstance
}

// Utility functions for React components
export function useOptimizedChartData(
  data: ChartDataPoint[],
  chartType: 'line' | 'area' | 'bar',
  symbol?: string,
  period?: string
): OptimizedChartData {
  const optimizer = getChartOptimizer()
  return optimizer.optimizeChartData(data, chartType, symbol, period)
}

// Performance monitoring hook
export function useChartPerformanceMonitor() {
  const optimizer = getChartOptimizer()
  
  return {
    stats: optimizer.getOptimizationStats(),
    clearCache: () => optimizer.clearCache(),
    updateConfig: (config: Partial<ChartOptimizationConfig>) => optimizer.updateConfig(config)
  }
}
