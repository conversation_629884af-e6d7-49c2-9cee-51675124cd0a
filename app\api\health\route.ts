/**
 * System Health Check API
 * GET /api/health
 */

import { NextRequest, NextResponse } from 'next/server'
import { getDataFlowIntegration } from '@/lib/data-flow-integration'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const detailed = searchParams.get('detailed') === 'true'
    const userId = searchParams.get('user_id')

    const dataFlow = getDataFlowIntegration()
    
    // Perform basic health check
    const healthStatus = await dataFlow.performHealthCheck()

    let response: any = {
      status: healthStatus.overall,
      timestamp: healthStatus.timestamp,
      services: healthStatus.services.map(service => ({
        name: service.service,
        status: service.status,
        responseTime: service.responseTime
      }))
    }

    // Add detailed information if requested
    if (detailed) {
      response.detailed = {
        services: healthStatus.services,
        environment: {
          nodeEnv: process.env.NODE_ENV,
          hasSupabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
          hasSupabaseKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
          hasApiKeys: {
            polygon: !!process.env.POLYGON_IO_API_KEY,
            twelveData: !!process.env.TWELVE_DATA_API_KEY,
            alphaVantage: !!process.env.ALPHA_VANTAGE_API_KEY,
            finnhub: !!process.env.FINNHUB_API_KEY
          }
        }
      }
    }

    // Test user data flow if user ID provided
    if (userId) {
      const userFlowTest = await dataFlow.testUserDataFlow(userId)
      response.userFlow = {
        success: userFlowTest.success,
        error: userFlowTest.error,
        responseTime: userFlowTest.data?.responseTime
      }

      // Test data consistency
      const consistencyTest = await dataFlow.validateDataConsistency(userId)
      response.dataConsistency = {
        success: consistencyTest.success,
        consistent: consistencyTest.data?.consistent,
        error: consistencyTest.error
      }
    }

    // Set appropriate status code
    const statusCode = healthStatus.overall === 'healthy' ? 200 : 
                      healthStatus.overall === 'degraded' ? 200 : 503

    return NextResponse.json(response, {
      status: statusCode,
      headers: {
        'Cache-Control': 'no-cache',
        'X-Health-Status': healthStatus.overall,
        'X-Service-Count': healthStatus.services.length.toString()
      }
    })

  } catch (error) {
    console.error('Health check error:', error)
    
    return NextResponse.json(
      {
        status: 'unhealthy',
        error: 'Health check failed',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}
