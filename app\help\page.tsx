"use client"

import { TopNav } from "@/components/top-nav"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { MessageCircle, Book, Mail, Phone } from "lucide-react"

export default function HelpPage() {
  return (
    <div className="min-h-screen bg-slate-900">
      <TopNav />
      <div className="container mx-auto p-4 space-y-6">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white">Help & Support</h1>
            <p className="text-slate-400 mt-2">Get the help you need with investry</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Live Chat Section */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <MessageCircle className="h-5 w-5 text-emerald-400" />
                Live Chat Support
              </CardTitle>
              <CardDescription className="text-slate-400">Get instant help from our support team</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-slate-300 text-sm">
                  Chat with our AI assistant or connect with a human agent for personalized help.
                </p>

                {/* Chatbot Integration Container */}
                <div
                  id="chatbot-container"
                  className="min-h-[400px] bg-slate-900 rounded-lg border border-slate-600 p-4"
                >
                  <div className="flex items-center justify-center h-full text-slate-400">
                    <div className="text-center">
                      <MessageCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>Chatbot integration goes here</p>
                      <p className="text-sm mt-2">This container is ready for your chatbot widget</p>
                    </div>
                  </div>
                </div>

                <Button
                  className="w-full bg-emerald-500 hover:bg-emerald-600"
                  onClick={() => {
                    // This is where you would initialize your chatbot
                    console.log("Initialize chatbot here")
                  }}
                >
                  Start Chat
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* FAQ and Resources */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Book className="h-5 w-5 text-blue-400" />
                Resources
              </CardTitle>
              <CardDescription className="text-slate-400">Find answers to common questions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-3">
                  <h3 className="text-white font-semibold">Quick Links</h3>
                  <div className="space-y-2">
                    <Button variant="ghost" className="w-full justify-start text-slate-300 hover:text-white">
                      Getting Started Guide
                    </Button>
                    <Button variant="ghost" className="w-full justify-start text-slate-300 hover:text-white">
                      Investment Basics
                    </Button>
                    <Button variant="ghost" className="w-full justify-start text-slate-300 hover:text-white">
                      Account Settings
                    </Button>
                    <Button variant="ghost" className="w-full justify-start text-slate-300 hover:text-white">
                      Security & Privacy
                    </Button>
                  </div>
                </div>

                <div className="border-t border-slate-600 pt-4">
                  <h3 className="text-white font-semibold mb-3">Contact Us</h3>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-slate-300">
                      <Mail className="h-4 w-4" />
                      <span className="text-sm"><EMAIL></span>
                    </div>
                    <div className="flex items-center gap-2 text-slate-300">
                      <Phone className="h-4 w-4" />
                      <span className="text-sm"></span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Integration Instructions */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white">Chatbot Integration Instructions</CardTitle>
            <CardDescription className="text-slate-400">How to integrate your chatbot widget</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 text-slate-300">
              <div>
                <h4 className="font-semibold text-white mb-2">Option 1: Widget Integration</h4>
                <p className="text-sm mb-2">
                  Replace the placeholder in the #chatbot-container div with your chatbot widget:
                </p>
                <pre className="bg-slate-900 p-3 rounded text-xs overflow-x-auto">
                  {`<!-- Replace the placeholder content with your chatbot widget -->
<div id="chatbot-container">
  <!-- Your chatbot widget code here -->
  <script src="your-chatbot-widget.js"></script>
</div>`}
                </pre>
              </div>

              <div>
                <h4 className="font-semibold text-white mb-2">Option 2: API Integration</h4>
                <p className="text-sm mb-2">Use the webhook endpoint for custom chat implementations:</p>
                <pre className="bg-slate-900 p-3 rounded text-xs overflow-x-auto">
                  {`POST /api/chat/webhook
Content-Type: application/json

{
  "message": "User message here",
  "userId": "user-id",
  "sessionId": "session-id"
}`}
                </pre>
              </div>

              <div>
                <h4 className="font-semibold text-white mb-2">Popular Chatbot Services</h4>
                <ul className="text-sm space-y-1 list-disc list-inside">
                  <li>Intercom - Customer messaging platform</li>
                  <li>Zendesk Chat - Live chat and chatbot</li>
                  <li>Drift - Conversational marketing platform</li>
                  <li>Crisp - Customer messaging platform</li>
                  <li>Custom OpenAI/ChatGPT integration</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
