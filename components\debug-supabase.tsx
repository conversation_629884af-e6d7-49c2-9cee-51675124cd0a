"use client"

import { isSupabaseConfigured, createClient } from '@/lib/supabase'
import { useAuth } from '@/components/auth-provider'

export function DebugSupabase() {
  const { isConfigured } = useAuth()
  
  const checkConfig = () => {
    console.log('=== Supabase Debug Info ===')
    console.log('Environment variables:')
    console.log('NEXT_PUBLIC_SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL)
    console.log('NEXT_PUBLIC_SUPABASE_ANON_KEY:', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set' : 'Not set')

    // Check if we can access the variables directly
    console.log('Direct access test:')
    console.log('URL length:', process.env.NEXT_PUBLIC_SUPABASE_URL?.length)
    console.log('Key length:', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.length)

    try {
      const configured = isSupabaseConfigured()
      console.log('isSupabaseConfigured():', configured)

      if (configured) {
        const client = createClient()
        console.log('createClient() success:', !!client)
      }
    } catch (error) {
      console.error('Configuration error:', error)
    }

    console.log('Auth provider isConfigured:', isConfigured)
    console.log('=== End Debug Info ===')
  }

  return (
    <div className="fixed bottom-4 right-4 bg-red-600 text-white p-4 rounded z-50">
      <div className="text-sm mb-2">
        <div>Supabase Configured: {isConfigured ? '✅' : '❌'}</div>
        <div>URL: {process.env.NEXT_PUBLIC_SUPABASE_URL ? '✅' : '❌'}</div>
        <div>Key: {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '✅' : '❌'}</div>
      </div>
      <button 
        onClick={checkConfig}
        className="bg-red-700 hover:bg-red-800 px-2 py-1 rounded text-xs"
      >
        Debug Console
      </button>
    </div>
  )
}
