"use client"

import { useAuth } from "@/components/auth-provider"
import { useEffect, useState } from "react"
import { createClient } from "@/lib/supabase"

export default function DebugAuthPage() {
  const { user, loading, isConfigured } = useAuth()
  const [sessionInfo, setSessionInfo] = useState<any>(null)
  const [clientInfo, setClientInfo] = useState<any>(null)

  useEffect(() => {
    const checkSession = async () => {
      try {
        if (isConfigured) {
          const supabase = createClient()
          const { data: { session }, error } = await supabase.auth.getSession()
          setSessionInfo({ session: session?.user, error })
          
          const { data: { user: currentUser }, error: userError } = await supabase.auth.getUser()
          setClientInfo({ user: currentUser, error: userError })
        }
      } catch (error) {
        console.error("Debug session check error:", error)
        setSessionInfo({ error: error.message })
      }
    }

    checkSession()
  }, [isConfigured])

  return (
    <div className="min-h-screen bg-slate-900 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8">Auth Debug Page</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Auth Provider State */}
          <div className="bg-slate-800 p-6 rounded-lg">
            <h2 className="text-xl font-semibold text-white mb-4">Auth Provider State</h2>
            <div className="space-y-2 text-sm">
              <div className="text-slate-300">
                <strong>Loading:</strong> {loading ? "true" : "false"}
              </div>
              <div className="text-slate-300">
                <strong>Configured:</strong> {isConfigured ? "true" : "false"}
              </div>
              <div className="text-slate-300">
                <strong>User:</strong> {user ? user.email : "null"}
              </div>
              <div className="text-slate-300">
                <strong>User ID:</strong> {user ? user.id : "null"}
              </div>
            </div>
          </div>

          {/* Direct Session Check */}
          <div className="bg-slate-800 p-6 rounded-lg">
            <h2 className="text-xl font-semibold text-white mb-4">Direct Session Check</h2>
            <div className="space-y-2 text-sm">
              <div className="text-slate-300">
                <strong>Session User:</strong> {sessionInfo?.session?.email || "null"}
              </div>
              <div className="text-slate-300">
                <strong>Session Error:</strong> {sessionInfo?.error?.message || "none"}
              </div>
              <div className="text-slate-300">
                <strong>Current User:</strong> {clientInfo?.user?.email || "null"}
              </div>
              <div className="text-slate-300">
                <strong>User Error:</strong> {clientInfo?.error?.message || "none"}
              </div>
            </div>
          </div>

          {/* Environment Info */}
          <div className="bg-slate-800 p-6 rounded-lg">
            <h2 className="text-xl font-semibold text-white mb-4">Environment Info</h2>
            <div className="space-y-2 text-sm">
              <div className="text-slate-300">
                <strong>Supabase URL:</strong> {process.env.NEXT_PUBLIC_SUPABASE_URL ? "Set" : "Not set"}
              </div>
              <div className="text-slate-300">
                <strong>Supabase Key:</strong> {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? "Set" : "Not set"}
              </div>
            </div>
          </div>

          {/* Local Storage Info */}
          <div className="bg-slate-800 p-6 rounded-lg">
            <h2 className="text-xl font-semibold text-white mb-4">Local Storage</h2>
            <div className="space-y-2 text-sm max-h-40 overflow-y-auto">
              {typeof window !== 'undefined' && Object.keys(localStorage)
                .filter(key => key.includes('supabase') || key.includes('sb-') || key.includes('investry'))
                .map(key => (
                  <div key={key} className="text-slate-300">
                    <strong>{key}:</strong> {localStorage.getItem(key)?.substring(0, 50)}...
                  </div>
                ))}
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="mt-8 space-x-4">
          <button
            onClick={() => window.location.href = '/auth/signin'}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
          >
            Go to Sign In
          </button>
          <button
            onClick={() => window.location.href = '/dashboard'}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded"
          >
            Go to Dashboard
          </button>
          <button
            onClick={() => window.location.reload()}
            className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded"
          >
            Reload Page
          </button>
        </div>
      </div>
    </div>
  )
}
