/**
 * Response Filtering Service for LLM Portfolio System
 * SECURITY: Prevents sensitive data leakage in LLM responses
 */

import type { PersonalizedPortfolio } from '@/lib/portfolio-generator'

export interface FilteringResult {
  filteredContent: string
  originalContent: string
  modificationsApplied: string[]
  riskLevel: 'low' | 'medium' | 'high'
  containsSensitiveData: boolean
}

export class ResponseFilter {

  /**
   * SECURITY: Filter LLM response to remove any sensitive data
   */
  static filterLLMResponse(response: string): FilteringResult {
    if (!response || typeof response !== 'string') {
      return {
        filteredContent: '',
        originalContent: response || '',
        modificationsApplied: ['Empty response handled'],
        riskLevel: 'low',
        containsSensitiveData: false
      }
    }

    const originalContent = response
    let filteredContent = response
    const modificationsApplied: string[] = []
    let containsSensitiveData = false

    // 1. Remove potential PII that might have been echoed
    const piiResult = this.removePIIFromResponse(filteredContent)
    if (piiResult.modified) {
      filteredContent = piiResult.content
      modificationsApplied.push('PII removed')
      containsSensitiveData = true
    }

    // 2. Remove specific user identifiers
    const identifierResult = this.removeUserIdentifiers(filteredContent)
    if (identifierResult.modified) {
      filteredContent = identifierResult.content
      modificationsApplied.push('User identifiers removed')
      containsSensitiveData = true
    }

    // 3. Remove exact financial amounts
    const financialResult = this.removeExactFinancialAmounts(filteredContent)
    if (financialResult.modified) {
      filteredContent = financialResult.content
      modificationsApplied.push('Exact amounts generalized')
    }

    // 4. Remove inappropriate content
    const contentResult = this.removeInappropriateContent(filteredContent)
    if (contentResult.modified) {
      filteredContent = contentResult.content
      modificationsApplied.push('Inappropriate content removed')
    }

    // 5. Validate portfolio JSON structure
    const portfolioResult = this.validatePortfolioResponse(filteredContent)
    if (portfolioResult.modified) {
      filteredContent = portfolioResult.content
      modificationsApplied.push('Portfolio structure validated')
    }

    // Determine risk level
    const riskLevel = this.assessRiskLevel(originalContent, containsSensitiveData, modificationsApplied)

    return {
      filteredContent,
      originalContent,
      modificationsApplied,
      riskLevel,
      containsSensitiveData
    }
  }

  /**
   * SECURITY: Remove PII from response
   */
  private static removePIIFromResponse(content: string): { content: string; modified: boolean } {
    const original = content
    
    let filtered = content
      // Email addresses
      .replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '[EMAIL_FILTERED]')
      // Phone numbers
      .replace(/\b(?:\+?1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}\b/g, '[PHONE_FILTERED]')
      // SSN patterns
      .replace(/\b\d{3}-\d{2}-\d{4}\b/g, '[SSN_FILTERED]')
      // Account numbers
      .replace(/\b\d{8,}\b/g, '[ACCOUNT_FILTERED]')
      // Names that might have been echoed
      .replace(/\b(user|client|investor)\s+[A-Z][a-z]+\s+[A-Z][a-z]+\b/g, '[NAME_FILTERED]')

    return {
      content: filtered,
      modified: original !== filtered
    }
  }

  /**
   * SECURITY: Remove user identifiers
   */
  private static removeUserIdentifiers(content: string): { content: string; modified: boolean } {
    const original = content
    
    let filtered = content
      // User IDs (various formats)
      .replace(/\b(user[-_]?id|userid|user[-_]?identifier):\s*[A-Za-z0-9-_]+\b/gi, '[USER_ID_FILTERED]')
      // UUID patterns
      .replace(/\b[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\b/gi, '[UUID_FILTERED]')
      // Session tokens
      .replace(/\b(token|session|auth):\s*[A-Za-z0-9+/=]{20,}\b/gi, '[TOKEN_FILTERED]')

    return {
      content: filtered,
      modified: original !== filtered
    }
  }

  /**
   * SECURITY: Remove exact financial amounts and replace with ranges
   */
  private static removeExactFinancialAmounts(content: string): { content: string; modified: boolean } {
    const original = content
    
    let filtered = content
      // Exact dollar amounts over $10,000
      .replace(/\$([1-9]\d{4,}|\d{6,})/g, (match, amount) => {
        const num = parseInt(amount)
        if (num >= 100000) return '$100,000+'
        if (num >= 50000) return '$50,000-$100,000'
        if (num >= 25000) return '$25,000-$50,000'
        return '$10,000-$25,000'
      })
      // Monthly amounts over $5,000
      .replace(/\$([1-9]\d{3,})\/month/g, (match, amount) => {
        const num = parseInt(amount)
        if (num >= 10000) return '$10,000+/month'
        if (num >= 5000) return '$5,000-$10,000/month'
        return match // Keep smaller amounts
      })

    return {
      content: filtered,
      modified: original !== filtered
    }
  }

  /**
   * SECURITY: Remove inappropriate content
   */
  private static removeInappropriateContent(content: string): { content: string; modified: boolean } {
    const original = content
    
    let filtered = content
      // Remove any attempts to provide non-financial advice
      .replace(/\b(i am not|this is not)\s+(financial|investment)\s+advice\b/gi, '')
      // Remove disclaimers that might confuse users
      .replace(/\b(disclaimer|warning):\s*[^.!?]*[.!?]/gi, '')
      // Remove references to the AI system itself
      .replace(/\b(as an? ai|i am an? ai|ai (model|system|assistant))\b/gi, '[AI_REFERENCE_REMOVED]')

    return {
      content: filtered,
      modified: original !== filtered
    }
  }

  /**
   * SECURITY: Validate portfolio JSON structure
   */
  private static validatePortfolioResponse(content: string): { content: string; modified: boolean } {
    try {
      // Try to extract JSON from the response
      const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/)
      if (!jsonMatch) {
        return { content, modified: false }
      }

      const jsonStr = jsonMatch[1]
      const portfolio = JSON.parse(jsonStr)

      // Validate portfolio structure
      if (!this.isValidPortfolioStructure(portfolio)) {
        // If invalid, remove the JSON block
        const filtered = content.replace(/```json\s*[\s\S]*?\s*```/, '[INVALID_PORTFOLIO_REMOVED]')
        return { content: filtered, modified: true }
      }

      // Sanitize portfolio data
      const sanitizedPortfolio = this.sanitizePortfolioData(portfolio)
      const sanitizedJson = JSON.stringify(sanitizedPortfolio, null, 2)
      const filtered = content.replace(jsonMatch[0], `\`\`\`json\n${sanitizedJson}\n\`\`\``)

      return {
        content: filtered,
        modified: JSON.stringify(portfolio) !== JSON.stringify(sanitizedPortfolio)
      }

    } catch (error) {
      // If JSON parsing fails, remove the malformed JSON
      const filtered = content.replace(/```json\s*[\s\S]*?\s*```/, '[MALFORMED_JSON_REMOVED]')
      return { content: filtered, modified: true }
    }
  }

  /**
   * SECURITY: Validate portfolio structure
   */
  private static isValidPortfolioStructure(portfolio: any): boolean {
    if (!portfolio || typeof portfolio !== 'object') return false

    const required = ['allocations', 'riskLevel', 'expectedReturn', 'strategy', 'rationale']
    for (const field of required) {
      if (!portfolio[field]) return false
    }

    if (!Array.isArray(portfolio.allocations)) return false

    // Validate allocations
    for (const alloc of portfolio.allocations) {
      if (!alloc.symbol || !alloc.name || typeof alloc.allocation !== 'number' ||
          !alloc.category || !alloc.rationale) {
        return false
      }
    }

    // Check allocations sum to approximately 100%
    const total = portfolio.allocations.reduce((sum: number, alloc: any) => sum + alloc.allocation, 0)
    if (Math.abs(total - 100) > 2) return false // Allow 2% tolerance

    return true
  }

  /**
   * SECURITY: Sanitize portfolio data
   */
  private static sanitizePortfolioData(portfolio: any): any {
    const sanitized = { ...portfolio }

    // Sanitize allocations
    if (Array.isArray(sanitized.allocations)) {
      sanitized.allocations = sanitized.allocations.map((alloc: any) => ({
        ...alloc,
        symbol: this.sanitizeStockSymbol(alloc.symbol),
        name: this.sanitizeCompanyName(alloc.name),
        rationale: this.sanitizeRationale(alloc.rationale)
      }))
    }

    // Sanitize text fields
    if (sanitized.strategy) {
      sanitized.strategy = this.sanitizeRationale(sanitized.strategy)
    }
    if (sanitized.rationale) {
      sanitized.rationale = this.sanitizeRationale(sanitized.rationale)
    }

    return sanitized
  }

  /**
   * SECURITY: Sanitize stock symbol
   */
  private static sanitizeStockSymbol(symbol: string): string {
    if (!symbol || typeof symbol !== 'string') return 'INVALID'
    
    // Only allow valid stock symbol characters
    const cleaned = symbol.toUpperCase().replace(/[^A-Z0-9.-]/g, '')
    
    // Validate length (most symbols are 1-5 characters)
    if (cleaned.length < 1 || cleaned.length > 10) return 'INVALID'
    
    return cleaned
  }

  /**
   * SECURITY: Sanitize company name
   */
  private static sanitizeCompanyName(name: string): string {
    if (!name || typeof name !== 'string') return 'Unknown Company'
    
    // Remove potential HTML/script content
    let sanitized = name.replace(/<[^>]*>/g, '')
    
    // Limit length
    if (sanitized.length > 100) {
      sanitized = sanitized.substring(0, 100) + '...'
    }
    
    return sanitized
  }

  /**
   * SECURITY: Sanitize rationale text
   */
  private static sanitizeRationale(rationale: string): string {
    if (!rationale || typeof rationale !== 'string') return 'Standard investment rationale'
    
    let sanitized = rationale
      // Remove HTML/script content
      .replace(/<[^>]*>/g, '')
      // Remove potential prompt injection
      .replace(/\b(ignore|forget|disregard)\s+(previous|above|all)\s+(instructions?|prompts?)\b/gi, '[FILTERED]')
      // Remove excessive special characters
      .replace(/[!@#$%^&*()_+=\[\]{}|;':",./<>?`~]{5,}/g, ' ')
    
    // Limit length
    if (sanitized.length > 500) {
      sanitized = sanitized.substring(0, 500) + '...'
    }
    
    return sanitized.trim()
  }

  /**
   * SECURITY: Assess risk level of response
   */
  private static assessRiskLevel(
    originalContent: string, 
    containsSensitiveData: boolean, 
    modifications: string[]
  ): 'low' | 'medium' | 'high' {
    if (containsSensitiveData) return 'high'
    if (modifications.length > 2) return 'medium'
    if (modifications.length > 0) return 'medium'
    return 'low'
  }

  /**
   * SECURITY: Filter portfolio object directly
   */
  static filterPortfolioObject(portfolio: PersonalizedPortfolio): PersonalizedPortfolio {
    const filtered = { ...portfolio }

    // Sanitize all text fields
    filtered.strategy = this.sanitizeRationale(filtered.strategy)
    filtered.rationale = this.sanitizeRationale(filtered.rationale)

    // Sanitize allocations
    filtered.allocations = filtered.allocations.map(alloc => ({
      ...alloc,
      symbol: this.sanitizeStockSymbol(alloc.symbol),
      name: this.sanitizeCompanyName(alloc.name),
      rationale: this.sanitizeRationale(alloc.rationale)
    }))

    return filtered
  }

  /**
   * SECURITY: Check if response contains sensitive patterns
   */
  static containsSensitiveData(content: string): boolean {
    if (!content) return false

    const sensitivePatterns = [
      // PII patterns
      /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/,
      /\b\d{3}-\d{2}-\d{4}\b/,
      /\b\d{8,}\b/,
      // User identifiers
      /\b[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\b/i,
      // Large exact amounts
      /\$[1-9]\d{4,}/
    ]

    return sensitivePatterns.some(pattern => pattern.test(content))
  }
}
