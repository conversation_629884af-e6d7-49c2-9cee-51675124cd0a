"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"


import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { TopNav } from "@/components/top-nav"
import { DollarSign, Wallet, History, Plus, Zap, Building2, Shield } from "lucide-react"
import { useNotifications } from "@/components/notification-system"
import { useAuth } from "@/components/auth-provider"
import { PlaidLink, PlaidLinkErrorBoundary } from "@/components/plaid-link"

export default function PaymentsPage() {
  const router = useRouter()
  const { addNotification } = useNotifications()
  const { user, loading: authLoading } = useAuth()
  const [loading, setLoading] = useState(true)
  const [userBalance, setUserBalance] = useState(0)
  const [recentPayments, setRecentPayments] = useState<any[]>([])
  const [linkedAccounts, setLinkedAccounts] = useState<any[]>([])
  const [showLinkAccount, setShowLinkAccount] = useState(false)
  const [depositAmount, setDepositAmount] = useState('')
  const [isDepositing, setIsDepositing] = useState(false)

  useEffect(() => {
    // Wait for auth to load
    if (authLoading) return

    // Check if user is authenticated
    if (!user) {
      router.push("/auth/signin")
      return
    }

    fetchUserData()
  }, [user, authLoading, router])

  const fetchUserData = async () => {
    try {
      // Fetch user balance
      const balanceResponse = await fetch(`/api/user-balance?user_id=${user?.id}`)
      if (balanceResponse.ok) {
        const balanceData = await balanceResponse.json()
        setUserBalance(balanceData.balance)
      }

      // Fetch linked accounts
      const accountsResponse = await fetch(`/api/linked-accounts?user_id=${user?.id}`)
      if (accountsResponse.ok) {
        const accountsData = await accountsResponse.json()
        setLinkedAccounts(accountsData.accounts || [])
      }

      // Fetch recent transactions/roundups
      const transactionsResponse = await fetch(`/api/roundup-history?user_id=${user?.id}&limit=10`)
      if (transactionsResponse.ok) {
        const transactionsData = await transactionsResponse.json()
        setRecentPayments(transactionsData.transactions || [])
      }

    } catch (error) {
      console.error('Error fetching user data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handlePlaidSuccess = (accounts: any[]) => {
    setLinkedAccounts(accounts)
    fetchUserData() // Refresh all data
  }

  const handlePlaidError = (error: any) => {
    console.error('Plaid linking error:', error)
    setShowLinkAccount(false)
  }

  const handleDeposit = async () => {
    if (!depositAmount || parseFloat(depositAmount) <= 0) {
      addNotification({
        type: "error",
        title: "Invalid Amount",
        message: "Please enter a valid deposit amount.",
      })
      return
    }

    setIsDepositing(true)
    try {
      // For demo purposes, we'll simulate a successful deposit
      // In production, this would integrate with ACH processing
      const amount = parseFloat(depositAmount)

      // Update user balance via API
      const response = await fetch('/api/user-balance/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user?.id,
          amount: amount,
          type: 'deposit',
          description: 'Manual deposit'
        }),
      })

      if (response.ok) {
        setUserBalance(prev => prev + amount)
        setDepositAmount('')
        addNotification({
          type: "success",
          title: "Deposit Successful!",
          message: `$${amount.toFixed(2)} has been added to your account.`,
        })

        // Check if user came from onboarding
        const urlParams = new URLSearchParams(window.location.search)
        if (urlParams.get('from') === 'onboarding') {
          // Redirect back to onboarding to continue with survey
          setTimeout(() => {
            router.push('/onboarding')
          }, 2000)
        }
      } else {
        throw new Error('Failed to process deposit')
      }
    } catch (error) {
      console.error('Deposit error:', error)
      addNotification({
        type: "error",
        title: "Deposit Failed",
        message: "Failed to process your deposit. Please try again.",
      })
    } finally {
      setIsDepositing(false)
    }
  }

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-white">Loading...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-900 text-white">
      <TopNav />

      <div className="container mx-auto px-4 py-8 space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Account Funding</h1>
            <p className="text-slate-400 mt-1">Add funds to start investing</p>
          </div>
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              onClick={() => router.push("/payments/manage")}
              className="border-slate-600 text-slate-300 hover:bg-slate-700"
            >
              <History className="h-4 w-4 mr-2" />
              Manage Payments
            </Button>
            <div className="text-right">
              <p className="text-sm text-slate-400">Current Balance</p>
              <p className="text-2xl font-bold text-emerald-400">${userBalance.toFixed(2)}</p>
            </div>
          </div>
        </div>

        <Tabs defaultValue="deposit" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 bg-slate-800">
            <TabsTrigger value="deposit" className="data-[state=active]:bg-slate-700">
              <DollarSign className="h-4 w-4 mr-2" />
              Add Funds
            </TabsTrigger>
            <TabsTrigger value="link-account" className="data-[state=active]:bg-slate-700">
              <Building2 className="h-4 w-4 mr-2" />
              Link Bank Account
            </TabsTrigger>
            <TabsTrigger value="history" className="data-[state=active]:bg-slate-700">
              <History className="h-4 w-4 mr-2" />
              Transaction History
            </TabsTrigger>
          </TabsList>

          <TabsContent value="deposit" className="space-y-6">
            {/* Manual Deposit */}
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5 text-emerald-400" />
                  Add Funds to Your Account
                </CardTitle>
                <CardDescription>
                  Deposit money to start investing. For demo purposes, this simulates a bank transfer.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="bg-slate-700/50 p-4 rounded-lg border border-slate-600">
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-slate-300">Current Balance:</span>
                    <span className="text-2xl font-bold text-emerald-400">${userBalance.toFixed(2)}</span>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="deposit-amount" className="text-slate-300">
                        Deposit Amount
                      </Label>
                      <div className="relative mt-1">
                        <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400">$</span>
                        <Input
                          id="deposit-amount"
                          type="number"
                          placeholder="0.00"
                          value={depositAmount}
                          onChange={(e) => setDepositAmount(e.target.value)}
                          className="pl-8 bg-slate-700 border-slate-600 text-white"
                          min="0"
                          step="0.01"
                        />
                      </div>
                    </div>

                    <div className="flex gap-2">
                      {[50, 100, 500, 1000].map((amount) => (
                        <Button
                          key={amount}
                          variant="outline"
                          size="sm"
                          onClick={() => setDepositAmount(amount.toString())}
                          className="border-slate-600 text-slate-300 hover:bg-slate-700"
                        >
                          ${amount}
                        </Button>
                      ))}
                    </div>

                    <Button
                      onClick={handleDeposit}
                      disabled={isDepositing || !depositAmount || parseFloat(depositAmount) <= 0}
                      className="w-full bg-emerald-600 hover:bg-emerald-700 text-white"
                    >
                      {isDepositing ? 'Processing...' : 'Add Funds'}
                    </Button>
                  </div>
                </div>

                <div className="bg-blue-900/20 border border-blue-700/50 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <Shield className="h-5 w-5 text-blue-400 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-blue-300 mb-1">Demo Mode</h4>
                      <p className="text-sm text-blue-200">
                        This is a simulated deposit for demonstration purposes. In production,
                        this would connect to your linked bank account for secure ACH transfers.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="link-account" className="space-y-6">
            {/* Bank Account Linking */}
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="h-5 w-5 text-emerald-400" />
                  Connect Your Bank Account
                </CardTitle>
                <CardDescription>Link your bank account to enable automatic round-ups and funding</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {linkedAccounts.length === 0 ? (
                  <div className="text-center py-8">
                    <Building2 className="h-16 w-16 text-slate-600 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-white mb-2">No Bank Account Connected</h3>
                    <p className="text-slate-400 mb-6 max-w-md mx-auto">
                      Connect your bank account to enable automatic round-ups on your purchases.
                      Every purchase will be rounded up to the nearest dollar and invested automatically.
                    </p>
                    <PlaidLinkErrorBoundary>
                      <PlaidLink
                        userId={user?.id || ''}
                        onSuccess={handlePlaidSuccess}
                        onError={handlePlaidError}
                      />
                    </PlaidLinkErrorBoundary>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <h4 className="font-medium text-white">Connected Accounts</h4>
                    {linkedAccounts.map((account) => (
                      <div key={account.id} className="flex items-center justify-between p-4 bg-slate-700/50 rounded-lg border border-slate-600">
                        <div className="flex items-center gap-3">
                          <Building2 className="h-5 w-5 text-emerald-400" />
                          <div>
                            <p className="font-medium text-white">{account.name}</p>
                            <p className="text-sm text-slate-400">{account.mask} • {account.type}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Shield className="h-4 w-4 text-emerald-400" />
                          <span className="text-sm text-emerald-400">Connected</span>
                        </div>
                      </div>
                    ))}
                    <PlaidLinkErrorBoundary>
                      <PlaidLink
                        userId={user?.id || ''}
                        onSuccess={handlePlaidSuccess}
                        onError={handlePlaidError}
                      >
                        <Button
                          variant="outline"
                          className="w-full border-slate-600 text-slate-300 hover:bg-slate-700"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add Another Account
                        </Button>
                      </PlaidLink>
                    </PlaidLinkErrorBoundary>
                  </div>
                )}

                <div className="flex items-start gap-2 text-sm text-slate-400 mt-4 p-4 bg-slate-700/50 rounded-lg">
                  <Shield className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium mb-1">Security & Privacy</p>
                    <ul className="space-y-1 text-xs">
                      <li>• Bank-level 256-bit encryption</li>
                      <li>• We never store your banking credentials</li>
                      <li>• Read-only access to transaction data</li>
                      <li>• Powered by Plaid for secure connections</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Round-ups Information */}
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5 text-emerald-400" />
                  How Round-ups Work
                </CardTitle>
                <CardDescription>Automatically invest your spare change</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="w-12 h-12 bg-emerald-500/20 rounded-full flex items-center justify-center mx-auto mb-3">
                      <DollarSign className="h-6 w-6 text-emerald-400" />
                    </div>
                    <h4 className="font-medium text-white mb-2">Make a Purchase</h4>
                    <p className="text-sm text-slate-400">Buy coffee for $4.35</p>
                  </div>
                  <div className="text-center">
                    <div className="w-12 h-12 bg-emerald-500/20 rounded-full flex items-center justify-center mx-auto mb-3">
                      <Zap className="h-6 w-6 text-emerald-400" />
                    </div>
                    <h4 className="font-medium text-white mb-2">Round Up</h4>
                    <p className="text-sm text-slate-400">We round up $0.65</p>
                  </div>
                  <div className="text-center">
                    <div className="w-12 h-12 bg-emerald-500/20 rounded-full flex items-center justify-center mx-auto mb-3">
                      <Wallet className="h-6 w-6 text-emerald-400" />
                    </div>
                    <h4 className="font-medium text-white mb-2">Invest</h4>
                    <p className="text-sm text-slate-400">$0.65 goes to your portfolio</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history" className="space-y-6">
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <History className="h-5 w-5 text-emerald-400" />
                  Recent Payments
                </CardTitle>
                <CardDescription>Your payment history and transaction details</CardDescription>
              </CardHeader>
              <CardContent>
                {recentPayments.length === 0 ? (
                  <div className="text-center py-8">
                    <Wallet className="h-12 w-12 text-slate-600 mx-auto mb-4" />
                    <p className="text-slate-400">No payment history yet</p>
                    <p className="text-sm text-slate-500 mt-1">Your transactions will appear here after you make your first payment</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {/* Payment history will be populated here */}
                    <p className="text-slate-400">Payment history coming soon...</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}