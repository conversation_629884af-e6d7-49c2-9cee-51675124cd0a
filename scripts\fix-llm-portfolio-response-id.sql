-- Fix llm_generated_portfolios table to make response_id nullable
-- This allows portfolios to be saved without requiring an LLM response record
-- (e.g., when user accepts a previously generated portfolio)

-- Make response_id nullable
ALTER TABLE llm_generated_portfolios 
ALTER COLUMN response_id DROP NOT NULL;

-- Add comment to clarify the change
COMMENT ON COLUMN llm_generated_portfolios.response_id IS 'Optional reference to LLM response. NULL when portfolio is accepted from cache or manual creation.';
