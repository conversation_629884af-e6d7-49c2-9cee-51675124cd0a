/**
 * Stock Quote API Route
 * GET /api/stocks/quote?symbol=AAPL
 * GET /api/stocks/quote?symbols=AAPL,MSFT,GOOGL (batch)
 */

import { NextRequest, NextResponse } from 'next/server'
import { getStockDataService, isApiConfigured } from '@/lib/stock-config'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const symbol = searchParams.get('symbol')
    const symbols = searchParams.get('symbols')

    // Validate input
    if (!symbol && !symbols) {
      return NextResponse.json(
        { error: 'Missing required parameter: symbol or symbols' },
        { status: 400 }
      )
    }

    const stockService = getStockDataService()

    // Handle single symbol request
    if (symbol) {
      const quote = await stockService.getStockQuote(symbol.toUpperCase())

      if (!quote) {
        return NextResponse.json(
          { error: 'Stock not found' },
          { status: 404 }
        )
      }

      return NextResponse.json({
        data: quote,
        source: quote.source,
        timestamp: new Date().toISOString()
      })
    }

    // Handle batch symbols request
    if (symbols) {
      const symbolList = symbols.split(',').map(s => s.trim().toUpperCase())
      
      if (symbolList.length > 20) {
        return NextResponse.json(
          { error: 'Too many symbols. Maximum 20 allowed.' },
          { status: 400 }
        )
      }

      const quotes = await stockService.getMultipleQuotes(symbolList)

      // Filter out null results
      const results = quotes.filter(Boolean)

      return NextResponse.json({
        data: results,
        count: results.length,
        timestamp: new Date().toISOString()
      })
    }

  } catch (error) {
    console.error('Stock quote API error:', error)
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
