/**
 * LLMPortfolioEngine - Main orchestrator for LLM-powered portfolio generation
 */

import type {
  ILLMPortfolioEngine,
  LLMSystemConfig,
  PromptData,
  LLMPortfolioResult,
  LLMRequest
} from './types'
import { AUDIT_ACTIONS, ValidationError } from './types'
import type { PersonalizedPortfolio } from '@/lib/portfolio-generator'
import { PromptTemplateBuilder } from './prompt-template-builder'
import { PortfolioCache } from './portfolio-cache'
import { PortfolioFallback } from './portfolio-fallback'
import { SupabaseLogger } from './supabase-logger'
import { LLMClient } from './llm-client'
import { InputSanitizer } from './input-sanitizer'
import { ResponseFilter } from './response-filter'

export class LLMPortfolioEngine implements ILLMPortfolioEngine {
  private promptBuilder: PromptTemplateBuilder
  private cache: PortfolioCache
  private fallback: PortfolioFallback
  private logger: SupabaseLogger
  private llmClient: LLMClient
  private config: LLMSystemConfig

  constructor(config: LLMSystemConfig) {
    this.config = config
    this.promptBuilder = new PromptTemplateBuilder()
    this.cache = new PortfolioCache(config.cache)
    this.fallback = new PortfolioFallback(config.enableFallback)
    this.logger = new SupabaseLogger()
    this.llmClient = new LLMClient(config.llm)
  }

  /**
   * Generate a personalized portfolio using LLM with caching and fallback
   */
  async generatePortfolio(data: PromptData): Promise<LLMPortfolioResult> {
    const startTime = Date.now()
    
    try {
      // SECURITY: Sanitize input data before processing
      const sanitizedData = await this.sanitizePromptData(data)

      // Build the prompt with sanitized data
      const rawPrompt = await this.promptBuilder.buildPortfolioPrompt(sanitizedData)

      // SECURITY: Additional prompt sanitization
      const prompt = InputSanitizer.sanitizeUserInput(rawPrompt, {
        maxLength: 10000,
        strictMode: true
      })

      // Log sanitization if changes were made
      if (rawPrompt !== prompt) {
        await this.logger.logPromptSanitization(
          data.userId,
          rawPrompt,
          prompt,
          ['Prompt sanitization applied'],
          'medium'
        )
      }

      const promptHash = this.promptBuilder.generatePromptHash(prompt)
      const cacheKey = PortfolioCache.generateCacheKey(data.userId, promptHash)

      // Check cache first
      const cachedResult = await this.checkCache(cacheKey, data.userId)
      if (cachedResult) {
        return cachedResult
      }

      // Create LLM request
      const llmRequest: LLMRequest = {
        prompt,
        promptHash,
        userId: data.userId,
        promptData: data,
        metadata: {
          timestamp: new Date().toISOString(),
          source: 'portfolio_generation'
        }
      }

      // Log the prompt
      const promptId = await this.logger.logPrompt(llmRequest)

      try {
        // Call LLM API
        const llmResponse = await this.llmClient.generatePortfolio({
          ...llmRequest,
          promptHash: promptId // Use the database ID as prompt ID
        })

        // Log the response
        await this.logger.logResponse(llmResponse)

        // SECURITY: Filter LLM response before parsing
        const filterResult = ResponseFilter.filterLLMResponse(llmResponse.responseText)

        // Log response filtering if modifications were made
        if (filterResult.modificationsApplied.length > 0) {
          await this.logger.logResponseFiltering(
            data.userId,
            filterResult.originalContent,
            filterResult.filteredContent,
            filterResult.modificationsApplied,
            filterResult.containsSensitiveData
          )
        }

        // Parse and validate the filtered portfolio
        const portfolio = await this.parseAndValidatePortfolio(filterResult.filteredContent)

        // SECURITY: Additional portfolio object filtering
        const filteredPortfolio = ResponseFilter.filterPortfolioObject(portfolio)
        
        // Log the portfolio
        await this.logger.logPortfolio(promptId, portfolio, llmResponse.id)

        // Cache the result
        await this.cache.set(cacheKey, portfolio, data.userId, promptHash)

        // Log successful generation
        await this.logger.logAudit({
          userId: data.userId,
          action: AUDIT_ACTIONS.GENERATE_PORTFOLIO,
          details: {
            promptId,
            responseId: llmResponse.id,
            processingTimeMs: llmResponse.processingTimeMs,
            tokensUsed: llmResponse.tokensUsed,
            model: llmResponse.model
          },
          executionTimeMs: Date.now() - startTime,
          success: true
        })

        // Log API usage
        await this.logger.logApiUsage({
          userId: data.userId,
          apiProvider: llmResponse.provider,
          model: llmResponse.model,
          tokensUsed: llmResponse.tokensUsed,
          costUsd: this.calculateCost(llmResponse.tokensUsed, llmResponse.model),
          requestType: 'completion',
          success: true
        })

        return {
          portfolio,
          source: 'llm',
          confidence: 0.9, // High confidence for successful LLM generation
          processingTimeMs: Date.now() - startTime,
          llmResponse,
          cacheHit: false
        }

      } catch (llmError) {
        console.error('LLM generation failed:', llmError)
        const errorMessage = llmError instanceof Error ? llmError.message : 'Unknown error'

        // Log the failure
        await this.logger.logAudit({
          userId: data.userId,
          action: AUDIT_ACTIONS.API_ERROR,
          details: {
            promptId,
            error: errorMessage,
            errorCode: (llmError as any).code
          },
          executionTimeMs: Date.now() - startTime,
          success: false,
          errorDetails: { error: errorMessage }
        })

        // Try fallback if enabled
        if (this.config.enableFallback && this.fallback.isAvailable()) {
          return await this.generateFallbackPortfolio(data, startTime, 'llm_api_error')
        }

        throw llmError
      }

    } catch (error) {
      console.error('Portfolio generation failed:', error)
      
      // Try fallback as last resort
      if (this.config.enableFallback && this.fallback.isAvailable()) {
        return await this.generateFallbackPortfolio(data, startTime, 'system_error')
      }

      throw error
    }
  }

  /**
   * Validate a portfolio structure and content
   */
  async validatePortfolio(portfolio: PersonalizedPortfolio): Promise<{ isValid: boolean; errors: string[] }> {
    const errors: string[] = []

    try {
      // Basic structure validation
      if (!portfolio.allocations || !Array.isArray(portfolio.allocations)) {
        errors.push('Portfolio must have an allocations array')
      }

      if (!portfolio.riskLevel) {
        errors.push('Portfolio must have a risk level')
      }

      if (!portfolio.expectedReturn) {
        errors.push('Portfolio must have an expected return')
      }

      if (!portfolio.strategy) {
        errors.push('Portfolio must have a strategy')
      }

      if (!portfolio.rationale) {
        errors.push('Portfolio must have a rationale')
      }

      // Allocation validation
      if (portfolio.allocations && Array.isArray(portfolio.allocations)) {
        const totalAllocation = portfolio.allocations.reduce((sum, alloc) => sum + alloc.allocation, 0)
        
        if (Math.abs(totalAllocation - 100) > 0.01) {
          errors.push(`Allocations must sum to 100%, got ${totalAllocation.toFixed(2)}%`)
        }

        portfolio.allocations.forEach((alloc, index) => {
          if (!alloc.symbol) {
            errors.push(`Allocation ${index + 1} missing symbol`)
          }
          if (!alloc.name) {
            errors.push(`Allocation ${index + 1} missing name`)
          }
          if (typeof alloc.allocation !== 'number' || alloc.allocation <= 0) {
            errors.push(`Allocation ${index + 1} has invalid allocation percentage`)
          }
          if (!alloc.category) {
            errors.push(`Allocation ${index + 1} missing category`)
          }
          if (!alloc.rationale) {
            errors.push(`Allocation ${index + 1} missing rationale`)
          }
        })
      }

      return {
        isValid: errors.length === 0,
        errors
      }

    } catch (error) {
      console.error('Portfolio validation error:', error)
      return {
        isValid: false,
        errors: ['Portfolio validation failed due to system error']
      }
    }
  }

  /**
   * Get generation statistics for a user
   */
  async getGenerationStats(userId: string): Promise<{
    totalGenerations: number
    cacheHits: number
    fallbackUses: number
    avgProcessingTime: number
    lastGeneration: Date | null
  }> {
    try {
      return await this.logger.getUserStats(userId)
    } catch (error) {
      console.error('Failed to get generation stats:', error)
      return {
        totalGenerations: 0,
        cacheHits: 0,
        fallbackUses: 0,
        avgProcessingTime: 0,
        lastGeneration: null
      }
    }
  }

  /**
   * Check cache for existing portfolio
   */
  private async checkCache(cacheKey: string, userId: string): Promise<LLMPortfolioResult | null> {
    try {
      const cachedEntry = await this.cache.get(cacheKey)
      
      if (cachedEntry) {
        // Log cache hit
        await this.logger.logAudit({
          userId,
          action: AUDIT_ACTIONS.CACHE_HIT,
          details: {
            cacheKey,
            hitCount: cachedEntry.hitCount,
            age: Date.now() - cachedEntry.createdAt.getTime()
          },
          executionTimeMs: 0,
          success: true
        })

        return {
          portfolio: cachedEntry.portfolioData,
          source: 'cache',
          confidence: 0.95, // High confidence for cached results
          processingTimeMs: 0,
          cacheHit: true
        }
      }

      return null
    } catch (error) {
      console.error('Cache check failed:', error)
      return null
    }
  }

  /**
   * Generate fallback portfolio
   */
  private async generateFallbackPortfolio(
    data: PromptData, 
    startTime: number, 
    reason: string
  ): Promise<LLMPortfolioResult> {
    try {
      const portfolio = await this.fallback.generateFallbackPortfolio(data)

      // Log fallback usage
      await this.logger.logAudit({
        userId: data.userId,
        action: AUDIT_ACTIONS.FALLBACK_USED,
        details: {
          reason,
          fallbackType: 'rule_based'
        },
        executionTimeMs: Date.now() - startTime,
        success: true
      })

      return {
        portfolio,
        source: 'fallback',
        confidence: 0.7, // Lower confidence for fallback
        processingTimeMs: Date.now() - startTime,
        fallbackReason: reason
      }

    } catch (fallbackError) {
      console.error('Fallback generation failed:', fallbackError)
      const errorMessage = fallbackError instanceof Error ? fallbackError.message : 'Unknown error'
      throw new Error(`Both LLM and fallback generation failed: ${errorMessage}`)
    }
  }

  /**
   * Parse and validate LLM response
   */
  private async parseAndValidatePortfolio(responseText: string): Promise<PersonalizedPortfolio> {
    try {
      // Clean the response text (remove any markdown formatting)
      const cleanedText = responseText.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim()
      
      // Parse JSON
      const portfolio = JSON.parse(cleanedText) as PersonalizedPortfolio

      // Validate the portfolio
      const validation = await this.validatePortfolio(portfolio)
      
      if (!validation.isValid) {
        throw new ValidationError(
          'Generated portfolio failed validation',
          validation.errors,
          { responseText: cleanedText }
        )
      }

      return portfolio

    } catch (error) {
      if (error instanceof ValidationError) {
        throw error
      }

      const errorMessage = error instanceof Error ? error.message : 'Unknown parsing error'
      throw new ValidationError(
        'Failed to parse LLM response as valid portfolio JSON',
        [`JSON parsing error: ${errorMessage}`],
        { responseText }
      )
    }
  }

  /**
   * Calculate API cost based on tokens and model
   */
  private calculateCost(tokens: number, model: string): number {
    // OpenAI pricing (as of 2024) - these should be configurable
    const pricing = {
      'gpt-4': 0.03 / 1000, // $0.03 per 1K tokens
      'gpt-4-turbo': 0.01 / 1000, // $0.01 per 1K tokens
      'gpt-3.5-turbo': 0.002 / 1000 // $0.002 per 1K tokens
    }

    const rate = pricing[model as keyof typeof pricing] || pricing['gpt-3.5-turbo']
    return tokens * rate
  }

  /**
   * SECURITY: Sanitize prompt data before processing
   */
  private async sanitizePromptData(data: PromptData): Promise<PromptData> {
    const sanitized: PromptData = {
      ...data,
      surveyData: {
        ...data.surveyData,
        primaryGoal: InputSanitizer.sanitizeUserInput(data.surveyData.primaryGoal, { maxLength: 200 }),
        timeHorizon: InputSanitizer.sanitizeUserInput(data.surveyData.timeHorizon, { maxLength: 100 }),
        experienceLevel: InputSanitizer.sanitizeUserInput(data.surveyData.experienceLevel, { maxLength: 100 }),
        interestedThemes: InputSanitizer.sanitizeInvestmentThemes(data.surveyData.interestedThemes || [])
      },
      userMajor: data.userMajor ? InputSanitizer.sanitizeUserMajor(data.userMajor) : undefined,
      additionalContext: data.additionalContext ?
        InputSanitizer.sanitizeUserInput(data.additionalContext, { maxLength: 1000, strictMode: true }) :
        undefined
    }

    // Check for security risks in the original data
    const originalText = JSON.stringify(data)
    if (InputSanitizer.containsSecurityRisks(originalText)) {
      await this.logger.logSuspiciousActivity(
        data.userId,
        'potential_prompt_injection',
        {
          originalDataLength: originalText.length,
          riskPatterns: 'detected',
          sanitizationApplied: true
        }
      )
    }

    return sanitized
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    try {
      this.cache.stopCleanupInterval()
      await this.cache.cleanup()
    } catch (error) {
      console.error('Cleanup error:', error)
    }
  }
}
