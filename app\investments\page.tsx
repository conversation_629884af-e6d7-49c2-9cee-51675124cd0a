"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { TrendingUp, TrendingDown, Activity, Plus, Wallet, RefreshCw, Info } from "lucide-react"
import { useMarketMovers, type MarketMover } from "@/hooks/use-stock-data"
import { GlobalSearch } from "@/components/global-search"
import { TopNav } from "@/components/top-nav"
import Link from "next/link"
import { useRouter } from "next/navigation"
import type { PersonalizedPortfolio } from "@/lib/portfolio-generator"
import { ReweightPortfolioDialog } from "@/components/reweight-portfolio-dialog"
import { useAuth } from "@/components/auth-provider"

export default function InvestmentsPage() {
  const router = useRouter()
  const { user } = useAuth()
  const [portfolio, setPortfolio] = useState<PersonalizedPortfolio | null>(null)
  const [loading, setLoading] = useState(true)
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const [showReweightDialog, setShowReweightDialog] = useState(false)

  // Use the market movers hook for real-time data
  const {
    gainers: topGainers,
    losers: topLosers,
    mostActive,
    loading: marketLoading,
    error: marketError,
    refetch: refetchMarketData
  } = useMarketMovers(10)

  useEffect(() => {
    const loadData = async () => {
      try {
        if (!user) {
          console.log("No user found, redirecting to sign-in")
          router.push("/auth/signin")
          return
        }

        // Load portfolio data from user-specific localStorage
        const userPortfolioKey = `demo-portfolio-${user.id}`
        const storedPortfolio = localStorage.getItem(userPortfolioKey)
        if (storedPortfolio) {
          const parsedPortfolio = JSON.parse(storedPortfolio)
          console.log("Loaded portfolio from localStorage for user:", user.email)
          setPortfolio(parsedPortfolio)
        } else {
          console.log("No portfolio found for user:", user.email)
        }
      } catch (error) {
        console.error("Error loading investment data:", error)
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [user, router])

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(value)
  }

  const formatPercent = (value: number) => {
    return `${value >= 0 ? "+" : ""}${value.toFixed(2)}%`
  }

  const StockCard = ({ stock }: { stock: MarketMover }) => (
    <Link href={`/investments/${stock.symbol}`}>
      <Card className="hover:bg-slate-800/50 transition-colors cursor-pointer">
        <CardContent className="p-4">
          <div className="flex justify-between items-start mb-2">
            <div>
              <h3 className="font-semibold text-white">{stock.symbol}</h3>
              <p className="text-sm text-slate-400 truncate">{stock.name}</p>
            </div>
            <div className="text-right">
              <p className="font-semibold text-white">{formatCurrency(stock.price)}</p>
              <p className={`text-sm ${stock.changePercent >= 0 ? "text-emerald-400" : "text-red-400"}`}>
                {formatPercent(stock.changePercent)}
              </p>
            </div>
          </div>
          <div className="flex justify-between items-center text-xs text-slate-500">
            <span>{stock.sector || 'N/A'}</span>
            <span>Vol: {(stock.volume / 1000000).toFixed(1)}M</span>
          </div>
        </CardContent>
      </Card>
    </Link>
  )

  const PortfolioCard = ({
    symbol,
    name,
    allocation,
    category,
  }: {
    symbol: string
    name: string
    allocation: number
    category: string
  }) => (
    <Link href={`/investments/${symbol}`}>
      <Card className="hover:bg-slate-800/50 transition-colors cursor-pointer">
        <CardContent className="p-4">
          <div className="flex justify-between items-start mb-2">
            <div className="flex-1">
              <h3 className="font-semibold text-white">{symbol}</h3>
              <p className="text-sm text-slate-400 truncate">{name}</p>
              <Badge variant="secondary" className="mt-1 text-xs">
                {category}
              </Badge>
            </div>
            <div className="text-right">
              <p className="font-semibold text-emerald-400">{allocation.toFixed(1)}%</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  )

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-900">
        <TopNav currentPage="investments" />
        <div className="container mx-auto p-4 space-y-6">
          <div className="flex justify-between items-center">
            <h1 className="text-3xl font-bold text-white">Investments</h1>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[...Array(6)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-4">
                  <div className="h-4 bg-slate-700 rounded mb-2"></div>
                  <div className="h-3 bg-slate-700 rounded mb-4"></div>
                  <div className="h-3 bg-slate-700 rounded"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-900">
      <TopNav currentPage="investments" />
      <div className="container mx-auto p-4 space-y-6">
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 backdrop-blur-sm mb-8">
          <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/5 to-blue-500/5"></div>
          <div className="relative p-5">
            <div className="flex justify-between items-center">
              <div className="space-y-1">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-6 bg-gradient-to-b from-emerald-400 to-emerald-600 rounded-full"></div>
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent">
                    Investments
                  </h1>
                </div>
                <p className="text-slate-400 max-w-md">
                  Track your portfolio performance and discover new opportunities
                </p>
                <div className="flex items-center gap-4 text-sm text-slate-500">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse"></div>
                    <span>Live Market Data</span>
                  </div>
                  <div className="w-1 h-4 bg-slate-600 rounded-full"></div>
                  <span>Updated 2 min ago</span>
                </div>
              </div>
              <div className="flex gap-3 items-center">
                {portfolio?.allocations && portfolio.allocations.length > 0 && (
                  <Button
                    onClick={() => setShowReweightDialog(true)}
                    variant="outline"
                    className="border-emerald-600 text-emerald-400 hover:bg-emerald-600 hover:text-white transition-colors"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Reweight Portfolio
                  </Button>
                )}
                <Button
                  onClick={() => router.push('/search')}
                  className="bg-emerald-600 hover:bg-emerald-700 transition-colors"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Investment
                </Button>
              </div>
            </div>
          </div>
        </div>

        <Tabs defaultValue="portfolio" className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-slate-800">
            <TabsTrigger value="portfolio" className="data-[state=active]:bg-slate-700">
              My Portfolio
            </TabsTrigger>
            <TabsTrigger value="gainers" className="data-[state=active]:bg-slate-700">
              <TrendingUp className="h-4 w-4 mr-2" />
              Top Gainers
            </TabsTrigger>
            <TabsTrigger value="losers" className="data-[state=active]:bg-slate-700">
              <TrendingDown className="h-4 w-4 mr-2" />
              Top Losers
            </TabsTrigger>
            <TabsTrigger value="active" className="data-[state=active]:bg-slate-700">
              <Activity className="h-4 w-4 mr-2" />
              Most Active
            </TabsTrigger>
          </TabsList>

          <TabsContent value="portfolio" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-white">Your Portfolio</CardTitle>
                <CardDescription>
                  {portfolio
                    ? `${portfolio.allocations.length} holdings • ${portfolio.riskLevel} risk • ${portfolio.expectedReturn} expected return`
                    : "Complete onboarding to see your personalized portfolio"}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {portfolio && portfolio.allocations ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {portfolio.allocations.map((holding) => (
                      <PortfolioCard
                        key={holding.symbol}
                        symbol={holding.symbol}
                        name={holding.name}
                        allocation={holding.allocation}
                        category={holding.category}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-slate-400 mb-4">
                      No portfolio found. Complete the onboarding survey to get started.
                    </p>
                    <Link href="/onboarding">
                      <Button className="bg-emerald-600 hover:bg-emerald-700">Start Onboarding</Button>
                    </Link>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="gainers" className="space-y-4">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-white flex items-center">
                      <TrendingUp className="h-5 w-5 mr-2 text-emerald-400" />
                      Top Gainers
                    </CardTitle>
                    <CardDescription>Stocks with the highest percentage gains today</CardDescription>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={refetchMarketData}
                    disabled={marketLoading}
                  >
                    <RefreshCw className={`h-4 w-4 ${marketLoading ? 'animate-spin' : ''}`} />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {marketLoading ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {Array.from({ length: 6 }).map((_, i) => (
                      <Card key={i} className="animate-pulse">
                        <CardContent className="p-4">
                          <div className="space-y-2">
                            <div className="h-5 bg-slate-700 rounded w-16"></div>
                            <div className="h-4 bg-slate-600 rounded w-24"></div>
                            <div className="h-4 bg-slate-600 rounded w-20"></div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : marketError ? (
                  <div className="text-center py-8">
                    <Info className="h-12 w-12 mb-4 mx-auto text-muted-foreground" />
                    <p className="text-muted-foreground mb-4">{marketError}</p>
                    <Button onClick={refetchMarketData} variant="outline">
                      Try Again
                    </Button>
                  </div>
                ) : topGainers.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {topGainers.map((stock) => (
                      <StockCard key={stock.symbol} stock={stock} />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Info className="h-12 w-12 mb-4 mx-auto text-muted-foreground" />
                    <p className="text-muted-foreground">No gainers data available</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="losers" className="space-y-4">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-white flex items-center">
                      <TrendingDown className="h-5 w-5 mr-2 text-red-400" />
                      Top Losers
                    </CardTitle>
                    <CardDescription>Stocks with the highest percentage losses today</CardDescription>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={refetchMarketData}
                    disabled={marketLoading}
                  >
                    <RefreshCw className={`h-4 w-4 ${marketLoading ? 'animate-spin' : ''}`} />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {marketLoading ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {Array.from({ length: 6 }).map((_, i) => (
                      <Card key={i} className="animate-pulse">
                        <CardContent className="p-4">
                          <div className="space-y-2">
                            <div className="h-5 bg-slate-700 rounded w-16"></div>
                            <div className="h-4 bg-slate-600 rounded w-24"></div>
                            <div className="h-4 bg-slate-600 rounded w-20"></div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : marketError ? (
                  <div className="text-center py-8">
                    <Info className="h-12 w-12 mb-4 mx-auto text-muted-foreground" />
                    <p className="text-muted-foreground mb-4">{marketError}</p>
                    <Button onClick={refetchMarketData} variant="outline">
                      Try Again
                    </Button>
                  </div>
                ) : topLosers.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {topLosers.map((stock) => (
                      <StockCard key={stock.symbol} stock={stock} />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Info className="h-12 w-12 mb-4 mx-auto text-muted-foreground" />
                    <p className="text-muted-foreground">No losers data available</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="active" className="space-y-4">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-white flex items-center">
                      <Activity className="h-5 w-5 mr-2 text-blue-400" />
                      Most Active
                    </CardTitle>
                    <CardDescription>Stocks with the highest trading volume today</CardDescription>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={refetchMarketData}
                    disabled={marketLoading}
                  >
                    <RefreshCw className={`h-4 w-4 ${marketLoading ? 'animate-spin' : ''}`} />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {marketLoading ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {Array.from({ length: 6 }).map((_, i) => (
                      <Card key={i} className="animate-pulse">
                        <CardContent className="p-4">
                          <div className="space-y-2">
                            <div className="h-5 bg-slate-700 rounded w-16"></div>
                            <div className="h-4 bg-slate-600 rounded w-24"></div>
                            <div className="h-4 bg-slate-600 rounded w-20"></div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : marketError ? (
                  <div className="text-center py-8">
                    <Info className="h-12 w-12 mb-4 mx-auto text-muted-foreground" />
                    <p className="text-muted-foreground mb-4">{marketError}</p>
                    <Button onClick={refetchMarketData} variant="outline">
                      Try Again
                    </Button>
                  </div>
                ) : mostActive.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {mostActive.map((stock) => (
                      <StockCard key={stock.symbol} stock={stock} />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Info className="h-12 w-12 mb-4 mx-auto text-muted-foreground" />
                    <p className="text-muted-foreground">No most active data available</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
        {isSearchOpen && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-start justify-center pt-20">
            <GlobalSearch onClose={() => setIsSearchOpen(false)} autoFocus />
          </div>
        )}

        {/* Reweight Portfolio Dialog */}
        <ReweightPortfolioDialog
          open={showReweightDialog}
          onOpenChange={setShowReweightDialog}
          currentPortfolio={portfolio}
          onPortfolioUpdated={(newPortfolio) => {
            setPortfolio(newPortfolio)
          }}
        />
      </div>

      {/* Footer */}
      <footer className="relative z-10 border-t border-slate-700/50 bg-slate-800/30 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center gap-2 mb-4 md:mb-0">
              <Wallet className="h-6 w-6 text-emerald-400" />
              <span className="text-white font-semibold text-lg">investry</span>
            </div>
            <div className="flex items-center gap-6 text-sm text-slate-400">
              <span>Empowering investors of all backgrounds</span>
              <div className="flex items-center gap-4">
                <a href="#" className="hover:text-white transition-colors">
                  Privacy
                </a>
                <a href="#" className="hover:text-white transition-colors">
                  Terms
                </a>
                <a href="#" className="hover:text-white transition-colors">
                  Support
                </a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
