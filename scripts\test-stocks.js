// Test stocks tables in Supabase
// Run with: node scripts/test-stocks.js

const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://fiwhyrlkcojfeovdfrjn.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZpd2h5cmxrY29qZmVvdmRmcmpuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxMDI4ODQsImV4cCI6MjA2NzY3ODg4NH0.9JLGagCUl0URZwx6QA67JsG5fTQmg28QE7AQlroyuLg'

const supabase = createClient(supabaseUrl, supabaseKey)

async function testStocksTables() {
  console.log('🔍 Testing stocks tables...\n')

  try {
    // Test stocks table
    console.log('📊 Testing "stocks" table:')
    const { data: stocks, error: stocksError } = await supabase
      .from('stocks')
      .select('*')
      .limit(5)

    if (stocksError) {
      console.log('❌ Error accessing stocks table:', stocksError.message)
    } else {
      console.log(`✅ Found ${stocks?.length || 0} records in stocks table`)
      if (stocks && stocks.length > 0) {
        console.log('📋 Sample stock record:')
        console.log(JSON.stringify(stocks[0], null, 2))
        console.log('\n📋 Available columns:', Object.keys(stocks[0]))
      }
    }

    console.log('\n' + '='.repeat(50) + '\n')

    // Test stocks_with_tags table
    console.log('📊 Testing "stocks_with_tags" table:')
    const { data: stocksWithTags, error: tagsError } = await supabase
      .from('stocks_with_tags')
      .select('*')
      .limit(5)

    if (tagsError) {
      console.log('❌ Error accessing stocks_with_tags table:', tagsError.message)
    } else {
      console.log(`✅ Found ${stocksWithTags?.length || 0} records in stocks_with_tags table`)
      if (stocksWithTags && stocksWithTags.length > 0) {
        console.log('📋 Sample stocks_with_tags record:')
        console.log(JSON.stringify(stocksWithTags[0], null, 2))
        console.log('\n📋 Available columns:', Object.keys(stocksWithTags[0]))
      }
    }

    console.log('\n' + '='.repeat(50) + '\n')

    // Test search functionality
    console.log('🔍 Testing search functionality:')
    
    // Test search for AAPL
    const { data: aaplSearch, error: searchError } = await supabase
      .from('stocks')
      .select('*')
      .eq('ticker', 'AAPL')
      .single()

    if (searchError) {
      console.log('❌ Error searching for AAPL:', searchError.message)
    } else if (aaplSearch) {
      console.log('✅ Found AAPL in stocks table')
      console.log('📋 AAPL data:', JSON.stringify(aaplSearch, null, 2))
    } else {
      console.log('⚠️ AAPL not found in stocks table')
    }

    // Test search in stocks_with_tags
    const { data: aaplTagsSearch, error: tagsSearchError } = await supabase
      .from('stocks_with_tags')
      .select('*')
      .eq('ticker', 'AAPL')
      .single()

    if (tagsSearchError) {
      console.log('❌ Error searching for AAPL in stocks_with_tags:', tagsSearchError.message)
    } else if (aaplTagsSearch) {
      console.log('✅ Found AAPL in stocks_with_tags table')
      console.log('📋 AAPL tags data:', JSON.stringify(aaplTagsSearch, null, 2))
    } else {
      console.log('⚠️ AAPL not found in stocks_with_tags table')
    }

    console.log('\n' + '='.repeat(50) + '\n')

    // Get table counts
    console.log('📊 Getting table counts:')
    
    const { count: stocksCount, error: countError } = await supabase
      .from('stocks')
      .select('*', { count: 'exact', head: true })

    if (countError) {
      console.log('❌ Error getting stocks count:', countError.message)
    } else {
      console.log(`📊 Total stocks: ${stocksCount}`)
    }

    const { count: tagsCount, error: tagsCountError } = await supabase
      .from('stocks_with_tags')
      .select('*', { count: 'exact', head: true })

    if (tagsCountError) {
      console.log('❌ Error getting stocks_with_tags count:', tagsCountError.message)
    } else {
      console.log(`📊 Total stocks_with_tags: ${tagsCount}`)
    }

    console.log('\n' + '='.repeat(50) + '\n')

    // Test the actual search function from the app
    console.log('🔍 Testing app search function:')
    
    const { data: searchResults, error: appSearchError } = await supabase
      .from('stocks_with_tags')
      .select('*')
      .or('ticker.ilike.%AAPL%,company_name.ilike.%Apple%,sector.ilike.%Technology%')
      .limit(5)

    if (appSearchError) {
      console.log('❌ Error with app search query:', appSearchError.message)
    } else {
      console.log(`✅ App search found ${searchResults?.length || 0} results`)
      if (searchResults && searchResults.length > 0) {
        searchResults.forEach((result, index) => {
          console.log(`📋 Result ${index + 1}: ${result.ticker} - ${result.company_name}`)
        })
      }
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error.message)
  }
}

testStocksTables().then(() => {
  console.log('\n🎉 Stock tables test completed!')
  process.exit(0)
}).catch(error => {
  console.error('💥 Test failed:', error)
  process.exit(1)
})
