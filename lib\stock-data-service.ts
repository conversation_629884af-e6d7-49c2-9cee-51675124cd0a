/**
 * Enhanced Stock Data Service
 * Integrates multiple free APIs for semi-realtime stock data
 * Handles caching, rate limiting, and fallback mechanisms with centralized cache management
 */

import { getCacheManager, createCacheKey } from './cache-manager'
import { getCacheTTL, getMarketAwareCacheTTL, getCacheTagsForDataType } from './cache-config'

export interface StockPrice {
  symbol: string
  price: number
  change: number
  changePercent: number
  volume: number
  marketCap?: number
  lastUpdated: string
  source: 'alphavantage' | 'finnhub' | 'yahoo' | 'cache'
}

export interface StockQuote extends StockPrice {
  name: string
  exchange: string
  sector?: string
  description?: string
  high: number
  low: number
  open: number
  previousClose: number
}

export interface StockFinancials {
  symbol: string
  // Valuation metrics
  peRatio?: number
  pegRatio?: number
  priceToBook?: number
  priceToSales?: number
  enterpriseValue?: number
  evToRevenue?: number
  evToEbitda?: number

  // Profitability metrics
  eps?: number
  epsGrowth?: number
  revenueGrowth?: number
  grossMargin?: number
  operatingMargin?: number
  netMargin?: number
  roe?: number
  roa?: number

  // Dividend metrics
  dividendYield?: number
  dividendPerShare?: number
  payoutRatio?: number

  // Financial strength
  debtToEquity?: number
  currentRatio?: number
  quickRatio?: number

  // Market data
  marketCap?: number
  sharesOutstanding?: number
  beta?: number
  week52High?: number
  week52Low?: number

  lastUpdated: string
  source: 'finnhub' | 'alphavantage' | 'cache'
}

export interface NewsArticle {
  id: string
  headline: string
  summary: string
  url: string
  source: string
  publishedAt: string
  category: string
  sentiment?: 'positive' | 'negative' | 'neutral'
  image?: string
  relatedSymbols: string[]
}

export interface StockNews {
  symbol: string
  articles: NewsArticle[]
  lastUpdated: string
  source: 'finnhub' | 'cache'
}

export interface MarketMover {
  symbol: string
  name: string
  price: number
  change: number
  changePercent: number
  volume: number
  marketCap?: number
  sector?: string
  lastUpdated: string
  source: 'finnhub' | 'cache'
}

export interface MarketMovers {
  gainers: MarketMover[]
  losers: MarketMover[]
  mostActive: MarketMover[]
  lastUpdated: string
  source: 'finnhub' | 'cache'
}

export interface ApiConfig {
  alphavantage?: {
    apiKey: string
    baseUrl: string
    dailyLimit: number
  }
  finnhub?: {
    apiKey: string
    baseUrl: string
    minuteLimit: number
  }
}

export interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number // Time to live in milliseconds
}

export class StockDataService {
  private cacheManager = getCacheManager()
  private apiUsage = {
    alphavantage: { daily: 0, lastReset: new Date().toDateString() },
    finnhub: { minute: 0, lastReset: Date.now() }
  }

  constructor(private config: ApiConfig) {}

  /**
   * Get real-time stock quote with fallback chain
   */
  async getStockQuote(symbol: string): Promise<StockQuote | null> {
    const cacheKey = createCacheKey('quote', symbol)

    // Check cache first with market-aware TTL
    const cached = this.cacheManager.get<StockQuote>(cacheKey)
    if (cached) {
      return cached
    }

    // Try APIs in order of preference
    let quote: StockQuote | null = null

    // 1. Try Finnhub first (higher rate limit, real-time data)
    if (this.canUseFinnhub()) {
      quote = await this.getFinnhubQuote(symbol)
      if (quote) {
        const ttl = getMarketAwareCacheTTL('stockQuotes')
        const tags = getCacheTagsForDataType('stockQuotes')
        this.cacheManager.set(cacheKey, quote, ttl, tags)
        return quote
      }
    }

    // 2. Try Alpha Vantage (limited but reliable)
    if (this.canUseAlphaVantage()) {
      quote = await this.getAlphaVantageQuote(symbol)
      if (quote) {
        const ttl = getMarketAwareCacheTTL('stockQuotes')
        const tags = getCacheTagsForDataType('stockQuotes')
        this.cacheManager.set(cacheKey, quote, ttl, tags)
        return quote
      }
    }

    // 3. Fallback to Yahoo Finance (unofficial but free)
    quote = await this.getYahooQuote(symbol)
    if (quote) {
      const ttl = getMarketAwareCacheTTL('stockQuotes')
      const tags = getCacheTagsForDataType('stockQuotes')
      this.cacheManager.set(cacheKey, quote, ttl, tags)
      return quote
    }

    return null
  }

  /**
   * Get multiple stock quotes efficiently
   */
  async getMultipleQuotes(symbols: string[]): Promise<StockQuote[]> {
    const results: StockQuote[] = []
    const uncachedSymbols: string[] = []

    // Check cache for all symbols first
    for (const symbol of symbols) {
      const cacheKey = createCacheKey('quote', symbol)
      const cached = this.cacheManager.get<StockQuote>(cacheKey)
      if (cached) {
        results.push(cached)
      } else {
        uncachedSymbols.push(symbol)
      }
    }

    // Batch fetch uncached symbols
    if (uncachedSymbols.length > 0) {
      const batchResults = await this.batchFetchQuotes(uncachedSymbols)
      results.push(...batchResults)
    }

    return results
  }

  /**
   * Batch fetch quotes with intelligent API selection
   */
  private async batchFetchQuotes(symbols: string[]): Promise<StockQuote[]> {
    const results: StockQuote[] = []

    // Use Finnhub for batch requests if available
    if (this.canUseFinnhub() && symbols.length <= 10) {
      const batchResults = await this.getFinnhubBatchQuotes(symbols)
      return batchResults
    }

    // Otherwise, fetch individually with rate limiting
    for (const symbol of symbols) {
      const quote = await this.getStockQuote(symbol)
      if (quote) {
        results.push(quote)
      }
      
      // Add delay to respect rate limits
      await this.delay(100)
    }

    return results
  }

  /**
   * Finnhub API integration
   */
  private async getFinnhubQuote(symbol: string): Promise<StockQuote | null> {
    if (!this.config.finnhub?.apiKey) return null

    try {
      this.apiUsage.finnhub.minute++
      
      const response = await fetch(
        `${this.config.finnhub.baseUrl}/quote?symbol=${symbol}&token=${this.config.finnhub.apiKey}`
      )

      if (!response.ok) return null

      const data = await response.json()
      
      if (data.c === 0) return null // No data available

      // Get company profile for additional info
      const profile = await this.getFinnhubProfile(symbol)

      return {
        symbol,
        name: profile?.name || symbol,
        price: data.c, // Current price
        change: data.d, // Change
        changePercent: data.dp, // Change percent
        volume: 0, // Not provided in basic quote
        high: data.h, // High
        low: data.l, // Low
        open: data.o, // Open
        previousClose: data.pc, // Previous close
        exchange: profile?.exchange || 'UNKNOWN',
        sector: profile?.finnhubIndustry,
        lastUpdated: new Date().toISOString(),
        source: 'finnhub'
      }
    } catch (error) {
      console.error('Finnhub API error:', error)
      return null
    }
  }

  /**
   * Get Finnhub company profile
   */
  private async getFinnhubProfile(symbol: string) {
    const cacheKey = createCacheKey('profile', symbol)
    const cached = this.cacheManager.get(cacheKey)
    if (cached) return cached

    try {
      const response = await fetch(
        `${this.config.finnhub.baseUrl}/stock/profile2?symbol=${symbol}&token=${this.config.finnhub.apiKey}`
      )

      if (!response.ok) return null

      const profile = await response.json()
      const ttl = getCacheTTL('companyProfiles')
      const tags = getCacheTagsForDataType('companyProfiles')
      this.cacheManager.set(cacheKey, profile, ttl, tags)

      return profile
    } catch (error) {
      console.error('Finnhub profile error:', error)
      return null
    }
  }

  /**
   * Finnhub batch quotes
   */
  private async getFinnhubBatchQuotes(symbols: string[]): Promise<StockQuote[]> {
    const results: StockQuote[] = []
    
    // Finnhub doesn't have true batch API, so we'll do parallel requests
    const promises = symbols.map(symbol => this.getFinnhubQuote(symbol))
    const quotes = await Promise.allSettled(promises)
    
    quotes.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value) {
        results.push(result.value)
      }
    })

    return results
  }

  /**
   * Alpha Vantage API integration
   */
  private async getAlphaVantageQuote(symbol: string): Promise<StockQuote | null> {
    if (!this.config.alphavantage?.apiKey) return null

    try {
      this.apiUsage.alphavantage.daily++
      
      const response = await fetch(
        `${this.config.alphavantage.baseUrl}/query?function=GLOBAL_QUOTE&symbol=${symbol}&apikey=${this.config.alphavantage.apiKey}`
      )

      if (!response.ok) return null

      const data = await response.json()
      const quote = data['Global Quote']
      
      if (!quote || Object.keys(quote).length === 0) return null

      return {
        symbol,
        name: symbol, // AV doesn't provide company name in quote
        price: parseFloat(quote['05. price']),
        change: parseFloat(quote['09. change']),
        changePercent: parseFloat(quote['10. change percent'].replace('%', '')),
        volume: parseInt(quote['06. volume']),
        high: parseFloat(quote['03. high']),
        low: parseFloat(quote['04. low']),
        open: parseFloat(quote['02. open']),
        previousClose: parseFloat(quote['08. previous close']),
        exchange: 'UNKNOWN',
        lastUpdated: new Date().toISOString(),
        source: 'alphavantage'
      }
    } catch (error) {
      console.error('Alpha Vantage API error:', error)
      return null
    }
  }

  /**
   * Yahoo Finance fallback (unofficial API)
   */
  private async getYahooQuote(symbol: string): Promise<StockQuote | null> {
    try {
      // Using a public Yahoo Finance API proxy
      const response = await fetch(
        `https://query1.finance.yahoo.com/v8/finance/chart/${symbol}`
      )

      if (!response.ok) return null

      const data = await response.json()
      const result = data.chart?.result?.[0]
      
      if (!result) return null

      const meta = result.meta
      const quote = result.indicators?.quote?.[0]

      return {
        symbol,
        name: meta.longName || meta.shortName || symbol,
        price: meta.regularMarketPrice,
        change: meta.regularMarketPrice - meta.previousClose,
        changePercent: ((meta.regularMarketPrice - meta.previousClose) / meta.previousClose) * 100,
        volume: meta.regularMarketVolume || 0,
        high: meta.regularMarketDayHigh,
        low: meta.regularMarketDayLow,
        open: quote?.open?.[0] || meta.regularMarketPrice,
        previousClose: meta.previousClose,
        exchange: meta.exchangeName || 'UNKNOWN',
        lastUpdated: new Date().toISOString(),
        source: 'yahoo'
      }
    } catch (error) {
      console.error('Yahoo Finance API error:', error)
      return null
    }
  }

  /**
   * Clear cache by tags (useful for invalidating related data)
   */
  clearCacheByTags(tags: string[]): number {
    return this.cacheManager.clearByTags(tags)
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return this.cacheManager.getStats()
  }

  /**
   * Rate limit checks
   */
  private canUseFinnhub(): boolean {
    const now = Date.now()
    const minuteAgo = now - 60 * 1000

    if (this.apiUsage.finnhub.lastReset < minuteAgo) {
      this.apiUsage.finnhub.minute = 0
      this.apiUsage.finnhub.lastReset = now
    }

    return this.apiUsage.finnhub.minute < (this.config.finnhub?.minuteLimit || 60)
  }

  private canUseAlphaVantage(): boolean {
    const today = new Date().toDateString()
    
    if (this.apiUsage.alphavantage.lastReset !== today) {
      this.apiUsage.alphavantage.daily = 0
      this.apiUsage.alphavantage.lastReset = today
    }

    return this.apiUsage.alphavantage.daily < (this.config.alphavantage?.dailyLimit || 25)
  }

  /**
   * Utility functions
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * Get API usage statistics
   */
  getApiUsage() {
    return {
      alphavantage: {
        used: this.apiUsage.alphavantage.daily,
        limit: this.config.alphavantage?.dailyLimit || 25,
        resetTime: 'Daily'
      },
      finnhub: {
        used: this.apiUsage.finnhub.minute,
        limit: this.config.finnhub?.minuteLimit || 60,
        resetTime: 'Per minute'
      }
    }
  }

  /**
   * Get stock financial metrics
   */
  async getStockFinancials(symbol: string): Promise<StockFinancials | null> {
    const cacheKey = createCacheKey('financials', symbol)

    // Check cache first with appropriate TTL
    const cached = this.cacheManager.get<StockFinancials>(cacheKey)
    if (cached) {
      return cached
    }

    // Try Finnhub for financial metrics
    if (this.canUseFinnhub()) {
      const financials = await this.getFinnhubFinancials(symbol)
      if (financials) {
        const ttl = getCacheTTL('financials')
        const tags = getCacheTagsForDataType('financials')
        this.cacheManager.set(cacheKey, financials, ttl, tags)
        return financials
      }
    }

    return null
  }

  /**
   * Get stock news
   */
  async getStockNews(symbol: string, limit: number = 10): Promise<StockNews | null> {
    const cacheKey = createCacheKey('news', symbol, limit.toString())

    // Check cache first with appropriate TTL
    const cached = this.cacheManager.get<StockNews>(cacheKey)
    if (cached) {
      return cached
    }

    // Try Finnhub for news
    if (this.canUseFinnhub()) {
      const news = await this.getFinnhubNews(symbol, limit)
      if (news) {
        const ttl = getCacheTTL('news')
        const tags = getCacheTagsForDataType('news')
        this.cacheManager.set(cacheKey, news, ttl, tags)
        return news
      }
    }

    return null
  }

  /**
   * Get market movers (gainers, losers, most active)
   */
  async getMarketMovers(): Promise<MarketMovers | null> {
    const cacheKey = createCacheKey('market-movers')

    // Check cache first with market-aware TTL
    const cached = this.cacheManager.get<MarketMovers>(cacheKey)
    if (cached) {
      return cached
    }

    // Try Finnhub for market movers
    if (this.canUseFinnhub()) {
      const movers = await this.getFinnhubMarketMovers()
      if (movers) {
        const ttl = getMarketAwareCacheTTL('marketMovers')
        const tags = getCacheTagsForDataType('marketMovers')
        this.cacheManager.set(cacheKey, movers, ttl, tags)
        return movers
      }
    }

    return null
  }

  /**
   * Get top gainers
   */
  async getTopGainers(limit: number = 10): Promise<MarketMover[]> {
    const movers = await this.getMarketMovers()
    return movers?.gainers.slice(0, limit) || []
  }

  /**
   * Get top losers
   */
  async getTopLosers(limit: number = 10): Promise<MarketMover[]> {
    const movers = await this.getMarketMovers()
    return movers?.losers.slice(0, limit) || []
  }

  /**
   * Get most active stocks
   */
  async getMostActive(limit: number = 10): Promise<MarketMover[]> {
    const movers = await this.getMarketMovers()
    return movers?.mostActive.slice(0, limit) || []
  }

  /**
   * Finnhub financial metrics integration
   */
  private async getFinnhubFinancials(symbol: string): Promise<StockFinancials | null> {
    if (!this.config.finnhub?.apiKey) return null

    try {
      this.apiUsage.finnhub.minute++

      // Get basic financials
      const basicFinancialsResponse = await fetch(
        `${this.config.finnhub.baseUrl}/stock/metric?symbol=${symbol}&metric=all&token=${this.config.finnhub.apiKey}`
      )

      if (!basicFinancialsResponse.ok) return null

      const basicData = await basicFinancialsResponse.json()
      const metrics = basicData.metric

      if (!metrics) return null

      // Get company profile for market cap
      const profile = await this.getFinnhubProfile(symbol)

      return {
        symbol,
        // Valuation metrics
        peRatio: metrics.peBasicExclExtraTTM || metrics.peTTM,
        pegRatio: metrics.pegRatio,
        priceToBook: metrics.pbAnnual || metrics.pbTTM,
        priceToSales: metrics.psAnnual || metrics.psTTM,
        enterpriseValue: metrics.enterpriseValue,
        evToRevenue: metrics.evToRevenueTTM,
        evToEbitda: metrics.evToEbitdaTTM,

        // Profitability metrics
        eps: metrics.epsBasicExclExtraItemsTTM || metrics.epsTTM,
        epsGrowth: metrics.epsGrowth5Y,
        revenueGrowth: metrics.revenueGrowth5Y,
        grossMargin: metrics.grossMarginTTM,
        operatingMargin: metrics.operatingMarginTTM,
        netMargin: metrics.netProfitMarginTTM,
        roe: metrics.roeTTM,
        roa: metrics.roaTTM,

        // Dividend metrics
        dividendYield: metrics.dividendYieldIndicatedAnnual,
        dividendPerShare: metrics.dividendPerShareAnnual,
        payoutRatio: metrics.payoutRatioTTM,

        // Financial strength
        debtToEquity: metrics.totalDebtToEquityAnnual,
        currentRatio: metrics.currentRatioAnnual,
        quickRatio: metrics.quickRatioAnnual,

        // Market data
        marketCap: profile?.marketCapitalization,
        sharesOutstanding: profile?.shareOutstanding,
        beta: metrics.beta,
        week52High: metrics['52WeekHigh'],
        week52Low: metrics['52WeekLow'],

        lastUpdated: new Date().toISOString(),
        source: 'finnhub'
      }
    } catch (error) {
      console.error('Finnhub financials API error:', error)
      return null
    }
  }

  /**
   * Finnhub news integration
   */
  private async getFinnhubNews(symbol: string, limit: number): Promise<StockNews | null> {
    if (!this.config.finnhub?.apiKey) return null

    try {
      this.apiUsage.finnhub.minute++

      // Get news from last 7 days
      const toDate = new Date()
      const fromDate = new Date(toDate.getTime() - 7 * 24 * 60 * 60 * 1000)

      const response = await fetch(
        `${this.config.finnhub.baseUrl}/company-news?symbol=${symbol}&from=${fromDate.toISOString().split('T')[0]}&to=${toDate.toISOString().split('T')[0]}&token=${this.config.finnhub.apiKey}`
      )

      if (!response.ok) return null

      const newsData = await response.json()

      if (!Array.isArray(newsData)) return null

      const articles: NewsArticle[] = newsData
        .slice(0, limit)
        .map((item: any, index: number) => ({
          id: `${symbol}-${item.datetime}-${index}`,
          headline: item.headline || 'No headline available',
          summary: item.summary || 'No summary available',
          url: item.url || '#',
          source: item.source || 'Unknown',
          publishedAt: new Date(item.datetime * 1000).toISOString(),
          category: item.category || 'general',
          image: item.image,
          relatedSymbols: item.related ? item.related.split(',') : [symbol]
        }))
        .filter(article => article.headline && article.url !== '#')

      return {
        symbol,
        articles,
        lastUpdated: new Date().toISOString(),
        source: 'finnhub'
      }
    } catch (error) {
      console.error('Finnhub news API error:', error)
      return null
    }
  }

  /**
   * Finnhub market movers integration
   */
  private async getFinnhubMarketMovers(): Promise<MarketMovers | null> {
    if (!this.config.finnhub?.apiKey) return null

    try {
      // Smaller list of popular stocks to avoid rate limits
      const popularSymbols = [
        'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'NFLX',
        'JPM', 'BAC', 'V', 'MA', 'JNJ', 'PFE', 'KO', 'WMT'
      ]

      console.log('Fetching market movers for symbols:', popularSymbols)

      // Fetch quotes with rate limiting
      const quotes: StockQuote[] = []
      for (const symbol of popularSymbols) {
        try {
          const quote = await this.getFinnhubQuote(symbol)
          if (quote) {
            quotes.push(quote)
          }
          // Add delay to respect rate limits
          await this.delay(100)
        } catch (error) {
          console.warn(`Failed to get quote for ${symbol}:`, error)
        }
      }

      console.log('Successfully fetched quotes for', quotes.length, 'symbols')

      if (!quotes.length) {
        console.warn('No quotes fetched for market movers')
        return null
      }

      // Convert to MarketMover format
      const movers: MarketMover[] = quotes.map(quote => ({
        symbol: quote.symbol,
        name: quote.name,
        price: quote.price,
        change: quote.change,
        changePercent: quote.changePercent,
        volume: quote.volume || 1000000, // Default volume if not available
        marketCap: undefined,
        sector: quote.sector,
        lastUpdated: quote.lastUpdated,
        source: 'finnhub'
      }))

      console.log('Created movers array with', movers.length, 'items')

      // Sort and categorize
      const gainers = movers
        .filter(m => m.changePercent > 0)
        .sort((a, b) => b.changePercent - a.changePercent)
        .slice(0, 10)

      const losers = movers
        .filter(m => m.changePercent < 0)
        .sort((a, b) => a.changePercent - b.changePercent)
        .slice(0, 10)

      const mostActive = movers
        .filter(m => m.volume > 0)
        .sort((a, b) => b.volume - a.volume)
        .slice(0, 10)

      console.log('Market movers result:', {
        gainers: gainers.length,
        losers: losers.length,
        mostActive: mostActive.length
      })

      return {
        gainers,
        losers,
        mostActive,
        lastUpdated: new Date().toISOString(),
        source: 'finnhub'
      }
    } catch (error) {
      console.error('Finnhub market movers API error:', error)
      return null
    }
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear()
  }
}
