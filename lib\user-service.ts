/**
 * User service for managing user profiles and account operations
 */

import { createClient } from './supabase'
import type { User } from '@supabase/supabase-js'

export interface UserProfile {
  id: string
  email: string
  first_name?: string
  last_name?: string
  university?: string
  major?: string
  graduation_year?: number
  is_student?: boolean
  created_at?: string
  updated_at?: string
}

/**
 * Get user's display name from user metadata
 */
export function getUserDisplayName(user: User | null): string {
  if (!user) return "Investor"
  
  const firstName = user.user_metadata?.first_name || ""
  const lastName = user.user_metadata?.last_name || ""
  const fullName = `${firstName} ${lastName}`.trim()
  
  if (fullName) return firstName // Return just first name for dashboard
  
  // Fallback to email username
  return user.email?.split('@')[0] || "Investor"
}

/**
 * Get user's full name from user metadata
 */
export function getUserFullName(user: User | null): string {
  if (!user) return ""
  
  const firstName = user.user_metadata?.first_name || ""
  const lastName = user.user_metadata?.last_name || ""
  const fullName = `${firstName} ${lastName}`.trim()
  
  return fullName || user.email?.split('@')[0] || "User"
}

/**
 * Update user metadata in Supabase auth
 */
export async function updateUserProfile(updates: {
  first_name?: string
  last_name?: string
  university?: string
  major?: string
  graduation_year?: number
  is_student?: boolean
}): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = createClient()
    
    const { error } = await supabase.auth.updateUser({
      data: updates
    })
    
    if (error) {
      console.error('Error updating user profile:', error)
      return { success: false, error: error.message }
    }
    
    return { success: true }
  } catch (error) {
    console.error('Error updating user profile:', error)
    return { success: false, error: 'Failed to update profile' }
  }
}

/**
 * Delete user account and all associated data
 */
export async function deleteUserAccount(userId: string): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = createClient()
    
    // First, delete user-specific data from custom tables
    const tablesToClean = [
      'user_profiles',
      'user_settings', 
      'financial_goals',
      'linked_accounts',
      'transactions',
      'roundups',
      'ach_transfers',
      'wallet_transactions',
      'user_balances',
      'portfolio_performance_cache',
      'portfolio_performance_history',
      'financial_audit_log',
      'llm_generated_portfolios',
      'portfolio_cache',
      'llm_audit_log',
      'llm_api_usage'
    ]
    
    // Delete from each table (ignore errors for tables that don't exist)
    for (const table of tablesToClean) {
      try {
        await supabase
          .from(table)
          .delete()
          .eq('user_id', userId)
      } catch (error) {
        console.warn(`Could not delete from ${table}:`, error)
      }
    }
    
    // Delete the auth user (this will cascade to auth-related tables)
    const { error: deleteError } = await supabase.auth.admin.deleteUser(userId)
    
    if (deleteError) {
      console.error('Error deleting user account:', deleteError)
      return { success: false, error: deleteError.message }
    }
    
    return { success: true }
  } catch (error) {
    console.error('Error deleting user account:', error)
    return { success: false, error: 'Failed to delete account' }
  }
}

/**
 * Clear user-specific localStorage data
 */
export function clearUserLocalStorage(userId: string): void {
  try {
    const keysToRemove = [
      `investry_survey-${userId}`,
      `demo-portfolio-${userId}`,
      `user-portfolio-${userId}`,
      `portfolio-metadata-${userId}`,
      `demo-goals-${userId}`,
      `investry_notifications_${userId}`,
      `investry_settings`
    ]
    
    keysToRemove.forEach(key => {
      localStorage.removeItem(key)
    })
    
    // Also remove any old non-user-specific keys
    const oldKeys = [
      'demo-portfolio',
      'user-portfolio',
      'investry_survey',
      'portfolio-metadata',
      'investry_remembered_email'
    ]
    
    oldKeys.forEach(key => {
      localStorage.removeItem(key)
    })
    
    console.log('User localStorage data cleared')
  } catch (error) {
    console.error('Error clearing localStorage:', error)
  }
}
