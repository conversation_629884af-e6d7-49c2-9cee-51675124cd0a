"use client"

import { <PERSON><PERSON><PERSON>, <PERSON>, Star, ChevronRight, Play, Clock, Users, Wallet } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { TopNav } from "@/components/top-nav"
import Link from "next/link"
import { useRouter } from "next/navigation"

export default function LearnPage() {
  const router = useRouter()

  const courses = [
    {
      id: "basics",
      title: "Stock Market Basics",
      description: "Learn how stocks work and why they matter",
      lessons: 5,
      duration: "15 min",
      progress: 40,
      completed: 2,
      color: "bg-blue-600",
      difficulty: "Beginner",
      students: 1234,
    },
    {
      id: "investing",
      title: "Investment Strategies",
      description: "Build a portfolio that matches your goals",
      lessons: 4,
      duration: "12 min",
      progress: 0,
      completed: 0,
      color: "bg-green-600",
      difficulty: "Intermediate",
      students: 856,
    },
    {
      id: "compound",
      title: "Compound Interest",
      description: "Discover the power of long-term investing",
      lessons: 3,
      duration: "10 min",
      progress: 100,
      completed: 3,
      color: "bg-purple-600",
      difficulty: "Beginner",
      students: 2341,
    },
    {
      id: "risk",
      title: "Risk Management",
      description: "Learn to balance risk and reward",
      lessons: 6,
      duration: "18 min",
      progress: 0,
      completed: 0,
      color: "bg-orange-600",
      difficulty: "Advanced",
      students: 567,
    },
  ]

  const achievements = [
    { title: "First Investment", description: "Made your first stock purchase", earned: true },
    { title: "Learning Streak", description: "5 days of continuous learning", earned: true },
    { title: "Portfolio Builder", description: "Created a diversified portfolio", earned: false },
    { title: "Risk Master", description: "Completed risk management course", earned: false },
  ]

  return (
    <div className="min-h-screen bg-slate-900 text-white">
      <TopNav currentPage="learn" />

      <div className="container mx-auto px-4 py-8 space-y-8">
        {/* Progress Stats */}
        <div className="grid md:grid-cols-3 gap-6">
          <Card className="bg-slate-800/50 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-slate-400 text-sm">Current Level</p>
                  <p className="text-2xl font-bold text-white">Level 3</p>
                </div>
                <Trophy className="h-8 w-8 text-amber-500" />
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-slate-400">Progress to Level 4</span>
                  <span className="text-amber-500">650/1000 XP</span>
                </div>
                <Progress value={65} className="h-2" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-slate-400 text-sm">Learning Streak</p>
                  <p className="text-2xl font-bold text-emerald-400">5 days</p>
                </div>
                <Star className="h-8 w-8 text-emerald-400" />
              </div>
              <p className="text-slate-400 text-sm">Keep it up! You're on fire 🔥</p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-slate-400 text-sm">Courses Completed</p>
                  <p className="text-2xl font-bold text-blue-400">1/4</p>
                </div>
                <BookOpen className="h-8 w-8 text-blue-400" />
              </div>
              <p className="text-slate-400 text-sm">3 more to go!</p>
            </CardContent>
          </Card>
        </div>

        {/* Courses */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold">Available Courses</h2>
            <Button variant="outline" className="border-slate-600 text-slate-300 hover:bg-slate-700 bg-transparent">
              View All
            </Button>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            {courses.map((course) => (
              <Link key={course.id} href={`/learn/${course.id}`}>
                <Card className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-colors cursor-pointer h-full">
                  <div className={`h-20 ${course.color} flex items-center justify-center relative`}>
                    <BookOpen className="h-10 w-10 text-white" />
                    <Badge variant="secondary" className="absolute top-2 right-2 bg-black/30 text-white border-none">
                      {course.difficulty}
                    </Badge>
                  </div>
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <h3 className="text-white font-semibold text-lg mb-2">{course.title}</h3>
                        <p className="text-slate-400 text-sm mb-4">{course.description}</p>
                      </div>
                      <ChevronRight className="h-5 w-5 text-slate-400 ml-2 flex-shrink-0" />
                    </div>

                    <div className="space-y-3">
                      <div className="flex items-center justify-between text-sm text-slate-400">
                        <div className="flex items-center gap-4">
                          <span className="flex items-center gap-1">
                            <Play className="h-3 w-3" />
                            {course.lessons} lessons
                          </span>
                          <span className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {course.duration}
                          </span>
                          <span className="flex items-center gap-1">
                            <Users className="h-3 w-3" />
                            {course.students.toLocaleString()}
                          </span>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-slate-400">
                            {course.completed}/{course.lessons} completed
                          </span>
                          <span className="text-emerald-400">{course.progress}%</span>
                        </div>
                        <Progress value={course.progress} className="h-2" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>

        {/* Achievements */}
        <div className="space-y-6">
          <h2 className="text-2xl font-bold">Achievements</h2>
          <div className="grid md:grid-cols-2 gap-4">
            {achievements.map((achievement, index) => (
              <Card
                key={index}
                className={`border-slate-700 ${
                  achievement.earned ? "bg-emerald-500/10 border-emerald-400/30" : "bg-slate-800/50"
                }`}
              >
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div
                      className={`w-10 h-10 rounded-full flex items-center justify-center ${
                        achievement.earned ? "bg-emerald-500/20" : "bg-slate-700"
                      }`}
                    >
                      <Trophy className={`h-5 w-5 ${achievement.earned ? "text-emerald-400" : "text-slate-500"}`} />
                    </div>
                    <div className="flex-1">
                      <h3 className={`font-medium ${achievement.earned ? "text-emerald-400" : "text-slate-400"}`}>
                        {achievement.title}
                      </h3>
                      <p className="text-slate-500 text-sm">{achievement.description}</p>
                    </div>
                    {achievement.earned && <Star className="h-5 w-5 text-amber-500 fill-current" />}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="relative z-10 border-t border-slate-700/50 bg-slate-800/30 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center gap-2 mb-4 md:mb-0">
              <Wallet className="h-6 w-6 text-emerald-400" />
              <span className="text-white font-semibold text-lg">investry</span>
            </div>
            <div className="flex items-center gap-6 text-sm text-slate-400">
              <span>Empowering investors of all backgrounds</span>
              <div className="flex items-center gap-4">
                <a href="#" className="hover:text-white transition-colors">
                  Privacy
                </a>
                <a href="#" className="hover:text-white transition-colors">
                  Terms
                </a>
                <a href="#" className="hover:text-white transition-colors">
                  Support
                </a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
