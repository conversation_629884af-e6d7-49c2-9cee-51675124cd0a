/**
 * LLM Portfolio Generation API Endpoint
 * POST /api/portfolio/generate-llm
 */

import { NextRequest, NextResponse } from 'next/server'
import { withAuth, addSecurityHeaders, addCorsHeaders, validateInput } from '@/lib/llm-portfolio/auth-middleware'
import { getLLMPortfolioEngine } from '@/lib/llm-portfolio'
import { InputSanitizer } from '@/lib/llm-portfolio/input-sanitizer'
import type { AuthContext } from '@/lib/llm-portfolio/auth-middleware'
import type { SurveyData } from '@/components/onboarding-survey'

// Input validation schema
const GENERATE_PORTFOLIO_SCHEMA = {
  required: ['surveyData'],
  properties: {
    surveyData: {
      type: 'object',
      required: ['primaryGoal', 'timeHorizon', 'riskTolerance', 'experienceLevel']
    },
    userMajor: { type: 'string' },
    additionalContext: { type: 'string' }
  }
}

async function generatePortfolioHandler(
  request: NextRequest,
  context: AuthContext
): Promise<NextResponse> {
  const startTime = Date.now()

  try {
    // Parse request body
    const body = await request.json()
    
    // Validate input
    const validation = validateInput(body, GENERATE_PORTFOLIO_SCHEMA)
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          error: 'Invalid input data',
          details: validation.errors
        },
        { status: 400 }
      )
    }

    const { surveyData, userMajor, additionalContext } = body

    // Validate survey data structure
    if (!isValidSurveyData(surveyData)) {
      return NextResponse.json(
        { error: 'Invalid survey data structure' },
        { status: 400 }
      )
    }

    // SECURITY: Check for security risks in input
    const inputText = JSON.stringify({ surveyData, userMajor, additionalContext })
    if (InputSanitizer.containsSecurityRisks(inputText)) {
      return NextResponse.json(
        { error: 'Input contains potentially unsafe content' },
        { status: 400 }
      )
    }

    // Get LLM portfolio engine
    const engine = getLLMPortfolioEngine()

    // Generate portfolio
    const result = await engine.generatePortfolio({
      surveyData,
      userId: context.userId,
      userMajor,
      additionalContext
    })

    const processingTime = Date.now() - startTime

    // Prepare response
    const response = NextResponse.json({
      success: true,
      data: {
        portfolio: result.portfolio,
        metadata: {
          source: result.source,
          confidence: result.confidence,
          processingTimeMs: processingTime,
          cacheHit: result.cacheHit || false,
          fallbackReason: result.fallbackReason,
          validationErrors: result.validationErrors
        }
      },
      rateLimitRemaining: context.rateLimitRemaining
    })

    // Add security headers
    addSecurityHeaders(response)
    addCorsHeaders(response, request.headers.get('origin') || undefined)

    return response

  } catch (error) {
    console.error('Portfolio generation error:', error)

    const processingTime = Date.now() - startTime
    
    // Handle specific error types
    if (error.name === 'LLMError') {
      return NextResponse.json(
        {
          error: error.message,
          code: error.code,
          processingTimeMs: processingTime
        },
        { status: error.code === 'RATE_LIMIT_EXCEEDED' ? 429 : 500 }
      )
    }

    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          error: 'Portfolio validation failed',
          details: error.errors,
          processingTimeMs: processingTime
        },
        { status: 422 }
      )
    }

    // Generic error response
    return NextResponse.json(
      {
        error: 'Portfolio generation failed',
        message: error.message,
        processingTimeMs: processingTime
      },
      { status: 500 }
    )
  }
}

/**
 * Validate survey data structure
 */
function isValidSurveyData(surveyData: any): surveyData is SurveyData {
  if (!surveyData || typeof surveyData !== 'object') {
    return false
  }

  const required = ['primaryGoal', 'timeHorizon', 'riskTolerance', 'experienceLevel']
  for (const field of required) {
    if (!surveyData[field]) {
      return false
    }
  }

  // Validate risk tolerance is a number between 1-5
  if (typeof surveyData.riskTolerance !== 'number' || 
      surveyData.riskTolerance < 1 || 
      surveyData.riskTolerance > 5) {
    return false
  }

  // Validate monthly investment if provided
  if (surveyData.monthlyInvestment !== undefined && 
      (typeof surveyData.monthlyInvestment !== 'number' || surveyData.monthlyInvestment < 0)) {
    return false
  }

  // Validate interested themes if provided
  if (surveyData.interestedThemes !== undefined && 
      !Array.isArray(surveyData.interestedThemes)) {
    return false
  }

  return true
}

// Export the handler with authentication middleware
export const POST = withAuth(generatePortfolioHandler, {
  requireAuth: true,
  rateLimitPerHour: 10
})

// Handle OPTIONS for CORS
export async function OPTIONS(request: NextRequest) {
  const response = new NextResponse(null, { status: 200 })
  addCorsHeaders(response, request.headers.get('origin') || undefined)
  return response
}

// Handle GET for endpoint info
export async function GET() {
  const response = NextResponse.json({
    endpoint: '/api/portfolio/generate-llm',
    method: 'POST',
    description: 'Generate personalized investment portfolio using LLM',
    authentication: 'Bearer token required',
    rateLimit: '10 requests per hour per user',
    requestBody: {
      surveyData: {
        primaryGoal: 'string (required)',
        timeHorizon: 'string (required)',
        riskTolerance: 'number 1-5 (required)',
        experienceLevel: 'string (required)',
        interestedThemes: 'string[] (optional)',
        monthlyInvestment: 'number (optional)',
        major: 'string (optional)'
      },
      userMajor: 'string (optional)',
      additionalContext: 'string (optional)'
    },
    responseFormat: {
      success: 'boolean',
      data: {
        portfolio: 'PersonalizedPortfolio',
        metadata: {
          source: 'llm | cache | fallback',
          confidence: 'number',
          processingTimeMs: 'number',
          cacheHit: 'boolean',
          fallbackReason: 'string (optional)',
          validationErrors: 'string[] (optional)'
        }
      },
      rateLimitRemaining: 'number'
    }
  })

  addSecurityHeaders(response)
  return response
}
