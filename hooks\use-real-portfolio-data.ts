"use client"

import { useState, useEffect, useCallback } from 'react'
import { createClient } from '@/lib/supabase'
import { useAuth } from '@/components/auth-provider'

interface PortfolioHolding {
  id: string
  portfolio_id: string
  symbol: string
  shares: number
  average_cost: number
  current_price: number | null
  total_cost?: number
  unrealized_gain_loss?: number
  unrealized_gain_loss_percent?: number
  target_percentage?: number
}

interface UserBalance {
  balance: number
  available_for_investment: number
  total_invested: number
  currency: string
}

interface PortfolioData {
  totalValue: number
  totalInvested: number
  totalReturn: number
  totalReturnPercent: number
  dailyChange: number
  dailyChangePercent: number
  holdings: PortfolioHolding[]
  balance: UserBalance | null
}

export function useRealPortfolioData() {
  const { user } = useAuth()
  const [data, setData] = useState<PortfolioData>({
    totalValue: 0,
    totalInvested: 0,
    totalReturn: 0,
    totalReturnPercent: 0,
    dailyChange: 0,
    dailyChangePercent: 0,
    holdings: [],
    balance: null
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchPortfolioData = useCallback(async () => {
    if (!user) {
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const supabase = createClient()

      // Get user's portfolio
      const { data: portfolios, error: portfolioError } = await supabase
        .from('portfolios')
        .select('id')
        .eq('user_id', user.id)
        .limit(1)

      if (portfolioError) {
        throw new Error(`Portfolio fetch error: ${portfolioError.message}`)
      }

      if (!portfolios || portfolios.length === 0) {
        // No portfolio found - user needs to create one
        setData({
          totalValue: 0,
          totalInvested: 0,
          totalReturn: 0,
          totalReturnPercent: 0,
          dailyChange: 0,
          dailyChangePercent: 0,
          holdings: [],
          balance: null
        })
        setLoading(false)
        return
      }

      const portfolioId = portfolios[0].id

      // Get user balance
      const { data: balanceData, error: balanceError } = await supabase
        .from('user_balances')
        .select('*')
        .eq('user_id', user.id)
        .eq('currency', 'USD')
        .single()

      let userBalance: UserBalance | null = null
      if (!balanceError && balanceData) {
        userBalance = {
          balance: balanceData.balance || 0,
          available_for_investment: balanceData.available_for_investment || 0,
          total_invested: balanceData.total_invested || 0,
          currency: balanceData.currency || 'USD'
        }
      }

      // Get portfolio holdings
      const { data: holdings, error: holdingsError } = await supabase
        .from('portfolio_holdings')
        .select('*')
        .eq('portfolio_id', portfolioId)

      if (holdingsError) {
        throw new Error(`Holdings fetch error: ${holdingsError.message}`)
      }

      const portfolioHoldings = holdings || []

      // Calculate portfolio totals
      let totalValue = 0
      let totalCost = 0

      for (const holding of portfolioHoldings) {
        const currentPrice = holding.current_price || 0
        const shares = holding.shares || 0
        const averageCost = holding.average_cost || 0
        
        const holdingValue = shares * currentPrice
        const holdingCost = shares * averageCost
        
        totalValue += holdingValue
        totalCost += holdingCost
      }

      // Add cash balance to total value
      const cashBalance = userBalance?.balance || 0
      const totalPortfolioValue = totalValue + cashBalance

      // Calculate returns
      const totalInvested = userBalance?.total_invested || totalCost
      const totalReturn = totalValue - totalInvested
      const totalReturnPercent = totalInvested > 0 ? (totalReturn / totalInvested) * 100 : 0

      // For daily change, we'll use a simple calculation for now
      // In a real implementation, you'd compare with yesterday's values
      const dailyChange = totalReturn * 0.1 // Placeholder - 10% of total return as daily
      const dailyChangePercent = totalPortfolioValue > 0 ? (dailyChange / totalPortfolioValue) * 100 : 0

      setData({
        totalValue: totalPortfolioValue,
        totalInvested,
        totalReturn,
        totalReturnPercent,
        dailyChange,
        dailyChangePercent,
        holdings: portfolioHoldings,
        balance: userBalance
      })

    } catch (err) {
      console.error('Error fetching portfolio data:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch portfolio data')
    } finally {
      setLoading(false)
    }
  }, [user])

  useEffect(() => {
    fetchPortfolioData()
  }, [fetchPortfolioData])

  return {
    data,
    loading,
    error,
    refetch: fetchPortfolioData
  }
}
