// Demo notification helpers for testing the notification system

export const createDemoNotifications = (addNotification: any) => {
  return {
    // Portfolio notifications
    portfolioGain: () => addNotification({
      type: "portfolio",
      title: "Portfolio Update",
      message: "Your portfolio gained +2.3% today ($230.45)",
      actionUrl: "/portfolio"
    }),

    portfolioLoss: () => addNotification({
      type: "portfolio", 
      title: "Portfolio Alert",
      message: "Your portfolio declined -1.2% today. Consider reviewing your positions.",
      actionUrl: "/portfolio"
    }),

    // Goal notifications
    goalProgress: () => addNotification({
      type: "goal",
      title: "Goal Progress",
      message: "You're 75% towards your emergency fund goal! Keep it up!",
      actionUrl: "/goals"
    }),

    goalAchieved: () => addNotification({
      type: "goal",
      title: "Goal Achieved! 🎉",
      message: "Congratulations! You've reached your $5,000 investment goal.",
      actionUrl: "/goals"
    }),

    // Market notifications
    marketNews: () => addNotification({
      type: "market",
      title: "Market Update",
      message: "Tech stocks rally as Q4 earnings exceed expectations",
    }),

    priceAlert: () => addNotification({
      type: "market",
      title: "Price Alert",
      message: "AAPL has reached your target price of $180.00",
    }),

    // Educational notifications
    newCourse: () => addNotification({
      type: "educational",
      title: "New Learning Content",
      message: "Check out our new course: 'Advanced Portfolio Strategies'",
      actionUrl: "/learn"
    }),

    courseComplete: () => addNotification({
      type: "educational",
      title: "Course Completed!",
      message: "You've completed 'Stock Market Basics' and earned 100 XP!",
      actionUrl: "/learn"
    }),

    // System notifications
    welcomeMessage: () => addNotification({
      type: "system",
      title: "Welcome to Investry!",
      message: "Complete your profile to get personalized investment recommendations.",
      actionUrl: "/onboarding"
    }),

    securityAlert: () => addNotification({
      type: "system",
      title: "Security Update",
      message: "Your account security settings have been updated successfully.",
      actionUrl: "/settings"
    })
  }
}
