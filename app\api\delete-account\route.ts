import { NextRequest, NextResponse } from 'next/server'
import { createUserClient, createAdminClient, isAdminConfigured } from '@/lib/supabase-clients'

// Check if service role key is configured
if (!isAdminConfigured()) {
  console.error('SUPABASE_SERVICE_ROLE_KEY is not configured in environment variables')
}

// Create clients using centralized utilities
const supabaseAdmin = createAdminClient()
const supabase = createUserClient()

export async function DELETE(request: NextRequest) {
  console.log('🗑️ DELETE /api/delete-account called')

  try {
    // Check if service role key is available
    if (!isAdminConfigured()) {
      console.error('❌ SUPABASE_SERVICE_ROLE_KEY not found in environment')
      return NextResponse.json(
        { error: 'Server configuration error: Service role key not configured' },
        { status: 500 }
      )
    }

    // Get the authorization header
    const authHeader = request.headers.get('authorization')
    console.log('🔑 Auth header present:', !!authHeader)

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log('❌ Invalid or missing auth header')
      return NextResponse.json(
        { error: 'Missing or invalid authorization header' },
        { status: 401 }
      )
    }

    const token = authHeader.split(' ')[1]
    console.log('🎫 Token extracted, length:', token.length)

    // Verify the user's JWT token
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      console.error('❌ Auth verification failed:', authError)
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    console.log('✅ User verified:', user.id)

    const userId = user.id

    // Parse request body for confirmation
    const body = await request.json()
    const { confirmationText } = body

    if (confirmationText !== 'DELETE') {
      return NextResponse.json(
        { error: 'Invalid confirmation text. Please type "DELETE" to confirm.' },
        { status: 400 }
      )
    }

    console.log(`Starting account deletion for user: ${userId}`)

    // List of tables to clean up (in order of dependencies)
    const tablesToClean = [
      'user_profiles',
      'user_settings', 
      'financial_goals',
      'linked_accounts',
      'transactions',
      'roundups',
      'ach_transfers',
      'wallet_transactions',
      'user_balances',
      'portfolio_performance_cache',
      'portfolio_performance_history',
      'financial_audit_log',
      'llm_generated_portfolios',
      'portfolio_cache',
      'llm_audit_log',
      'llm_api_usage'
    ]

    // Delete from each table (ignore errors for tables that don't exist)
    for (const table of tablesToClean) {
      try {
        const { error } = await supabaseAdmin
          .from(table)
          .delete()
          .eq('user_id', userId)
        
        if (error) {
          console.warn(`Could not delete from ${table}:`, error.message)
        } else {
          console.log(`Successfully cleaned up ${table}`)
        }
      } catch (error) {
        console.warn(`Error cleaning up ${table}:`, error)
      }
    }

    // Delete the auth user using admin client
    const { error: deleteError } = await supabaseAdmin.auth.admin.deleteUser(userId)
    
    if (deleteError) {
      console.error('Error deleting user account:', deleteError)
      return NextResponse.json(
        { error: `Failed to delete account: ${deleteError.message}` },
        { status: 500 }
      )
    }

    console.log(`Successfully deleted user account: ${userId}`)

    return NextResponse.json(
      { 
        success: true, 
        message: 'Account successfully deleted' 
      },
      { status: 200 }
    )

  } catch (error) {
    console.error('Unexpected error during account deletion:', error)
    return NextResponse.json(
      { error: 'An unexpected error occurred while deleting the account' },
      { status: 500 }
    )
  }
}

// Add a GET method for testing the route
export async function GET() {
  return NextResponse.json({
    message: 'Delete account API route is working',
    timestamp: new Date().toISOString(),
    serviceRoleConfigured: !!process.env.SUPABASE_SERVICE_ROLE_KEY
  })
}
