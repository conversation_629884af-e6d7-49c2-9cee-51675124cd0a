/**
 * Response Formatter
 * Standardizes API response formats and HTTP status codes
 */

import { NextResponse } from 'next/server'

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  code?: string
  message?: string
  timestamp: string
  metadata?: {
    cached?: boolean
    source?: string
    responseTime?: number
    pagination?: {
      page: number
      limit: number
      total: number
      hasMore: boolean
    }
  }
}

export interface ErrorDetails {
  code: string
  message: string
  details?: any
  retryable?: boolean
  statusCode?: number
}

export class ResponseFormatter {
  /**
   * Create a successful response
   */
  static success<T>(
    data: T,
    options: {
      message?: string
      cached?: boolean
      source?: string
      responseTime?: number
      headers?: Record<string, string>
      statusCode?: number
    } = {}
  ): NextResponse {
    const response: ApiResponse<T> = {
      success: true,
      data,
      timestamp: new Date().toISOString()
    }

    if (options.message) {
      response.message = options.message
    }

    if (options.cached !== undefined || options.source || options.responseTime !== undefined) {
      response.metadata = {
        cached: options.cached,
        source: options.source,
        responseTime: options.responseTime
      }
    }

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...options.headers
    }

    if (options.cached !== undefined) {
      headers['X-Cache-Status'] = options.cached ? 'HIT' : 'MISS'
    }

    if (options.source) {
      headers['X-Data-Source'] = options.source
    }

    return NextResponse.json(response, {
      status: options.statusCode || 200,
      headers
    })
  }

  /**
   * Create an error response
   */
  static error(
    error: ErrorDetails | string,
    options: {
      headers?: Record<string, string>
      details?: any
    } = {}
  ): NextResponse {
    let errorDetails: ErrorDetails

    if (typeof error === 'string') {
      errorDetails = {
        code: 'GENERIC_ERROR',
        message: error,
        statusCode: 500
      }
    } else {
      errorDetails = error
    }

    const response: ApiResponse = {
      success: false,
      error: errorDetails.message,
      code: errorDetails.code,
      timestamp: new Date().toISOString()
    }

    if (options.details || errorDetails.details) {
      response.metadata = {
        ...options.details,
        ...errorDetails.details
      }
    }

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...options.headers
    }

    if (errorDetails.retryable !== undefined) {
      headers['X-Retryable'] = errorDetails.retryable.toString()
    }

    return NextResponse.json(response, {
      status: errorDetails.statusCode || 500,
      headers
    })
  }

  /**
   * Create a paginated response
   */
  static paginated<T>(
    data: T[],
    pagination: {
      page: number
      limit: number
      total: number
    },
    options: {
      message?: string
      cached?: boolean
      source?: string
      headers?: Record<string, string>
    } = {}
  ): NextResponse {
    const hasMore = pagination.page * pagination.limit < pagination.total

    const response: ApiResponse<T[]> = {
      success: true,
      data,
      timestamp: new Date().toISOString(),
      metadata: {
        cached: options.cached,
        source: options.source,
        pagination: {
          ...pagination,
          hasMore
        }
      }
    }

    if (options.message) {
      response.message = options.message
    }

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'X-Total-Count': pagination.total.toString(),
      'X-Page': pagination.page.toString(),
      'X-Limit': pagination.limit.toString(),
      'X-Has-More': hasMore.toString(),
      ...options.headers
    }

    if (options.cached !== undefined) {
      headers['X-Cache-Status'] = options.cached ? 'HIT' : 'MISS'
    }

    if (options.source) {
      headers['X-Data-Source'] = options.source
    }

    return NextResponse.json(response, {
      status: 200,
      headers
    })
  }

  /**
   * Create a cached response with appropriate headers
   */
  static cached<T>(
    data: T,
    options: {
      maxAge?: number
      staleWhileRevalidate?: number
      source?: string
      lastModified?: string
      etag?: string
    } = {}
  ): NextResponse {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'X-Cache-Status': 'HIT'
    }

    if (options.maxAge !== undefined) {
      const cacheControl = [`public`, `max-age=${options.maxAge}`]
      if (options.staleWhileRevalidate) {
        cacheControl.push(`stale-while-revalidate=${options.staleWhileRevalidate}`)
      }
      headers['Cache-Control'] = cacheControl.join(', ')
    }

    if (options.source) {
      headers['X-Data-Source'] = options.source
    }

    if (options.lastModified) {
      headers['Last-Modified'] = options.lastModified
    }

    if (options.etag) {
      headers['ETag'] = options.etag
    }

    const response: ApiResponse<T> = {
      success: true,
      data,
      timestamp: new Date().toISOString(),
      metadata: {
        cached: true,
        source: options.source
      }
    }

    return NextResponse.json(response, {
      status: 200,
      headers
    })
  }

  /**
   * Handle common error types
   */
  static authRequired(message: string = 'Authentication required'): NextResponse {
    return this.error({
      code: 'AUTH_REQUIRED',
      message,
      statusCode: 401
    })
  }

  static forbidden(message: string = 'Access forbidden'): NextResponse {
    return this.error({
      code: 'FORBIDDEN',
      message,
      statusCode: 403
    })
  }

  static notFound(resource: string = 'Resource'): NextResponse {
    return this.error({
      code: 'NOT_FOUND',
      message: `${resource} not found`,
      statusCode: 404
    })
  }

  static badRequest(message: string, details?: any): NextResponse {
    return this.error({
      code: 'BAD_REQUEST',
      message,
      details,
      statusCode: 400
    })
  }

  static rateLimit(resetTime?: number): NextResponse {
    const headers: Record<string, string> = {}
    if (resetTime) {
      headers['X-RateLimit-Reset'] = resetTime.toString()
    }

    return this.error({
      code: 'RATE_LIMIT_EXCEEDED',
      message: 'Rate limit exceeded',
      retryable: true,
      statusCode: 429
    }, { headers })
  }

  static serviceUnavailable(service: string, retryable: boolean = true): NextResponse {
    return this.error({
      code: 'SERVICE_UNAVAILABLE',
      message: `${service} service is currently unavailable`,
      retryable,
      statusCode: 503
    })
  }

  static internalError(message: string = 'Internal server error', details?: any): NextResponse {
    return this.error({
      code: 'INTERNAL_ERROR',
      message,
      details,
      retryable: false,
      statusCode: 500
    })
  }

  /**
   * Validate and format timeframe parameter
   */
  static validateTimeframe(timeframe: string, validTimeframes: string[]): string | null {
    const normalized = timeframe.toUpperCase()
    return validTimeframes.includes(normalized) ? normalized : null
  }

  /**
   * Create response with performance metrics
   */
  static withMetrics<T>(
    data: T,
    startTime: number,
    options: {
      cached?: boolean
      source?: string
      operations?: number
      headers?: Record<string, string>
    } = {}
  ): NextResponse {
    const responseTime = Date.now() - startTime

    const headers: Record<string, string> = {
      'X-Response-Time': `${responseTime}ms`,
      ...options.headers
    }

    if (options.operations) {
      headers['X-Operations'] = options.operations.toString()
    }

    return this.success(data, {
      responseTime,
      cached: options.cached,
      source: options.source,
      headers
    })
  }
}

// Common error codes and messages
export const ErrorCodes = {
  // Authentication & Authorization
  AUTH_REQUIRED: 'Authentication required',
  AUTH_FAILED: 'Authentication failed',
  FORBIDDEN: 'Access forbidden',
  INSUFFICIENT_PERMISSIONS: 'Insufficient permissions',

  // Validation
  BAD_REQUEST: 'Bad request',
  INVALID_PARAMETER: 'Invalid parameter',
  MISSING_PARAMETER: 'Missing required parameter',
  INVALID_TIMEFRAME: 'Invalid timeframe',

  // Resources
  NOT_FOUND: 'Resource not found',
  PORTFOLIO_NOT_FOUND: 'Portfolio not found',
  USER_NOT_FOUND: 'User not found',

  // Rate Limiting
  RATE_LIMIT_EXCEEDED: 'Rate limit exceeded',

  // External Services
  SERVICE_UNAVAILABLE: 'Service unavailable',
  API_ERROR: 'External API error',
  PROVIDER_ERROR: 'Data provider error',

  // Internal
  INTERNAL_ERROR: 'Internal server error',
  DATABASE_ERROR: 'Database error',
  CALCULATION_ERROR: 'Calculation error'
} as const

export type ErrorCode = keyof typeof ErrorCodes
