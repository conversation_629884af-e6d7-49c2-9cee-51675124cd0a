/**
 * News Service
 * Dedicated service for fetching and managing stock news
 */

import { NewsArticle, StockNews } from './stock-data-service'

export interface NewsServiceConfig {
  finnhub?: {
    apiKey: string
    baseUrl: string
  }
}

export class NewsService {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>()
  private apiUsage = {
    finnhub: {
      minute: 0,
      lastReset: Date.now()
    }
  }

  constructor(private config: NewsServiceConfig) {}

  /**
   * Get news for a specific stock symbol
   */
  async getStockNews(symbol: string, limit: number = 10): Promise<StockNews | null> {
    const cacheKey = `news:${symbol}:${limit}`
    
    // Check cache first (15 minute TTL)
    const cached = this.getFromCache<StockNews>(cacheKey)
    if (cached) {
      return cached
    }

    // Try Finnhub
    if (this.canUseFinnhub()) {
      const news = await this.getFinnhubNews(symbol, limit)
      if (news) {
        this.setCache(cacheKey, news, 15 * 60 * 1000) // 15 minute cache
        return news
      }
    }

    return null
  }

  /**
   * Get general market news
   */
  async getMarketNews(category: string = 'general', limit: number = 20): Promise<NewsArticle[]> {
    const cacheKey = `market-news:${category}:${limit}`
    
    // Check cache first (30 minute TTL)
    const cached = this.getFromCache<NewsArticle[]>(cacheKey)
    if (cached) {
      return cached
    }

    // Try Finnhub
    if (this.canUseFinnhub()) {
      const news = await this.getFinnhubMarketNews(category, limit)
      if (news) {
        this.setCache(cacheKey, news, 30 * 60 * 1000) // 30 minute cache
        return news
      }
    }

    return []
  }

  /**
   * Finnhub company news
   */
  private async getFinnhubNews(symbol: string, limit: number): Promise<StockNews | null> {
    if (!this.config.finnhub?.apiKey) return null

    try {
      this.apiUsage.finnhub.minute++

      // Get news from last 7 days
      const toDate = new Date()
      const fromDate = new Date(toDate.getTime() - 7 * 24 * 60 * 60 * 1000)
      
      const response = await fetch(
        `${this.config.finnhub.baseUrl}/company-news?symbol=${symbol}&from=${fromDate.toISOString().split('T')[0]}&to=${toDate.toISOString().split('T')[0]}&token=${this.config.finnhub.apiKey}`
      )

      if (!response.ok) return null

      const newsData = await response.json()

      if (!Array.isArray(newsData)) return null

      const articles: NewsArticle[] = newsData
        .slice(0, limit)
        .map((item: any, index: number) => ({
          id: `${symbol}-${item.datetime}-${index}`,
          headline: item.headline || 'No headline available',
          summary: item.summary || 'No summary available',
          url: item.url || '#',
          source: item.source || 'Unknown',
          publishedAt: new Date(item.datetime * 1000).toISOString(),
          category: item.category || 'general',
          image: item.image,
          relatedSymbols: item.related ? item.related.split(',') : [symbol]
        }))
        .filter(article => article.headline && article.url !== '#')

      return {
        symbol,
        articles,
        lastUpdated: new Date().toISOString(),
        source: 'finnhub'
      }
    } catch (error) {
      console.error('Finnhub company news API error:', error)
      return null
    }
  }

  /**
   * Finnhub market news
   */
  private async getFinnhubMarketNews(category: string, limit: number): Promise<NewsArticle[]> {
    if (!this.config.finnhub?.apiKey) return null

    try {
      this.apiUsage.finnhub.minute++

      const response = await fetch(
        `${this.config.finnhub.baseUrl}/news?category=${category}&token=${this.config.finnhub.apiKey}`
      )

      if (!response.ok) return []

      const newsData = await response.json()

      if (!Array.isArray(newsData)) return []

      return newsData
        .slice(0, limit)
        .map((item: any, index: number) => ({
          id: `market-${item.datetime}-${index}`,
          headline: item.headline || 'No headline available',
          summary: item.summary || 'No summary available',
          url: item.url || '#',
          source: item.source || 'Unknown',
          publishedAt: new Date(item.datetime * 1000).toISOString(),
          category: item.category || category,
          image: item.image,
          relatedSymbols: item.related ? item.related.split(',') : []
        }))
        .filter(article => article.headline && article.url !== '#')
    } catch (error) {
      console.error('Finnhub market news API error:', error)
      return []
    }
  }

  /**
   * Cache management
   */
  private getFromCache<T>(key: string): T | null {
    const cached = this.cache.get(key)
    if (!cached) return null

    if (Date.now() > cached.timestamp + cached.ttl) {
      this.cache.delete(key)
      return null
    }

    return cached.data as T
  }

  private setCache(key: string, data: any, ttl: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }

  /**
   * Rate limiting
   */
  private canUseFinnhub(): boolean {
    const now = Date.now()
    const minuteAgo = now - 60 * 1000

    if (this.apiUsage.finnhub.lastReset < minuteAgo) {
      this.apiUsage.finnhub.minute = 0
      this.apiUsage.finnhub.lastReset = now
    }

    return this.apiUsage.finnhub.minute < 60 // Finnhub free tier limit
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear()
  }
}

// Singleton instance
let newsServiceInstance: NewsService | null = null

export function getNewsService(): NewsService {
  if (!newsServiceInstance) {
    const config: NewsServiceConfig = {
      finnhub: {
        apiKey: process.env.FINNHUB_API_KEY || '',
        baseUrl: 'https://finnhub.io/api/v1'
      }
    }
    newsServiceInstance = new NewsService(config)
  }
  return newsServiceInstance
}
