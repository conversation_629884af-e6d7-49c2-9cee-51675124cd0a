-- LLM Portfolio Generation System Database Schema
-- Run this script in Supabase SQL Editor to create the necessary tables

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create LLM prompts table to store all prompts sent to LLM
CREATE TABLE IF NOT EXISTS llm_prompts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  prompt_hash VARCHAR(64) NOT NULL, -- SHA-256 hash of the prompt for caching
  prompt_text TEXT NOT NULL,
  prompt_type VARCHAR(50) DEFAULT 'portfolio_generation',
  survey_data JSONB, -- Store the original survey data
  user_metadata JSONB, -- Store user context (major, experience, etc.)
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create LLM responses table to store all responses from LLM
CREATE TABLE IF NOT EXISTS llm_responses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  prompt_id UUID REFERENCES llm_prompts(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  response_text TEXT NOT NULL,
  response_tokens INTEGER, -- Number of tokens in response
  model_used VARCHAR(100), -- e.g., 'gpt-4', 'claude-3-sonnet'
  api_provider VARCHAR(50), -- e.g., 'openai', 'anthropic'
  processing_time_ms INTEGER, -- Time taken to get response
  success BOOLEAN DEFAULT TRUE,
  error_message TEXT, -- Store error if any
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create generated portfolios table to store parsed portfolio data
CREATE TABLE IF NOT EXISTS llm_generated_portfolios (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  response_id UUID NULL REFERENCES llm_responses(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  portfolio_data JSONB NOT NULL, -- Parsed portfolio structure
  risk_level VARCHAR(20),
  expected_return VARCHAR(20),
  strategy TEXT,
  rebalance_frequency VARCHAR(20),
  rationale TEXT,
  allocations JSONB, -- Array of allocation objects
  validation_status VARCHAR(20) DEFAULT 'pending', -- pending, valid, invalid
  validation_errors JSONB, -- Store validation issues
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create portfolio cache table for fast lookups
CREATE TABLE IF NOT EXISTS portfolio_cache (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  cache_key VARCHAR(128) NOT NULL UNIQUE, -- user_id + prompt_hash
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  prompt_hash VARCHAR(64) NOT NULL,
  portfolio_id UUID REFERENCES llm_generated_portfolios(id) ON DELETE CASCADE,
  hit_count INTEGER DEFAULT 0,
  last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '24 hours'),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create LLM audit logs for tracking all system interactions
CREATE TABLE IF NOT EXISTS llm_audit_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  action VARCHAR(100) NOT NULL, -- e.g., 'generate_portfolio', 'cache_hit', 'fallback_used'
  details JSONB, -- Store action-specific details
  ip_address INET,
  user_agent TEXT,
  api_endpoint VARCHAR(200),
  execution_time_ms INTEGER,
  success BOOLEAN DEFAULT TRUE,
  error_details JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create API usage tracking table
CREATE TABLE IF NOT EXISTS llm_api_usage (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  api_provider VARCHAR(50) NOT NULL,
  model_used VARCHAR(100),
  tokens_used INTEGER,
  cost_usd DECIMAL(10, 6), -- Track API costs
  request_type VARCHAR(50), -- e.g., 'completion', 'chat'
  success BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_llm_prompts_user_id ON llm_prompts(user_id);
CREATE INDEX IF NOT EXISTS idx_llm_prompts_hash ON llm_prompts(prompt_hash);
CREATE INDEX IF NOT EXISTS idx_llm_prompts_created_at ON llm_prompts(created_at);

CREATE INDEX IF NOT EXISTS idx_llm_responses_prompt_id ON llm_responses(prompt_id);
CREATE INDEX IF NOT EXISTS idx_llm_responses_user_id ON llm_responses(user_id);
CREATE INDEX IF NOT EXISTS idx_llm_responses_created_at ON llm_responses(created_at);

CREATE INDEX IF NOT EXISTS idx_llm_portfolios_user_id ON llm_generated_portfolios(user_id);
CREATE INDEX IF NOT EXISTS idx_llm_portfolios_active ON llm_generated_portfolios(is_active);
CREATE INDEX IF NOT EXISTS idx_llm_portfolios_created_at ON llm_generated_portfolios(created_at);

CREATE INDEX IF NOT EXISTS idx_portfolio_cache_key ON portfolio_cache(cache_key);
CREATE INDEX IF NOT EXISTS idx_portfolio_cache_user_id ON portfolio_cache(user_id);
CREATE INDEX IF NOT EXISTS idx_portfolio_cache_expires ON portfolio_cache(expires_at);

CREATE INDEX IF NOT EXISTS idx_llm_audit_user_id ON llm_audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_llm_audit_action ON llm_audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_llm_audit_created_at ON llm_audit_logs(created_at);

CREATE INDEX IF NOT EXISTS idx_llm_usage_user_id ON llm_api_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_llm_usage_provider ON llm_api_usage(api_provider);
CREATE INDEX IF NOT EXISTS idx_llm_usage_created_at ON llm_api_usage(created_at);

-- Create RLS (Row Level Security) policies
ALTER TABLE llm_prompts ENABLE ROW LEVEL SECURITY;
ALTER TABLE llm_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE llm_generated_portfolios ENABLE ROW LEVEL SECURITY;
ALTER TABLE portfolio_cache ENABLE ROW LEVEL SECURITY;
ALTER TABLE llm_audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE llm_api_usage ENABLE ROW LEVEL SECURITY;

-- RLS Policies - Users can only access their own data
CREATE POLICY "Users can view their own prompts" ON llm_prompts
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own responses" ON llm_responses
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own portfolios" ON llm_generated_portfolios
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own cache" ON portfolio_cache
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own audit logs" ON llm_audit_logs
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own usage" ON llm_api_usage
  FOR SELECT USING (auth.uid() = user_id);

-- Service role policies for backend operations
CREATE POLICY "Service role can manage all prompts" ON llm_prompts
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can manage all responses" ON llm_responses
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can manage all portfolios" ON llm_generated_portfolios
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can manage all cache" ON portfolio_cache
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can manage all audit logs" ON llm_audit_logs
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can manage all usage" ON llm_api_usage
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Create functions for cache cleanup
CREATE OR REPLACE FUNCTION cleanup_expired_cache()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM portfolio_cache WHERE expires_at < NOW();
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to get user's portfolio generation stats
CREATE OR REPLACE FUNCTION get_user_portfolio_stats(p_user_id UUID)
RETURNS TABLE (
  total_generations INTEGER,
  cache_hits INTEGER,
  fallback_uses INTEGER,
  avg_processing_time NUMERIC,
  last_generation TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(DISTINCT p.id)::INTEGER as total_generations,
    COUNT(CASE WHEN al.action = 'cache_hit' THEN 1 END)::INTEGER as cache_hits,
    COUNT(CASE WHEN al.action = 'fallback_used' THEN 1 END)::INTEGER as fallback_uses,
    AVG(r.processing_time_ms)::NUMERIC as avg_processing_time,
    MAX(p.created_at) as last_generation
  FROM llm_prompts p
  LEFT JOIN llm_responses r ON p.id = r.prompt_id
  LEFT JOIN llm_audit_logs al ON p.user_id = al.user_id
  WHERE p.user_id = p_user_id;
END;
$$ LANGUAGE plpgsql;

-- Add comments for documentation
COMMENT ON TABLE llm_prompts IS 'Stores all prompts sent to LLM for portfolio generation';
COMMENT ON TABLE llm_responses IS 'Stores all responses received from LLM APIs';
COMMENT ON TABLE llm_generated_portfolios IS 'Stores parsed and validated portfolio data from LLM responses';
COMMENT ON TABLE portfolio_cache IS 'Caches portfolio results to avoid redundant LLM API calls';
COMMENT ON TABLE llm_audit_logs IS 'Comprehensive audit trail for all LLM system interactions';
COMMENT ON TABLE llm_api_usage IS 'Tracks API usage and costs for monitoring and billing';
