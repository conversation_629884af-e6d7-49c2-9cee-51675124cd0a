/**
 * Cache Warming and Preloading System
 * Implements cache warming for frequently accessed data and preloading strategies
 */

import { getCacheManager, create<PERSON><PERSON><PERSON><PERSON> } from './cache-manager'
import { getBrowserCache } from './browser-cache'
import { getCacheConfig } from './cache-config'
import { getPortfolioPerformanceCalculator } from './portfolio-performance-calculator'
import { getHistoricalDataService } from './historical-data-service'

export interface CacheWarmingConfig {
  enabled: boolean
  popularSymbols: string[]
  commonPeriods: Array<'1D' | '1W' | '1M' | '3M' | '1Y'>
  portfolioConfigs: Array<{
    name: string
    allocations: Array<{ symbol: string; allocation: number }>
  }>
  warmingSchedule: {
    marketOpen: boolean
    marketClose: boolean
    interval: number // minutes
  }
}

export interface WarmingProgress {
  total: number
  completed: number
  failed: number
  inProgress: boolean
  lastRun: Date | null
  nextRun: Date | null
}

export class CacheWarmingService {
  private config: CacheWarmingConfig
  private cacheManager = getCacheManager()
  private browserCache = getBrowserCache()
  private cacheConfig = getCacheConfig()
  private warmingTimer: NodeJS.Timeout | null = null
  private progress: WarmingProgress = {
    total: 0,
    completed: 0,
    failed: 0,
    inProgress: false,
    lastRun: null,
    nextRun: null
  }

  constructor(config: Partial<CacheWarmingConfig> = {}) {
    this.config = {
      enabled: true,
      popularSymbols: [
        'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'NFLX',
        'JPM', 'BAC', 'V', 'MA', 'JNJ', 'PFE', 'KO', 'WMT', 'DIS', 'INTC'
      ],
      commonPeriods: ['1D', '1W', '1M', '3M', '1Y'],
      portfolioConfigs: [
        {
          name: 'Conservative',
          allocations: [
            { symbol: 'VTI', allocation: 40 },
            { symbol: 'BND', allocation: 30 },
            { symbol: 'VEA', allocation: 20 },
            { symbol: 'VWO', allocation: 10 }
          ]
        },
        {
          name: 'Balanced',
          allocations: [
            { symbol: 'VTI', allocation: 60 },
            { symbol: 'BND', allocation: 20 },
            { symbol: 'VEA', allocation: 15 },
            { symbol: 'VWO', allocation: 5 }
          ]
        },
        {
          name: 'Aggressive',
          allocations: [
            { symbol: 'VTI', allocation: 70 },
            { symbol: 'VEA', allocation: 20 },
            { symbol: 'VWO', allocation: 10 }
          ]
        }
      ],
      warmingSchedule: {
        marketOpen: true,
        marketClose: true,
        interval: 60 // 1 hour
      },
      ...config
    }

    if (this.config.enabled) {
      this.startWarmingSchedule()
    }
  }

  /**
   * Start the cache warming schedule
   */
  startWarmingSchedule(): void {
    if (this.warmingTimer) {
      clearInterval(this.warmingTimer)
    }

    // Schedule warming at regular intervals
    this.warmingTimer = setInterval(() => {
      this.performScheduledWarming()
    }, this.config.warmingSchedule.interval * 60 * 1000)

    // Initial warming
    this.performScheduledWarming()
  }

  /**
   * Stop the cache warming schedule
   */
  stopWarmingSchedule(): void {
    if (this.warmingTimer) {
      clearInterval(this.warmingTimer)
      this.warmingTimer = null
    }
  }

  /**
   * Perform scheduled cache warming
   */
  private async performScheduledWarming(): Promise<void> {
    const now = new Date()
    const hour = now.getHours()
    const day = now.getDay()

    // Check if we should warm based on market hours
    const isMarketHours = day >= 1 && day <= 5 && hour >= 9 && hour < 16
    const isMarketOpen = isMarketHours && this.config.warmingSchedule.marketOpen
    const isMarketClose = !isMarketHours && this.config.warmingSchedule.marketClose

    if (isMarketOpen || isMarketClose) {
      await this.warmFrequentlyAccessedData()
    }
  }

  /**
   * Warm frequently accessed data
   */
  async warmFrequentlyAccessedData(): Promise<WarmingProgress> {
    if (this.progress.inProgress) {
      return this.progress
    }

    this.progress = {
      total: 0,
      completed: 0,
      failed: 0,
      inProgress: true,
      lastRun: new Date(),
      nextRun: new Date(Date.now() + this.config.warmingSchedule.interval * 60 * 1000)
    }

    try {
      // Calculate total warming tasks
      const stockQuoteTasks = this.config.popularSymbols.length
      const historicalTasks = this.config.popularSymbols.length * this.config.commonPeriods.length
      const portfolioTasks = this.config.portfolioConfigs.length * this.config.commonPeriods.length
      const marketDataTasks = 3 // gainers, losers, most active

      this.progress.total = stockQuoteTasks + historicalTasks + portfolioTasks + marketDataTasks

      // Warm stock quotes
      await this.warmStockQuotes()

      // Warm historical data
      await this.warmHistoricalData()

      // Warm portfolio performance
      await this.warmPortfolioPerformance()

      // Warm market data
      await this.warmMarketData()

    } catch (error) {
      console.error('Cache warming error:', error)
    } finally {
      this.progress.inProgress = false
    }

    return this.progress
  }

  /**
   * Warm stock quotes for popular symbols
   */
  private async warmStockQuotes(): Promise<void> {
    const promises = this.config.popularSymbols.map(async (symbol) => {
      try {
        const response = await fetch(`/api/stocks/quote?symbol=${symbol}`)
        if (response.ok) {
          this.progress.completed++
        } else {
          this.progress.failed++
        }
      } catch (error) {
        this.progress.failed++
        console.warn(`Failed to warm quote for ${symbol}:`, error)
      }
    })

    await Promise.allSettled(promises)
  }

  /**
   * Warm historical data for popular symbols
   */
  private async warmHistoricalData(): Promise<void> {
    const historicalService = getHistoricalDataService()
    const promises: Promise<void>[] = []

    for (const symbol of this.config.popularSymbols) {
      for (const period of this.config.commonPeriods) {
        promises.push(
          historicalService.getHistoricalData(symbol, period, '1d')
            .then(() => this.progress.completed++)
            .catch(() => this.progress.failed++)
        )
      }
    }

    await Promise.allSettled(promises)
  }

  /**
   * Warm portfolio performance for common configurations
   */
  private async warmPortfolioPerformance(): Promise<void> {
    const calculator = getPortfolioPerformanceCalculator()
    const promises: Promise<void>[] = []

    for (const config of this.config.portfolioConfigs) {
      for (const period of this.config.commonPeriods) {
        promises.push(
          calculator.calculatePortfolioPerformance(config.allocations, period, '1d', 10000)
            .then(() => this.progress.completed++)
            .catch(() => this.progress.failed++)
        )
      }
    }

    await Promise.allSettled(promises)
  }

  /**
   * Warm market data (gainers, losers, most active)
   */
  private async warmMarketData(): Promise<void> {
    const endpoints = [
      '/api/stocks/gainers?limit=10',
      '/api/stocks/losers?limit=10',
      '/api/stocks/most-active?limit=10'
    ]

    const promises = endpoints.map(async (endpoint) => {
      try {
        const response = await fetch(endpoint)
        if (response.ok) {
          this.progress.completed++
        } else {
          this.progress.failed++
        }
      } catch (error) {
        this.progress.failed++
        console.warn(`Failed to warm market data ${endpoint}:`, error)
      }
    })

    await Promise.allSettled(promises)
  }

  /**
   * Preload data for a specific user's portfolio
   */
  async preloadUserPortfolio(allocations: Array<{ symbol: string; allocation: number }>): Promise<void> {
    const symbols = allocations.map(a => a.symbol)
    const calculator = getPortfolioPerformanceCalculator()

    // Preload stock quotes
    const quotePromises = symbols.map(symbol => 
      fetch(`/api/stocks/quote?symbol=${symbol}`).catch(() => {})
    )

    // Preload historical data for common periods
    const historicalPromises: Promise<any>[] = []
    const historicalService = getHistoricalDataService()
    
    for (const symbol of symbols) {
      for (const period of ['1D', '1W', '1M', '3M'] as const) {
        historicalPromises.push(
          historicalService.getHistoricalData(symbol, period, '1d').catch(() => {})
        )
      }
    }

    // Preload portfolio performance
    const portfolioPromises = this.config.commonPeriods.map(period =>
      calculator.calculatePortfolioPerformance(allocations, period, '1d', 10000).catch(() => {})
    )

    await Promise.allSettled([
      ...quotePromises,
      ...historicalPromises,
      ...portfolioPromises
    ])
  }

  /**
   * Get warming progress
   */
  getProgress(): WarmingProgress {
    return { ...this.progress }
  }

  /**
   * Update warming configuration
   */
  updateConfig(newConfig: Partial<CacheWarmingConfig>): void {
    this.config = { ...this.config, ...newConfig }
    
    if (this.config.enabled && !this.warmingTimer) {
      this.startWarmingSchedule()
    } else if (!this.config.enabled && this.warmingTimer) {
      this.stopWarmingSchedule()
    }
  }

  /**
   * Get warming configuration
   */
  getConfig(): CacheWarmingConfig {
    return { ...this.config }
  }

  /**
   * Manual cache warming trigger
   */
  async triggerManualWarming(): Promise<WarmingProgress> {
    return await this.warmFrequentlyAccessedData()
  }

  /**
   * Cleanup on destroy
   */
  destroy(): void {
    this.stopWarmingSchedule()
  }
}

// Singleton instance
let cacheWarmingServiceInstance: CacheWarmingService | null = null

export function getCacheWarmingService(): CacheWarmingService {
  if (!cacheWarmingServiceInstance) {
    cacheWarmingServiceInstance = new CacheWarmingService()
  }
  return cacheWarmingServiceInstance
}

// Utility functions for cache preloading
export async function preloadDashboardData(): Promise<void> {
  const warmingService = getCacheWarmingService()
  
  // Preload market movers
  const marketPromises = [
    fetch('/api/stocks/gainers?limit=10'),
    fetch('/api/stocks/losers?limit=10'),
    fetch('/api/stocks/most-active?limit=10')
  ]

  await Promise.allSettled(marketPromises)
}

export async function preloadStockPageData(symbol: string): Promise<void> {
  const historicalService = getHistoricalDataService()
  
  // Preload stock quote, financials, news, and historical data
  const promises = [
    fetch(`/api/stocks/quote?symbol=${symbol}`),
    fetch(`/api/stocks/financials?symbol=${symbol}`),
    fetch(`/api/stocks/news?symbol=${symbol}&limit=10`),
    historicalService.getHistoricalData(symbol, '1M', '1d'),
    historicalService.getHistoricalData(symbol, '3M', '1d'),
    historicalService.getHistoricalData(symbol, '1Y', '1d')
  ]

  await Promise.allSettled(promises.map(p => p.catch(() => {})))
}
