import { NextResponse } from 'next/server'
import Stripe from 'stripe'
import { createClient } from '@supabase/supabase-js'

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2023-10-16',
})

// Initialize Supabase
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''
)

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('session_id')
    
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      )
    }
    
    // Retrieve the checkout session from Stripe
    const session = await stripe.checkout.sessions.retrieve(sessionId, {
      expand: ['subscription', 'customer']
    })
    
    if (!session) {
      return NextResponse.json(
        { error: 'Invalid session ID' },
        { status: 404 }
      )
    }
    
    // Check if subscription was created successfully
    if (!session.subscription) {
      return NextResponse.json(
        { error: 'No subscription found for this session' },
        { status: 400 }
      )
    }
    
    const subscription = session.subscription as Stripe.Subscription
    const userId = session.metadata?.userId
    const planName = session.metadata?.planName || 'Unknown Plan'
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID not found in session metadata' },
        { status: 400 }
      )
    }
    
    // Record the subscription in your database
    try {
      // Check if subscription already exists
      const { data: existingSubscription } = await supabase
        .from('subscriptions')
        .select('id')
        .eq('stripe_subscription_id', subscription.id)
        .single()
      
      if (existingSubscription) {
        // Subscription already processed
        return NextResponse.json({
          success: true,
          planName,
          subscriptionId: subscription.id,
          status: subscription.status,
          trialEnd: subscription.trial_end ? new Date(subscription.trial_end * 1000).toISOString() : null,
          currentPeriodEnd: new Date(subscription.current_period_end * 1000).toISOString(),
          alreadyProcessed: true
        })
      }
      
      // Get the price information
      const priceId = subscription.items.data[0]?.price.id
      let planId = null
      
      if (priceId) {
        const { data: plan } = await supabase
          .from('subscription_plans')
          .select('id')
          .eq('stripe_price_id', priceId)
          .single()
        
        planId = plan?.id
      }
      
      // Insert subscription record
      const { error: subscriptionError } = await supabase
        .from('subscriptions')
        .insert({
          user_id: userId,
          plan_id: planId,
          stripe_subscription_id: subscription.id,
          stripe_customer_id: subscription.customer as string,
          price_id: priceId,
          status: subscription.status,
          current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
          current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
          cancel_at_period_end: subscription.cancel_at_period_end,
          created_at: new Date().toISOString()
        })
      
      if (subscriptionError) {
        console.error('Error recording subscription:', subscriptionError)
        // Don't fail the verification - webhook will handle this as backup
      }
      
      // Update user's subscription status or permissions if needed
      // This could involve updating a user_subscriptions table or user metadata
      
    } catch (dbError) {
      console.error('Database error during subscription verification:', dbError)
      // Continue with success response - webhook will handle database updates
    }
    
    return NextResponse.json({
      success: true,
      planName,
      subscriptionId: subscription.id,
      status: subscription.status,
      trialEnd: subscription.trial_end ? new Date(subscription.trial_end * 1000).toISOString() : null,
      currentPeriodStart: new Date(subscription.current_period_start * 1000).toISOString(),
      currentPeriodEnd: new Date(subscription.current_period_end * 1000).toISOString(),
      cancelAtPeriodEnd: subscription.cancel_at_period_end,
      customerId: subscription.customer
    })
    
  } catch (error) {
    console.error('Subscription verification error:', error)
    return NextResponse.json(
      { error: 'Failed to verify subscription' },
      { status: 500 }
    )
  }
}
