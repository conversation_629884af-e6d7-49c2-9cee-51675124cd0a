"use client"

import { useRouter, usePathname } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Wallet, Bell, Settings, Zap, HelpCircle, Crown } from "lucide-react"
import { SearchTrigger } from "./global-search"
import { NotificationsDropdown } from "./notifications-dropdown"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { useSubscription } from "@/hooks/use-subscription"

interface TopNavProps {
  currentPage?: "dashboard" | "investments" | "charts" | "learn" | "profile" | "stock"
}

export function TopNav({ currentPage }: TopNavProps) {
  const router = useRouter()
  const pathname = usePathname()
  const subscription = useSubscription()



  const navItems = [
    { id: "dashboard", label: "Dashboard", path: "/dashboard" },
    { id: "investments", label: "Investments", path: "/investments" },
    { id: "charts", label: "Charts", path: "/charts" },
    { id: "learn", label: "Learn", path: "/learn" },
    { id: "profile", label: "Profile", path: "/profile" },
  ]

  const isActive = (itemId: string) => {
    if (currentPage) return currentPage === itemId
    return pathname === navItems.find((item) => item.id === itemId)?.path
  }

  return (
    <TooltipProvider>
      <nav className="border-b border-slate-700 bg-slate-800/50 backdrop-blur-sm sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4">
          <div className="relative flex items-center">
            {/* Left section: Logo and Search */}
            <div className="flex items-center gap-6">
              <button
                onClick={() => router.push("/dashboard")}
                className="flex items-center gap-2 hover:opacity-80 transition-opacity"
              >
                <div className="relative">
                  <Wallet className="h-8 w-8 text-emerald-400" />
                </div>
                <span className="text-xl font-bold bg-gradient-to-r from-emerald-400 to-blue-400 bg-clip-text text-transparent">
                  investry
                </span>
              </button>
              <div className="max-w-56">
                <SearchTrigger placeholder="Search investry..." />
              </div>
            </div>

            {/* Center section: Navigation tabs - absolutely centered */}
            <div className="absolute left-1/2 transform -translate-x-1/2 flex items-center gap-6">
              {navItems.map((item) => (
                <Button
                  key={item.id}
                  variant="ghost"
                  size="sm"
                  onClick={() => router.push(item.path)}
                  className={`text-slate-300 hover:text-white hover-lift transition-colors ${
                    isActive(item.id) ? "text-emerald-400 bg-emerald-400/10" : ""
                  }`}
                >
                  {item.label}
                </Button>
              ))}
            </div>

            {/* Right section: Actions */}
            <div className="ml-auto flex items-center gap-3">
              <NotificationsDropdown />
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => router.push("/help")}
                    className="text-slate-300 hover:text-white hover-lift"
                  >
                    <HelpCircle className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Help & Support</p>
                </TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => router.push("/settings")}
                    className="text-slate-300 hover:text-white hover-lift"
                  >
                    <Settings className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Settings</p>
                </TooltipContent>
              </Tooltip>

              {/* Premium Badge */}
              {subscription.hasActiveSubscription && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => router.push("/subscription/manage")}
                      className="text-yellow-400 hover:text-yellow-300 hover:bg-yellow-400/10 hover-lift transition-all duration-200"
                    >
                      <Crown className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Premium Active - Manage Subscription</p>
                  </TooltipContent>
                </Tooltip>
              )}

              <Button
                size="sm"
                onClick={() => router.push("/payments")}
                className="bg-transparent border border-slate-600 text-white hover:bg-slate-700 hover-lift transition-all duration-200"
              >
                Add Funds
              </Button>
              <Button
                size="sm"
                onClick={() => router.push("/investments")}
                className="bg-emerald-500 hover:bg-emerald-600 text-white hover-lift transition-all duration-200 flex items-center gap-1"
              >
                <Zap className="h-4 w-4" />
                Invest Now
              </Button>
            </div>
          </div>
        </div>
      </nav>
    </TooltipProvider>
  )
}
