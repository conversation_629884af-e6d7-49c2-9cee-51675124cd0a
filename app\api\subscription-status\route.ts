import { NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import Stripe from 'stripe'

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2023-10-16',
})

// Initialize Supabase
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''
)

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id')
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }
    
    // Get user's active subscription from database
    const { data: subscription, error } = await supabase
      .from('subscriptions')
      .select(`
        id,
        stripe_subscription_id,
        status,
        current_period_start,
        current_period_end,
        cancel_at_period_end,
        canceled_at,
        subscription_plans (
          name,
          description,
          amount,
          currency,
          interval
        )
      `)
      .eq('user_id', userId)
      .eq('status', 'active')
      .single()
    
    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error fetching subscription:', error)
      return NextResponse.json(
        { error: 'Failed to fetch subscription status' },
        { status: 500 }
      )
    }
    
    // If no active subscription found, user is on free plan
    if (!subscription) {
      return NextResponse.json({
        hasActiveSubscription: false,
        plan: 'free',
        planName: 'Investry Free',
        status: 'free'
      })
    }
    
    // Get additional details from Stripe if needed
    let stripeSubscription = null
    if (subscription.stripe_subscription_id) {
      try {
        stripeSubscription = await stripe.subscriptions.retrieve(subscription.stripe_subscription_id)
      } catch (stripeError) {
        console.error('Error fetching Stripe subscription:', stripeError)
        // Continue without Stripe data
      }
    }
    
    // Determine if subscription is in trial
    const now = new Date()
    const periodEnd = new Date(subscription.current_period_end)
    const isTrialing = stripeSubscription?.status === 'trialing'
    const trialEnd = stripeSubscription?.trial_end ? new Date(stripeSubscription.trial_end * 1000) : null
    
    return NextResponse.json({
      hasActiveSubscription: true,
      plan: 'premium',
      planName: subscription.subscription_plans?.name || 'Investry Premium',
      status: subscription.status,
      currentPeriodStart: subscription.current_period_start,
      currentPeriodEnd: subscription.current_period_end,
      cancelAtPeriodEnd: subscription.cancel_at_period_end,
      canceledAt: subscription.canceled_at,
      isTrialing,
      trialEnd: trialEnd?.toISOString(),
      amount: subscription.subscription_plans?.amount,
      currency: subscription.subscription_plans?.currency,
      interval: subscription.subscription_plans?.interval,
      stripeSubscriptionId: subscription.stripe_subscription_id,
      nextBillingDate: periodEnd.toISOString(),
      daysUntilRenewal: Math.ceil((periodEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
    })
    
  } catch (error) {
    console.error('Subscription status API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
