import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function POST(request: NextRequest) {
  try {
    // Get user from auth token in header
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authentication required - missing token' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')

    // Create client for token validation (using anon key)
    const authClient = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    )

    // Verify the token and get user
    const { data: { user }, error: authError } = await authClient.auth.getUser(token)

    if (authError || !user) {
      console.error('Auth error:', authError)
      return NextResponse.json(
        { error: 'Authentication failed' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { portfolio, surveyData } = body

    if (!portfolio || !portfolio.allocations) {
      return NextResponse.json(
        { error: 'Invalid portfolio data' },
        { status: 400 }
      )
    }

    // Create service role client for database operations
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )

    // Check if user already has a portfolio
    const { data: existingPortfolio } = await supabase
      .from('portfolios')
      .select('id')
      .eq('user_id', user.id)
      .single()

    let portfolioRecord

    if (existingPortfolio) {
      // User already has a portfolio, update it instead
      const { data: updatedPortfolio, error: updateError } = await supabase
        .from('portfolios')
        .update({
          name: 'My Investment Portfolio',
          description: `Personalized portfolio based on ${portfolio.strategy}`,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', user.id)
        .select('id')
        .single()

      if (updateError) {
        console.error('Error updating portfolio:', updateError)
        return NextResponse.json(
          { error: 'Failed to update portfolio' },
          { status: 500 }
        )
      }
      portfolioRecord = updatedPortfolio
    } else {
      // Create new portfolio
      const { data: newPortfolio, error: portfolioError } = await supabase
        .from('portfolios')
        .insert({
          user_id: user.id,
          name: 'My Investment Portfolio',
          description: `Personalized portfolio based on ${portfolio.strategy}`,
          total_value: 0, // Will be updated when user adds funds
          initial_value: 0, // Start at $0, will increase when user adds funds
          is_default: true
        })
        .select('id')
        .single()

      if (portfolioError) {
        console.error('Error creating portfolio:', portfolioError)
        return NextResponse.json(
          { error: 'Failed to create portfolio' },
          { status: 500 }
        )
      }
      portfolioRecord = newPortfolio
    }

    // Create portfolio holdings based on allocations
    if (portfolio.allocations && Array.isArray(portfolio.allocations)) {
      for (const allocation of portfolio.allocations) {
        // Handle both LLM format (percentage) and rule-based format (allocation)
        const percentage = allocation.percentage || allocation.allocation
        if (allocation.symbol && percentage > 0) {
          const { error: holdingError } = await supabase
            .from('portfolio_holdings')
            .upsert({
              portfolio_id: portfolioRecord.id,
              symbol: allocation.symbol,
              shares: 0, // No shares initially since user has no funds
              average_cost: 0,
              current_price: 0,
              target_percentage: percentage, // Store target allocation percentage
              unrealized_gain_loss: 0,
              unrealized_gain_loss_percent: 0,
              last_price_update: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }, {
              onConflict: 'portfolio_id,symbol'
            })

          if (holdingError) {
            console.error(`Error creating holding for ${allocation.symbol}:`, holdingError)
            // Don't fail the request if individual holding creation fails
          } else {
            console.log(`Created holding for ${allocation.symbol} with ${percentage}% allocation`)
          }
        }
      }
    }

    // Initialize portfolio performance history
    const { error: performanceError } = await supabase
      .from('portfolio_performance_history')
      .upsert({
        portfolio_id: portfolioRecord.id,
        date: new Date().toISOString().split('T')[0], // Today's date
        total_value: 0,
        daily_return: 0,
        daily_return_percent: 0,
        total_return: 0,
        total_return_percent: 0
      }, {
        onConflict: 'portfolio_id,date'
      })

    if (performanceError) {
      console.error('Error initializing portfolio performance:', performanceError)
      // Don't fail the request if performance initialization fails
    }

    // Initialize portfolio performance cache
    const initialCacheData = {
      total_value: 0,
      daily_return: 0,
      daily_return_percent: 0,
      total_return: 0,
      total_return_percent: 0,
      last_updated: new Date().toISOString(),
      holdings_count: portfolio.allocations?.length || 0
    }

    const { error: cacheError } = await supabase
      .from('portfolio_performance_cache')
      .upsert({
        portfolio_id: portfolioRecord.id,
        cache_key: 'daily_performance',
        cache_data: initialCacheData,
        expires_at: new Date(Date.now() + 60 * 1000).toISOString() // 1 minute TTL
      }, {
        onConflict: 'portfolio_id,cache_key'
      })

    if (cacheError) {
      console.error('Error initializing portfolio cache:', cacheError)
      // Don't fail the request if cache initialization fails
    }

    // Save the LLM-generated portfolio data
    const { error: llmPortfolioError } = await supabase
      .from('llm_generated_portfolios')
      .insert({
        user_id: user.id,
        response_id: null, // Explicitly set to null for accepted portfolios
        portfolio_data: portfolio,
        risk_level: portfolio.riskLevel,
        expected_return: portfolio.expectedReturn,
        strategy: portfolio.strategy,
        rebalance_frequency: portfolio.rebalanceFrequency,
        rationale: portfolio.rationale,
        allocations: portfolio.allocations,
        validation_status: 'valid',
        is_active: true
      })

    if (llmPortfolioError) {
      console.error('Error saving LLM portfolio:', llmPortfolioError)
      // Don't fail the request if LLM portfolio save fails, but log the detailed error
      console.error('LLM Portfolio Error Details:', JSON.stringify(llmPortfolioError, null, 2))
    }

    // Update user profile to mark onboarding as completed
    if (surveyData) {
      const { error: profileError } = await supabase
        .from('user_profiles')
        .upsert({
          id: user.id,
          email: user.email,
          major: surveyData.major,
          risk_tolerance: portfolio.riskLevel?.toLowerCase(),
          investment_goals: [surveyData.primaryGoal],
          monthly_budget: surveyData.monthlyInvestment,
          onboarding_completed: true
        })

      if (profileError) {
        console.error('Error updating user profile:', profileError)
        // Don't fail the request if profile update fails
      }
    }

    // Initialize user balance if it doesn't exist (start at $0)
    const { error: balanceError } = await supabase
      .from('user_balances')
      .upsert({
        user_id: user.id,
        balance: 0, // Start at $0 - user must deposit funds
        currency: 'USD',
        balance_type: 'cash',
        available_for_investment: 0,
        total_invested: 0
      })

    if (balanceError) {
      console.error('Error initializing user balance:', balanceError)
      // Don't fail the request if balance initialization fails
    }

    return NextResponse.json({
      success: true,
      portfolioId: portfolioRecord.id,
      message: 'Portfolio accepted and saved successfully'
    })

  } catch (error) {
    console.error('Error accepting portfolio:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
