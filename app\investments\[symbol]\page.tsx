"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { useAuth } from "@/components/auth-provider"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { LoadingSkeleton } from "@/components/loading-skeleton"
import { GlobalSearch } from "@/components/global-search"
import { TopNav } from "@/components/top-nav"
import {
  TrendingUp,
  TrendingDown,
  BarChart3,
  Info,
  ArrowLeft,
  Plus,
  Minus,
  RefreshCw,
} from "lucide-react"
import { getRelatedTags } from "@/lib/stock-classifier"
import { useStockQuote, useStockFinancials, useStockNews, useHistoricalData } from "@/hooks/use-stock-data"
import { <PERSON>DataAdapter } from "@/lib/chart-data-adapters"
import { getChartOptimizer } from "@/lib/chart-performance-optimizer"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Area, AreaChart } from 'recharts'

export default function StockDetailPage() {
  const { user, loading } = useAuth()
  const params = useParams()
  const router = useRouter()
  const symbol = params.symbol as string
  const [showSearch, setShowSearch] = useState(false)
  const [searchCategory, setSearchCategory] = useState("")
  const [relatedTags, setRelatedTags] = useState<string[]>([])

  // Use our new stock data hook for real-time data
  const { data: stock, loading: dataLoading, error: stockError, refetch } = useStockQuote(
    symbol ? symbol.toUpperCase() : '',
    true // auto-refresh
  )

  // Use financial data and news hooks
  const { data: financials, loading: financialsLoading, error: financialsError, refetch: refetchFinancials } = useStockFinancials(
    symbol ? symbol.toUpperCase() : ''
  )
  const { data: news, loading: newsLoading, error: newsError, refetch: refetchNews } = useStockNews(
    symbol ? symbol.toUpperCase() : '',
    10
  )

  // Use historical data hook for charts
  const { data: historicalData, loading: chartLoading, error: chartError, refetch: refetchChart } = useHistoricalData(
    symbol ? symbol.toUpperCase() : '',
    '1M',
    '1d'
  )

  useEffect(() => {
    // Don't do anything while auth is loading
    if (loading) return

    // Check if user is authenticated
    const isAuthenticated = localStorage.getItem("investry_auth")
    if (!user && !isAuthenticated) {
      router.push("/auth/signin")
      return
    }
  }, [user, loading, router])

  // Set related tags when stock data loads
  useEffect(() => {
    if (stock) {
      setRelatedTags(getRelatedTags(stock.sector || stock.name || 'General'))
    }
  }, [stock])

  const handleTagClick = (tag: string) => {
    setSearchCategory(tag)
    setShowSearch(true)
  }

  const handleBuyStock = () => {
    // Implement buy functionality
    console.log("Buy stock:", stock?.symbol)
  }

  const handleSellStock = () => {
    // Implement sell functionality
    console.log("Sell stock:", stock?.symbol)
  }

  // Show loading while auth is loading or data is loading
  if (loading || dataLoading) {
    return (
      <div className="min-h-screen bg-slate-900 text-white">
        <TopNav currentPage="stock" />
        <div className="container mx-auto px-4 py-6">
          <LoadingSkeleton />
        </div>
      </div>
    )
  }

  if (stockError || !stock) {
    return (
      <div className="min-h-screen bg-slate-900 text-white">
        <TopNav currentPage="stock" />
        <div className="container mx-auto px-4 py-6">
          <Alert variant="destructive">
            <Info className="h-4 w-4" />
            <AlertDescription>{stockError || "Stock not found"}</AlertDescription>
          </Alert>
          <div className="flex gap-2 mt-4">
            <Button onClick={() => router.push("/investments")} variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Investments
            </Button>
            <Button onClick={refetch} variant="outline">
              Try Again
            </Button>
          </div>
        </div>
      </div>
    )
  }

  const isPositive = stock.change >= 0

  return (
    <div className="min-h-screen bg-slate-900 text-white">
      <TopNav currentPage="stock" />

      <div className="container mx-auto px-4 py-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <Button onClick={() => router.push("/investments")} variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Investments
          </Button>
          <div className="flex gap-2">
            <Button onClick={refetch} variant="outline" size="sm" disabled={dataLoading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${dataLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button onClick={handleBuyStock} className="bg-emerald-500 hover:bg-emerald-600">
              <Plus className="h-4 w-4 mr-2" />
              Buy
            </Button>
            <Button onClick={handleSellStock} variant="outline">
              <Minus className="h-4 w-4 mr-2" />
              Sell
            </Button>
          </div>
        </div>

        {/* Stock Overview */}
        <Card>
          <CardHeader>
            <div className="flex items-start justify-between">
              <div>
                <CardTitle className="text-2xl">{stock.symbol}</CardTitle>
                <CardDescription className="text-lg">{stock.name}</CardDescription>
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold">${stock.price.toFixed(2)}</div>
                <div className={`flex items-center gap-1 ${isPositive ? "text-emerald-400" : "text-red-400"}`}>
                  {isPositive ? <TrendingUp className="h-4 w-4" /> : <TrendingDown className="h-4 w-4" />}
                  <span>
                    {isPositive ? "+" : ""}
                    {stock.change.toFixed(2)} ({stock.changePercent.toFixed(2)}%)
                  </span>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Key Metrics */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <div className="text-sm text-muted-foreground">Volume</div>
                <div className="font-medium">{stock.volume.toLocaleString()}</div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Market Cap</div>
                <div className="font-medium">
                  {stock.marketCap ? `$${(stock.marketCap / 1000000000).toFixed(1)}B` : 'N/A'}
                </div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Sector</div>
                <div className="font-medium">{stock.sector || 'N/A'}</div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Exchange</div>
                <div className="font-medium">
                  {stock.exchange === 'NMS' ? 'NASDAQ' :
                   stock.exchange === 'PCX' ? 'NYSE Arca' :
                   stock.exchange === 'NYQ' ? 'NYSE' :
                   stock.exchange === 'ASE' ? 'NYSE American' :
                   stock.exchange || 'N/A'}
                </div>
              </div>
            </div>

            {/* Stock Info */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <div className="text-sm text-muted-foreground">Open</div>
                <div className="font-medium">${stock.open?.toFixed(2) || 'N/A'}</div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">High</div>
                <div className="font-medium">${stock.high?.toFixed(2) || 'N/A'}</div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Low</div>
                <div className="font-medium">${stock.low?.toFixed(2) || 'N/A'}</div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Prev Close</div>
                <div className="font-medium">${stock.previousClose?.toFixed(2) || 'N/A'}</div>
              </div>
            </div>

            {/* Data Source & Last Updated */}
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <div>
                Data Source: <span className="font-medium">{stock.source || 'Unknown'}</span>
              </div>
              <div>
                Last Updated: <span className="font-medium">
                  {stock.lastUpdated ? new Date(stock.lastUpdated).toLocaleTimeString() : 'N/A'}
                </span>
              </div>
            </div>

            {/* Related Tags */}
            {relatedTags.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-muted-foreground mb-2">Related Categories</h4>
                <div className="flex flex-wrap gap-2">
                  {relatedTags.map((tag, index) => (
                    <Badge
                      key={index}
                      variant="secondary"
                      className="cursor-pointer hover:bg-accent transition-colors"
                      onClick={() => handleTagClick(tag)}
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Detailed Information Tabs */}
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="financials">Financials</TabsTrigger>
            <TabsTrigger value="news">News</TabsTrigger>
            <TabsTrigger value="analysis">Analysis</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Stock Performance</CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={refetchChart}
                    disabled={chartLoading}
                  >
                    <RefreshCw className={`h-4 w-4 ${chartLoading ? 'animate-spin' : ''}`} />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {chartLoading ? (
                  <div className="h-64 flex items-center justify-center">
                    <div className="text-center">
                      <RefreshCw className="h-8 w-8 mb-4 mx-auto animate-spin text-muted-foreground" />
                      <p className="text-muted-foreground">Loading chart data...</p>
                    </div>
                  </div>
                ) : chartError ? (
                  <div className="h-64 flex items-center justify-center">
                    <div className="text-center">
                      <BarChart3 className="h-12 w-12 mb-4 mx-auto text-muted-foreground" />
                      <p className="text-muted-foreground mb-2">{chartError}</p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={refetchChart}
                      >
                        Try Again
                      </Button>
                    </div>
                  </div>
                ) : historicalData && historicalData.data.length > 0 ? (
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart data={ChartDataAdapter.toAreaChart(historicalData)}>
                        <defs>
                          <linearGradient id="colorPrice" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.3}/>
                            <stop offset="95%" stopColor="#3b82f6" stopOpacity={0}/>
                          </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                        <XAxis
                          dataKey="date"
                          stroke="#9ca3af"
                          fontSize={12}
                          tickFormatter={(value) => {
                            const date = new Date(value)
                            return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
                          }}
                        />
                        <YAxis
                          stroke="#9ca3af"
                          fontSize={12}
                          tickFormatter={(value) => `$${value.toFixed(2)}`}
                        />
                        <Tooltip
                          contentStyle={{
                            backgroundColor: '#1f2937',
                            border: '1px solid #374151',
                            borderRadius: '6px',
                            color: '#f9fafb'
                          }}
                          formatter={(value: number) => [`$${value.toFixed(2)}`, 'Price']}
                          labelFormatter={(label) => {
                            const date = new Date(label)
                            return date.toLocaleDateString('en-US', {
                              weekday: 'long',
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            })
                          }}
                        />
                        <Area
                          type="monotone"
                          dataKey="value"
                          stroke="#3b82f6"
                          strokeWidth={2}
                          fillOpacity={1}
                          fill="url(#colorPrice)"
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>
                ) : (
                  <div className="h-64 flex items-center justify-center text-muted-foreground">
                    <div className="text-center">
                      <BarChart3 className="h-12 w-12 mb-4 mx-auto" />
                      <p>No chart data available</p>
                      <p className="text-sm">Historical data not found for this symbol</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="financials" className="space-y-4">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Financial Metrics</CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={refetchFinancials}
                    disabled={financialsLoading}
                  >
                    <RefreshCw className={`h-4 w-4 ${financialsLoading ? 'animate-spin' : ''}`} />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {financialsLoading ? (
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {Array.from({ length: 12 }).map((_, i) => (
                      <div key={i} className="space-y-2">
                        <div className="h-4 bg-slate-700 rounded animate-pulse"></div>
                        <div className="h-6 bg-slate-600 rounded animate-pulse"></div>
                      </div>
                    ))}
                  </div>
                ) : financialsError ? (
                  <Alert variant="destructive">
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                      {financialsError}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={refetchFinancials}
                        className="ml-2"
                      >
                        Retry
                      </Button>
                    </AlertDescription>
                  </Alert>
                ) : financials ? (
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {/* Valuation Metrics */}
                    <div>
                      <div className="text-sm text-muted-foreground">P/E Ratio</div>
                      <div className="text-lg font-semibold">
                        {financials.peRatio ? financials.peRatio.toFixed(2) : 'N/A'}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">EPS</div>
                      <div className="text-lg font-semibold">
                        {financials.eps ? `$${financials.eps.toFixed(2)}` : 'N/A'}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">Dividend Yield</div>
                      <div className="text-lg font-semibold">
                        {financials.dividendYield ? `${financials.dividendYield.toFixed(2)}%` : 'N/A'}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">52W High</div>
                      <div className="text-lg font-semibold">
                        {financials.week52High ? `$${financials.week52High.toFixed(2)}` : 'N/A'}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">52W Low</div>
                      <div className="text-lg font-semibold">
                        {financials.week52Low ? `$${financials.week52Low.toFixed(2)}` : 'N/A'}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">Beta</div>
                      <div className="text-lg font-semibold">
                        {financials.beta ? financials.beta.toFixed(2) : 'N/A'}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">Market Cap</div>
                      <div className="text-lg font-semibold">
                        {financials.marketCap ? `$${(financials.marketCap / 1000000000).toFixed(2)}B` : 'N/A'}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">P/B Ratio</div>
                      <div className="text-lg font-semibold">
                        {financials.priceToBook ? financials.priceToBook.toFixed(2) : 'N/A'}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">ROE</div>
                      <div className="text-lg font-semibold">
                        {financials.roe ? `${financials.roe.toFixed(2)}%` : 'N/A'}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">Debt/Equity</div>
                      <div className="text-lg font-semibold">
                        {financials.debtToEquity ? financials.debtToEquity.toFixed(2) : 'N/A'}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">Current Ratio</div>
                      <div className="text-lg font-semibold">
                        {financials.currentRatio ? financials.currentRatio.toFixed(2) : 'N/A'}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">Gross Margin</div>
                      <div className="text-lg font-semibold">
                        {financials.grossMargin ? `${financials.grossMargin.toFixed(2)}%` : 'N/A'}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center text-muted-foreground py-8">
                    <Info className="h-12 w-12 mb-4 mx-auto" />
                    <p>No financial data available for this symbol</p>
                    <Button
                      variant="outline"
                      onClick={refetchFinancials}
                      className="mt-2"
                    >
                      Try Again
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="news" className="space-y-4">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Latest News</CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={refetchNews}
                    disabled={newsLoading}
                  >
                    <RefreshCw className={`h-4 w-4 ${newsLoading ? 'animate-spin' : ''}`} />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {newsLoading ? (
                  <div className="space-y-4">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <div key={i} className="border-l-4 border-slate-600 pl-4 space-y-2">
                        <div className="h-5 bg-slate-700 rounded animate-pulse"></div>
                        <div className="h-4 bg-slate-600 rounded animate-pulse w-3/4"></div>
                        <div className="h-3 bg-slate-600 rounded animate-pulse w-1/4"></div>
                      </div>
                    ))}
                  </div>
                ) : newsError ? (
                  <Alert variant="destructive">
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                      {newsError}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={refetchNews}
                        className="ml-2"
                      >
                        Retry
                      </Button>
                    </AlertDescription>
                  </Alert>
                ) : news && news.articles.length > 0 ? (
                  <div className="space-y-4">
                    {news.articles.map((article, index) => {
                      const borderColors = [
                        'border-emerald-400',
                        'border-blue-400',
                        'border-orange-400',
                        'border-purple-400',
                        'border-pink-400'
                      ]
                      const borderColor = borderColors[index % borderColors.length]

                      return (
                        <div key={article.id} className={`border-l-4 ${borderColor} pl-4 hover:bg-slate-800/50 rounded-r transition-colors cursor-pointer`}>
                          <a
                            href={article.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="block"
                          >
                            <h4 className="font-medium hover:text-blue-400 transition-colors">
                              {article.headline}
                            </h4>
                            <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                              {article.summary}
                            </p>
                            <div className="flex items-center justify-between mt-2">
                              <p className="text-xs text-muted-foreground">
                                {new Date(article.publishedAt).toLocaleDateString()} • {article.source}
                              </p>
                              <Badge variant="outline" className="text-xs">
                                {article.category}
                              </Badge>
                            </div>
                          </a>
                        </div>
                      )
                    })}
                  </div>
                ) : (
                  <div className="text-center text-muted-foreground py-8">
                    <Info className="h-12 w-12 mb-4 mx-auto" />
                    <p>No news available for this symbol</p>
                    <Button
                      variant="outline"
                      onClick={refetchNews}
                      className="mt-2"
                    >
                      Try Again
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analysis" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Investment Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-emerald-400 mb-2">Strengths</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• Strong market position in growing sector</li>
                      <li>• Consistent revenue growth over past 5 years</li>
                      <li>• Solid balance sheet with low debt levels</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-medium text-red-400 mb-2">Risks</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• Increased competition in core markets</li>
                      <li>• Regulatory challenges in key regions</li>
                      <li>• Dependence on key product lines</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-medium text-blue-400 mb-2">Opportunities</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• Expansion into emerging markets</li>
                      <li>• New technology adoption</li>
                      <li>• Strategic partnerships and acquisitions</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Search Dialog */}
        <GlobalSearch
          isOpen={showSearch}
          onClose={() => setShowSearch(false)}
          initialCategory={searchCategory}
          relatedTags={relatedTags}
        />
      </div>
    </div>
  )
}
