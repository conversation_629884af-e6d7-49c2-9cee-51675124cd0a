/**
 * Historical Data Service
 * Fetches historical stock data from all API tiers with intelligent fallbacks and centralized caching
 */

import { getApiConfig, getApiFallbackChain } from './api-tier-config'
import { getPolygonService } from './polygon-service'
import { getTwelveDataService } from './twelve-data-service'
import { getCacheManager, createCacheKey } from './cache-manager'
import { getCacheTTL, getMarketAwareCacheTTL, getCacheTagsForDataType } from './cache-config'

export interface HistoricalDataPoint {
  date: string
  timestamp: number
  open: number
  high: number
  low: number
  close: number
  volume: number
  adjustedClose?: number
}

export interface HistoricalData {
  symbol: string
  data: HistoricalDataPoint[]
  interval: string
  period: string
  lastUpdated: string
  source: string
}

export type TimePeriod = '1D' | '1W' | '1M' | '3M' | '6M' | '1Y' | 'YTD' | 'ALL'
export type TimeInterval = '1m' | '5m' | '15m' | '30m' | '1h' | '1d' | '1w' | '1M'

export class HistoricalDataService {
  private cacheManager = getCacheManager()

  /**
   * Get historical data with intelligent API selection
   */
  async getHistoricalData(
    symbol: string,
    period: TimePeriod = '1M',
    interval: TimeInterval = '1d'
  ): Promise<HistoricalData | null> {
    const cacheKey = createCacheKey('historical', symbol, period, interval)

    // Check cache first with market-aware TTL
    const cached = this.cacheManager.get<HistoricalData>(cacheKey)
    if (cached) return cached

    const config = getApiConfig()
    const fallbackChain = getApiFallbackChain()

    // Try APIs in fallback order
    for (const apiName of fallbackChain) {
      try {
        let historicalData: HistoricalData | null = null

        switch (apiName) {
          case 'polygon':
            if (config.apis.polygon?.apiKey) {
              historicalData = await this.getPolygonHistoricalData(symbol, period, interval)
            }
            break
          case 'twelveData':
            if (config.apis.twelveData?.apiKey) {
              historicalData = await this.getTwelveDataHistoricalData(symbol, period, interval)
            }
            break
          case 'finnhub':
            if (config.apis.finnhub?.apiKey) {
              historicalData = await this.getFinnhubHistoricalData(symbol, period, interval)
            }
            break
          case 'alphavantage':
            if (config.apis.alphavantage?.apiKey) {
              historicalData = await this.getAlphaVantageHistoricalData(symbol, period, interval)
            }
            break
        }

        if (historicalData && historicalData.data.length > 0) {
          // Cache based on data freshness needs with smart TTL
          const ttl = this.getSmartCacheTTL(period, interval)
          const tags = this.getCacheTagsForHistoricalData(period, interval)
          this.cacheManager.set(cacheKey, historicalData, ttl, tags)
          return historicalData
        }
      } catch (error) {
        console.warn(`${apiName} historical data failed for ${symbol}:`, error)
        continue
      }
    }

    return null
  }

  /**
   * Polygon.io historical data (Enterprise tier)
   */
  private async getPolygonHistoricalData(
    symbol: string, 
    period: TimePeriod, 
    interval: TimeInterval
  ): Promise<HistoricalData | null> {
    const polygonService = getPolygonService()
    if (!polygonService) return null

    try {
      const { from, to } = this.getDateRange(period)
      const multiplier = this.getPolygonMultiplier(interval)
      const timespan = this.getPolygonTimespan(interval)

      const response = await fetch(
        `https://api.polygon.io/v2/aggs/ticker/${symbol}/range/${multiplier}/${timespan}/${from}/${to}?adjusted=true&sort=asc&apikey=${process.env.POLYGON_IO_API_KEY}`
      )

      if (!response.ok) return null

      const data = await response.json()
      if (!data.results) return null

      const historicalData: HistoricalDataPoint[] = data.results.map((item: any) => ({
        date: new Date(item.t).toISOString().split('T')[0],
        timestamp: item.t,
        open: item.o,
        high: item.h,
        low: item.l,
        close: item.c,
        volume: item.v,
        adjustedClose: item.c
      }))

      return {
        symbol,
        data: historicalData,
        interval,
        period,
        lastUpdated: new Date().toISOString(),
        source: 'polygon'
      }
    } catch (error) {
      console.error('Polygon historical data error:', error)
      return null
    }
  }

  /**
   * Twelve Data historical data (Mid-tier)
   */
  private async getTwelveDataHistoricalData(
    symbol: string, 
    period: TimePeriod, 
    interval: TimeInterval
  ): Promise<HistoricalData | null> {
    try {
      const twelveInterval = this.getTwelveDataInterval(interval)
      const outputSize = this.getTwelveDataOutputSize(period)

      const response = await fetch(
        `https://api.twelvedata.com/time_series?symbol=${symbol}&interval=${twelveInterval}&outputsize=${outputSize}&apikey=${process.env.TWELVE_DATA_API_KEY}`
      )

      if (!response.ok) return null

      const data = await response.json()
      if (data.status === 'error' || !data.values) return null

      const historicalData: HistoricalDataPoint[] = data.values.map((item: any) => ({
        date: item.datetime.split(' ')[0],
        timestamp: new Date(item.datetime).getTime(),
        open: parseFloat(item.open),
        high: parseFloat(item.high),
        low: parseFloat(item.low),
        close: parseFloat(item.close),
        volume: parseInt(item.volume) || 0,
        adjustedClose: parseFloat(item.close)
      })).reverse() // Twelve Data returns newest first, we want oldest first

      return {
        symbol,
        data: historicalData,
        interval,
        period,
        lastUpdated: new Date().toISOString(),
        source: 'twelvedata'
      }
    } catch (error) {
      console.error('Twelve Data historical data error:', error)
      return null
    }
  }

  /**
   * Finnhub historical data (All tiers)
   */
  private async getFinnhubHistoricalData(
    symbol: string, 
    period: TimePeriod, 
    interval: TimeInterval
  ): Promise<HistoricalData | null> {
    try {
      const { from, to } = this.getDateRange(period)
      const resolution = this.getFinnhubResolution(interval)

      const response = await fetch(
        `https://finnhub.io/api/v1/stock/candle?symbol=${symbol}&resolution=${resolution}&from=${Math.floor(from.getTime() / 1000)}&to=${Math.floor(to.getTime() / 1000)}&token=${process.env.FINNHUB_API_KEY}`
      )

      if (!response.ok) return null

      const data = await response.json()
      if (data.s !== 'ok' || !data.c) return null

      const historicalData: HistoricalDataPoint[] = data.c.map((close: number, index: number) => ({
        date: new Date(data.t[index] * 1000).toISOString().split('T')[0],
        timestamp: data.t[index] * 1000,
        open: data.o[index],
        high: data.h[index],
        low: data.l[index],
        close: close,
        volume: data.v[index],
        adjustedClose: close
      }))

      return {
        symbol,
        data: historicalData,
        interval,
        period,
        lastUpdated: new Date().toISOString(),
        source: 'finnhub'
      }
    } catch (error) {
      console.error('Finnhub historical data error:', error)
      return null
    }
  }

  /**
   * Alpha Vantage historical data (Startup tier)
   */
  private async getAlphaVantageHistoricalData(
    symbol: string, 
    period: TimePeriod, 
    interval: TimeInterval
  ): Promise<HistoricalData | null> {
    try {
      // Alpha Vantage has different functions for different intervals
      let func = 'TIME_SERIES_DAILY'
      let intervalKey = 'Time Series (Daily)'

      if (interval.includes('m') || interval.includes('h')) {
        func = 'TIME_SERIES_INTRADAY'
        intervalKey = `Time Series (${interval})`
      }

      const response = await fetch(
        `https://www.alphavantage.co/query?function=${func}&symbol=${symbol}&interval=${interval}&apikey=${process.env.ALPHA_VANTAGE_API_KEY}`
      )

      if (!response.ok) return null

      const data = await response.json()
      const timeSeries = data[intervalKey] || data['Time Series (Daily)']
      
      if (!timeSeries) return null

      const historicalData: HistoricalDataPoint[] = Object.entries(timeSeries)
        .map(([date, values]: [string, any]) => ({
          date,
          timestamp: new Date(date).getTime(),
          open: parseFloat(values['1. open']),
          high: parseFloat(values['2. high']),
          low: parseFloat(values['3. low']),
          close: parseFloat(values['4. close']),
          volume: parseInt(values['5. volume']) || 0,
          adjustedClose: parseFloat(values['4. close'])
        }))
        .sort((a, b) => a.timestamp - b.timestamp) // Sort by date ascending

      return {
        symbol,
        data: historicalData,
        interval,
        period,
        lastUpdated: new Date().toISOString(),
        source: 'alphavantage'
      }
    } catch (error) {
      console.error('Alpha Vantage historical data error:', error)
      return null
    }
  }

  /**
   * Helper methods for date ranges and API-specific formatting
   */
  private getDateRange(period: TimePeriod): { from: Date; to: Date } {
    const to = new Date()
    const from = new Date()

    switch (period) {
      case '1D':
        from.setDate(to.getDate() - 1)
        break
      case '1W':
        from.setDate(to.getDate() - 7)
        break
      case '1M':
        from.setMonth(to.getMonth() - 1)
        break
      case '3M':
        from.setMonth(to.getMonth() - 3)
        break
      case '6M':
        from.setMonth(to.getMonth() - 6)
        break
      case '1Y':
        from.setFullYear(to.getFullYear() - 1)
        break
      case 'YTD':
        from.setMonth(0, 1) // January 1st of current year
        break
      case 'ALL':
        from.setFullYear(to.getFullYear() - 5) // 5 years back
        break
    }

    return { from, to }
  }

  private getPolygonMultiplier(interval: TimeInterval): number {
    const multipliers: Record<TimeInterval, number> = {
      '1m': 1, '5m': 5, '15m': 15, '30m': 30, '1h': 1, '1d': 1, '1w': 1, '1M': 1
    }
    return multipliers[interval] || 1
  }

  private getPolygonTimespan(interval: TimeInterval): string {
    if (interval.includes('m')) return 'minute'
    if (interval.includes('h')) return 'hour'
    if (interval.includes('d')) return 'day'
    if (interval.includes('w')) return 'week'
    if (interval.includes('M')) return 'month'
    return 'day'
  }

  private getTwelveDataInterval(interval: TimeInterval): string {
    return interval // Twelve Data uses same format
  }

  private getTwelveDataOutputSize(period: TimePeriod): number {
    const sizes: Record<TimePeriod, number> = {
      '1D': 24, '1W': 7, '1M': 30, '3M': 90, '6M': 180, '1Y': 365, 'YTD': 365, 'ALL': 5000
    }
    return sizes[period] || 30
  }

  private getFinnhubResolution(interval: TimeInterval): string {
    const resolutions: Record<TimeInterval, string> = {
      '1m': '1', '5m': '5', '15m': '15', '30m': '30', '1h': '60', '1d': 'D', '1w': 'W', '1M': 'M'
    }
    return resolutions[interval] || 'D'
  }

  /**
   * Get smart cache TTL based on data type and market conditions
   */
  private getSmartCacheTTL(period: TimePeriod, interval: TimeInterval): number {
    // Determine data type based on interval
    let dataType: 'historicalIntraday' | 'historicalDaily' | 'historicalWeekly'

    if (interval.includes('m') || interval.includes('h')) {
      dataType = 'historicalIntraday'
    } else if (interval.includes('w') || interval.includes('M')) {
      dataType = 'historicalWeekly'
    } else {
      dataType = 'historicalDaily'
    }

    return getMarketAwareCacheTTL(dataType)
  }

  /**
   * Get cache tags for historical data
   */
  private getCacheTagsForHistoricalData(period: TimePeriod, interval: TimeInterval): string[] {
    const baseTags = ['historical']

    if (interval.includes('m') || interval.includes('h')) {
      baseTags.push('intraday')
    } else if (interval.includes('w') || interval.includes('M')) {
      baseTags.push('weekly')
    } else {
      baseTags.push('daily')
    }

    // Add period-specific tags
    baseTags.push(period.toLowerCase())

    return baseTags
  }

  /**
   * Clear cache by symbol (useful for data updates)
   */
  clearCacheForSymbol(symbol: string): void {
    const pattern = new RegExp(`historical:${symbol}:.*`)
    const keys = this.cacheManager.getKeys().filter(key => pattern.test(key))
    keys.forEach(key => this.cacheManager.delete(key))
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return this.cacheManager.getStats()
  }
}

// Singleton instance
let historicalDataServiceInstance: HistoricalDataService | null = null

export function getHistoricalDataService(): HistoricalDataService {
  if (!historicalDataServiceInstance) {
    historicalDataServiceInstance = new HistoricalDataService()
  }
  return historicalDataServiceInstance
}
