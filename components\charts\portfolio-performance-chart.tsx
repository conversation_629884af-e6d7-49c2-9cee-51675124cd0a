/**
 * Portfolio Performance Chart Component
 * Displays portfolio performance with capital deposits labeling
 */

'use client'

import React from 'react'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ReferenceLine,
  ReferenceDot,
  Area,
  AreaChart
} from 'recharts'
import { useTheme } from 'next-themes'
import { TrendingUp, DollarSign, Percent } from 'lucide-react'
import {
  ChartContainer,
  ChartControls,
  EmptyChartState,
  PerformanceIndicator,
  TimeRange,
  ValueType,
  chartUtils
} from './chart-base'
import {
  usePortfolioChartData,
  useChartState,
  useTransformedChartData,
  useChartStats
} from '@/hooks/use-chart-data'

export interface PortfolioPerformanceChartProps {
  portfolioId?: string
  title?: string
  height?: number
  autoRefresh?: boolean
  refreshInterval?: number
  showControls?: boolean
  showPerformance?: boolean
  showDeposits?: boolean
  chartType?: 'line' | 'area'
  className?: string
  initialTimeRange?: TimeRange
  initialValueType?: ValueType
}

export function PortfolioPerformanceChart({
  portfolioId,
  title = 'Portfolio Performance',
  height = 400,
  autoRefresh = false,
  refreshInterval = 300000, // 5 minutes
  showControls = true,
  showPerformance = true,
  showDeposits = true,
  chartType = 'area',
  className,
  initialTimeRange = '1M',
  initialValueType = 'absolute'
}: PortfolioPerformanceChartProps) {
  const { theme } = useTheme()
  const { timeRange, valueType, setTimeRange, setValueType } = useChartState(
    initialTimeRange,
    initialValueType
  )

  const {
    data: portfolioData,
    loading,
    error,
    lastUpdated,
    refresh
  } = usePortfolioChartData({
    timeRange,
    portfolioId,
    autoRefresh,
    refreshInterval
  })

  const { valueTimeline, totalInvested, currentValue, netReturn, deposits } = portfolioData
  const baseValue = totalInvested || (valueTimeline.length > 0 ? valueTimeline[0].value : 0)
  const transformedData = useTransformedChartData(valueTimeline, valueType, baseValue)
  const stats = useChartStats(transformedData)
  const colors = chartUtils.getChartColors(theme as 'light' | 'dark')

  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      const value = payload[0].value
      const timestamp = data.timestamp

      // Check if there's a deposit at this point
      const deposit = deposits?.find(d => 
        Math.abs(d.timestamp - timestamp) < 24 * 60 * 60 * 1000 // Within 24 hours
      )

      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="text-sm font-medium">
            {chartUtils.formatDate(timestamp, timeRange)}
          </p>
          <p className="text-sm text-muted-foreground">
            {new Date(timestamp).toLocaleString()}
          </p>
          <p className="text-sm font-semibold mt-1">
            Portfolio Value: {' '}
            {valueType === 'percentage' 
              ? chartUtils.formatPercentage(value)
              : chartUtils.formatCurrency(value)
            }
          </p>
          {valueType === 'percentage' && (
            <p className="text-xs text-muted-foreground">
              vs. initial {chartUtils.formatCurrency(baseValue)}
            </p>
          )}
          {deposit && (
            <div className="mt-2 pt-2 border-t">
              <p className="text-xs text-green-600 font-medium flex items-center">
                <DollarSign className="h-3 w-3 mr-1" />
                {deposit.label}
              </p>
            </div>
          )}
        </div>
      )
    }
    return null
  }

  // Format Y-axis values
  const formatYAxis = (value: number) => {
    if (valueType === 'percentage') {
      return `${value.toFixed(1)}%`
    }
    return value >= 1000 
      ? `$${(value / 1000).toFixed(1)}k`
      : `$${value.toFixed(0)}`
  }

  // Format X-axis values
  const formatXAxis = (timestamp: number) => {
    return chartUtils.formatDate(timestamp, timeRange)
  }

  const subtitle = lastUpdated 
    ? `Last updated: ${lastUpdated.toLocaleTimeString()}`
    : undefined

  return (
    <ChartContainer
      title={title}
      subtitle={subtitle}
      loading={loading}
      error={error}
      onRefresh={refresh}
      className={className}
    >
      <div className="space-y-4">
        {/* Performance Summary */}
        {showPerformance && (
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 p-4 bg-muted/50 rounded-lg">
            <div className="text-center">
              <p className="text-xs text-muted-foreground mb-1">Current Value</p>
              <p className="text-lg font-bold">
                {chartUtils.formatCurrency(currentValue)}
              </p>
            </div>
            <div className="text-center">
              <p className="text-xs text-muted-foreground mb-1">Total Return</p>
              <p className={`text-lg font-bold ${
                netReturn.amount >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {netReturn.amount >= 0 ? '+' : ''}
                {chartUtils.formatCurrency(netReturn.amount)}
              </p>
            </div>
            <div className="text-center">
              <p className="text-xs text-muted-foreground mb-1">Return %</p>
              <p className={`text-lg font-bold ${
                netReturn.percentage >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {netReturn.percentage >= 0 ? '+' : ''}
                {netReturn.percentage.toFixed(2)}%
              </p>
            </div>
          </div>
        )}

        {/* Chart Controls */}
        {showControls && (
          <ChartControls
            timeRange={timeRange}
            valueType={valueType}
            onTimeRangeChange={setTimeRange}
            onValueTypeChange={setValueType}
            disabled={loading}
            showValueType={true}
          />
        )}

        {/* Chart */}
        {transformedData.length === 0 ? (
          <EmptyChartState
            title="No portfolio data available"
            description="Start investing to see your portfolio performance over time."
            action={{
              label: 'Refresh',
              onClick: refresh
            }}
          />
        ) : (
          <div style={{ height: height > 0 ? `${height}px` : '100%' }} className={height === 0 ? 'h-full' : ''}>
            <ResponsiveContainer width="100%" height="100%">
              {chartType === 'area' ? (
                <AreaChart
                  data={transformedData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <defs>
                    <linearGradient id="portfolioGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop 
                        offset="5%" 
                        stopColor={stats.change >= 0 ? colors.success : colors.danger} 
                        stopOpacity={0.3}
                      />
                      <stop 
                        offset="95%" 
                        stopColor={stats.change >= 0 ? colors.success : colors.danger} 
                        stopOpacity={0.05}
                      />
                    </linearGradient>
                  </defs>
                  <CartesianGrid 
                    strokeDasharray="3 3" 
                    stroke={colors.muted}
                    opacity={0.3}
                  />
                  <XAxis
                    dataKey="timestamp"
                    tickFormatter={formatXAxis}
                    stroke={colors.muted}
                    fontSize={12}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    tickFormatter={formatYAxis}
                    stroke={colors.muted}
                    fontSize={12}
                    tickLine={false}
                    axisLine={false}
                    domain={['dataMin - 5', 'dataMax + 5']}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  
                  {/* Reference line at zero for percentage view */}
                  {valueType === 'percentage' && (
                    <ReferenceLine 
                      y={0} 
                      stroke={colors.muted} 
                      strokeDasharray="2 2"
                      opacity={0.5}
                    />
                  )}
                  
                  {/* Deposit markers */}
                  {showDeposits && deposits?.map((deposit, index) => (
                    <ReferenceDot
                      key={index}
                      x={deposit.timestamp}
                      y={transformedData.find(d => 
                        Math.abs(d.timestamp - deposit.timestamp) < 24 * 60 * 60 * 1000
                      )?.value || 0}
                      r={4}
                      fill={colors.warning}
                      stroke={colors.background}
                      strokeWidth={2}
                    />
                  ))}
                  
                  <Area
                    type="monotone"
                    dataKey="value"
                    stroke={stats.change >= 0 ? colors.success : colors.danger}
                    strokeWidth={2}
                    fill="url(#portfolioGradient)"
                    dot={false}
                    activeDot={{ 
                      r: 4, 
                      fill: stats.change >= 0 ? colors.success : colors.danger,
                      stroke: colors.background,
                      strokeWidth: 2
                    }}
                  />
                </AreaChart>
              ) : (
                <LineChart
                  data={transformedData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid 
                    strokeDasharray="3 3" 
                    stroke={colors.muted}
                    opacity={0.3}
                  />
                  <XAxis
                    dataKey="timestamp"
                    tickFormatter={formatXAxis}
                    stroke={colors.muted}
                    fontSize={12}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    tickFormatter={formatYAxis}
                    stroke={colors.muted}
                    fontSize={12}
                    tickLine={false}
                    axisLine={false}
                    domain={['dataMin - 5', 'dataMax + 5']}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  
                  {/* Reference line at zero for percentage view */}
                  {valueType === 'percentage' && (
                    <ReferenceLine 
                      y={0} 
                      stroke={colors.muted} 
                      strokeDasharray="2 2"
                      opacity={0.5}
                    />
                  )}
                  
                  {/* Deposit markers */}
                  {showDeposits && deposits?.map((deposit, index) => (
                    <ReferenceDot
                      key={index}
                      x={deposit.timestamp}
                      y={transformedData.find(d => 
                        Math.abs(d.timestamp - deposit.timestamp) < 24 * 60 * 60 * 1000
                      )?.value || 0}
                      r={4}
                      fill={colors.warning}
                      stroke={colors.background}
                      strokeWidth={2}
                    />
                  ))}
                  
                  <Line
                    type="monotone"
                    dataKey="value"
                    stroke={stats.change >= 0 ? colors.success : colors.danger}
                    strokeWidth={2}
                    dot={false}
                    activeDot={{ 
                      r: 4, 
                      fill: stats.change >= 0 ? colors.success : colors.danger,
                      stroke: colors.background,
                      strokeWidth: 2
                    }}
                  />
                </LineChart>
              )}
            </ResponsiveContainer>
          </div>
        )}

        {/* Chart Legend */}
        {showDeposits && deposits && deposits.length > 0 && (
          <div className="flex items-center justify-center space-x-4 text-xs text-muted-foreground">
            <div className="flex items-center space-x-1">
              <div 
                className="w-2 h-2 rounded-full" 
                style={{ backgroundColor: colors.warning }}
              />
              <span>Capital Deposits</span>
            </div>
          </div>
        )}

        {/* Chart Statistics */}
        {transformedData.length > 0 && (
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 pt-4 border-t">
            <div className="text-center">
              <p className="text-xs text-muted-foreground">Period Change</p>
              <p className={`text-sm font-medium ${
                stats.change >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {stats.change >= 0 ? '+' : ''}
                {valueType === 'percentage' 
                  ? chartUtils.formatPercentage(stats.change)
                  : chartUtils.formatCurrency(stats.change)
                }
              </p>
            </div>
            <div className="text-center">
              <p className="text-xs text-muted-foreground">Change %</p>
              <p className={`text-sm font-medium ${
                stats.changePercent >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {stats.changePercent >= 0 ? '+' : ''}
                {stats.changePercent.toFixed(2)}%
              </p>
            </div>
            <div className="text-center">
              <p className="text-xs text-muted-foreground">Period High</p>
              <p className="text-sm font-medium">
                {valueType === 'percentage' 
                  ? chartUtils.formatPercentage(stats.max)
                  : chartUtils.formatCurrency(stats.max)
                }
              </p>
            </div>
            <div className="text-center">
              <p className="text-xs text-muted-foreground">Period Low</p>
              <p className="text-sm font-medium">
                {valueType === 'percentage' 
                  ? chartUtils.formatPercentage(stats.min)
                  : chartUtils.formatCurrency(stats.min)
                }
              </p>
            </div>
          </div>
        )}
      </div>
    </ChartContainer>
  )
}

/**
 * Compact Portfolio Performance Chart (for dashboard widgets)
 */
export interface CompactPortfolioChartProps {
  portfolioId?: string
  timeRange?: TimeRange
  height?: number
  showValue?: boolean
  className?: string
}

export function CompactPortfolioChart({
  portfolioId,
  timeRange = '1M',
  height = 100,
  showValue = true,
  className
}: CompactPortfolioChartProps) {
  const { theme } = useTheme()
  const { data, loading, error } = usePortfolioChartData({
    timeRange,
    portfolioId
  })

  const { valueTimeline, currentValue, netReturn } = data
  const stats = useChartStats(valueTimeline)
  const colors = chartUtils.getChartColors(theme as 'light' | 'dark')

  if (loading || error || valueTimeline.length === 0) {
    return (
      <div className={`flex items-center justify-center ${className}`} style={{ height: `${height}px` }}>
        <div className="text-xs text-muted-foreground">
          {loading ? 'Loading...' : error ? 'Error' : 'No data'}
        </div>
      </div>
    )
  }

  return (
    <div className={className}>
      {showValue && (
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium">
            {chartUtils.formatCurrency(currentValue)}
          </span>
          <span className={`text-xs ${
            netReturn.amount >= 0 ? 'text-green-600' : 'text-red-600'
          }`}>
            {netReturn.amount >= 0 ? '+' : ''}{netReturn.percentage.toFixed(2)}%
          </span>
        </div>
      )}
      <div style={{ height: `${height}px` }}>
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart data={valueTimeline} margin={{ top: 0, right: 0, left: 0, bottom: 0 }}>
            <defs>
              <linearGradient id="compactGradient" x1="0" y1="0" x2="0" y2="1">
                <stop 
                  offset="5%" 
                  stopColor={stats.change >= 0 ? colors.success : colors.danger} 
                  stopOpacity={0.3}
                />
                <stop 
                  offset="95%" 
                  stopColor={stats.change >= 0 ? colors.success : colors.danger} 
                  stopOpacity={0.05}
                />
              </linearGradient>
            </defs>
            <Area
              type="monotone"
              dataKey="value"
              stroke={stats.change >= 0 ? colors.success : colors.danger}
              strokeWidth={1.5}
              fill="url(#compactGradient)"
              dot={false}
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>
    </div>
  )
}
