"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { BottomNav } from "@/components/bottom-nav"
import { TrendingUp, PieChart, Settings, User, Search, Plus, ArrowRight, Wallet, RefreshCw, BarChart3, LineChart, Activity } from "lucide-react"
import { ChartQuickAccess } from "@/components/chart-navigation"
import { TopNav } from "@/components/top-nav"
import { useAuth } from "@/components/auth-provider"
import { ReweightPortfolioDialog } from "@/components/reweight-portfolio-dialog"
import { GoalTracker } from "@/components/goal-tracker"

import { useMult<PERSON>leStockQuotes } from "@/hooks/use-stock-data"
import { useRealPortfolioData } from "@/hooks/use-real-portfolio-data"
import type { TimePeriod } from "@/lib/historical-data-service"
import {
  PortfolioPerformanceChart,
  TimeRange
} from "@/components/charts"
import { hasCompletedOnboarding } from "@/lib/onboarding-utils"
import { debugAuthState, clearAllInvestryData, forceSignOut } from "@/lib/debug-auth"
import { getUserDisplayName } from "@/lib/user-service"

// Add this before the component
Date.prototype.getDayOfYear = function () {
  const start = new Date(this.getFullYear(), 0, 0)
  const diff = this.getTime() - start.getTime()
  return Math.floor(diff / (1000 * 60 * 60 * 24))
}

export default function DashboardPage() {
  const router = useRouter()
  const { user, loading: authLoading } = useAuth()
  const [portfolio, setPortfolio] = useState<any>(null)
  const [portfolioSymbols, setPortfolioSymbols] = useState<string[]>([])

  const [selectedPeriod, setSelectedPeriod] = useState<TimePeriod>("1M")
  const [showChartSettings, setShowChartSettings] = useState(false)

  // Use the multiple stock quotes hook for real-time portfolio data
  const { data: stockData, loading: stockLoading, error: stockError, refetch: refetchStocks } = useMultipleStockQuotes(portfolioSymbols)

  // Use real portfolio data instead of static demo data
  const { data: realPortfolioData, loading: portfolioDataLoading, error: portfolioDataError, refetch: refetchPortfolioData } = useRealPortfolioData()

  // Create portfolio performance object from real data
  const portfolioPerformance = {
    data: [
      {
        date: '2024-01-01',
        timestamp: new Date('2024-01-01').getTime(),
        value: realPortfolioData.totalInvested || 0,
        totalReturn: 0,
        totalReturnPercent: 0,
        dailyReturn: 0,
        dailyReturnPercent: 0
      },
      {
        date: new Date().toISOString().split('T')[0],
        timestamp: Date.now(),
        value: realPortfolioData.totalValue || 0,
        totalReturn: realPortfolioData.totalReturn || 0,
        totalReturnPercent: realPortfolioData.totalReturnPercent || 0,
        dailyReturn: realPortfolioData.dailyChange || 0,
        dailyReturnPercent: realPortfolioData.dailyChangePercent || 0
      }
    ],
    summary: {
      totalValue: realPortfolioData.totalValue || 0,
      totalReturn: realPortfolioData.totalReturn || 0,
      totalReturnPercent: realPortfolioData.totalReturnPercent || 0,
      dailyChange: realPortfolioData.dailyChange || 0,
      dailyChangePercent: realPortfolioData.dailyChangePercent || 0,
      bestDay: { date: new Date().toISOString().split('T')[0], return: realPortfolioData.dailyChange || 0, returnPercent: realPortfolioData.dailyChangePercent || 0 },
      worstDay: { date: '2024-01-01', return: 0, returnPercent: 0 },
      volatility: 0,
      sharpeRatio: 0,
      maxDrawdown: 0
    },
    period: selectedPeriod,
    interval: '1d' as const,
    lastUpdated: new Date().toISOString(),
    allocations: []
  }
  const performanceLoading = portfolioDataLoading
  const [showReweightDialog, setShowReweightDialog] = useState(false)

  // Chart settings
  const [chartType, setChartType] = useState("line")
  const [showBenchmark, setShowBenchmark] = useState(true)
  const [benchmarkType, setBenchmarkType] = useState("SPY")

  useEffect(() => {
    // Wait for auth to load
    if (authLoading) return

    // Check authentication with Supabase
    if (!user) {
      console.log("No user found, redirecting to sign-in")
      router.push("/auth/signin")
      return
    }

    console.log("User authenticated:", user.email)

    // Debug current auth state
    debugAuthState(user.id)

    // Clean up any old non-user-specific localStorage data to prevent cross-contamination
    const cleanupOldData = () => {
      const keysToClean = [
        'demo-portfolio',
        'user-portfolio',
        'investry_survey',
        'portfolio-metadata'
      ]

      keysToClean.forEach(key => {
        if (localStorage.getItem(key)) {
          console.log(`Cleaning up old localStorage key: ${key}`)
          localStorage.removeItem(key)
        }
      })

      // Also clean up any user-specific data that might be lingering
      console.log(`Checking onboarding status for user: ${user.id}`)
      console.log(`Has completed onboarding: ${hasCompletedOnboarding(user.id)}`)
    }

    cleanupOldData()

    // Load portfolio data for the current user
    const loadPortfolio = async () => {
      try {
        // Use user-specific localStorage key
        const userPortfolioKey = `demo-portfolio-${user.id}`
        const storedPortfolio = localStorage.getItem(userPortfolioKey)

        if (storedPortfolio) {
          const portfolioData = JSON.parse(storedPortfolio)
          setPortfolio(portfolioData)

          // Extract symbols to fetch real stock data
          const symbols = portfolioData.allocations.map((allocation: any) => allocation.symbol)
          setPortfolioSymbols(symbols)
          console.log("Loaded existing portfolio for user:", user.email)
        } else {
          // New user - no portfolio data found, check if they need onboarding
          console.log("No portfolio found for new user:", user.email)

          if (!hasCompletedOnboarding(user.id)) {
            // New user who hasn't completed onboarding - redirect them
            console.log("New user needs onboarding, redirecting...")
            router.push("/onboarding")
            return
          }

          setPortfolio(null)
          setPortfolioSymbols([])
        }
      } catch (error) {
        console.error("Error loading portfolio:", error)
        setPortfolio(null)
        setPortfolioSymbols([])
      }
    }

    loadPortfolio()
  }, [user, authLoading, router])

  // Portfolio data is now handled by the usePortfolioPerformance hook

  // Portfolio data is now provided by portfolioPerformance hook

  // Show loading while auth is being determined
  if (authLoading || !user) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-white">Loading...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-900 text-white flex flex-col">
      <TopNav currentPage="dashboard" />

      <div className="flex-1 pb-20">
        <div className="container mx-auto px-4 py-6 space-y-6">
          {/* Welcome Section */}
          <div className="space-y-2">
            <h1 className="text-3xl font-bold">Welcome back, {getUserDisplayName(user)}</h1>
            <p className="text-slate-400">Track your investments and continue your financial journey.</p>
          </div>



          {/* Portfolio Summary Card */}
          <Card className="bg-gradient-to-r from-emerald-500/20 to-blue-500/20 border-emerald-400/30 shadow-neon">
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <p className="text-emerald-300 text-sm font-medium">Total Portfolio Value</p>
                  <h2 className="text-4xl font-bold text-white">
                    ${portfolioPerformance?.summary.totalValue?.toLocaleString("en-US", { minimumFractionDigits: 2 }) || "10,000.00"}
                  </h2>
                  <div className="flex items-center gap-2">
                    <TrendingUp className={`h-4 w-4 ${
                      (portfolioPerformance?.summary.dailyChange || 0) >= 0 ? "text-emerald-400" : "text-red-400"
                    }`} />
                    <span className={`font-medium ${
                      (portfolioPerformance?.summary.dailyChange || 0) >= 0 ? "text-emerald-400" : "text-red-400"
                    }`}>
                      {(portfolioPerformance?.summary.dailyChange || 0) >= 0 ? "+" : ""}
                      ${Math.abs(portfolioPerformance?.summary.dailyChange || 136.80).toFixed(2)}
                      ({(portfolioPerformance?.summary.dailyChangePercent || 0) >= 0 ? "+" : ""}
                      {Math.abs(portfolioPerformance?.summary.dailyChangePercent || 1.37).toFixed(2)}%) today
                    </span>
                  </div>
                </div>

                <div className="text-right space-y-3">
                  <div className={`rounded-lg p-3 ${
                    (portfolioPerformance?.summary.totalReturn || 0) >= 0 ? "bg-emerald-400/20" : "bg-red-400/20"
                  }`}>
                    <p className={`text-xs ${
                      (portfolioPerformance?.summary.totalReturn || 0) >= 0 ? "text-emerald-300" : "text-red-300"
                    }`}>Total Return</p>
                    <p className={`text-lg font-semibold ${
                      (portfolioPerformance?.summary.totalReturn || 0) >= 0 ? "text-emerald-400" : "text-red-400"
                    }`}>
                      {(portfolioPerformance?.summary.totalReturn || 0) >= 0 ? "+" : ""}
                      ${Math.abs(portfolioPerformance?.summary.totalReturn || 342.18).toFixed(2)}
                    </p>
                  </div>
                  <div className="bg-blue-400/20 rounded-lg p-3">
                    <p className="text-xs text-blue-300">Period</p>
                    <p className="text-lg font-semibold text-blue-400">{selectedPeriod}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Portfolio Section */}
          <div className="space-y-4">

            <div className="grid md:grid-cols-2 gap-6">
              {/* Investment Allocation */}
              <Card className="bg-slate-800/50 border-slate-700 relative z-10 flex flex-col">
                <CardHeader className="flex-shrink-0">
                  <CardTitle className="flex items-center gap-2 text-white">
                    <PieChart className="h-5 w-5" />
                    Portfolio Performance
                  </CardTitle>
                  <CardDescription className="text-slate-400">Tech-focused growth strategy</CardDescription>
                </CardHeader>
                <CardContent className="flex flex-col flex-1 min-h-0 pb-0">
                  {/* Time Period Selector */}
                  <div className="flex items-center justify-between mb-3 flex-shrink-0">
                    <div className="flex items-center gap-1">
                      {(["1D", "1W", "1M", "3M", "1Y", "YTD", "ALL"] as TimePeriod[]).map((period) => (
                        <Button
                          key={period}
                          variant={selectedPeriod === period ? "default" : "ghost"}
                          size="sm"
                          onClick={() => {
                            setSelectedPeriod(period)
                          }}
                          disabled={performanceLoading}
                          className={`h-7 px-2 text-xs ${
                            selectedPeriod === period
                              ? "bg-emerald-500 text-white"
                              : "text-slate-400 hover:text-white"
                          }`}
                        >
                          {period}
                        </Button>
                      ))}
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowChartSettings(true)}
                      className="text-slate-400 hover:text-white"
                    >
                      <Settings className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Responsive Chart Container */}
                  <div className="flex-1 min-h-0 bg-slate-900/20 rounded-lg overflow-auto">
                    <div className="w-full h-full min-h-[200px] p-2">
                      {performanceLoading ? (
                        <div className="flex items-center justify-center h-full min-h-[200px]">
                          <div className="text-slate-400 text-sm">Loading chart...</div>
                        </div>
                      ) : (
                        <div className="w-full h-full min-h-[200px]">
                          <PortfolioPerformanceChart
                            height={0} // Let chart be responsive
                            autoRefresh={false}
                            refreshInterval={300000}
                            showControls={false}
                            showPerformance={false}
                            showDeposits={true}
                            chartType="area"
                            initialTimeRange={selectedPeriod as TimeRange}
                            initialValueType="absolute"
                            className="w-full h-full [&_.recharts-wrapper]:!bg-transparent [&_*]:!text-slate-300 [&_.recharts-wrapper]:!w-full [&_.recharts-wrapper]:!h-full"
                          />
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Performance Stats - Docked at bottom inside card */}
                  <div className="flex-shrink-0 border-t border-slate-700/30 bg-slate-800/90 px-2 py-3 mt-2">
                    <div className="flex items-center justify-between text-xs">
                      <div className="flex items-center gap-3">
                        <div>
                          <span className="text-slate-400">Change: </span>
                          <span className={`font-medium ${
                            (portfolioPerformance?.summary.dailyChangePercent || 0) >= 0 ? "text-emerald-400" : "text-red-400"
                          }`}>
                            {(portfolioPerformance?.summary.dailyChangePercent || 0) >= 0 ? "+" : ""}
                            {Math.abs(portfolioPerformance?.summary.dailyChangePercent || 1.37).toFixed(2)}%
                          </span>
                        </div>
                        <div>
                          <span className="text-slate-400">Return: </span>
                          <span className={`font-medium ${
                            (portfolioPerformance?.summary.totalReturn || 0) >= 0 ? "text-emerald-400" : "text-red-400"
                          }`}>
                            {(portfolioPerformance?.summary.totalReturn || 0) >= 0 ? "+" : ""}
                            ${Math.abs(portfolioPerformance?.summary.totalReturn || 342.18).toFixed(2)}
                          </span>
                        </div>
                      </div>
                      <div className="text-xs text-slate-500">
                        Updated {portfolioPerformance?.lastUpdated ?
                          new Date(portfolioPerformance.lastUpdated).toLocaleTimeString() :
                          new Date().toLocaleTimeString()
                        }
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Top Holdings */}
              <Card className="bg-slate-800/50 border-slate-700 relative z-0">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-white">Top Holdings</CardTitle>
                      <CardDescription className="text-slate-400">Live market data</CardDescription>
                    </div>
                    {portfolio && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={refetchStocks}
                        disabled={stockLoading}
                      >
                        <RefreshCw className={`h-4 w-4 ${stockLoading ? 'animate-spin' : ''}`} />
                      </Button>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="space-y-3">
                      {stockLoading ? (
                        // Loading skeleton
                        Array.from({ length: 6 }).map((_, i) => (
                          <div key={i} className="flex items-center justify-between animate-pulse">
                            <div className="flex items-center gap-3">
                              <div className="w-8 h-8 bg-slate-700 rounded-full"></div>
                              <div className="space-y-1">
                                <div className="h-4 bg-slate-700 rounded w-12"></div>
                                <div className="h-3 bg-slate-600 rounded w-8"></div>
                              </div>
                            </div>
                            <div className="text-right space-y-1">
                              <div className="h-4 bg-slate-700 rounded w-16"></div>
                              <div className="h-3 bg-slate-600 rounded w-12"></div>
                            </div>
                          </div>
                        ))
                      ) : stockError ? (
                        <div className="text-center text-slate-400 py-4">
                          <p>Error loading stock data</p>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={refetchStocks}
                            className="mt-2"
                          >
                            Try Again
                          </Button>
                        </div>
                      ) : portfolio?.allocations?.slice(0, 6).map((allocation: any) => {
                        const stock = stockData.find((s) => s.symbol === allocation.symbol)
                        return (
                          <Link
                            key={allocation.symbol}
                            href={`/investments/${allocation.symbol}`}
                            className="block hover:bg-slate-700/50 rounded-lg p-2 -m-2 transition-colors cursor-pointer"
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <div className="w-8 h-8 bg-slate-700 rounded-full flex items-center justify-center">
                                  <span className="text-xs font-medium text-white">{allocation.symbol.charAt(0)}</span>
                                </div>
                                <div>
                                  <p className="font-medium text-white">{allocation.symbol}</p>
                                  <p className="text-xs text-slate-400">{allocation.allocation}%</p>
                                </div>
                              </div>
                              <div className="text-right">
                                <p className="font-medium text-white">${stock?.price?.toFixed(2) || "---"}</p>
                                <p className={`text-xs ${(stock?.changePercent || 0) >= 0 ? "text-emerald-400" : "text-red-400"}`}>
                                  {(stock?.changePercent || 0) >= 0 ? "+" : ""}
                                  {stock?.changePercent?.toFixed(1) || "0.0"}%
                                </p>
                              </div>
                            </div>
                          </Link>
                        )
                      }) || (
                        <div className="text-center text-slate-400 py-4">
                          <p>No portfolio data available</p>
                          <p className="text-sm">Complete onboarding to see your holdings</p>
                        </div>
                      )}
                    </div>

                    {/* Action Buttons */}
                    {portfolio?.allocations && portfolio.allocations.length > 0 && (
                      <div className="space-y-2">
                        <Button
                          variant="outline"
                          onClick={() => router.push("/investments")}
                          className="w-full border-slate-600 text-slate-300 hover:bg-slate-700 group"
                        >
                          View All Holdings
                          <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                        </Button>
                        <Button
                          onClick={() => setShowReweightDialog(true)}
                          className="w-full bg-emerald-600 hover:bg-emerald-700 text-white group"
                        >
                          <RefreshCw className="mr-2 h-4 w-4 group-hover:rotate-180 transition-transform duration-300" />
                          Reweight Portfolio
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Goal Tracking Section */}
          <GoalTracker />

          {/* Quick Actions */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <Button
              variant="outline"
              className="h-20 flex-col gap-2 border-slate-600 hover:bg-slate-700 bg-transparent"
              onClick={() => router.push("/search")}
            >
              <Search className="h-5 w-5" />
              <span className="text-sm">Search Stocks</span>
            </Button>

            <Button
              variant="outline"
              className="h-20 flex-col gap-2 border-slate-600 hover:bg-slate-700 bg-transparent"
              onClick={() => router.push("/investments")}
            >
              <Plus className="h-5 w-5" />
              <span className="text-sm">Add Investment</span>
            </Button>

            <Button
              variant="outline"
              className="h-20 flex-col gap-2 border-slate-600 hover:bg-slate-700 bg-transparent"
              onClick={() => router.push("/charts")}
            >
              <BarChart3 className="h-5 w-5" />
              <span className="text-sm">View Charts</span>
            </Button>

            <Button
              variant="outline"
              className="h-20 flex-col gap-2 border-slate-600 hover:bg-slate-700 bg-transparent"
              onClick={() => router.push("/learn")}
            >
              <TrendingUp className="h-5 w-5" />
              <span className="text-sm">Learn More</span>
            </Button>

            <Button
              variant="outline"
              className="h-20 flex-col gap-2 border-slate-600 hover:bg-slate-700 bg-transparent"
              onClick={() => router.push("/profile")}
            >
              <User className="h-5 w-5" />
              <span className="text-sm">Profile</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Bottom Navigation */}
      <BottomNav />

      {/* Footer */}
      <footer className="mt-auto border-t border-slate-700/50 bg-slate-800/30 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center gap-2 mb-4 md:mb-0">
              <Wallet className="h-6 w-6 text-emerald-400" />
              <span className="text-white font-semibold text-lg">investry</span>
            </div>
            <div className="flex items-center gap-6 text-sm text-slate-400">
              <span>Empowering investors of all backgrounds</span>
              <div className="flex items-center gap-4">
                <a href="#" className="hover:text-white transition-colors">
                  Privacy
                </a>
                <a href="#" className="hover:text-white transition-colors">
                  Terms
                </a>
                <a href="#" className="hover:text-white transition-colors">
                  Support
                </a>
              </div>
            </div>
          </div>
        </div>
      </footer>

      {/* Chart Settings Modal */}
      <Dialog open={showChartSettings} onOpenChange={setShowChartSettings}>
        <DialogContent className="bg-slate-800 border-slate-700 text-white">
          <DialogHeader>
            <DialogTitle>Portfolio Performance Settings</DialogTitle>
            <DialogDescription className="text-slate-400">
              Customize how your portfolio performance is displayed
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Time Period Selection */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Default Time Period</Label>
              <RadioGroup value={selectedPeriod} onValueChange={(value) => setSelectedPeriod(value as TimePeriod)}>
                <div className="grid grid-cols-4 gap-2">
                  {["1D", "1W", "1M", "3M", "1Y", "YTD", "All"].map((period) => (
                    <div key={period} className="flex items-center space-x-2">
                      <RadioGroupItem value={period} id={period} />
                      <Label htmlFor={period} className="text-sm">{period}</Label>
                    </div>
                  ))}
                </div>
              </RadioGroup>
            </div>

            {/* Chart Type */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Chart Type</Label>
              <RadioGroup value={chartType} onValueChange={setChartType}>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="line" id="line" />
                    <Label htmlFor="line" className="text-sm">Line Chart</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="area" id="area" />
                    <Label htmlFor="area" className="text-sm">Area Chart</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="candlestick" id="candlestick" />
                    <Label htmlFor="candlestick" className="text-sm">Candlestick Chart</Label>
                  </div>
                </div>
              </RadioGroup>
            </div>

            {/* Benchmark Comparison */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Benchmark Comparison</Label>
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="showBenchmark"
                    checked={showBenchmark}
                    onChange={(e) => setShowBenchmark(e.target.checked)}
                    className="rounded border-slate-600 bg-slate-700"
                  />
                  <Label htmlFor="showBenchmark" className="text-sm">Show benchmark comparison</Label>
                </div>

                {showBenchmark && (
                  <RadioGroup value={benchmarkType} onValueChange={setBenchmarkType}>
                    <div className="space-y-2 ml-6">
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="SPY" id="SPY" />
                        <Label htmlFor="SPY" className="text-sm">S&P 500 (SPY)</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="QQQ" id="QQQ" />
                        <Label htmlFor="QQQ" className="text-sm">NASDAQ 100 (QQQ)</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="VTI" id="VTI" />
                        <Label htmlFor="VTI" className="text-sm">Total Stock Market (VTI)</Label>
                      </div>
                    </div>
                  </RadioGroup>
                )}
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              onClick={() => setShowChartSettings(false)}
              className="border-slate-600 text-slate-300 hover:bg-slate-700"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                setShowChartSettings(false)
                // Here you would typically trigger a chart refresh with new settings
                console.log('Chart settings updated:', { selectedPeriod, chartType, showBenchmark, benchmarkType })
              }}
              className="bg-emerald-600 hover:bg-emerald-700"
            >
              Apply Settings
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Reweight Portfolio Dialog */}
      <ReweightPortfolioDialog
        open={showReweightDialog}
        onOpenChange={setShowReweightDialog}
        currentPortfolio={portfolio}
        onPortfolioUpdated={(newPortfolio) => {
          setPortfolio(newPortfolio)
          // Refresh stock data for new holdings
          if (newPortfolio?.allocations) {
            refetchStocks()
          }
        }}
      />

      <style jsx>{`
        .shadow-neon {
          box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
        }
      `}</style>
    </div>
  )
}
