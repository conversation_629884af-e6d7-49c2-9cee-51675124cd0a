"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { ArrowLeft, ArrowRight, CheckCircle, Play, BookOpen, Clock } from "lucide-react"

interface LessonPageProps {
  params: {
    courseId: string
    lessonId: string
  }
}

export default function LessonPage({ params }: LessonPageProps) {
  const router = useRouter()
  const [lesson, setLesson] = useState<any>(null)
  const [course, setCourse] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [completed, setCompleted] = useState(false)

  useEffect(() => {
    // Mock lesson data - in real app, fetch from API
    const courses = {
      "investing-basics": {
        id: "investing-basics",
        title: "Investing Basics for Students",
        lessons: [
          {
            id: "what-is-investing",
            title: "What is Investing?",
            duration: "15 min",
            type: "video",
            content: {
              video: "https://example.com/video1.mp4",
              transcript: `
                Welcome to your first lesson on investing! In this lesson, we'll cover the fundamental concept of investing and why it's crucial for building long-term wealth.

                **What is Investing?**
                
                Investing is the act of putting money into financial instruments, property, or other ventures with the expectation of generating income or profit over time. Unlike saving, which typically involves putting money in low-risk, low-return accounts, investing involves taking calculated risks to potentially earn higher returns.

                **Key Concepts:**
                
                1. **Time Horizon**: The length of time you plan to hold an investment
                2. **Risk vs. Return**: Generally, higher potential returns come with higher risk
                3. **Compound Interest**: Earning returns on your returns over time
                4. **Diversification**: Spreading investments across different assets to reduce risk

                **Why Should Students Invest?**
                
                As a student, you have one of the most valuable assets: time. Starting to invest early, even with small amounts, can lead to significant wealth accumulation due to compound interest.

                **Example:**
                If you invest $100 per month starting at age 20 with an average annual return of 7%, you could have over $1.3 million by age 65!
              `,
            },
            quiz: [
              {
                question: "What is the main difference between saving and investing?",
                options: [
                  "Saving is for short-term, investing is for long-term",
                  "Saving involves higher risk than investing",
                  "Investing typically offers higher potential returns but with more risk",
                  "There is no difference",
                ],
                correct: 2,
              },
            ],
          },
          {
            id: "risk-and-return",
            title: "Understanding Risk and Return",
            duration: "20 min",
            type: "video",
            content: {
              transcript: `
                In this lesson, we'll explore the fundamental relationship between risk and return in investing.

                **The Risk-Return Relationship**
                
                One of the most important concepts in investing is understanding that risk and return are directly related. Generally speaking:
                - Higher potential returns come with higher risk
                - Lower risk investments typically offer lower returns
                - There's no such thing as a "free lunch" in investing

                **Types of Risk:**
                
                1. **Market Risk**: The risk that the entire market will decline
                2. **Company-Specific Risk**: Risk related to individual companies
                3. **Inflation Risk**: The risk that inflation will erode purchasing power
                4. **Interest Rate Risk**: How changes in interest rates affect investments
                5. **Liquidity Risk**: The risk of not being able to sell an investment quickly

                **Measuring Risk:**
                
                - **Volatility**: How much an investment's price fluctuates
                - **Standard Deviation**: A statistical measure of volatility
                - **Beta**: How much an investment moves relative to the market

                **Managing Risk:**
                
                1. **Diversification**: Don't put all eggs in one basket
                2. **Asset Allocation**: Spread investments across different asset classes
                3. **Time Diversification**: Invest regularly over time
                4. **Risk Tolerance Assessment**: Know your comfort level with risk
              `,
            },
            quiz: [
              {
                question: "What is the general relationship between risk and return?",
                options: [
                  "Higher risk always means higher returns",
                  "Lower risk always means higher returns",
                  "Higher potential returns generally come with higher risk",
                  "Risk and return are not related",
                ],
                correct: 2,
              },
            ],
          },
        ],
      },
    }

    const courseData = courses[params.courseId as keyof typeof courses]
    const lessonData = courseData?.lessons.find((l: any) => l.id === params.lessonId)

    setCourse(courseData)
    setLesson(lessonData)
    setLoading(false)

    // Check if lesson is completed (mock data)
    const completedLessons = JSON.parse(localStorage.getItem("completedLessons") || "[]")
    setCompleted(completedLessons.includes(params.lessonId))
  }, [params.courseId, params.lessonId])

  const markAsCompleted = () => {
    const completedLessons = JSON.parse(localStorage.getItem("completedLessons") || "[]")
    if (!completedLessons.includes(params.lessonId)) {
      completedLessons.push(params.lessonId)
      localStorage.setItem("completedLessons", JSON.stringify(completedLessons))
      setCompleted(true)
    }
  }

  const goToNextLesson = () => {
    if (!course) return
    const currentIndex = course.lessons.findIndex((l: any) => l.id === params.lessonId)
    const nextLesson = course.lessons[currentIndex + 1]
    if (nextLesson) {
      router.push(`/learn/${params.courseId}/${nextLesson.id}`)
    } else {
      router.push(`/learn/${params.courseId}`)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!lesson || !course) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2">Lesson Not Found</h1>
          <p className="text-muted-foreground mb-4">The lesson you're looking for doesn't exist.</p>
          <Button onClick={() => router.push("/learn")}>Back to Courses</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm" onClick={() => router.push(`/learn/${params.courseId}`)}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Course
            </Button>
            <div>
              <h1 className="text-2xl font-bold">{lesson.title}</h1>
              <p className="text-muted-foreground">{course.title}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline">{lesson.type}</Badge>
            <Badge variant="secondary">
              <Clock className="h-3 w-3 mr-1" />
              {lesson.duration}
            </Badge>
            {completed && (
              <Badge variant="default" className="bg-green-600">
                <CheckCircle className="h-3 w-3 mr-1" />
                Completed
              </Badge>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-3 space-y-6">
            {/* Video Player */}
            {lesson.type === "video" && (
              <Card>
                <CardContent className="p-0">
                  <div className="aspect-video bg-black rounded-t-lg flex items-center justify-center">
                    <div className="text-center text-white">
                      <Play className="h-16 w-16 mx-auto mb-4 opacity-70" />
                      <p className="text-lg">Video Player</p>
                      <p className="text-sm opacity-70">Click to play lesson video</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Lesson Content */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="h-5 w-5" />
                  Lesson Content
                </CardTitle>
              </CardHeader>
              <CardContent className="prose max-w-none">
                <div className="whitespace-pre-line text-sm leading-relaxed">{lesson.content.transcript}</div>
              </CardContent>
            </Card>

            {/* Quiz */}
            {lesson.quiz && (
              <Card>
                <CardHeader>
                  <CardTitle>Knowledge Check</CardTitle>
                  <CardDescription>Test your understanding of this lesson</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {lesson.quiz.map((question: any, index: number) => (
                    <div key={index} className="space-y-3">
                      <h4 className="font-medium">{question.question}</h4>
                      <div className="space-y-2">
                        {question.options.map((option: string, optionIndex: number) => (
                          <label key={optionIndex} className="flex items-center space-x-2 cursor-pointer">
                            <input type="radio" name={`question-${index}`} className="text-primary" />
                            <span className="text-sm">{option}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  ))}
                  <Button className="w-full">Submit Quiz</Button>
                </CardContent>
              </Card>
            )}

            {/* Navigation */}
            <div className="flex justify-between">
              <Button variant="outline" onClick={() => router.push(`/learn/${params.courseId}`)}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Course
              </Button>
              <div className="flex gap-2">
                {!completed && (
                  <Button onClick={markAsCompleted}>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Mark as Complete
                  </Button>
                )}
                <Button onClick={goToNextLesson}>
                  Next Lesson
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Lesson Progress</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between text-sm">
                    <span>Status</span>
                    <span className={completed ? "text-green-600" : "text-muted-foreground"}>
                      {completed ? "Completed" : "In Progress"}
                    </span>
                  </div>
                  <Progress value={completed ? 100 : 50} className="h-2" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Course Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <h4 className="font-medium">{course.title}</h4>
                  <div className="text-sm text-muted-foreground space-y-1">
                    <p>{course.lessons.length} lessons</p>
                    <p>Beginner level</p>
                    <p>~2 hours total</p>
                  </div>
                  <Button variant="outline" size="sm" className="w-full bg-transparent">
                    View All Lessons
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
