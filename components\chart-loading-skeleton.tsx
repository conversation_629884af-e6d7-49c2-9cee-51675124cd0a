"use client"

/**
 * Chart Loading Skeleton Component
 * Provides loading states for different chart types with smooth animations
 */

import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { BarChart3, TrendingUp, RefreshCw } from 'lucide-react'

interface ChartLoadingSkeletonProps {
  type?: 'line' | 'area' | 'candlestick' | 'bar' | 'portfolio'
  height?: number
  showHeader?: boolean
  showControls?: boolean
  showStats?: boolean
  title?: string
  animated?: boolean
}

export function ChartLoadingSkeleton({
  type = 'line',
  height = 300,
  showHeader = true,
  showControls = false,
  showStats = false,
  title = 'Loading Chart...',
  animated = true
}: ChartLoadingSkeletonProps) {
  return (
    <Card className="bg-slate-800/50 border-slate-700">
      {showHeader && (
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <Skeleton className="h-6 w-32" />
              {showStats && <Skeleton className="h-4 w-24" />}
            </div>
            {showControls && (
              <div className="flex gap-2">
                <Skeleton className="h-8 w-20" />
                <Skeleton className="h-8 w-8" />
              </div>
            )}
          </div>
        </CardHeader>
      )}
      <CardContent>
        <div className="space-y-4">
          {/* Time period selector skeleton */}
          {showControls && (
            <div className="flex gap-2">
              {['1D', '1W', '1M', '3M', '1Y'].map((period) => (
                <Skeleton key={period} className="h-7 w-10" />
              ))}
            </div>
          )}

          {/* Chart area */}
          <div 
            className="relative bg-slate-900/50 rounded-lg border border-slate-700/50"
            style={{ height: `${height}px` }}
          >
            {animated ? (
              <AnimatedChartSkeleton type={type} height={height} />
            ) : (
              <StaticChartSkeleton type={type} height={height} />
            )}
            
            {/* Loading overlay */}
            <div className="absolute inset-0 flex items-center justify-center bg-slate-900/20 backdrop-blur-sm">
              <div className="text-center">
                <RefreshCw className="h-8 w-8 mb-3 mx-auto animate-spin text-slate-400" />
                <p className="text-sm text-slate-400">{title}</p>
              </div>
            </div>
          </div>

          {/* Stats skeleton */}
          {showStats && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className="text-center space-y-2">
                  <Skeleton className="h-6 w-16 mx-auto" />
                  <Skeleton className="h-4 w-12 mx-auto" />
                </div>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

function AnimatedChartSkeleton({ type, height }: { type: string; height: number }) {
  const generatePath = () => {
    const points = 20
    const width = 100
    let path = `M 0 ${height * 0.7}`
    
    for (let i = 1; i <= points; i++) {
      const x = (i / points) * width
      const y = height * 0.3 + Math.random() * height * 0.4
      path += ` L ${x} ${y}`
    }
    
    return path
  }

  const [path, setPath] = React.useState(generatePath())

  React.useEffect(() => {
    const interval = setInterval(() => {
      setPath(generatePath())
    }, 2000)

    return () => clearInterval(interval)
  }, [height])

  if (type === 'bar') {
    return (
      <svg className="w-full h-full" viewBox={`0 0 100 ${height}`}>
        {Array.from({ length: 12 }).map((_, i) => (
          <rect
            key={i}
            x={i * 8 + 2}
            y={height * 0.3 + Math.random() * height * 0.4}
            width="6"
            height={height * 0.2 + Math.random() * height * 0.3}
            fill="rgb(148 163 184)"
            opacity="0.3"
            className="animate-pulse"
            style={{ animationDelay: `${i * 100}ms` }}
          />
        ))}
      </svg>
    )
  }

  if (type === 'candlestick') {
    return (
      <svg className="w-full h-full" viewBox={`0 0 100 ${height}`}>
        {Array.from({ length: 10 }).map((_, i) => {
          const x = i * 10 + 5
          const high = height * 0.2 + Math.random() * height * 0.2
          const low = height * 0.6 + Math.random() * height * 0.2
          const open = high + Math.random() * (low - high)
          const close = high + Math.random() * (low - high)
          
          return (
            <g key={i} className="animate-pulse" style={{ animationDelay: `${i * 150}ms` }}>
              {/* Wick */}
              <line
                x1={x}
                y1={high}
                x2={x}
                y2={low}
                stroke="rgb(148 163 184)"
                strokeWidth="0.5"
                opacity="0.4"
              />
              {/* Body */}
              <rect
                x={x - 2}
                y={Math.min(open, close)}
                width="4"
                height={Math.abs(close - open)}
                fill={close > open ? "rgb(34 197 94)" : "rgb(239 68 68)"}
                opacity="0.3"
              />
            </g>
          )
        })}
      </svg>
    )
  }

  // Default line/area chart
  return (
    <svg className="w-full h-full" viewBox={`0 0 100 ${height}`}>
      {/* Grid lines */}
      <defs>
        <pattern id="grid-skeleton" width="10" height={height / 5} patternUnits="userSpaceOnUse">
          <path
            d={`M 10 0 L 0 0 0 ${height / 5}`}
            fill="none"
            stroke="rgb(148 163 184)"
            strokeWidth="0.5"
            opacity="0.1"
          />
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#grid-skeleton)" />

      {/* Animated line */}
      <path
        d={path}
        fill="none"
        stroke="rgb(59 130 246)"
        strokeWidth="2"
        opacity="0.4"
        className="transition-all duration-2000 ease-in-out"
      />

      {/* Data points */}
      {Array.from({ length: 8 }).map((_, i) => (
        <circle
          key={i}
          cx={(i / 7) * 100}
          cy={height * 0.3 + Math.random() * height * 0.4}
          r="2"
          fill="rgb(59 130 246)"
          opacity="0.4"
          className="animate-pulse"
          style={{ animationDelay: `${i * 200}ms` }}
        />
      ))}

      {/* Area fill for area charts */}
      {type === 'area' && (
        <defs>
          <linearGradient id="area-gradient-skeleton" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor="rgb(59 130 246)" stopOpacity="0.2" />
            <stop offset="100%" stopColor="rgb(59 130 246)" stopOpacity="0" />
          </linearGradient>
        </defs>
      )}
      {type === 'area' && (
        <path
          d={`${path} L 100 ${height} L 0 ${height} Z`}
          fill="url(#area-gradient-skeleton)"
          className="transition-all duration-2000 ease-in-out"
        />
      )}
    </svg>
  )
}

function StaticChartSkeleton({ type, height }: { type: string; height: number }) {
  if (type === 'bar') {
    return (
      <div className="w-full h-full flex items-end justify-around p-4">
        {Array.from({ length: 12 }).map((_, i) => (
          <Skeleton
            key={i}
            className="w-4"
            style={{ height: `${20 + Math.random() * 60}%` }}
          />
        ))}
      </div>
    )
  }

  return (
    <div className="w-full h-full p-4">
      {/* Y-axis labels */}
      <div className="absolute left-2 top-4 bottom-4 flex flex-col justify-between">
        {Array.from({ length: 5 }).map((_, i) => (
          <Skeleton key={i} className="h-3 w-8" />
        ))}
      </div>

      {/* Chart area */}
      <div className="ml-12 mr-4 h-full relative">
        {/* Grid lines */}
        <div className="absolute inset-0 flex flex-col justify-between">
          {Array.from({ length: 5 }).map((_, i) => (
            <Skeleton key={i} className="h-px w-full" />
          ))}
        </div>

        {/* Chart line */}
        <div className="absolute inset-0 flex items-center">
          <Skeleton className="h-0.5 w-full" />
        </div>
      </div>

      {/* X-axis labels */}
      <div className="ml-12 mr-4 mt-2 flex justify-between">
        {Array.from({ length: 6 }).map((_, i) => (
          <Skeleton key={i} className="h-3 w-8" />
        ))}
      </div>
    </div>
  )
}

// Portfolio-specific loading skeleton
export function PortfolioChartLoadingSkeleton() {
  return (
    <ChartLoadingSkeleton
      type="area"
      height={160}
      showHeader={false}
      showControls={true}
      showStats={true}
      title="Loading portfolio performance..."
      animated={true}
    />
  )
}

// Stock chart loading skeleton
export function StockChartLoadingSkeleton() {
  return (
    <ChartLoadingSkeleton
      type="area"
      height={300}
      showHeader={true}
      showControls={false}
      showStats={false}
      title="Loading stock chart..."
      animated={true}
    />
  )
}

// Market movers loading skeleton
export function MarketMoversLoadingSkeleton() {
  return (
    <div className="space-y-4">
      {Array.from({ length: 5 }).map((_, i) => (
        <div key={i} className="flex items-center justify-between p-3 bg-slate-800/30 rounded-lg">
          <div className="flex items-center gap-3">
            <Skeleton className="w-8 h-8 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-3 w-24" />
            </div>
          </div>
          <div className="text-right space-y-2">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-3 w-12" />
          </div>
        </div>
      ))}
    </div>
  )
}

// Add React import for useState
import React from 'react'
