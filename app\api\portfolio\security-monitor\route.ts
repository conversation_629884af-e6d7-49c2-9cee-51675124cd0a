/**
 * Security Monitoring API Endpoint
 * GET /api/portfolio/security-monitor
 * SECURITY: Monitor LLM system security events and threats
 */

import { NextRequest, NextResponse } from 'next/server'
import { withAuth, addSecurityHeaders } from '@/lib/llm-portfolio/auth-middleware'
import { createClient } from '@supabase/supabase-js'
import type { AuthContext } from '@/lib/llm-portfolio/auth-middleware'

async function securityMonitorHandler(
  request: NextRequest,
  context: AuthContext
): Promise<NextResponse> {
  try {
    // Initialize Supabase client
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    )

    const url = new URL(request.url)
    const timeframe = url.searchParams.get('timeframe') || '24h'
    const userId = url.searchParams.get('user_id') || context.userId

    // Calculate time range
    const now = new Date()
    const timeRanges = {
      '1h': new Date(now.getTime() - 60 * 60 * 1000),
      '24h': new Date(now.getTime() - 24 * 60 * 60 * 1000),
      '7d': new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
      '30d': new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    }
    const startTime = timeRanges[timeframe as keyof typeof timeRanges] || timeRanges['24h']

    // Get security events
    const { data: securityEvents, error: securityError } = await supabase
      .from('llm_audit_logs')
      .select('*')
      .eq('user_id', userId)
      .like('action', 'security_%')
      .gte('created_at', startTime.toISOString())
      .order('created_at', { ascending: false })
      .limit(100)

    if (securityError) {
      throw new Error(`Failed to fetch security events: ${securityError.message}`)
    }

    // Get general audit logs for context
    const { data: auditLogs, error: auditError } = await supabase
      .from('llm_audit_logs')
      .select('action, success, created_at, details')
      .eq('user_id', userId)
      .gte('created_at', startTime.toISOString())
      .order('created_at', { ascending: false })
      .limit(50)

    if (auditError) {
      throw new Error(`Failed to fetch audit logs: ${auditError.message}`)
    }

    // Analyze security events
    const analysis = analyzeSecurityEvents(securityEvents || [])
    const activitySummary = analyzeActivityLogs(auditLogs || [])

    const response = NextResponse.json({
      success: true,
      data: {
        timeframe,
        userId,
        securitySummary: {
          totalSecurityEvents: securityEvents?.length || 0,
          riskLevel: analysis.overallRiskLevel,
          threatCategories: analysis.threatCategories,
          recentThreats: analysis.recentThreats.slice(0, 10)
        },
        activitySummary: {
          totalRequests: activitySummary.totalRequests,
          successRate: activitySummary.successRate,
          averageResponseTime: activitySummary.averageResponseTime,
          mostCommonActions: activitySummary.mostCommonActions
        },
        securityEvents: securityEvents?.slice(0, 20) || [],
        recommendations: generateSecurityRecommendations(analysis),
        timestamp: new Date().toISOString()
      }
    })

    addSecurityHeaders(response)
    return response

  } catch (error) {
    console.error('Security monitoring error:', error)
    
    const response = NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch security monitoring data',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )

    addSecurityHeaders(response)
    return response
  }
}

/**
 * Analyze security events for threats and patterns
 */
function analyzeSecurityEvents(events: any[]) {
  const threatCategories = {
    inputSanitization: 0,
    responseFiltering: 0,
    promptInjection: 0,
    suspiciousActivity: 0
  }

  const recentThreats: any[] = []
  let highRiskEvents = 0

  events.forEach(event => {
    const action = event.action.replace('security_', '')
    const severity = event.details?.severity || 'low'
    
    // Count by category
    if (action === 'input_sanitization') threatCategories.inputSanitization++
    else if (action === 'response_filtering') threatCategories.responseFiltering++
    else if (action === 'prompt_injection_attempt') threatCategories.promptInjection++
    else if (action === 'suspicious_activity') threatCategories.suspiciousActivity++

    // Track high-risk events
    if (severity === 'high' || severity === 'critical') {
      highRiskEvents++
      recentThreats.push({
        timestamp: event.created_at,
        type: action,
        severity,
        details: event.details
      })
    }
  })

  // Determine overall risk level
  let overallRiskLevel: 'low' | 'medium' | 'high' | 'critical' = 'low'
  if (highRiskEvents > 10) overallRiskLevel = 'critical'
  else if (highRiskEvents > 5) overallRiskLevel = 'high'
  else if (highRiskEvents > 0 || events.length > 20) overallRiskLevel = 'medium'

  return {
    overallRiskLevel,
    threatCategories,
    recentThreats: recentThreats.sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    ),
    highRiskEventCount: highRiskEvents
  }
}

/**
 * Analyze general activity logs
 */
function analyzeActivityLogs(logs: any[]) {
  const actionCounts: Record<string, number> = {}
  let successCount = 0
  let totalResponseTime = 0
  let responseTimeCount = 0

  logs.forEach(log => {
    // Count actions
    actionCounts[log.action] = (actionCounts[log.action] || 0) + 1
    
    // Count successes
    if (log.success) successCount++
    
    // Track response times
    if (log.details?.processingTimeMs) {
      totalResponseTime += log.details.processingTimeMs
      responseTimeCount++
    }
  })

  const mostCommonActions = Object.entries(actionCounts)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 5)
    .map(([action, count]) => ({ action, count }))

  return {
    totalRequests: logs.length,
    successRate: logs.length > 0 ? (successCount / logs.length) * 100 : 0,
    averageResponseTime: responseTimeCount > 0 ? totalResponseTime / responseTimeCount : 0,
    mostCommonActions
  }
}

/**
 * Generate security recommendations based on analysis
 */
function generateSecurityRecommendations(analysis: any): string[] {
  const recommendations: string[] = []

  if (analysis.overallRiskLevel === 'critical') {
    recommendations.push('CRITICAL: Immediate security review required - multiple high-risk events detected')
    recommendations.push('Consider temporarily restricting LLM access until threats are investigated')
  } else if (analysis.overallRiskLevel === 'high') {
    recommendations.push('HIGH RISK: Enhanced monitoring recommended')
    recommendations.push('Review recent prompt injection attempts and user behavior patterns')
  }

  if (analysis.threatCategories.promptInjection > 0) {
    recommendations.push('Prompt injection attempts detected - review input sanitization rules')
  }

  if (analysis.threatCategories.suspiciousActivity > 5) {
    recommendations.push('Multiple suspicious activities detected - consider user behavior analysis')
  }

  if (analysis.threatCategories.inputSanitization > 10) {
    recommendations.push('High input sanitization activity - users may be testing system boundaries')
  }

  if (analysis.threatCategories.responseFiltering > 5) {
    recommendations.push('Response filtering active - LLM may be generating sensitive content')
  }

  if (recommendations.length === 0) {
    recommendations.push('Security status normal - continue regular monitoring')
  }

  return recommendations
}

// Export with admin-level authentication
export const GET = withAuth(securityMonitorHandler, {
  requireAuth: true,
  rateLimitPerHour: 100,
  allowedRoles: ['admin', 'security'] // Only allow admin/security roles
})

// Handle OPTIONS for CORS
export async function OPTIONS() {
  const response = new NextResponse(null, { status: 200 })
  addSecurityHeaders(response)
  return response
}
