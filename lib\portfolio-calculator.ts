/**
 * Portfolio Calculator
 * Computes portfolio performance metrics and builds time series data
 */

import { createClient } from './supabase'
import { getStockHistoryService } from './stock-history-service'
import { getStockDataService } from './stock-config'
import { getUserCapitalResolver } from './user-capital-resolver'
import type { Timeframe } from './types/stock-history'

export interface PortfolioHolding {
  id: string
  portfolio_id: string
  symbol: string
  shares: number
  average_cost: number
  current_price: number | null
  total_cost?: number
  unrealized_gain_loss?: number
  unrealized_gain_loss_percent?: number
  last_price_update?: string
  created_at: string
  updated_at: string
}

export interface InvestmentTransaction {
  id: string
  user_id: string
  portfolio_id: string
  symbol: string
  transaction_type: 'buy' | 'sell'
  shares: number
  price: number
  total_amount: number
  fees: number
  status: string
  executed_at: string
  created_at: string
}

export interface PortfolioMetrics {
  totalValue: number
  totalInvested: number
  totalCash: number
  totalReturn: number
  totalReturnPercent: number
  dayChange: number
  dayChangePercent: number
  unrealizedGainLoss: number
  unrealizedGainLossPercent: number
  realizedGainLoss: number
  holdingsCount: number
  lastUpdated: string
}

export interface PortfolioTimeSeriesPoint {
  date: string
  timestamp: number
  portfolioValue: number
  cashValue: number
  investedValue: number
  totalValue: number
  totalReturn: number
  totalReturnPercent: number
  dayChange: number
  dayChangePercent: number
  deposits: number
  withdrawals: number
  netFlow: number
}

export interface PortfolioPerformanceData {
  metrics: PortfolioMetrics
  timeSeries: PortfolioTimeSeriesPoint[]
  holdings: PortfolioHolding[]
  topPerformers: Array<{ symbol: string; return: number; returnPercent: number }>
  worstPerformers: Array<{ symbol: string; return: number; returnPercent: number }>
  sectorAllocation: Array<{ sector: string; value: number; percent: number }>
  period: Timeframe
  startDate: string
  endDate: string
  lastCalculated: string
}

export class PortfolioCalculator {
  private supabase = createClient()
  private stockHistoryService = getStockHistoryService()
  private stockDataService = getStockDataService()
  private capitalResolver = getUserCapitalResolver()

  /**
   * Calculate comprehensive portfolio performance
   */
  async calculatePortfolioPerformance(
    userId: string,
    portfolioId?: string,
    timeframe: Timeframe = '1M'
  ): Promise<PortfolioPerformanceData | null> {
    try {
      // Get user's portfolio (default if not specified)
      const portfolio = await this.getPortfolio(userId, portfolioId)
      if (!portfolio) {
        return this.getEmptyPortfolioData(timeframe)
      }

      // Get current holdings
      const holdings = await this.getPortfolioHoldings(portfolio.id)
      
      // Get user capital information
      const capitalSummary = await this.capitalResolver.getUserCapitalSummary(userId)
      
      // Update current prices for all holdings
      const updatedHoldings = await this.updateHoldingPrices(holdings)
      
      // Calculate current metrics
      const metrics = await this.calculateCurrentMetrics(
        updatedHoldings, 
        capitalSummary,
        portfolio.initial_value || 10000
      )

      // Build time series data
      const timeSeries = await this.buildTimeSeries(
        userId,
        portfolio.id,
        timeframe,
        portfolio.performance_start_date || portfolio.created_at
      )

      // Calculate performance analytics
      const topPerformers = this.getTopPerformers(updatedHoldings, 5)
      const worstPerformers = this.getWorstPerformers(updatedHoldings, 5)
      const sectorAllocation = await this.calculateSectorAllocation(updatedHoldings)

      // Determine date range
      const { startDate, endDate } = this.getDateRange(timeframe)

      return {
        metrics,
        timeSeries,
        holdings: updatedHoldings,
        topPerformers,
        worstPerformers,
        sectorAllocation,
        period: timeframe,
        startDate,
        endDate,
        lastCalculated: new Date().toISOString()
      }

    } catch (error) {
      console.error('Error calculating portfolio performance:', error)
      return null
    }
  }

  /**
   * Get portfolio (default if not specified)
   */
  private async getPortfolio(userId: string, portfolioId?: string) {
    let query = this.supabase
      .from('portfolios')
      .select('*')
      .eq('user_id', userId)

    if (portfolioId) {
      query = query.eq('id', portfolioId)
    } else {
      query = query.eq('is_default', true)
    }

    const { data, error } = await query.single()

    if (error) {
      console.error('Error fetching portfolio:', error)
      return null
    }

    return data
  }

  /**
   * Get portfolio holdings
   */
  private async getPortfolioHoldings(portfolioId: string): Promise<PortfolioHolding[]> {
    const { data, error } = await this.supabase
      .from('portfolio_holdings')
      .select('*')
      .eq('portfolio_id', portfolioId)
      .gt('shares', 0) // Only active holdings

    if (error) {
      console.error('Error fetching portfolio holdings:', error)
      return []
    }

    return data || []
  }

  /**
   * Update current prices for holdings
   */
  private async updateHoldingPrices(holdings: PortfolioHolding[]): Promise<PortfolioHolding[]> {
    if (holdings.length === 0) return []

    try {
      // Get current prices for all symbols
      const symbols = holdings.map(h => h.symbol)
      const pricePromises = symbols.map(symbol => 
        this.stockDataService.getStockQuote(symbol)
      )

      const priceResults = await Promise.allSettled(pricePromises)

      // Update holdings with current prices
      return holdings.map((holding, index) => {
        const priceResult = priceResults[index]
        let currentPrice = holding.current_price

        if (priceResult.status === 'fulfilled' && priceResult.value) {
          currentPrice = priceResult.value.price
        }

        // Calculate derived values
        const totalCost = holding.shares * holding.average_cost
        const currentValue = holding.shares * (currentPrice || holding.average_cost)
        const unrealizedGainLoss = currentValue - totalCost
        const unrealizedGainLossPercent = totalCost > 0 ? (unrealizedGainLoss / totalCost) * 100 : 0

        return {
          ...holding,
          current_price: currentPrice,
          total_cost: totalCost,
          unrealized_gain_loss: unrealizedGainLoss,
          unrealized_gain_loss_percent: unrealizedGainLossPercent,
          last_price_update: new Date().toISOString()
        }
      })

    } catch (error) {
      console.error('Error updating holding prices:', error)
      return holdings
    }
  }

  /**
   * Calculate current portfolio metrics
   */
  private async calculateCurrentMetrics(
    holdings: PortfolioHolding[],
    capitalSummary: any,
    initialValue: number
  ): Promise<PortfolioMetrics> {
    const totalCash = capitalSummary?.totalCash || 0
    const totalInvested = holdings.reduce((sum, h) => sum + (h.shares * (h.current_price || h.average_cost)), 0)
    const totalValue = totalCash + totalInvested
    const totalCost = holdings.reduce((sum, h) => sum + (h.total_cost || 0), 0)
    
    const totalReturn = totalValue - initialValue
    const totalReturnPercent = initialValue > 0 ? (totalReturn / initialValue) * 100 : 0
    
    const unrealizedGainLoss = holdings.reduce((sum, h) => sum + (h.unrealized_gain_loss || 0), 0)
    const unrealizedGainLossPercent = totalCost > 0 ? (unrealizedGainLoss / totalCost) * 100 : 0

    return {
      totalValue,
      totalInvested,
      totalCash,
      totalReturn,
      totalReturnPercent,
      dayChange: 0, // Will be calculated from time series
      dayChangePercent: 0,
      unrealizedGainLoss,
      unrealizedGainLossPercent,
      realizedGainLoss: 0, // Would need transaction analysis
      holdingsCount: holdings.length,
      lastUpdated: new Date().toISOString()
    }
  }

  /**
   * Build time series data
   */
  private async buildTimeSeries(
    userId: string,
    portfolioId: string,
    timeframe: Timeframe,
    startDate: string
  ): Promise<PortfolioTimeSeriesPoint[]> {
    try {
      // Get date range for timeframe
      const { startDate: rangeStart, endDate: rangeEnd } = this.getDateRange(timeframe, startDate)

      // Get capital history
      const capitalHistory = await this.capitalResolver.getCapitalHistory(
        userId,
        rangeStart,
        rangeEnd,
        'daily'
      )

      // Get portfolio performance history from database
      const { data: performanceHistory } = await this.supabase
        .from('portfolio_performance_history')
        .select('*')
        .eq('portfolio_id', portfolioId)
        .gte('date', rangeStart.split('T')[0])
        .lte('date', rangeEnd.split('T')[0])
        .order('date', { ascending: true })

      // Combine capital and performance data
      const timeSeries: PortfolioTimeSeriesPoint[] = []
      
      for (const capitalPoint of capitalHistory) {
        const performancePoint = performanceHistory?.find(p => p.date === capitalPoint.date)
        
        timeSeries.push({
          date: capitalPoint.date,
          timestamp: capitalPoint.timestamp,
          portfolioValue: performancePoint?.total_value || capitalPoint.totalValue,
          cashValue: capitalPoint.cashBalance,
          investedValue: capitalPoint.investedAmount,
          totalValue: capitalPoint.totalValue,
          totalReturn: performancePoint?.total_return || 0,
          totalReturnPercent: performancePoint?.total_return_percent || 0,
          dayChange: performancePoint?.daily_return || 0,
          dayChangePercent: performancePoint?.daily_return_percent || 0,
          deposits: capitalPoint.deposits,
          withdrawals: capitalPoint.withdrawals,
          netFlow: capitalPoint.netFlow
        })
      }

      return timeSeries

    } catch (error) {
      console.error('Error building time series:', error)
      return []
    }
  }

  /**
   * Get top performing holdings
   */
  private getTopPerformers(holdings: PortfolioHolding[], limit: number = 5) {
    return holdings
      .filter(h => h.unrealized_gain_loss_percent !== undefined)
      .sort((a, b) => (b.unrealized_gain_loss_percent || 0) - (a.unrealized_gain_loss_percent || 0))
      .slice(0, limit)
      .map(h => ({
        symbol: h.symbol,
        return: h.unrealized_gain_loss || 0,
        returnPercent: h.unrealized_gain_loss_percent || 0
      }))
  }

  /**
   * Get worst performing holdings
   */
  private getWorstPerformers(holdings: PortfolioHolding[], limit: number = 5) {
    return holdings
      .filter(h => h.unrealized_gain_loss_percent !== undefined)
      .sort((a, b) => (a.unrealized_gain_loss_percent || 0) - (b.unrealized_gain_loss_percent || 0))
      .slice(0, limit)
      .map(h => ({
        symbol: h.symbol,
        return: h.unrealized_gain_loss || 0,
        returnPercent: h.unrealized_gain_loss_percent || 0
      }))
  }

  /**
   * Calculate sector allocation (placeholder - would need sector data)
   */
  private async calculateSectorAllocation(holdings: PortfolioHolding[]) {
    // This would integrate with stock data service to get sector information
    // For now, return empty array
    return []
  }

  /**
   * Get date range for timeframe
   */
  private getDateRange(timeframe: Timeframe, portfolioStartDate?: string) {
    const now = new Date()
    const endDate = now.toISOString()
    let startDate: string

    switch (timeframe) {
      case '1D':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString()
        break
      case '1W':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString()
        break
      case '1M':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString()
        break
      case '3M':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000).toISOString()
        break
      case '1Y':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000).toISOString()
        break
      case 'All':
        startDate = portfolioStartDate || new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000).toISOString()
        break
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString()
    }

    return { startDate, endDate }
  }

  /**
   * Return empty portfolio data for edge cases
   */
  private getEmptyPortfolioData(timeframe: Timeframe): PortfolioPerformanceData {
    const { startDate, endDate } = this.getDateRange(timeframe)
    
    return {
      metrics: {
        totalValue: 0,
        totalInvested: 0,
        totalCash: 0,
        totalReturn: 0,
        totalReturnPercent: 0,
        dayChange: 0,
        dayChangePercent: 0,
        unrealizedGainLoss: 0,
        unrealizedGainLossPercent: 0,
        realizedGainLoss: 0,
        holdingsCount: 0,
        lastUpdated: new Date().toISOString()
      },
      timeSeries: [],
      holdings: [],
      topPerformers: [],
      worstPerformers: [],
      sectorAllocation: [],
      period: timeframe,
      startDate,
      endDate,
      lastCalculated: new Date().toISOString()
    }
  }
}

// Singleton instance
let portfolioCalculator: PortfolioCalculator | null = null

export function getPortfolioCalculator(): PortfolioCalculator {
  if (!portfolioCalculator) {
    portfolioCalculator = new PortfolioCalculator()
  }
  return portfolioCalculator
}
