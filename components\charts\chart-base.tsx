/**
 * Base Chart Components
 * Reusable chart components with common functionality
 */

'use client'

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { RefreshCw, TrendingUp, TrendingDown } from 'lucide-react'
import { cn } from '@/lib/utils'

export type TimeRange = '1D' | '1W' | '1M' | '3M' | '1Y' | 'All'
export type ValueType = 'absolute' | 'percentage'

export interface ChartDataPoint {
  timestamp: number
  value: number
  date?: string
  label?: string
}

export interface ChartProps {
  title?: string
  subtitle?: string
  data: ChartDataPoint[]
  loading?: boolean
  error?: string
  timeRange?: TimeRange
  valueType?: ValueType
  onTimeRangeChange?: (range: TimeRange) => void
  onValueTypeChange?: (type: ValueType) => void
  onRefresh?: () => void
  className?: string
  height?: number
  showControls?: boolean
  showRefresh?: boolean
}

export interface ChartContainerProps {
  title?: string
  subtitle?: string
  loading?: boolean
  error?: string
  onRefresh?: () => void
  showRefresh?: boolean
  className?: string
  children: React.ReactNode
}

/**
 * Chart Container with loading states and error handling
 */
export function ChartContainer({
  title,
  subtitle,
  loading,
  error,
  onRefresh,
  showRefresh = true,
  className,
  children
}: ChartContainerProps) {
  return (
    <Card className={cn('w-full', className)}>
      {(title || subtitle || showRefresh) && (
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div className="space-y-1">
            {title && (
              <CardTitle className="text-base font-medium">{title}</CardTitle>
            )}
            {subtitle && (
              <p className="text-sm text-muted-foreground">{subtitle}</p>
            )}
          </div>
          {showRefresh && onRefresh && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onRefresh}
              disabled={loading}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={cn('h-4 w-4', loading && 'animate-spin')} />
            </Button>
          )}
        </CardHeader>
      )}
      <CardContent>
        {error ? (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        ) : loading ? (
          <ChartSkeleton />
        ) : (
          children
        )}
      </CardContent>
    </Card>
  )
}

/**
 * Chart loading skeleton
 */
export function ChartSkeleton({ height = 300 }: { height?: number }) {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Skeleton className="h-4 w-32" />
        <Skeleton className="h-4 w-20" />
      </div>
      <Skeleton className={`w-full`} style={{ height: `${height}px` }} />
      <div className="flex justify-center space-x-2">
        {Array.from({ length: 6 }).map((_, i) => (
          <Skeleton key={i} className="h-8 w-12" />
        ))}
      </div>
    </div>
  )
}

/**
 * Time Range Selector
 */
export interface TimeRangeSelectorProps {
  value: TimeRange
  onChange: (range: TimeRange) => void
  disabled?: boolean
  className?: string
}

export function TimeRangeSelector({
  value,
  onChange,
  disabled,
  className
}: TimeRangeSelectorProps) {
  const ranges: TimeRange[] = ['1D', '1W', '1M', '3M', '1Y', 'All']

  return (
    <div className={cn('flex space-x-1', className)}>
      {ranges.map((range) => (
        <Button
          key={range}
          variant={value === range ? 'default' : 'outline'}
          size="sm"
          onClick={() => onChange(range)}
          disabled={disabled}
          className="h-8 px-3 text-xs"
        >
          {range}
        </Button>
      ))}
    </div>
  )
}

/**
 * Value Type Toggle ($ vs %)
 */
export interface ValueTypeToggleProps {
  value: ValueType
  onChange: (type: ValueType) => void
  disabled?: boolean
  className?: string
}

export function ValueTypeToggle({
  value,
  onChange,
  disabled,
  className
}: ValueTypeToggleProps) {
  return (
    <div className={cn('flex space-x-1', className)}>
      <Button
        variant={value === 'absolute' ? 'default' : 'outline'}
        size="sm"
        onClick={() => onChange('absolute')}
        disabled={disabled}
        className="h-8 px-3 text-xs"
      >
        $
      </Button>
      <Button
        variant={value === 'percentage' ? 'default' : 'outline'}
        size="sm"
        onClick={() => onChange('percentage')}
        disabled={disabled}
        className="h-8 px-3 text-xs"
      >
        %
      </Button>
    </div>
  )
}

/**
 * Chart Controls (Time Range + Value Type)
 */
export interface ChartControlsProps {
  timeRange: TimeRange
  valueType: ValueType
  onTimeRangeChange: (range: TimeRange) => void
  onValueTypeChange: (type: ValueType) => void
  disabled?: boolean
  showValueType?: boolean
  className?: string
}

export function ChartControls({
  timeRange,
  valueType,
  onTimeRangeChange,
  onValueTypeChange,
  disabled,
  showValueType = true,
  className
}: ChartControlsProps) {
  return (
    <div className={cn('flex flex-col sm:flex-row gap-2 justify-between items-start sm:items-center', className)}>
      <TimeRangeSelector
        value={timeRange}
        onChange={onTimeRangeChange}
        disabled={disabled}
      />
      {showValueType && (
        <ValueTypeToggle
          value={valueType}
          onChange={onValueTypeChange}
          disabled={disabled}
        />
      )}
    </div>
  )
}

/**
 * Performance Indicator
 */
export interface PerformanceIndicatorProps {
  value: number
  previousValue?: number
  format?: 'currency' | 'percentage'
  className?: string
}

export function PerformanceIndicator({
  value,
  previousValue,
  format = 'currency',
  className
}: PerformanceIndicatorProps) {
  const change = previousValue ? value - previousValue : 0
  const changePercent = previousValue ? (change / previousValue) * 100 : 0
  const isPositive = change >= 0

  const formatValue = (val: number) => {
    if (format === 'percentage') {
      return `${val.toFixed(2)}%`
    }
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(val)
  }

  return (
    <div className={cn('flex items-center space-x-2', className)}>
      <span className="text-2xl font-bold">
        {formatValue(value)}
      </span>
      {previousValue && (
        <div className={cn(
          'flex items-center space-x-1 text-sm',
          isPositive ? 'text-green-600' : 'text-red-600'
        )}>
          {isPositive ? (
            <TrendingUp className="h-4 w-4" />
          ) : (
            <TrendingDown className="h-4 w-4" />
          )}
          <span>
            {isPositive ? '+' : ''}{formatValue(change)} ({changePercent.toFixed(2)}%)
          </span>
        </div>
      )}
    </div>
  )
}

/**
 * Empty Chart State
 */
export interface EmptyChartStateProps {
  title?: string
  description?: string
  action?: {
    label: string
    onClick: () => void
  }
  className?: string
}

export function EmptyChartState({
  title = 'No data available',
  description = 'There is no data to display for the selected time range.',
  action,
  className
}: EmptyChartStateProps) {
  return (
    <div className={cn('flex flex-col items-center justify-center py-12 text-center', className)}>
      <div className="rounded-full bg-muted p-3 mb-4">
        <TrendingUp className="h-6 w-6 text-muted-foreground" />
      </div>
      <h3 className="text-lg font-medium mb-2">{title}</h3>
      <p className="text-sm text-muted-foreground mb-4 max-w-sm">
        {description}
      </p>
      {action && (
        <Button onClick={action.onClick} variant="outline">
          {action.label}
        </Button>
      )}
    </div>
  )
}

/**
 * Chart utility functions
 */
export const chartUtils = {
  formatCurrency: (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value)
  },

  formatPercentage: (value: number) => {
    return `${value.toFixed(2)}%`
  },

  formatDate: (timestamp: number, timeRange: TimeRange) => {
    const date = new Date(timestamp)
    
    switch (timeRange) {
      case '1D':
        return date.toLocaleTimeString('en-US', { 
          hour: '2-digit', 
          minute: '2-digit' 
        })
      case '1W':
      case '1M':
        return date.toLocaleDateString('en-US', { 
          month: 'short', 
          day: 'numeric' 
        })
      case '3M':
      case '1Y':
      case 'All':
        return date.toLocaleDateString('en-US', { 
          month: 'short', 
          year: 'numeric' 
        })
      default:
        return date.toLocaleDateString('en-US')
    }
  },

  getChartColors: (theme?: 'light' | 'dark') => ({
    primary: theme === 'dark' ? '#3b82f6' : '#2563eb',
    success: theme === 'dark' ? '#10b981' : '#059669',
    danger: theme === 'dark' ? '#ef4444' : '#dc2626',
    warning: theme === 'dark' ? '#f59e0b' : '#d97706',
    muted: theme === 'dark' ? '#6b7280' : '#9ca3af',
    background: theme === 'dark' ? '#1f2937' : '#ffffff',
    foreground: theme === 'dark' ? '#f9fafb' : '#111827'
  })
}
