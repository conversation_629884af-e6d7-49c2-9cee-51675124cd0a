"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Wallet, Eye, EyeOff, ArrowLeft } from "lucide-react"
import { useNotifications } from "@/components/notification-system"
import { useAuth } from "@/components/auth-provider"
import { hasCompletedOnboarding, getRedirectPath } from "@/lib/onboarding-utils"

export default function SignInPage() {
  const router = useRouter()
  const { addNotification } = useNotifications()
  const { signIn, signOut, user, loading: authLoading, isConfigured } = useAuth()
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [rememberMe, setRememberMe] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  // Removed 2FA auth flow

  useEffect(() => {
    // If user is already authenticated, redirect to dashboard
    if (!authLoading && user) {
      console.log("User already authenticated, redirecting to dashboard")
      const redirectPath = getRedirectPath(user.id)
      router.push(redirectPath)
      return
    }

    // Load remembered email only if not loading and no user
    if (!authLoading && !user) {
      const rememberedEmail = localStorage.getItem("investry_remembered_email")
      if (rememberedEmail) {
        setEmail(rememberedEmail)
        setRememberMe(true)
      }
    }
  }, [user, authLoading, router])

  // Handle auth state changes and redirect after successful authentication
  useEffect(() => {
    console.log("Auth state effect:", { authLoading, user: user?.email, isLoading })

    if (!authLoading && user && !isLoading) {
      // User is authenticated, redirect to dashboard
      const redirectPath = getRedirectPath(user.id)
      const isNewUser = !hasCompletedOnboarding(user.id)

      addNotification({
        type: "success",
        title: isNewUser ? "Welcome to investry!" : "Welcome back!",
        message: isNewUser
          ? "Let's create your personalized investment portfolio."
          : "You've successfully signed in to investry.",
      })

      console.log("Auth state changed - redirecting to:", redirectPath)
      router.push(redirectPath)
    }
  }, [user, authLoading, isLoading, router, addNotification])

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // Check if Supabase is configured
      if (!isConfigured) {
        addNotification({
          type: "error",
          title: "Configuration Error",
          message: "Database connection not configured. Please contact support.",
        })
        setIsLoading(false)
        return
      }

      // Basic validation
      if (!email || !password) {
        addNotification({
          type: "error",
          title: "Missing information",
          message: "Please enter both email and password.",
        })
        setIsLoading(false)
        return
      }

      console.log("Attempting to sign in with email:", email)

      // Sign in with regular auth provider
      const { data, error } = await signIn(email, password)

      console.log("Sign in response:", { data: data?.user?.email, error: error?.message })

      if (error) {
        addNotification({
          type: "error",
          title: "Sign in failed",
          message: error.message || "Invalid email or password. Please try again.",
        })
        setIsLoading(false)
        return
      }

      if (data?.user) {
        // Handle remember me
        if (rememberMe) {
          localStorage.setItem("investry_remembered_email", email)
        } else {
          localStorage.removeItem("investry_remembered_email")
        }

        console.log("Sign in successful, waiting for auth state to update...")
        // Don't redirect immediately - let the auth state change effect handle it
        // This prevents race conditions where the middleware hasn't updated yet
      }
    } catch (error) {
      console.error("Sign in error:", error)
      addNotification({
        type: "error",
        title: "Sign in failed",
        message: "An unexpected error occurred. Please try again.",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleGoogleSignIn = async () => {
    addNotification({
      type: "info",
      title: "Google Sign In",
      message: "Google OAuth will be available once your Supabase project is fully configured.",
    })
  }

  // Removed 2FA phone verification handler

  // Show loading while auth state is being determined
  if (authLoading) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-white">Loading...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Back button */}
        <Button variant="ghost" onClick={() => router.push("/")} className="text-slate-400 hover:text-white">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Home
        </Button>

        {/* Logo */}
        <div className="text-center">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Wallet className="h-8 w-8 text-emerald-400" />
            <span className="text-2xl font-bold bg-gradient-to-r from-emerald-400 to-blue-400 bg-clip-text text-transparent">
              investry
            </span>
          </div>
          <h1 className="text-2xl font-bold text-white">Welcome back</h1>
          <p className="text-slate-400">Sign in to your account to continue</p>

          {user && (
            <div className="mt-4 p-3 bg-yellow-500/20 border border-yellow-400/30 rounded-lg">
              <p className="text-yellow-300 text-sm mb-2">
                ✓ You're already signed in as {user.email}
              </p>
              <div className="flex gap-2 justify-center">
                <Button
                  onClick={() => router.push('/dashboard')}
                  className="bg-emerald-500 hover:bg-emerald-600 text-white text-sm px-4 py-2"
                >
                  Go to Dashboard
                </Button>
                <Button
                  onClick={async () => {
                    await signOut()
                    window.location.reload()
                  }}
                  variant="outline"
                  className="border-slate-600 text-slate-300 text-sm px-4 py-2"
                >
                  Sign Out
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Show status if already authenticated */}
        {user && !authLoading && (
          <div className="mb-4 p-3 bg-emerald-900/20 border border-emerald-700 rounded-lg">
            <p className="text-emerald-400 text-sm">
              ✓ You're already signed in as {user.email}
            </p>
            <button
              onClick={() => router.push('/dashboard')}
              className="mt-2 text-emerald-400 hover:text-emerald-300 text-sm underline"
            >
              Go to Dashboard
            </button>
          </div>
        )}

        {/* Sign In Form */}
        <Card className="bg-slate-800/50 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white">Sign In</CardTitle>
            <CardDescription className="text-slate-400">Enter your credentials to access your account</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSignIn} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email" className="text-slate-300">
                  Email
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                  className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password" className="text-slate-300">
                  Password
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter your password"
                    className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 pr-10"
                    required
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 text-slate-400 hover:text-white"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="remember"
                  checked={rememberMe}
                  onCheckedChange={(checked) => setRememberMe(checked as boolean)}
                />
                <Label htmlFor="remember" className="text-sm text-slate-300">
                  Remember me
                </Label>
              </div>

              <Button
                type="submit"
                className="w-full bg-emerald-500 hover:bg-emerald-600 text-white"
                disabled={isLoading}
              >
                {isLoading ? "Signing in..." : "Sign In"}
              </Button>

              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t border-slate-600" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-slate-800 px-2 text-slate-400">Or continue with</span>
                </div>
              </div>

              <Button
                type="button"
                variant="outline"
                className="w-full bg-white hover:bg-gray-50 text-gray-900 border-gray-300"
                onClick={handleGoogleSignIn}
                disabled={isLoading}
              >
                <div className="h-5 w-5 mr-2 flex items-center justify-center">
                  <svg width="20" height="20" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                      fill="#4285F4"
                    />
                    <path
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                      fill="#34A853"
                    />
                    <path
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                      fill="#FBBC05"
                    />
                    <path
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                      fill="#EA4335"
                    />
                  </svg>
                </div>
                {isLoading ? "Signing in..." : "Continue with Google"}
              </Button>
            </form>

            <div className="mt-6 text-center">
              <p className="text-slate-400">
                Don't have an account?{" "}
                <Button
                  variant="link"
                  onClick={() => router.push("/auth/signup")}
                  className="text-emerald-400 hover:text-emerald-300 p-0"
                >
                  Sign up
                </Button>
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Demo Notice */}
        <Card className="bg-blue-500/10 border-blue-400/30">
          <CardContent className="p-4">
            <p className="text-blue-300 text-sm text-center">
              <strong>Demo Mode:</strong> Use any email and password to sign in
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
