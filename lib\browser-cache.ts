/**
 * Browser-Side Cache Storage
 * Implements localStorage/sessionStorage caching for client-side data persistence
 */

import { getCacheConfig } from './cache-config'

export interface BrowserCacheEntry {
  data: any
  timestamp: number
  ttl: number
  key: string
  tags?: string[]
  compressed?: boolean
}

export interface BrowserCacheOptions {
  useSessionStorage?: boolean
  enableCompression?: boolean
  maxStorageSize?: number // in bytes
  keyPrefix?: string
}

export class BrowserCache {
  private storage: Storage
  private options: Required<BrowserCacheOptions>
  private cacheConfig = getCacheConfig()

  constructor(options: BrowserCacheOptions = {}) {
    this.options = {
      useSessionStorage: false,
      enableCompression: false,
      maxStorageSize: 5 * 1024 * 1024, // 5MB default
      keyPrefix: 'investry_cache_',
      ...options
    }

    // Use sessionStorage or localStorage based on options
    this.storage = this.options.useSessionStorage 
      ? (typeof window !== 'undefined' ? sessionStorage : null as any)
      : (typeof window !== 'undefined' ? localStorage : null as any)

    if (!this.storage) {
      console.warn('Browser storage not available')
    }
  }

  /**
   * Get item from browser cache
   */
  get<T>(key: string): T | null {
    if (!this.storage) return null

    try {
      const fullKey = this.getFullKey(key)
      const item = this.storage.getItem(fullKey)
      
      if (!item) return null

      const entry: BrowserCacheEntry = JSON.parse(item)

      // Check if expired
      if (Date.now() > entry.timestamp + entry.ttl) {
        this.storage.removeItem(fullKey)
        return null
      }

      // Decompress if needed
      let data = entry.data
      if (entry.compressed && this.options.enableCompression) {
        data = this.decompress(data)
      }

      return data as T
    } catch (error) {
      console.warn('Browser cache get error:', error)
      return null
    }
  }

  /**
   * Set item in browser cache
   */
  set<T>(key: string, data: T, ttl?: number, tags?: string[]): boolean {
    if (!this.storage) return false

    try {
      const fullKey = this.getFullKey(key)
      
      // Check storage quota before setting
      if (!this.hasStorageSpace(fullKey, data)) {
        this.cleanup()
        if (!this.hasStorageSpace(fullKey, data)) {
          console.warn('Insufficient storage space for cache entry')
          return false
        }
      }

      let processedData = data
      let compressed = false

      // Compress if enabled and data is large
      if (this.options.enableCompression && this.shouldCompress(data)) {
        processedData = this.compress(data)
        compressed = true
      }

      const entry: BrowserCacheEntry = {
        data: processedData,
        timestamp: Date.now(),
        ttl: ttl || this.cacheConfig.getTTL('staticContent'),
        key,
        tags,
        compressed
      }

      this.storage.setItem(fullKey, JSON.stringify(entry))
      return true
    } catch (error) {
      console.warn('Browser cache set error:', error)
      
      // Try to free up space and retry once
      if (error instanceof Error && error.name === 'QuotaExceededError') {
        this.cleanup(true) // Aggressive cleanup
        try {
          const entry: BrowserCacheEntry = {
            data,
            timestamp: Date.now(),
            ttl: ttl || this.cacheConfig.getTTL('staticContent'),
            key,
            tags
          }
          this.storage.setItem(this.getFullKey(key), JSON.stringify(entry))
          return true
        } catch (retryError) {
          console.error('Browser cache retry failed:', retryError)
        }
      }
      
      return false
    }
  }

  /**
   * Delete item from browser cache
   */
  delete(key: string): boolean {
    if (!this.storage) return false

    try {
      const fullKey = this.getFullKey(key)
      this.storage.removeItem(fullKey)
      return true
    } catch (error) {
      console.warn('Browser cache delete error:', error)
      return false
    }
  }

  /**
   * Check if key exists in cache
   */
  has(key: string): boolean {
    if (!this.storage) return false

    try {
      const fullKey = this.getFullKey(key)
      const item = this.storage.getItem(fullKey)
      
      if (!item) return false

      const entry: BrowserCacheEntry = JSON.parse(item)
      
      // Check if expired
      if (Date.now() > entry.timestamp + entry.ttl) {
        this.storage.removeItem(fullKey)
        return false
      }

      return true
    } catch (error) {
      return false
    }
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    if (!this.storage) return

    try {
      const keysToRemove: string[] = []
      
      for (let i = 0; i < this.storage.length; i++) {
        const key = this.storage.key(i)
        if (key?.startsWith(this.options.keyPrefix)) {
          keysToRemove.push(key)
        }
      }

      keysToRemove.forEach(key => this.storage.removeItem(key))
    } catch (error) {
      console.warn('Browser cache clear error:', error)
    }
  }

  /**
   * Clear cache entries by tags
   */
  clearByTags(tags: string[]): number {
    if (!this.storage) return 0

    let cleared = 0

    try {
      const keysToRemove: string[] = []
      
      for (let i = 0; i < this.storage.length; i++) {
        const key = this.storage.key(i)
        if (key?.startsWith(this.options.keyPrefix)) {
          try {
            const item = this.storage.getItem(key)
            if (item) {
              const entry: BrowserCacheEntry = JSON.parse(item)
              if (entry.tags && entry.tags.some(tag => tags.includes(tag))) {
                keysToRemove.push(key)
              }
            }
          } catch (parseError) {
            // Invalid entry, remove it
            keysToRemove.push(key)
          }
        }
      }

      keysToRemove.forEach(key => {
        this.storage.removeItem(key)
        cleared++
      })
    } catch (error) {
      console.warn('Browser cache clearByTags error:', error)
    }

    return cleared
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    totalEntries: number
    totalSize: number
    oldestEntry: number
    newestEntry: number
    storageUsed: number
    storageAvailable: number
  } {
    if (!this.storage) {
      return {
        totalEntries: 0,
        totalSize: 0,
        oldestEntry: 0,
        newestEntry: 0,
        storageUsed: 0,
        storageAvailable: 0
      }
    }

    let totalEntries = 0
    let totalSize = 0
    let oldestEntry = Date.now()
    let newestEntry = 0

    try {
      for (let i = 0; i < this.storage.length; i++) {
        const key = this.storage.key(i)
        if (key?.startsWith(this.options.keyPrefix)) {
          const item = this.storage.getItem(key)
          if (item) {
            totalEntries++
            totalSize += item.length
            
            try {
              const entry: BrowserCacheEntry = JSON.parse(item)
              if (entry.timestamp < oldestEntry) oldestEntry = entry.timestamp
              if (entry.timestamp > newestEntry) newestEntry = entry.timestamp
            } catch (parseError) {
              // Invalid entry, skip
            }
          }
        }
      }
    } catch (error) {
      console.warn('Browser cache stats error:', error)
    }

    return {
      totalEntries,
      totalSize,
      oldestEntry: totalEntries > 0 ? oldestEntry : 0,
      newestEntry: totalEntries > 0 ? newestEntry : 0,
      storageUsed: this.getStorageUsed(),
      storageAvailable: this.options.maxStorageSize - this.getStorageUsed()
    }
  }

  /**
   * Cleanup expired entries
   */
  cleanup(aggressive: boolean = false): number {
    if (!this.storage) return 0

    let cleaned = 0
    const now = Date.now()

    try {
      const keysToRemove: string[] = []
      const entries: Array<{ key: string; entry: BrowserCacheEntry; size: number }> = []
      
      for (let i = 0; i < this.storage.length; i++) {
        const key = this.storage.key(i)
        if (key?.startsWith(this.options.keyPrefix)) {
          const item = this.storage.getItem(key)
          if (item) {
            try {
              const entry: BrowserCacheEntry = JSON.parse(item)
              
              // Remove expired entries
              if (now > entry.timestamp + entry.ttl) {
                keysToRemove.push(key)
              } else if (aggressive) {
                entries.push({ key, entry, size: item.length })
              }
            } catch (parseError) {
              // Invalid entry, remove it
              keysToRemove.push(key)
            }
          }
        }
      }

      // Remove expired entries
      keysToRemove.forEach(key => {
        this.storage.removeItem(key)
        cleaned++
      })

      // If aggressive cleanup, remove oldest entries until we have space
      if (aggressive && entries.length > 0) {
        entries.sort((a, b) => a.entry.timestamp - b.entry.timestamp)
        
        const targetSize = this.options.maxStorageSize * 0.7 // Target 70% usage
        let currentSize = this.getStorageUsed()
        
        for (const { key } of entries) {
          if (currentSize <= targetSize) break
          
          const item = this.storage.getItem(key)
          if (item) {
            currentSize -= item.length
            this.storage.removeItem(key)
            cleaned++
          }
        }
      }
    } catch (error) {
      console.warn('Browser cache cleanup error:', error)
    }

    return cleaned
  }

  /**
   * Private helper methods
   */
  private getFullKey(key: string): string {
    return `${this.options.keyPrefix}${key}`
  }

  private getStorageUsed(): number {
    if (!this.storage) return 0

    let used = 0
    try {
      for (let i = 0; i < this.storage.length; i++) {
        const key = this.storage.key(i)
        if (key?.startsWith(this.options.keyPrefix)) {
          const item = this.storage.getItem(key)
          if (item) {
            used += item.length * 2 // Rough estimate (UTF-16)
          }
        }
      }
    } catch (error) {
      console.warn('Storage usage calculation error:', error)
    }
    return used
  }

  private hasStorageSpace(key: string, data: any): boolean {
    const estimatedSize = JSON.stringify({ key, data }).length * 2
    return this.getStorageUsed() + estimatedSize <= this.options.maxStorageSize
  }

  private shouldCompress(data: any): boolean {
    const size = JSON.stringify(data).length
    return size > 1024 // Compress if larger than 1KB
  }

  private compress(data: any): string {
    // Simple compression using JSON.stringify with minimal whitespace
    // In a real implementation, you might use a library like pako for gzip compression
    return JSON.stringify(data)
  }

  private decompress(data: string): any {
    // Simple decompression
    return JSON.parse(data)
  }
}

// Singleton instances
let browserCacheInstance: BrowserCache | null = null
let sessionCacheInstance: BrowserCache | null = null

export function getBrowserCache(): BrowserCache {
  if (!browserCacheInstance) {
    browserCacheInstance = new BrowserCache({ useSessionStorage: false })
  }
  return browserCacheInstance
}

export function getSessionCache(): BrowserCache {
  if (!sessionCacheInstance) {
    sessionCacheInstance = new BrowserCache({ useSessionStorage: true })
  }
  return sessionCacheInstance
}
