/**
 * Stock News API Route
 * GET /api/stocks/news?symbol=AAPL&limit=10
 */

import { NextRequest, NextResponse } from 'next/server'
import { getStockDataService } from '@/lib/stock-config'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const symbol = searchParams.get('symbol')
    const limitParam = searchParams.get('limit')
    const limit = limitParam ? parseInt(limitParam, 10) : 10

    // Validate input
    if (!symbol) {
      return NextResponse.json(
        { error: 'Missing required parameter: symbol' },
        { status: 400 }
      )
    }

    // Validate limit
    if (isNaN(limit) || limit < 1 || limit > 50) {
      return NextResponse.json(
        { error: 'Limit must be a number between 1 and 50' },
        { status: 400 }
      )
    }

    const stockService = getStockDataService()
    const news = await stockService.getStockNews(symbol.toUpperCase(), limit)

    if (!news) {
      return NextResponse.json(
        { error: 'News not found for this symbol' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      data: news,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('News API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
