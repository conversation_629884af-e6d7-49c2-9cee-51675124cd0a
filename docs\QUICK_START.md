# LLM Portfolio System - Quick Start Guide

## 🚀 Get Started in 5 Minutes

This guide will get your LLM Portfolio Generation System up and running quickly.

## Prerequisites

- ✅ Existing Investry.ai application running
- ✅ Supabase database configured
- ✅ OpenAI API account (optional but recommended)

## Step 1: Install Dependencies

```bash
npm install openai --legacy-peer-deps
```

## Step 2: Database Setup

1. Open your Supabase SQL Editor
2. Copy and paste the contents of `scripts/create-llm-tables-supabase.sql`
3. Click "Run" to create the LLM system tables

## Step 3: Environment Configuration

Add these lines to your `.env.local` file:

```env
# OpenAI Configuration (get from https://platform.openai.com/api-keys)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000
OPENAI_TEMPERATURE=0.7

# LLM System Configuration
LLM_CACHE_TTL_HOURS=24
LLM_MAX_RETRIES=3
LLM_RATE_LIMIT_PER_USER=10
LLM_ENABLE_FALLBACK=true
```

## Step 4: Test the System

Run the test script to verify everything works:

```bash
node scripts/test-llm-system.js
```

You should see output like:
```
🚀 Testing LLM Portfolio Generation System

1️⃣ Checking Configuration...
Configuration Status: { configured: true, errors: 'None' }

2️⃣ Performing Health Check...
System Health: { status: 'healthy', ... }

3️⃣ Testing Portfolio Generation (Fallback Mode)...
Portfolio Generation Result:
- Source: rules
- Confidence: 0.8
- Processing Time: 45 ms
- Allocations Count: 8
- Risk Level: Moderate
- Total Allocation: 100.00%
- Allocation Valid: ✅

✅ All tests completed successfully!
```

## Step 5: Start Using the System

The system is now integrated into your existing onboarding flow. When users complete the investment survey, they'll automatically get AI-powered portfolio recommendations with intelligent fallback to your existing rule-based system.

### 🔄 New Feature: Portfolio Reweighting

You now have **"Reweight Portfolio"** buttons on both the Dashboard and Investments pages that allow users to:

- **Request specific changes** to their existing portfolio
- **Get AI-powered rebalancing** based on their needs
- **See detailed comparisons** between old and new portfolios
- **Accept or reject** the new recommendations

**Example reweight requests:**
- "I want to reduce risk and increase bond allocation to 50%"
- "Add more technology exposure, I want 30% in tech stocks"
- "Make the portfolio more conservative for retirement"
- "Focus on dividend-paying stocks for income generation"

## API Endpoints Available

### Generate Portfolio
```bash
POST /api/portfolio/generate-llm
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
  "surveyData": {
    "primaryGoal": "Long-term wealth building",
    "timeHorizon": "10+ years",
    "riskTolerance": 3,
    "experienceLevel": "Beginner",
    "interestedThemes": ["Technology", "Healthcare"],
    "monthlyInvestment": 1000
  },
  "userMajor": "computer-science"
}
```

### Check System Status
```bash
GET /api/portfolio/llm-status
```

### Get User Statistics
```bash
GET /api/portfolio/user-stats
Authorization: Bearer YOUR_JWT_TOKEN
```

### Reweight Portfolio
```bash
POST /api/portfolio/reweight
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
  "currentPortfolio": {
    "allocations": [...],
    "riskLevel": "Moderate",
    "expectedReturn": "8-12%",
    "strategy": "...",
    "rationale": "..."
  },
  "reweightReason": "I want to reduce risk and increase bond allocation",
  "surveyData": {
    "primaryGoal": "Long-term wealth building",
    "timeHorizon": "10+ years",
    "riskTolerance": 3,
    "experienceLevel": "Intermediate"
  },
  "userMajor": "computer-science"
}
```

## How It Works

1. **User completes survey** → Survey data is processed
2. **LLM generates portfolio** → AI analyzes user profile and creates personalized recommendations
3. **Fallback if needed** → If LLM fails, system automatically uses your existing rule-based generator
4. **Results cached** → Future similar requests are served from cache for better performance
5. **Everything logged** → All interactions are audited in Supabase for monitoring

## Configuration Options

### Without OpenAI API Key
- System works in **fallback mode only**
- Uses your existing rule-based portfolio generator
- Still provides caching and audit logging benefits

### With OpenAI API Key
- Full **AI-powered portfolio generation**
- Intelligent analysis of user preferences
- More sophisticated and personalized recommendations
- Automatic fallback if AI service is unavailable

## Monitoring

Check system health anytime:
```bash
curl http://localhost:3000/api/portfolio/llm-status
```

View user statistics in your app or via API:
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:3000/api/portfolio/user-stats
```

## Troubleshooting

### ❌ "Configuration not valid" error
- Check your OpenAI API key is correct
- Verify environment variables are loaded
- Run `node scripts/test-llm-system.js` for detailed diagnostics

### ❌ Database connection errors
- Ensure Supabase tables are created
- Check your Supabase credentials
- Verify RLS policies are set up correctly

### ❌ Portfolio generation fails
- System should automatically fallback to rule-based generation
- Check logs for specific error messages
- Verify rate limits aren't exceeded

## Next Steps

1. **Monitor Usage**: Check the `/api/portfolio/llm-status` endpoint regularly
2. **Optimize Prompts**: Review generated portfolios and adjust prompts if needed
3. **Scale Up**: Increase rate limits and cache settings as usage grows
4. **Add Features**: Explore advanced features like custom prompts and A/B testing

## Support

- 📖 **Full Documentation**: See `docs/LLM_PORTFOLIO_SYSTEM.md`
- 🧪 **Test Script**: Run `node scripts/test-llm-system.js`
- 🔍 **Health Check**: Visit `/api/portfolio/llm-status`
- 📊 **Database**: Check Supabase for audit logs and usage data

## Security Notes

- 🔒 All API endpoints require authentication
- 🛡️ Rate limiting prevents abuse
- 📝 Comprehensive audit logging
- 🚫 No user data sent to external APIs without consent
- ✅ Automatic fallback ensures service reliability

---

**That's it!** Your LLM Portfolio Generation System is now ready to provide AI-powered investment recommendations to your users. The system will intelligently use AI when available and gracefully fallback to your existing system when needed.
