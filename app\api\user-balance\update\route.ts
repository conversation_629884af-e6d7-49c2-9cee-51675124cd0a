import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { userId, amount, type, description } = body

    if (!userId || !amount || amount <= 0) {
      return NextResponse.json(
        { error: 'User ID and valid amount are required' },
        { status: 400 }
      )
    }

    // Create service role client for database operations
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )

    // Update user balance
    const { data: currentBalance, error: balanceError } = await supabase
      .from('user_balances')
      .select('balance, available_for_investment')
      .eq('user_id', userId)
      .single()

    if (balanceError && balanceError.code !== 'PGRST116') { // PGRST116 = no rows found
      console.error('Error fetching current balance:', balanceError)
      return NextResponse.json(
        { error: 'Failed to fetch current balance' },
        { status: 500 }
      )
    }

    const currentBalanceAmount = currentBalance?.balance || 0
    const currentAvailable = currentBalance?.available_for_investment || 0
    const newBalance = currentBalanceAmount + amount
    const newAvailable = currentAvailable + amount

    // Update or insert user balance
    const { error: updateError } = await supabase
      .from('user_balances')
      .upsert({
        user_id: userId,
        balance: newBalance,
        currency: 'USD',
        balance_type: 'cash',
        available_for_investment: newAvailable,
        total_invested: 0 // Keep existing total_invested
      }, {
        onConflict: 'user_id,currency'
      })

    if (updateError) {
      console.error('Error updating balance:', updateError)
      return NextResponse.json(
        { error: 'Failed to update balance' },
        { status: 500 }
      )
    }

    // Create wallet transaction record
    const { error: transactionError } = await supabase
      .from('wallet_transactions')
      .insert({
        user_id: userId,
        transaction_type: type || 'deposit',
        amount: amount,
        currency: 'USD',
        description: description || 'Manual deposit',
        status: 'completed',
        processed_at: new Date().toISOString()
      })

    if (transactionError) {
      console.error('Error creating transaction record:', transactionError)
      // Don't fail the request if transaction logging fails
    }

    return NextResponse.json({
      success: true,
      newBalance: newBalance,
      amount: amount,
      message: 'Balance updated successfully'
    })

  } catch (error) {
    console.error('Balance update error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
