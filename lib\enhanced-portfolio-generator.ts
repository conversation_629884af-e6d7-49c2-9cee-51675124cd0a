/**
 * Enhanced Portfolio Generator - LLM-powered with fallback to rule-based system
 * This replaces the existing portfolio generation while maintaining backward compatibility
 */

import type { SurveyData } from "@/components/onboarding-survey"
import type { PersonalizedPortfolio } from "@/lib/portfolio-generator"
import { generatePersonalizedPortfolio } from "@/lib/portfolio-generator"
import { getLLMPortfolioEngine, isLLMConfigured } from "@/lib/llm-portfolio"

export interface EnhancedPortfolioOptions {
  useLLM?: boolean
  fallbackToRules?: boolean
  additionalContext?: string
  cacheResults?: boolean
}

export interface EnhancedPortfolioResult {
  portfolio: PersonalizedPortfolio
  source: 'llm' | 'rules' | 'cache'
  confidence: number
  processingTimeMs: number
  metadata?: {
    llmUsed: boolean
    cacheHit: boolean
    fallbackReason?: string
    validationErrors?: string[]
  }
}

/**
 * Generate a personalized portfolio using LLM with intelligent fallback
 */
export async function generateEnhancedPortfolio(
  surveyData: SurveyData,
  userId: string,
  major?: string,
  options: EnhancedPortfolioOptions = {}
): Promise<EnhancedPortfolioResult> {
  const startTime = Date.now()
  const {
    useLLM = true,
    fallbackToRules = true,
    additionalContext,
    cacheResults = true
  } = options

  // Check if LLM is configured and should be used
  const shouldUseLLM = useLLM && isLLMConfigured()

  if (shouldUseLLM) {
    try {
      // Attempt LLM generation
      const engine = getLLMPortfolioEngine()
      
      const result = await engine.generatePortfolio({
        surveyData,
        userId,
        userMajor: major,
        additionalContext
      })

      return {
        portfolio: result.portfolio,
        source: result.source,
        confidence: result.confidence,
        processingTimeMs: Date.now() - startTime,
        metadata: {
          llmUsed: true,
          cacheHit: result.cacheHit || false,
          fallbackReason: result.fallbackReason,
          validationErrors: result.validationErrors
        }
      }

    } catch (error) {
      console.error('LLM portfolio generation failed:', error)
      
      // If LLM fails and fallback is enabled, use rule-based system
      if (fallbackToRules) {
        console.log('Falling back to rule-based portfolio generation')
        return generateRuleBasedPortfolio(surveyData, userId, major, startTime, error.message)
      }
      
      throw error
    }
  } else {
    // Use rule-based system directly
    return generateRuleBasedPortfolio(surveyData, userId, major, startTime, 'LLM not configured')
  }
}

/**
 * Generate portfolio using the original rule-based system
 */
function generateRuleBasedPortfolio(
  surveyData: SurveyData,
  userId: string,
  major?: string,
  startTime?: number,
  fallbackReason?: string
): EnhancedPortfolioResult {
  const actualStartTime = startTime || Date.now()
  
  try {
    const portfolio = generatePersonalizedPortfolio(surveyData, userId, major)
    
    return {
      portfolio,
      source: 'rules',
      confidence: 0.8, // Good confidence for rule-based system
      processingTimeMs: Date.now() - actualStartTime,
      metadata: {
        llmUsed: false,
        cacheHit: false,
        fallbackReason
      }
    }
  } catch (error) {
    console.error('Rule-based portfolio generation failed:', error)
    throw new Error(`Both LLM and rule-based generation failed: ${error.message}`)
  }
}

/**
 * Validate portfolio before returning
 */
export async function validateEnhancedPortfolio(
  portfolio: PersonalizedPortfolio
): Promise<{ isValid: boolean; errors: string[] }> {
  const errors: string[] = []

  // Basic validation
  if (!portfolio.allocations || !Array.isArray(portfolio.allocations)) {
    errors.push('Portfolio must have allocations array')
  }

  if (portfolio.allocations) {
    // Check allocations sum to 100%
    const totalAllocation = portfolio.allocations.reduce((sum, alloc) => sum + alloc.allocation, 0)
    if (Math.abs(totalAllocation - 100) > 0.01) {
      errors.push(`Allocations must sum to 100%, got ${totalAllocation.toFixed(2)}%`)
    }

    // Check individual allocations
    portfolio.allocations.forEach((alloc, index) => {
      if (!alloc.symbol) errors.push(`Allocation ${index + 1} missing symbol`)
      if (!alloc.name) errors.push(`Allocation ${index + 1} missing name`)
      if (typeof alloc.allocation !== 'number' || alloc.allocation <= 0) {
        errors.push(`Allocation ${index + 1} has invalid percentage`)
      }
      if (!alloc.category) errors.push(`Allocation ${index + 1} missing category`)
      if (!alloc.rationale) errors.push(`Allocation ${index + 1} missing rationale`)
    })
  }

  // Check required fields
  if (!portfolio.riskLevel) errors.push('Portfolio missing risk level')
  if (!portfolio.expectedReturn) errors.push('Portfolio missing expected return')
  if (!portfolio.strategy) errors.push('Portfolio missing strategy')
  if (!portfolio.rationale) errors.push('Portfolio missing rationale')

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Get portfolio generation recommendations based on user profile
 */
export function getPortfolioRecommendations(surveyData: SurveyData): {
  shouldUseLLM: boolean
  reason: string
  confidence: number
} {
  // Recommend LLM for complex scenarios
  const complexityFactors = [
    surveyData.interestedThemes && surveyData.interestedThemes.length > 3,
    surveyData.riskTolerance === 4 || surveyData.riskTolerance === 5,
    surveyData.experienceLevel === 'Advanced',
    surveyData.monthlyInvestment > 2000,
    surveyData.primaryGoal.includes('specific') || surveyData.primaryGoal.includes('custom')
  ]

  const complexityScore = complexityFactors.filter(Boolean).length

  if (complexityScore >= 3) {
    return {
      shouldUseLLM: true,
      reason: 'Complex investment profile benefits from AI-powered analysis',
      confidence: 0.9
    }
  } else if (complexityScore >= 1) {
    return {
      shouldUseLLM: true,
      reason: 'Moderate complexity suitable for AI enhancement',
      confidence: 0.7
    }
  } else {
    return {
      shouldUseLLM: false,
      reason: 'Simple profile can be handled by rule-based system',
      confidence: 0.8
    }
  }
}

/**
 * Compare LLM vs rule-based portfolio for the same input
 */
export async function comparePortfolioMethods(
  surveyData: SurveyData,
  userId: string,
  major?: string
): Promise<{
  llmResult?: EnhancedPortfolioResult
  rulesResult: EnhancedPortfolioResult
  comparison: {
    diversificationScore: { llm?: number; rules: number }
    riskAlignment: { llm?: number; rules: number }
    complexity: { llm?: number; rules: number }
    recommendation: 'llm' | 'rules' | 'similar'
  }
}> {
  // Generate rule-based portfolio
  const rulesResult = generateRuleBasedPortfolio(surveyData, userId, major)
  
  let llmResult: EnhancedPortfolioResult | undefined
  
  // Try LLM generation
  if (isLLMConfigured()) {
    try {
      llmResult = await generateEnhancedPortfolio(surveyData, userId, major, {
        useLLM: true,
        fallbackToRules: false
      })
    } catch (error) {
      console.error('LLM comparison failed:', error)
    }
  }

  // Calculate comparison metrics
  const comparison = {
    diversificationScore: {
      llm: llmResult ? calculateDiversificationScore(llmResult.portfolio) : undefined,
      rules: calculateDiversificationScore(rulesResult.portfolio)
    },
    riskAlignment: {
      llm: llmResult ? calculateRiskAlignment(llmResult.portfolio, surveyData) : undefined,
      rules: calculateRiskAlignment(rulesResult.portfolio, surveyData)
    },
    complexity: {
      llm: llmResult ? calculateComplexityScore(llmResult.portfolio) : undefined,
      rules: calculateComplexityScore(rulesResult.portfolio)
    },
    recommendation: 'rules' as 'llm' | 'rules' | 'similar'
  }

  // Determine recommendation
  if (llmResult) {
    const llmScore = (comparison.diversificationScore.llm || 0) + 
                    (comparison.riskAlignment.llm || 0) + 
                    (comparison.complexity.llm || 0)
    const rulesScore = comparison.diversificationScore.rules + 
                      comparison.riskAlignment.rules + 
                      comparison.complexity.rules

    if (llmScore > rulesScore + 0.2) {
      comparison.recommendation = 'llm'
    } else if (Math.abs(llmScore - rulesScore) <= 0.2) {
      comparison.recommendation = 'similar'
    }
  }

  return {
    llmResult,
    rulesResult,
    comparison
  }
}

/**
 * Calculate diversification score (0-1)
 */
function calculateDiversificationScore(portfolio: PersonalizedPortfolio): number {
  const categories = new Set(portfolio.allocations.map(a => a.category))
  const categoryCount = categories.size
  const maxCategories = 7 // Reasonable maximum
  
  return Math.min(categoryCount / maxCategories, 1)
}

/**
 * Calculate risk alignment score (0-1)
 */
function calculateRiskAlignment(portfolio: PersonalizedPortfolio, surveyData: SurveyData): number {
  const expectedRiskLevels = {
    1: 'Conservative',
    2: 'Conservative',
    3: 'Moderate',
    4: 'Aggressive',
    5: 'Aggressive'
  }
  
  const expectedRisk = expectedRiskLevels[surveyData.riskTolerance as keyof typeof expectedRiskLevels]
  return portfolio.riskLevel === expectedRisk ? 1 : 0.5
}

/**
 * Calculate complexity score (0-1)
 */
function calculateComplexityScore(portfolio: PersonalizedPortfolio): number {
  const allocationCount = portfolio.allocations.length
  const maxAllocations = 15
  
  // More allocations = higher complexity (up to a point)
  return Math.min(allocationCount / maxAllocations, 1)
}

// Export the enhanced generator as the default
export { generateEnhancedPortfolio as generatePersonalizedPortfolio }
