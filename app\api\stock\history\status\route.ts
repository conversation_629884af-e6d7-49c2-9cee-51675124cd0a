/**
 * Stock History Service Status Route
 * GET /api/stock/history/status
 */

import { NextRequest, NextResponse } from 'next/server'
import { getStockHistoryService } from '@/lib/stock-history-service'
import { API_PROVIDERS } from '@/lib/types/stock-history'

export async function GET(request: NextRequest) {
  try {
    const historyService = getStockHistoryService()
    
    // Get service status
    const apiUsage = historyService.getApiUsage()
    const cacheMetrics = historyService.getCacheMetrics()
    const cacheHitRate = historyService.getCacheHitRate()

    // Check API key availability
    const apiStatus = {
      polygon: {
        configured: !!process.env.POLYGON_IO_API_KEY,
        available: !!process.env.POLYGON_IO_API_KEY && apiUsage.polygon?.available !== false
      },
      twelvedata: {
        configured: !!process.env.TWELVE_DATA_API_KEY,
        available: !!process.env.TWELVE_DATA_API_KEY && apiUsage.twelvedata?.available !== false
      },
      alphavantage: {
        configured: !!process.env.ALPHA_VANTAGE_API_KEY,
        available: !!process.env.ALPHA_VANTAGE_API_KEY && apiUsage.alphavantage?.available !== false
      },
      finnhub: {
        configured: !!process.env.FINNHUB_API_KEY,
        available: !!process.env.FINNHUB_API_KEY && apiUsage.finnhub?.available !== false
      }
    }

    // Get provider priorities
    const providers = Object.entries(API_PROVIDERS).map(([name, config]) => ({
      name,
      priority: config.priority,
      configured: apiStatus[name as keyof typeof apiStatus]?.configured || false,
      available: apiStatus[name as keyof typeof apiStatus]?.available || false,
      rateLimit: config.rateLimit,
      supports: config.supports
    })).sort((a, b) => a.priority - b.priority)

    return NextResponse.json({
      service: {
        status: 'operational',
        version: '1.0.0',
        uptime: process.uptime(),
        timestamp: new Date().toISOString()
      },
      providers,
      cache: {
        hitRate: Math.round(cacheHitRate * 100) / 100,
        totalEntries: cacheMetrics.totalEntries,
        memoryUsage: cacheMetrics.memoryUsage,
        oldestEntry: cacheMetrics.oldestEntry,
        newestEntry: cacheMetrics.newestEntry
      },
      apiUsage,
      features: {
        timeframes: ['1D', '1W', '1M', '3M', '1Y', 'All'],
        batchRequests: true,
        cacheManagement: true,
        fallbackSupport: true,
        realTimeData: providers.some(p => p.available && API_PROVIDERS[p.name]?.supports.realtime)
      }
    })

  } catch (error) {
    console.error('Stock history status error:', error)
    
    return NextResponse.json(
      {
        service: {
          status: 'error',
          error: 'Failed to get service status'
        }
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/stock/history/status/cache/clear
 * Clear expired cache entries
 */
export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    const historyService = getStockHistoryService()

    switch (action) {
      case 'clear-expired':
        const clearedCount = historyService.clearExpiredCache()
        return NextResponse.json({
          message: 'Expired cache entries cleared',
          clearedEntries: clearedCount,
          timestamp: new Date().toISOString()
        })

      case 'preload':
        const body = await request.json()
        const symbols = body.symbols || ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA']
        const timeframes = body.timeframes || ['1D', '1W', '1M']
        
        await historyService.preloadCache(symbols, timeframes)
        
        return NextResponse.json({
          message: 'Cache preloaded successfully',
          symbols,
          timeframes,
          timestamp: new Date().toISOString()
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: clear-expired, preload' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Stock history cache management error:', error)
    
    return NextResponse.json(
      {
        error: 'Cache management operation failed',
        code: 'CACHE_OPERATION_FAILED'
      },
      { status: 500 }
    )
  }
}
