"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Wallet, ArrowLeft, Eye, EyeOff } from "lucide-react"
import { useNotifications } from "@/components/notification-system"
import { useAuth } from "@/components/auth-provider"
import { hasCompletedOnboarding, getRedirectPath } from "@/lib/onboarding-utils"

const majors = [
  "Computer Science",
  "Business Administration",
  "Engineering",
  "Economics",
  "Finance",
  "Marketing",
  "Psychology",
  "Biology",
  "Chemistry",
  "Mathematics",
  "English",
  "History",
  "Political Science",
  "Communications",
  "Art",
  "Music",
  "Education",
  "Nursing",
  "Pre-Med",
  "Other",
]

export default function SignUpPage() {
  const router = useRouter()
  const { addNotification } = useNotifications()
  const { signUp, user, loading: authLoading, isConfigured } = useAuth()
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
    isStudent: false,
    school: "",
    major: "",
    graduationYear: "",
    agreeToTerms: false,
  })
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  // Handle auth state changes and redirect after successful authentication
  useEffect(() => {
    // If user is already authenticated, redirect to dashboard
    if (!authLoading && user) {
      console.log("User already authenticated, redirecting to dashboard")
      const redirectPath = getRedirectPath(user.id)
      router.push(redirectPath)
      return
    }
  }, [user, authLoading, router])

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // Check if Supabase is configured
      if (!isConfigured) {
        addNotification({
          type: "error",
          title: "Configuration Error",
          message: "Database connection not configured. Please contact support.",
        })
        setIsLoading(false)
        return
      }

      // Validation
      if (formData.isStudent && (!formData.school || !formData.major || !formData.graduationYear)) {
        addNotification({
          type: "error",
          title: "Student information required",
          message: "Please complete all student information fields.",
        })
        setIsLoading(false)
        return
      }

      if (formData.password !== formData.confirmPassword) {
        addNotification({
          type: "error",
          title: "Password mismatch",
          message: "Passwords do not match. Please try again.",
        })
        setIsLoading(false)
        return
      }

      if (formData.password.length < 6) {
        addNotification({
          type: "error",
          title: "Password too short",
          message: "Password must be at least 6 characters long.",
        })
        setIsLoading(false)
        return
      }

      if (!formData.agreeToTerms) {
        addNotification({
          type: "error",
          title: "Terms required",
          message: "Please agree to the terms and conditions.",
        })
        setIsLoading(false)
        return
      }

      // Prepare user metadata for Supabase
      const userData = {
        first_name: formData.firstName,
        last_name: formData.lastName,
        university: formData.school,
        major: formData.major,
        graduation_year: formData.graduationYear ? parseInt(formData.graduationYear) : null,
        is_student: formData.isStudent,
      }

      // Sign up with regular auth provider
      const { data, error } = await signUp(formData.email, formData.password, userData)

      if (error) {
        addNotification({
          type: "error",
          title: "Sign up failed",
          message: error.message || "An error occurred during sign up. Please try again.",
        })
        setIsLoading(false)
        return
      }

      if (data?.user) {
        // Check if email confirmation is required
        if (data.user.email_confirmed_at) {
          console.log("Sign up successful with confirmed email, waiting for auth state to update...")
          // User is immediately signed in (email confirmation disabled)
          // Let the auth state change effect handle the redirect
        } else {
          // Email confirmation required
          addNotification({
            type: "success",
            title: "Account created!",
            message: "Please check your email to verify your account, then sign in.",
          })

          // Redirect to signin page after successful signup with email confirmation
          setTimeout(() => {
            router.push("/auth/signin")
          }, 2000)
        }
      }
    } catch (error) {
      console.error("Sign up error:", error)
      addNotification({
        type: "error",
        title: "Sign up failed",
        message: "An unexpected error occurred. Please try again.",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleGoogleSignUp = async () => {
    addNotification({
      type: "info",
      title: "Google Sign Up",
      message: "Google OAuth will be available once your Supabase project is fully configured.",
    })
  }

  // Removed 2FA handlers - using simple signup flow

  const currentYear = new Date().getFullYear()
  const graduationYears = Array.from({ length: 8 }, (_, i) => currentYear + i)

  return (
    <div className="min-h-screen bg-slate-900 py-8 px-4">
      <div className="max-w-md mx-auto space-y-6">
        {/* Back button */}
        <Button variant="ghost" onClick={() => router.push("/")} className="text-slate-400 hover:text-white">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Home
        </Button>

        {/* Logo */}
        <div className="text-center">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Wallet className="h-8 w-8 text-emerald-400" />
            <span className="text-2xl font-bold bg-gradient-to-r from-emerald-400 to-blue-400 bg-clip-text text-transparent">
              investry
            </span>
          </div>
          <h1 className="text-2xl font-bold text-white">Join investry</h1>
          <p className="text-slate-400">Start your investment journey today</p>
        </div>

        {/* Show status if already authenticated */}
        {user && !authLoading && (
          <div className="mb-4 p-3 bg-emerald-900/20 border border-emerald-700 rounded-lg">
            <p className="text-emerald-400 text-sm">
              ✓ You're already signed in as {user.email}
            </p>
            <button
              onClick={() => router.push('/dashboard')}
              className="mt-2 text-emerald-400 hover:text-emerald-300 text-sm underline"
            >
              Go to Dashboard
            </button>
          </div>
        )}

        {/* Sign Up Form */}
        <Card className="bg-slate-800/50 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white">Create Account</CardTitle>
            <CardDescription className="text-slate-400">
              Tell us about yourself to get personalized investment recommendations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSignUp} className="space-y-4">
              {/* Google Sign Up */}
              <Button
                type="button"
                variant="outline"
                className="w-full bg-white hover:bg-gray-50 text-gray-900 border-gray-300"
                onClick={handleGoogleSignUp}
                disabled={isLoading}
              >
                <div className="h-5 w-5 mr-2 flex items-center justify-center">
                  <svg width="20" height="20" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                      fill="#4285F4"
                    />
                    <path
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                      fill="#34A853"
                    />
                    <path
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                      fill="#FBBC05"
                    />
                    <path
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                      fill="#EA4335"
                    />
                  </svg>
                </div>
                {isLoading ? "Creating Account..." : "Continue with Google"}
              </Button>

              {/* Divider */}
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t border-slate-600" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-slate-800 px-2 text-slate-400">Or continue with</span>
                </div>
              </div>
              {/* Name Fields */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName" className="text-slate-300">
                    First Name
                  </Label>
                  <Input
                    id="firstName"
                    value={formData.firstName}
                    onChange={(e) => handleInputChange("firstName", e.target.value)}
                    placeholder="John"
                    className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName" className="text-slate-300">
                    Last Name
                  </Label>
                  <Input
                    id="lastName"
                    value={formData.lastName}
                    onChange={(e) => handleInputChange("lastName", e.target.value)}
                    placeholder="Doe"
                    className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400"
                    required
                  />
                </div>
              </div>

              {/* Email */}
              <div className="space-y-2">
                <Label htmlFor="email" className="text-slate-300">
                  Email
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  placeholder="<EMAIL>"
                  className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400"
                  required
                />
              </div>

              {/* Password Fields */}
              <div className="space-y-2">
                <Label htmlFor="password" className="text-slate-300">
                  Password
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={formData.password}
                    onChange={(e) => handleInputChange("password", e.target.value)}
                    placeholder="Create a strong password"
                    className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 pr-10"
                    required
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 text-slate-400 hover:text-white"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword" className="text-slate-300">
                  Confirm Password
                </Label>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    value={formData.confirmPassword}
                    onChange={(e) => handleInputChange("confirmPassword", e.target.value)}
                    placeholder="Confirm your password"
                    className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 pr-10"
                    required
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 text-slate-400 hover:text-white"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              {/* Student Status */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isStudent"
                  checked={formData.isStudent}
                  onCheckedChange={(checked) => handleInputChange("isStudent", checked as boolean)}
                />
                <Label htmlFor="isStudent" className="text-slate-300">
                  I'm currently a college student
                </Label>
              </div>

              {/* Conditional Student Fields */}
              {formData.isStudent && (
                <div className="space-y-4 border-l-2 border-emerald-500/30 pl-4">
                  {/* School Info */}
                  <div className="space-y-2">
                    <Label htmlFor="school" className="text-slate-300">
                      School/University
                    </Label>
                    <Input
                      id="school"
                      value={formData.school}
                      onChange={(e) => handleInputChange("school", e.target.value)}
                      placeholder="University of California, Berkeley"
                      className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400"
                      required
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label className="text-slate-300">Major</Label>
                      <Select value={formData.major} onValueChange={(value) => handleInputChange("major", value)}>
                        <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                          <SelectValue placeholder="Select major" />
                        </SelectTrigger>
                        <SelectContent className="bg-slate-700 border-slate-600">
                          {majors.map((major) => (
                            <SelectItem key={major} value={major} className="text-white hover:bg-slate-600">
                              {major}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label className="text-slate-300">Graduation Year</Label>
                      <Select
                        value={formData.graduationYear}
                        onValueChange={(value) => handleInputChange("graduationYear", value)}
                      >
                        <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                          <SelectValue placeholder="Year" />
                        </SelectTrigger>
                        <SelectContent className="bg-slate-700 border-slate-600">
                          {graduationYears.map((year) => (
                            <SelectItem key={year} value={year.toString()} className="text-white hover:bg-slate-600">
                              {year}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              )}

              {/* Terms Agreement */}
              <div className="flex items-start space-x-2">
                <Checkbox
                  id="terms"
                  checked={formData.agreeToTerms}
                  onCheckedChange={(checked) => handleInputChange("agreeToTerms", checked as boolean)}
                />
                <Label htmlFor="terms" className="text-sm text-slate-300 leading-relaxed">
                  I agree to the{" "}
                  <Button variant="link" className="text-emerald-400 hover:text-emerald-300 p-0 h-auto">
                    Terms of Service
                  </Button>{" "}
                  and{" "}
                  <Button variant="link" className="text-emerald-400 hover:text-emerald-300 p-0 h-auto">
                    Privacy Policy
                  </Button>
                </Label>
              </div>

              <Button
                type="submit"
                className="w-full bg-emerald-500 hover:bg-emerald-600 text-white"
                disabled={isLoading}
              >
                {isLoading ? "Creating Account..." : "Create Account"}
              </Button>
            </form>

            <div className="mt-6 text-center">
              <p className="text-slate-400">
                Already have an account?{" "}
                <Button
                  variant="link"
                  onClick={() => router.push("/auth/signin")}
                  className="text-emerald-400 hover:text-emerald-300 p-0"
                >
                  Sign in
                </Button>
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Benefits */}
        <Card className="bg-emerald-500/10 border-emerald-400/30">
          <CardContent className="p-4">
            <div className="text-center space-y-2">
              <h3 className="font-semibold text-emerald-400">Why Choose investry?</h3>
              <ul className="text-sm text-emerald-300 space-y-1">
                <li>• Start investing with as little as $10</li>
                <li>• Personalized recommendations based on your goals</li>
                <li>• Learn while you invest with our courses</li>
                <li>• No hidden fees or minimum balances</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
