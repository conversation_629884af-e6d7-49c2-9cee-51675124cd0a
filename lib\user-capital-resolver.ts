/**
 * User Capital Resolver
 * Service to fetch and resolve user capital data from Supabase
 */

import { createClient } from './supabase'

export interface UserBalance {
  id: string
  user_id: string
  balance: number
  currency: string
  balance_type: 'cash' | 'invested' | 'pending'
  available_for_investment: number
  total_invested: number
  pending_transactions: number
  created_at: string
  updated_at: string
}

export interface WalletTransaction {
  id: string
  user_id: string
  transaction_type: 'deposit' | 'withdrawal' | 'investment' | 'dividend' | 'fee' | 'refund'
  amount: number
  currency: string
  description: string | null
  reference_id: string | null
  reference_type: string | null
  status: 'pending' | 'completed' | 'failed' | 'cancelled'
  processed_at: string
  created_at: string
  updated_at: string
}

export interface UserCapitalSummary {
  totalCash: number
  totalInvested: number
  totalPending: number
  totalValue: number
  availableForInvestment: number
  currency: string
  lastUpdated: string
}

export interface CapitalHistory {
  date: string
  timestamp: number
  cashBalance: number
  investedAmount: number
  totalValue: number
  deposits: number
  withdrawals: number
  netFlow: number
}

export interface TransactionSummary {
  totalDeposits: number
  totalWithdrawals: number
  totalInvestments: number
  totalDividends: number
  totalFees: number
  netDeposits: number
  transactionCount: number
  firstTransaction: string | null
  lastTransaction: string | null
}

export class UserCapitalResolver {
  private supabase = createClient()

  /**
   * Get current user capital summary
   */
  async getUserCapitalSummary(userId: string): Promise<UserCapitalSummary | null> {
    try {
      const { data: balances, error } = await this.supabase
        .from('user_balances')
        .select('*')
        .eq('user_id', userId)

      if (error) {
        console.error('Error fetching user balances:', error)
        return null
      }

      if (!balances || balances.length === 0) {
        // Return default empty summary
        return {
          totalCash: 0,
          totalInvested: 0,
          totalPending: 0,
          totalValue: 0,
          availableForInvestment: 0,
          currency: 'USD',
          lastUpdated: new Date().toISOString()
        }
      }

      // Aggregate balances by type
      let totalCash = 0
      let totalInvested = 0
      let totalPending = 0
      let availableForInvestment = 0
      const currency = balances[0]?.currency || 'USD'
      let lastUpdated = balances[0]?.updated_at || new Date().toISOString()

      for (const balance of balances) {
        switch (balance.balance_type) {
          case 'cash':
            totalCash += balance.balance || 0
            availableForInvestment += balance.available_for_investment || 0
            break
          case 'invested':
            totalInvested += balance.total_invested || 0
            break
          case 'pending':
            totalPending += balance.pending_transactions || 0
            break
        }
        
        // Keep the most recent update time
        if (balance.updated_at > lastUpdated) {
          lastUpdated = balance.updated_at
        }
      }

      return {
        totalCash,
        totalInvested,
        totalPending,
        totalValue: totalCash + totalInvested,
        availableForInvestment,
        currency,
        lastUpdated
      }

    } catch (error) {
      console.error('Error in getUserCapitalSummary:', error)
      return null
    }
  }

  /**
   * Get wallet transaction history
   */
  async getWalletTransactions(
    userId: string,
    options: {
      limit?: number
      offset?: number
      startDate?: string
      endDate?: string
      transactionTypes?: string[]
      status?: string
    } = {}
  ): Promise<WalletTransaction[]> {
    try {
      let query = this.supabase
        .from('wallet_transactions')
        .select('*')
        .eq('user_id', userId)
        .order('processed_at', { ascending: false })

      // Apply filters
      if (options.startDate) {
        query = query.gte('processed_at', options.startDate)
      }
      if (options.endDate) {
        query = query.lte('processed_at', options.endDate)
      }
      if (options.transactionTypes && options.transactionTypes.length > 0) {
        query = query.in('transaction_type', options.transactionTypes)
      }
      if (options.status) {
        query = query.eq('status', options.status)
      }
      if (options.limit) {
        query = query.limit(options.limit)
      }
      if (options.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 50) - 1)
      }

      const { data, error } = await query

      if (error) {
        console.error('Error fetching wallet transactions:', error)
        return []
      }

      return data || []

    } catch (error) {
      console.error('Error in getWalletTransactions:', error)
      return []
    }
  }

  /**
   * Get transaction summary statistics
   */
  async getTransactionSummary(
    userId: string,
    startDate?: string,
    endDate?: string
  ): Promise<TransactionSummary> {
    try {
      const transactions = await this.getWalletTransactions(userId, {
        startDate,
        endDate,
        status: 'completed'
      })

      let totalDeposits = 0
      let totalWithdrawals = 0
      let totalInvestments = 0
      let totalDividends = 0
      let totalFees = 0
      let firstTransaction: string | null = null
      let lastTransaction: string | null = null

      for (const transaction of transactions) {
        const amount = Math.abs(transaction.amount)
        
        switch (transaction.transaction_type) {
          case 'deposit':
            totalDeposits += amount
            break
          case 'withdrawal':
            totalWithdrawals += amount
            break
          case 'investment':
            totalInvestments += amount
            break
          case 'dividend':
            totalDividends += amount
            break
          case 'fee':
            totalFees += amount
            break
        }

        // Track first and last transaction dates
        if (!firstTransaction || transaction.processed_at < firstTransaction) {
          firstTransaction = transaction.processed_at
        }
        if (!lastTransaction || transaction.processed_at > lastTransaction) {
          lastTransaction = transaction.processed_at
        }
      }

      return {
        totalDeposits,
        totalWithdrawals,
        totalInvestments,
        totalDividends,
        totalFees,
        netDeposits: totalDeposits - totalWithdrawals,
        transactionCount: transactions.length,
        firstTransaction,
        lastTransaction
      }

    } catch (error) {
      console.error('Error in getTransactionSummary:', error)
      return {
        totalDeposits: 0,
        totalWithdrawals: 0,
        totalInvestments: 0,
        totalDividends: 0,
        totalFees: 0,
        netDeposits: 0,
        transactionCount: 0,
        firstTransaction: null,
        lastTransaction: null
      }
    }
  }

  /**
   * Build capital history time series
   */
  async getCapitalHistory(
    userId: string,
    startDate: string,
    endDate: string,
    interval: 'daily' | 'weekly' | 'monthly' = 'daily'
  ): Promise<CapitalHistory[]> {
    try {
      // Get all transactions in the date range
      const transactions = await this.getWalletTransactions(userId, {
        startDate,
        endDate,
        status: 'completed'
      })

      // Get current capital summary for baseline
      const currentSummary = await this.getUserCapitalSummary(userId)
      if (!currentSummary) {
        return []
      }

      // Build time series by working backwards from current state
      const history: CapitalHistory[] = []
      const dates = this.generateDateRange(startDate, endDate, interval)

      let runningCash = currentSummary.totalCash
      let runningInvested = currentSummary.totalInvested

      // Process dates in reverse order (newest to oldest)
      for (let i = dates.length - 1; i >= 0; i--) {
        const date = dates[i]
        const nextDate = i < dates.length - 1 ? dates[i + 1] : endDate

        // Get transactions for this period
        const periodTransactions = transactions.filter(t => 
          t.processed_at >= date && t.processed_at < nextDate
        )

        let deposits = 0
        let withdrawals = 0

        // Calculate period flows
        for (const transaction of periodTransactions) {
          switch (transaction.transaction_type) {
            case 'deposit':
              deposits += transaction.amount
              break
            case 'withdrawal':
              withdrawals += Math.abs(transaction.amount)
              break
          }
        }

        history.unshift({
          date: date.split('T')[0],
          timestamp: new Date(date).getTime(),
          cashBalance: runningCash,
          investedAmount: runningInvested,
          totalValue: runningCash + runningInvested,
          deposits,
          withdrawals,
          netFlow: deposits - withdrawals
        })

        // Adjust running totals for previous period
        runningCash -= deposits - withdrawals
      }

      return history

    } catch (error) {
      console.error('Error in getCapitalHistory:', error)
      return []
    }
  }

  /**
   * Initialize user capital system
   */
  async initializeUserCapital(userId: string, initialBalance: number = 10000): Promise<boolean> {
    try {
      // Check if user already has balances
      const existing = await this.getUserCapitalSummary(userId)
      if (existing && existing.totalValue > 0) {
        return true // Already initialized
      }

      // Create initial cash balance
      const { error } = await this.supabase
        .from('user_balances')
        .upsert({
          user_id: userId,
          balance: initialBalance,
          currency: 'USD',
          balance_type: 'cash',
          available_for_investment: initialBalance,
          total_invested: 0,
          pending_transactions: 0
        }, {
          onConflict: 'user_id,currency'
        })

      if (error) {
        console.error('Error initializing user capital:', error)
        return false
      }

      // Create initial deposit transaction
      await this.supabase
        .from('wallet_transactions')
        .insert({
          user_id: userId,
          transaction_type: 'deposit',
          amount: initialBalance,
          currency: 'USD',
          description: 'Initial account funding',
          status: 'completed',
          processed_at: new Date().toISOString()
        })

      return true

    } catch (error) {
      console.error('Error in initializeUserCapital:', error)
      return false
    }
  }

  // Helper method to generate date ranges
  private generateDateRange(
    startDate: string,
    endDate: string,
    interval: 'daily' | 'weekly' | 'monthly'
  ): string[] {
    const dates: string[] = []
    const start = new Date(startDate)
    const end = new Date(endDate)

    let current = new Date(start)
    while (current <= end) {
      dates.push(current.toISOString())
      
      switch (interval) {
        case 'daily':
          current.setDate(current.getDate() + 1)
          break
        case 'weekly':
          current.setDate(current.getDate() + 7)
          break
        case 'monthly':
          current.setMonth(current.getMonth() + 1)
          break
      }
    }

    return dates
  }
}

// Singleton instance
let userCapitalResolver: UserCapitalResolver | null = null

export function getUserCapitalResolver(): UserCapitalResolver {
  if (!userCapitalResolver) {
    userCapitalResolver = new UserCapitalResolver()
  }
  return userCapitalResolver
}
