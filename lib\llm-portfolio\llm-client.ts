/**
 * LLMClient - OpenAI API integration for portfolio generation
 */

import OpenAI from 'openai'
import type {
  LLMConfig,
  LLMRequest,
  LLMResponse
} from './types'
import { LLMError } from './types'

export class LLMClient {
  private openai: OpenAI
  private config: LLMConfig
  private rateLimitMap: Map<string, { count: number; resetTime: number }> = new Map()

  constructor(config: LLMConfig) {
    this.config = config
    
    if (!config.apiKey) {
      throw new Error('OpenAI API key is required')
    }

    this.openai = new OpenAI({
      apiKey: config.apiKey,
      timeout: config.timeoutMs || 30000
    })
  }

  /**
   * Generate portfolio using OpenAI API
   */
  async generatePortfolio(request: LLMRequest): Promise<LLMResponse> {
    const startTime = Date.now()
    let attempt = 0
    let lastError: Error | null = null

    // Check rate limit
    await this.checkRateLimit(request.userId)

    while (attempt < this.config.maxRetries) {
      try {
        attempt++
        
        const completion = await this.openai.chat.completions.create({
          model: this.config.model,
          messages: [
            {
              role: 'system',
              content: 'You are an expert financial advisor. Generate portfolio recommendations as valid JSON objects only. Do not include any explanatory text outside the JSON.'
            },
            {
              role: 'user',
              content: request.prompt
            }
          ],
          max_tokens: this.config.maxTokens,
          temperature: this.config.temperature,
          response_format: { type: 'json_object' }
        })

        const processingTime = Date.now() - startTime
        const responseText = completion.choices[0]?.message?.content || ''
        const tokensUsed = completion.usage?.total_tokens || 0

        // Update rate limit tracking
        this.updateRateLimit(request.userId)

        return {
          id: completion.id,
          promptId: request.promptHash,
          responseText,
          tokensUsed,
          model: completion.model,
          provider: 'openai',
          processingTimeMs: processingTime,
          success: true,
          createdAt: new Date()
        }

      } catch (error) {
        lastError = error as Error
        console.error(`LLM API attempt ${attempt} failed:`, error)

        // Check if it's a rate limit error
        if (this.isRateLimitError(error)) {
          const waitTime = this.calculateBackoffTime(attempt)
          console.log(`Rate limited, waiting ${waitTime}ms before retry ${attempt + 1}`)
          await this.sleep(waitTime)
          continue
        }

        // Check if it's a retryable error
        if (!this.isRetryableError(error) || attempt >= this.config.maxRetries) {
          break
        }

        // Exponential backoff for retryable errors
        const waitTime = this.calculateBackoffTime(attempt)
        await this.sleep(waitTime)
      }
    }

    // All retries failed
    const processingTime = Date.now() - startTime
    const errorMessage = lastError?.message || 'Unknown error occurred'

    throw new LLMError(
      `Failed to generate portfolio after ${attempt} attempts: ${errorMessage}`,
      'GENERATION_FAILED',
      {
        attempts: attempt,
        processingTimeMs: processingTime,
        lastError: errorMessage
      }
    )
  }

  /**
   * Validate a portfolio using OpenAI API
   */
  async validatePortfolio(portfolioJson: string, userId: string): Promise<{
    isValid: boolean
    errors: string[]
    suggestions: string[]
  }> {
    try {
      await this.checkRateLimit(userId)

      const completion = await this.openai.chat.completions.create({
        model: this.config.model,
        messages: [
          {
            role: 'system',
            content: 'You are a portfolio validation expert. Analyze the given portfolio and return validation results as JSON.'
          },
          {
            role: 'user',
            content: `Please validate this portfolio JSON and return validation results:

${portfolioJson}

Return as JSON:
{
  "isValid": true/false,
  "errors": ["list of specific issues"],
  "suggestions": ["list of improvements"]
}`
          }
        ],
        max_tokens: 1000,
        temperature: 0.1,
        response_format: { type: 'json_object' }
      })

      const responseText = completion.choices[0]?.message?.content || '{}'
      const result = JSON.parse(responseText)

      this.updateRateLimit(userId)

      return {
        isValid: result.isValid || false,
        errors: result.errors || [],
        suggestions: result.suggestions || []
      }

    } catch (error) {
      console.error('Portfolio validation error:', error)
      return {
        isValid: false,
        errors: ['Failed to validate portfolio due to API error'],
        suggestions: []
      }
    }
  }

  /**
   * Check if user has exceeded rate limit
   */
  private async checkRateLimit(userId: string): Promise<void> {
    const now = Date.now()
    const userLimit = this.rateLimitMap.get(userId)

    if (!userLimit) {
      return // No previous requests
    }

    // Reset counter if time window has passed (1 hour)
    if (now > userLimit.resetTime) {
      this.rateLimitMap.delete(userId)
      return
    }

    // Check if user has exceeded limit
    const maxRequestsPerHour = 10 // Configurable rate limit
    if (userLimit.count >= maxRequestsPerHour) {
      const waitTime = userLimit.resetTime - now
      throw new LLMError(
        `Rate limit exceeded. Try again in ${Math.ceil(waitTime / 1000 / 60)} minutes.`,
        'RATE_LIMIT_EXCEEDED',
        { waitTimeMs: waitTime, resetTime: userLimit.resetTime }
      )
    }
  }

  /**
   * Update rate limit tracking for user
   */
  private updateRateLimit(userId: string): void {
    const now = Date.now()
    const oneHour = 60 * 60 * 1000
    const userLimit = this.rateLimitMap.get(userId)

    if (!userLimit || now > userLimit.resetTime) {
      // First request or reset time passed
      this.rateLimitMap.set(userId, {
        count: 1,
        resetTime: now + oneHour
      })
    } else {
      // Increment counter
      userLimit.count++
    }
  }

  /**
   * Check if error is rate limit related
   */
  private isRateLimitError(error: any): boolean {
    return error?.status === 429 || 
           error?.code === 'rate_limit_exceeded' ||
           error?.message?.toLowerCase().includes('rate limit')
  }

  /**
   * Check if error is retryable
   */
  private isRetryableError(error: any): boolean {
    const retryableStatuses = [429, 500, 502, 503, 504]
    const retryableCodes = [
      'rate_limit_exceeded',
      'server_error',
      'timeout',
      'connection_error'
    ]

    return retryableStatuses.includes(error?.status) ||
           retryableCodes.includes(error?.code) ||
           error?.message?.toLowerCase().includes('timeout') ||
           error?.message?.toLowerCase().includes('connection')
  }

  /**
   * Calculate exponential backoff time
   */
  private calculateBackoffTime(attempt: number): number {
    const baseDelay = 1000 // 1 second
    const maxDelay = 30000 // 30 seconds
    const delay = Math.min(baseDelay * Math.pow(2, attempt - 1), maxDelay)
    
    // Add jitter to prevent thundering herd
    const jitter = Math.random() * 0.1 * delay
    return delay + jitter
  }

  /**
   * Sleep for specified milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * Get API usage statistics
   */
  getUsageStats(): {
    totalRequests: number
    rateLimitedUsers: number
    averageResponseTime: number
  } {
    return {
      totalRequests: 0, // Would need to track this
      rateLimitedUsers: this.rateLimitMap.size,
      averageResponseTime: 0 // Would need to track this
    }
  }

  /**
   * Clear rate limit for a user (admin function)
   */
  clearUserRateLimit(userId: string): void {
    this.rateLimitMap.delete(userId)
  }

  /**
   * Get remaining requests for a user
   */
  getRemainingRequests(userId: string): number {
    const userLimit = this.rateLimitMap.get(userId)
    if (!userLimit || Date.now() > userLimit.resetTime) {
      return 10 // Max requests per hour
    }
    return Math.max(0, 10 - userLimit.count)
  }

  /**
   * Test API connection
   */
  async testConnection(): Promise<boolean> {
    try {
      const completion = await this.openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: 'Hello' }],
        max_tokens: 5
      })
      
      return !!completion.choices[0]?.message?.content
    } catch (error) {
      console.error('API connection test failed:', error)
      return false
    }
  }
}
