"use client"

/**
 * Cache Management UI Component
 * Provides cache management controls and statistics display
 */

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Trash2, RefreshCw, Settings, BarChart3, Clock, Database, HardDrive } from 'lucide-react'
import { useCacheManager } from '@/hooks/use-stock-data'
import { getCacheConfig, type CacheTTLConfig } from '@/lib/cache-config'

export function CacheManagement() {
  const { stats, refreshStats, clearAllCache, clearCacheByTags, cleanupExpiredCache } = useCacheManager()
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [showTTLSettings, setShowTTLSettings] = useState(false)
  const cacheConfig = getCacheConfig()

  // Common cache tags
  const commonTags = [
    'quotes', 'historical', 'portfolio', 'news', 'financials', 
    'market', 'technical', 'user', 'static'
  ]

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDuration = (ms: number): string => {
    if (ms < 1000) return `${ms}ms`
    if (ms < 60000) return `${Math.floor(ms / 1000)}s`
    if (ms < 3600000) return `${Math.floor(ms / 60000)}m`
    return `${Math.floor(ms / 3600000)}h`
  }

  const handleClearByTags = () => {
    if (selectedTags.length > 0) {
      clearCacheByTags(selectedTags)
      setSelectedTags([])
    }
  }

  const handleCleanupExpired = () => {
    cleanupExpiredCache()
  }

  return (
    <div className="space-y-6">
      {/* Cache Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Memory Cache Stats */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Memory Cache</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Entries</span>
                <Badge variant="secondary">{stats.memory.totalEntries}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Hit Rate</span>
                <Badge variant={stats.memory.hitRate > 0.8 ? "default" : "destructive"}>
                  {(stats.memory.hitRate * 100).toFixed(1)}%
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Memory Usage</span>
                <span className="text-sm font-medium">{formatBytes(stats.memory.memoryUsage)}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Browser Cache Stats */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Browser Cache</CardTitle>
            <HardDrive className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Entries</span>
                <Badge variant="secondary">{stats.browser.totalEntries}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Storage Used</span>
                <span className="text-sm font-medium">{formatBytes(stats.browser.storageUsed)}</span>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Storage</span>
                  <span className="text-xs text-muted-foreground">
                    {formatBytes(stats.browser.storageUsed)} / {formatBytes(stats.browser.storageUsed + stats.browser.storageAvailable)}
                  </span>
                </div>
                <Progress 
                  value={(stats.browser.storageUsed / (stats.browser.storageUsed + stats.browser.storageAvailable)) * 100} 
                  className="h-2"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Cache Management Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Cache Management
          </CardTitle>
          <CardDescription>
            Manage cache data and performance settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="actions" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="actions">Actions</TabsTrigger>
              <TabsTrigger value="selective">Selective Clear</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>

            <TabsContent value="actions" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button onClick={refreshStats} variant="outline" className="w-full">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh Stats
                </Button>
                <Button onClick={handleCleanupExpired} variant="outline" className="w-full">
                  <Clock className="h-4 w-4 mr-2" />
                  Cleanup Expired
                </Button>
                <Button onClick={clearAllCache} variant="destructive" className="w-full">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear All Cache
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="selective" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="cache-tags">Select Cache Categories to Clear</Label>
                  <div className="grid grid-cols-3 gap-2 mt-2">
                    {commonTags.map(tag => (
                      <label key={tag} className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={selectedTags.includes(tag)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedTags([...selectedTags, tag])
                            } else {
                              setSelectedTags(selectedTags.filter(t => t !== tag))
                            }
                          }}
                          className="rounded"
                        />
                        <span className="text-sm capitalize">{tag}</span>
                      </label>
                    ))}
                  </div>
                </div>
                <Button 
                  onClick={handleClearByTags} 
                  disabled={selectedTags.length === 0}
                  variant="destructive"
                  className="w-full"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear Selected ({selectedTags.length})
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="settings" className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium">Cache TTL Settings</h4>
                    <p className="text-sm text-muted-foreground">
                      Adjust cache time-to-live for different data types
                    </p>
                  </div>
                  <Dialog open={showTTLSettings} onOpenChange={setShowTTLSettings}>
                    <DialogTrigger asChild>
                      <Button variant="outline" size="sm">
                        <Settings className="h-4 w-4 mr-2" />
                        Configure TTL
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>Cache TTL Configuration</DialogTitle>
                        <DialogDescription>
                          Adjust cache time-to-live settings for different data types. Changes take effect immediately.
                        </DialogDescription>
                      </DialogHeader>
                      <TTLConfigurationPanel />
                    </DialogContent>
                  </Dialog>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Environment:</span>
                    <Badge variant="outline" className="ml-2">
                      {cacheConfig.getEnvironment()}
                    </Badge>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Auto-cleanup:</span>
                    <Badge variant="default" className="ml-2">Enabled</Badge>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Cache Performance Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Cache Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {(stats.memory.hitRate * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-muted-foreground">Hit Rate</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                {stats.memory.totalEntries + stats.browser.totalEntries}
              </div>
              <div className="text-sm text-muted-foreground">Total Entries</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                {formatBytes(stats.memory.memoryUsage + stats.browser.storageUsed)}
              </div>
              <div className="text-sm text-muted-foreground">Total Size</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {stats.browser.storageAvailable > 0 ? 
                  formatBytes(stats.browser.storageAvailable) : 'N/A'
                }
              </div>
              <div className="text-sm text-muted-foreground">Available</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// TTL Configuration Panel Component
function TTLConfigurationPanel() {
  const cacheConfig = getCacheConfig()
  const [ttlValues, setTTLValues] = useState(cacheConfig.getAllTTLs())

  const dataTypeLabels: Record<keyof CacheTTLConfig, string> = {
    realTimeQuotes: 'Real-time Quotes',
    marketStatus: 'Market Status',
    tickData: 'Tick Data',
    stockQuotes: 'Stock Quotes',
    marketMovers: 'Market Movers',
    stockSearch: 'Stock Search',
    financials: 'Financial Data',
    earnings: 'Earnings',
    dividends: 'Dividends',
    news: 'News',
    analysis: 'Analysis',
    sentiment: 'Sentiment',
    historicalDaily: 'Historical Daily',
    historicalIntraday: 'Historical Intraday',
    historicalWeekly: 'Historical Weekly',
    portfolioPerformance: 'Portfolio Performance',
    portfolioAllocations: 'Portfolio Allocations',
    portfolioAnalysis: 'Portfolio Analysis',
    companyProfiles: 'Company Profiles',
    sectorData: 'Sector Data',
    exchangeInfo: 'Exchange Info',
    technicalIndicators: 'Technical Indicators',
    chartData: 'Chart Data',
    userPreferences: 'User Preferences',
    watchlists: 'Watchlists',
    staticContent: 'Static Content',
    configurations: 'Configurations'
  }

  const handleTTLChange = (dataType: keyof CacheTTLConfig, value: string) => {
    const ttlMs = parseInt(value) * 1000 // Convert seconds to milliseconds
    if (!isNaN(ttlMs) && ttlMs > 0) {
      cacheConfig.setTTL(dataType, ttlMs)
      setTTLValues(prev => ({ ...prev, [dataType]: ttlMs }))
    }
  }

  const resetToDefaults = () => {
    cacheConfig.resetAllTTLs()
    setTTLValues(cacheConfig.getAllTTLs())
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h4 className="text-sm font-medium">TTL Settings (seconds)</h4>
        <Button onClick={resetToDefaults} variant="outline" size="sm">
          Reset to Defaults
        </Button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
        {Object.entries(dataTypeLabels).map(([key, label]) => {
          const dataType = key as keyof CacheTTLConfig
          const currentValue = Math.floor(ttlValues[dataType] / 1000) // Convert to seconds
          
          return (
            <div key={key} className="space-y-2">
              <Label htmlFor={key} className="text-xs">
                {label}
              </Label>
              <div className="flex items-center space-x-2">
                <Input
                  id={key}
                  type="number"
                  value={currentValue}
                  onChange={(e) => handleTTLChange(dataType, e.target.value)}
                  className="h-8 text-xs"
                  min="1"
                />
                <span className="text-xs text-muted-foreground">
                  {cacheConfig.getTTLDescription(ttlValues[dataType])}
                </span>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
