# Investry Premium Payment System Setup Guide

This guide will help you set up the complete payment system for your Investry application, focused on Investry Premium subscriptions and account funding.

## Overview

The payment system includes:
- ✅ One-time payments (account funding)
- ✅ Investry Premium recurring subscriptions
- ✅ Payment history and management
- ✅ Stripe integration with webhooks
- ✅ Database tables for payments, subscriptions, and user balances
- ✅ Secure payment processing
- ✅ Premium status indicators in UI

## Prerequisites

1. **Stripe Account**: Sign up at [stripe.com](https://stripe.com)
2. **Supabase Project**: Your existing Supabase setup
3. **Environment Variables**: Already configured in `.env.local`

## Step 1: Set up Stripe

### 1.1 Get Your API Keys
1. Go to [Stripe Dashboard](https://dashboard.stripe.com/apikeys)
2. Copy your **Publishable key** (starts with `pk_test_`)
3. Copy your **Secret key** (starts with `sk_test_`)
4. Your keys are already configured in `.env.local`

### 1.2 Create Products and Prices
1. Go to [Stripe Products](https://dashboard.stripe.com/products)
2. Create a product for Investry Premium:
   - **Product Name**: Investry Premium
   - **Description**: Unlock the full power of professional investing
   - **Price**: $19.99/month (recurring)

3. Copy the **Price ID** (starts with `price_`) and update it in:
   - `app/pricing/page.tsx` (replace the stripePriceId value)
   - `database/sample-subscription-plans.sql`

### 1.3 Set up Webhooks
1. Go to [Stripe Webhooks](https://dashboard.stripe.com/webhooks)
2. Click "Add endpoint"
3. Set endpoint URL to: `https://yourdomain.com/api/webhooks/stripe`
4. Select these events:
   - `checkout.session.completed`
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
   - `invoice.payment_succeeded`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
5. Copy the **Webhook Secret** (starts with `whsec_`)
6. Update `STRIPE_WEBHOOK_SECRET` in `.env.local`

## Step 2: Set up Database Tables

### 2.1 Create Payment Tables
1. Open your Supabase SQL Editor
2. Run the SQL from `database/payment-tables.sql`
3. This creates:
   - `user_balances` - Track user account balances
   - `payments` - Store payment transactions
   - `subscription_plans` - Available subscription plans
   - `subscriptions` - User subscriptions
   - `payment_methods` - Saved payment methods

### 2.2 Insert Sample Data
1. Run the SQL from `database/sample-subscription-plans.sql`
2. Update the `stripe_price_id` values with your actual Stripe price IDs

## Step 3: Test the Payment System

### 3.1 Test One-time Payments
1. Go to `/payments`
2. Try adding funds with test card: `4242 4242 4242 4242`
3. Check that:
   - Payment completes successfully
   - User balance updates
   - Payment appears in history

### 3.2 Test Subscriptions
1. Go to `/pricing`
2. Select a plan and complete checkout
3. Check that:
   - Subscription is created
   - User is redirected to success page
   - Subscription appears in database

### 3.3 Test Webhooks
1. Use Stripe CLI to forward webhooks locally:
   ```bash
   stripe listen --forward-to localhost:3000/api/webhooks/stripe
   ```
2. Make a test payment
3. Check webhook logs in Stripe Dashboard

## Step 4: Production Setup

### 4.1 Update Environment Variables
1. Replace test keys with live keys in production
2. Update `NEXT_PUBLIC_BASE_URL` to your production domain
3. Set up webhook endpoint with your production URL

### 4.2 Security Considerations
- ✅ Webhook signature verification implemented
- ✅ Row Level Security (RLS) enabled on all tables
- ✅ Server-side API key usage only
- ✅ Input validation on all endpoints

## Available Pages and APIs

### Pages
- `/payments` - Add funds to account
- `/payments/manage` - View payment history and settings
- `/pricing` - Subscription plans
- `/subscription/success` - Subscription confirmation
- `/payments/success` - Payment confirmation

### API Endpoints
- `POST /api/create-checkout-session` - Create one-time payment
- `POST /api/create-subscription-session` - Create subscription
- `GET /api/verify-payment` - Verify payment completion
- `GET /api/verify-subscription` - Verify subscription
- `POST /api/webhooks/stripe` - Handle Stripe webhooks
- `GET /api/user-balance` - Get user balance
- `GET /api/payment-history` - Get payment history

## Troubleshooting

### Common Issues

1. **Webhook not receiving events**
   - Check webhook URL is correct
   - Verify webhook secret in environment variables
   - Check Stripe webhook logs

2. **Payment not updating balance**
   - Check webhook is processing correctly
   - Verify database functions are working
   - Check Supabase logs

3. **Subscription not creating**
   - Verify price IDs match Stripe products
   - Check customer creation in Stripe
   - Review subscription webhook handling

### Testing Cards
- **Success**: 4242 4242 4242 4242
- **Decline**: 4000 0000 0000 0002
- **Insufficient funds**: 4000 0000 0000 9995

## Support

If you need help:
1. Check Stripe Dashboard logs
2. Review Supabase function logs
3. Test with Stripe CLI
4. Contact support with specific error messages

## Next Steps

Consider adding:
- Payment method management
- Subscription plan changes
- Refund processing
- Invoice generation
- Usage-based billing
- Multi-currency support
