import { createServerClient } from '@supabase/ssr'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function middleware(req: NextRequest) {
  let response = NextResponse.next({
    request: {
      headers: req.headers,
    },
  })

  // Check if Supabase is configured first
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseKey ||
      supabaseUrl === 'your_supabase_url_here' ||
      supabaseKey === 'your_supabase_anon_key_here') {
    // If Supabase is not configured, allow all routes (demo mode)
    return response
  }

  let supabase
  try {
    supabase = createServerClient(
      supabaseUrl,
      supabaseKey,
      {
        cookies: {
          getAll() {
            return req.cookies.getAll()
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              req.cookies.set(name, value)
              response.cookies.set(name, value, options)
            })
          },
        },
      }
    )
  } catch (error) {
    console.error('Middleware: Failed to create Supabase client:', error)
    // If client creation fails, allow all routes
    return response
  }

  // Get session with error handling
  let session = null
  try {
    // Debug: Log all cookies
    const allCookies = req.cookies.getAll()
    const supabaseCookies = allCookies.filter(cookie => cookie.name.includes('supabase') || cookie.name.includes('sb-'))
    console.log('Middleware: Supabase cookies:', supabaseCookies.map(c => c.name))

    const {
      data: { session: userSession },
    } = await supabase.auth.getSession()
    session = userSession
    console.log('Middleware: Session check for', req.nextUrl.pathname, '- User:', session?.user?.email || 'none')
  } catch (error) {
    console.error('Middleware: Failed to get session:', error)
    // If session check fails, allow the request to continue
    return response
  }

  // Protected routes that require authentication
  const protectedRoutes = [
    '/dashboard',
    '/profile',
    '/portfolio',
    '/investments',
    '/settings',
    '/onboarding',
    '/learn',
    '/history',
    '/notifications',
    '/help'
  ]

  // Check if the current path is protected
  const isProtectedRoute = protectedRoutes.some(route =>
    req.nextUrl.pathname.startsWith(route)
  )

  // Temporarily disable middleware protection to allow client-side auth handling
  // The client-side auth provider will handle redirects properly
  // TODO: Re-enable once cookie-based session storage is properly configured

  // if (isProtectedRoute && !session) {
  //   console.log('Middleware: Redirecting unauthenticated user from', req.nextUrl.pathname, 'to signin')
  //   const redirectUrl = new URL('/auth/signin', req.url)
  //   redirectUrl.searchParams.set('redirectTo', req.nextUrl.pathname)
  //   return NextResponse.redirect(redirectUrl)
  // }

  // Allow access to auth pages regardless of authentication status
  // Users should be able to stay on signin/signup pages even if authenticated

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',
  ],
}
