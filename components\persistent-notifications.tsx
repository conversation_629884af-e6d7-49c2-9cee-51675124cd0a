"use client"

import { createContext, useContext, useState, useC<PERSON>back, useEffect, type ReactNode } from "react"
import { useAuth } from "@/components/auth-provider"

export interface PersistentNotification {
  id: string
  type: "portfolio" | "goal" | "market" | "educational" | "system"
  title: string
  message: string
  read: boolean
  createdAt: Date
  icon?: string
  actionUrl?: string
}

interface PersistentNotificationContextType {
  notifications: PersistentNotification[]
  unreadCount: number
  addNotification: (notification: Omit<PersistentNotification, "id" | "read" | "createdAt">) => void
  markAsRead: (id: string) => void
  markAsUnread: (id: string) => void
  markAllAsRead: () => void
  markAllAsUnread: () => void
  deleteNotification: (id: string) => void
  getRecentNotifications: (limit?: number) => PersistentNotification[]
}

const PersistentNotificationContext = createContext<PersistentNotificationContextType | undefined>(undefined)

export function usePersistentNotifications() {
  const context = useContext(PersistentNotificationContext)
  if (!context) {
    throw new Error("usePersistentNotifications must be used within a PersistentNotificationProvider")
  }
  return context
}

interface PersistentNotificationProviderProps {
  children: ReactNode
}

export function PersistentNotificationProvider({ children }: PersistentNotificationProviderProps) {
  const { user } = useAuth()
  const [notifications, setNotifications] = useState<PersistentNotification[]>([])

  // Load notifications from Supabase instead of localStorage
  useEffect(() => {
    if (user) {
      // TODO: Implement Supabase notifications table
      // For now, create initial notifications without localStorage fallback
      createInitialNotifications()
    }
  }, [user])

  // Save notifications to localStorage whenever they change
  useEffect(() => {
    if (user && notifications.length > 0) {
      localStorage.setItem(`investry_notifications_${user.id}`, JSON.stringify(notifications))
    }
  }, [notifications, user])

  const createInitialNotifications = useCallback(() => {
    const initialNotifications: PersistentNotification[] = [
      {
        id: "welcome-1",
        type: "system",
        title: "Welcome to Investry!",
        message: "Complete your investment profile to get personalized recommendations.",
        read: false,
        createdAt: new Date(),
        actionUrl: "/onboarding"
      },
      {
        id: "portfolio-1",
        type: "portfolio",
        title: "Portfolio Setup",
        message: "Create your first portfolio to start tracking your investments.",
        read: false,
        createdAt: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
        actionUrl: "/portfolio"
      },
      {
        id: "educational-1",
        type: "educational",
        title: "New Learning Module",
        message: "Check out our guide on 'Understanding Stock Market Basics'.",
        read: false,
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
        actionUrl: "/learn"
      }
    ]
    setNotifications(initialNotifications)
  }, [])

  const addNotification = useCallback((notification: Omit<PersistentNotification, "id" | "read" | "createdAt">) => {
    const id = `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const newNotification: PersistentNotification = {
      ...notification,
      id,
      read: false,
      createdAt: new Date()
    }

    setNotifications(prev => [newNotification, ...prev])
  }, [])

  const markAsRead = useCallback((id: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id
          ? { ...notification, read: true }
          : notification
      )
    )
  }, [])

  const markAsUnread = useCallback((id: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id
          ? { ...notification, read: false }
          : notification
      )
    )
  }, [])

  const markAllAsRead = useCallback(() => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, read: true }))
    )
  }, [])

  const markAllAsUnread = useCallback(() => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, read: false }))
    )
  }, [])

  const deleteNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id))
  }, [])

  const getRecentNotifications = useCallback((limit = 5) => {
    return notifications
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(0, limit)
  }, [notifications])

  const unreadCount = notifications.filter(n => !n.read).length

  const contextValue = {
    notifications,
    unreadCount,
    addNotification,
    markAsRead,
    markAsUnread,
    markAllAsRead,
    markAllAsUnread,
    deleteNotification,
    getRecentNotifications
  }

  return (
    <PersistentNotificationContext.Provider value={contextValue}>
      {children}
    </PersistentNotificationContext.Provider>
  )
}

// Helper function to create notifications for common events
export const createNotificationHelpers = () => {
  const { addNotification } = usePersistentNotifications()

  return {
    portfolioUpdate: (message: string) => addNotification({
      type: "portfolio",
      title: "Portfolio Update",
      message,
      actionUrl: "/portfolio"
    }),

    goalProgress: (message: string) => addNotification({
      type: "goal",
      title: "Goal Progress",
      message,
      actionUrl: "/goals"
    }),

    marketAlert: (message: string) => addNotification({
      type: "market",
      title: "Market Alert",
      message
    }),

    educationalContent: (title: string, message: string) => addNotification({
      type: "educational",
      title,
      message,
      actionUrl: "/learn"
    }),

    systemNotification: (title: string, message: string, actionUrl?: string) => addNotification({
      type: "system",
      title,
      message,
      actionUrl
    })
  }
}
