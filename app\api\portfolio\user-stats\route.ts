/**
 * User Portfolio Generation Statistics API Endpoint
 * GET /api/portfolio/user-stats
 */

import { NextRequest, NextResponse } from 'next/server'
import { withAuth, addSecurityHeaders } from '@/lib/llm-portfolio/auth-middleware'
import { getLLMPortfolioEngine } from '@/lib/llm-portfolio'
import type { AuthContext } from '@/lib/llm-portfolio/auth-middleware'

async function userStatsHandler(
  request: NextRequest,
  context: AuthContext
): Promise<NextResponse> {
  try {
    // Get LLM portfolio engine
    const engine = getLLMPortfolioEngine()

    // Get user's generation statistics
    const stats = await engine.getGenerationStats(context.userId)

    const response = NextResponse.json({
      success: true,
      data: {
        userId: context.userId,
        statistics: stats,
        timestamp: new Date().toISOString()
      }
    })

    addSecurityHeaders(response)
    return response

  } catch (error) {
    console.error('User stats error:', error)
    
    const response = NextResponse.json(
      {
        success: false,
        error: 'Failed to get user statistics',
        message: error.message
      },
      { status: 500 }
    )

    addSecurityHeaders(response)
    return response
  }
}

// Export with authentication required
export const GET = withAuth(userStatsHandler, {
  requireAuth: true,
  rateLimitPerHour: 30
})

// Handle OPTIONS for CORS
export async function OPTIONS() {
  const response = new NextResponse(null, { status: 200 })
  addSecurityHeaders(response)
  return response
}
