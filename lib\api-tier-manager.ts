/**
 * API Tier Management System
 * Automatically manages API tier selection and fallback logic
 */

import { getApiConfig, getCurrentApiTier, getRecommendedTier, type ApiTier } from './api-tier-config'
import { getEnhancedStockService } from './enhanced-stock-service'
import { getTwelveDataService } from './twelve-data-service'
import { getTaapiService } from './taapi-service'

export interface TierUsageMetrics {
  userCount: number
  dailyRequests: number
  monthlyRequests: number
  errorRate: number
  averageResponseTime: number
  costPerRequest: number
  lastUpdated: Date
}

export interface TierRecommendation {
  currentTier: ApiTier
  recommendedTier: ApiTier
  reason: string
  estimatedMonthlyCost: number
  potentialSavings?: number
  upgradeRequired: boolean
  features: {
    current: string[]
    recommended: string[]
    new: string[]
  }
}

export class ApiTierManager {
  private metrics: TierUsageMetrics = {
    userCount: 0,
    dailyRequests: 0,
    monthlyRequests: 0,
    errorRate: 0,
    averageResponseTime: 0,
    costPerRequest: 0,
    lastUpdated: new Date()
  }

  private requestLog: Array<{ timestamp: Date; success: boolean; responseTime: number }> = []

  /**
   * Get current tier status and recommendations
   */
  getTierRecommendation(): TierRecommendation {
    const currentTier = getCurrentApiTier()
    const recommendedTier = getRecommendedTier(this.metrics.userCount)
    const currentConfig = getApiConfig()

    const currentFeatures = Object.entries(currentConfig.features)
      .filter(([_, enabled]) => enabled)
      .map(([feature, _]) => feature)

    let recommendedFeatures: string[] = []
    let newFeatures: string[] = []
    let reason = ''
    let upgradeRequired = false

    if (recommendedTier !== currentTier) {
      const recommendedConfig = getApiConfig()
      recommendedFeatures = Object.entries(recommendedConfig.features)
        .filter(([_, enabled]) => enabled)
        .map(([feature, _]) => feature)
      
      newFeatures = recommendedFeatures.filter(f => !currentFeatures.includes(f))
      upgradeRequired = true

      if (this.metrics.userCount > currentConfig.maxUsers) {
        reason = `User count (${this.metrics.userCount}) exceeds current tier limit (${currentConfig.maxUsers})`
      } else if (this.metrics.errorRate > 0.1) {
        reason = `High error rate (${(this.metrics.errorRate * 100).toFixed(1)}%) suggests API limits are being hit`
      } else if (this.metrics.dailyRequests > 1000) {
        reason = `High request volume (${this.metrics.dailyRequests}/day) would benefit from higher tier`
      }
    } else {
      recommendedFeatures = currentFeatures
      reason = 'Current tier is appropriate for your usage'
    }

    return {
      currentTier,
      recommendedTier,
      reason,
      estimatedMonthlyCost: this.calculateMonthlyCost(recommendedTier),
      potentialSavings: upgradeRequired ? undefined : this.calculatePotentialSavings(),
      upgradeRequired,
      features: {
        current: currentFeatures,
        recommended: recommendedFeatures,
        new: newFeatures
      }
    }
  }

  /**
   * Update usage metrics
   */
  updateMetrics(userCount: number, requestSuccess: boolean, responseTime: number) {
    this.metrics.userCount = userCount
    this.metrics.lastUpdated = new Date()

    // Add to request log
    this.requestLog.push({
      timestamp: new Date(),
      success: requestSuccess,
      responseTime
    })

    // Keep only last 24 hours of logs
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
    this.requestLog = this.requestLog.filter(log => log.timestamp > oneDayAgo)

    // Calculate metrics from recent logs
    this.calculateMetricsFromLogs()
  }

  /**
   * Get the best available service for current tier
   */
  getBestStockService() {
    const currentTier = getCurrentApiTier()
    
    switch (currentTier) {
      case 'enterprise':
        // Try enterprise services first, fallback to lower tiers
        return getEnhancedStockService()
      
      case 'mid-tier':
        // Try mid-tier services first
        const twelveData = getTwelveDataService()
        if (twelveData) return twelveData
        return getEnhancedStockService()
      
      case 'startup':
      default:
        return getEnhancedStockService()
    }
  }

  /**
   * Get technical analysis service for current tier
   */
  getTechnicalAnalysisService() {
    const currentTier = getCurrentApiTier()
    
    switch (currentTier) {
      case 'enterprise':
        return getTaapiService('unlimited') || getTaapiService('standard') || getTaapiService('starter')
      
      case 'mid-tier':
        return getTaapiService('standard') || getTaapiService('starter')
      
      case 'startup':
      default:
        return getTaapiService('starter')
    }
  }

  /**
   * Check if tier upgrade is needed
   */
  shouldUpgrade(): boolean {
    const recommendation = this.getTierRecommendation()
    return recommendation.upgradeRequired
  }

  /**
   * Get upgrade urgency level
   */
  getUpgradeUrgency(): 'low' | 'medium' | 'high' | 'critical' {
    const config = getApiConfig()
    
    if (this.metrics.userCount > config.maxUsers * 1.2) return 'critical'
    if (this.metrics.errorRate > 0.2) return 'critical'
    if (this.metrics.userCount > config.maxUsers) return 'high'
    if (this.metrics.errorRate > 0.1) return 'high'
    if (this.metrics.dailyRequests > 1000) return 'medium'
    
    return 'low'
  }

  /**
   * Get cost optimization suggestions
   */
  getCostOptimizations(): Array<{ suggestion: string; estimatedSavings: number }> {
    const suggestions: Array<{ suggestion: string; estimatedSavings: number }> = []
    const currentTier = getCurrentApiTier()
    
    if (currentTier === 'enterprise' && this.metrics.userCount < 1000) {
      suggestions.push({
        suggestion: 'Consider downgrading to mid-tier - your user count is low',
        estimatedSavings: 3000
      })
    }
    
    if (currentTier === 'mid-tier' && this.metrics.userCount < 50) {
      suggestions.push({
        suggestion: 'Consider downgrading to startup tier - your user count is low',
        estimatedSavings: 250
      })
    }
    
    if (this.metrics.errorRate < 0.01 && this.metrics.dailyRequests < 100) {
      suggestions.push({
        suggestion: 'Enable more aggressive caching to reduce API calls',
        estimatedSavings: 50
      })
    }
    
    return suggestions
  }

  /**
   * Private helper methods
   */
  private calculateMetricsFromLogs() {
    if (this.requestLog.length === 0) return

    // Daily requests
    this.metrics.dailyRequests = this.requestLog.length

    // Monthly requests (estimate)
    this.metrics.monthlyRequests = this.metrics.dailyRequests * 30

    // Error rate
    const failures = this.requestLog.filter(log => !log.success).length
    this.metrics.errorRate = failures / this.requestLog.length

    // Average response time
    const totalResponseTime = this.requestLog.reduce((sum, log) => sum + log.responseTime, 0)
    this.metrics.averageResponseTime = totalResponseTime / this.requestLog.length

    // Cost per request (estimate)
    const config = getApiConfig()
    this.metrics.costPerRequest = config.monthlyCost / Math.max(this.metrics.monthlyRequests, 1)
  }

  private calculateMonthlyCost(tier: ApiTier): number {
    const config = getApiConfig()
    return config.monthlyCost
  }

  private calculatePotentialSavings(): number {
    const currentTier = getCurrentApiTier()
    const recommendedTier = getRecommendedTier(this.metrics.userCount)
    
    if (recommendedTier === currentTier) return 0
    
    const currentCost = this.calculateMonthlyCost(currentTier)
    const recommendedCost = this.calculateMonthlyCost(recommendedTier)
    
    return Math.max(0, currentCost - recommendedCost)
  }

  /**
   * Get current metrics
   */
  getMetrics(): TierUsageMetrics {
    return { ...this.metrics }
  }

  /**
   * Export metrics for monitoring
   */
  exportMetrics() {
    return {
      ...this.metrics,
      recommendation: this.getTierRecommendation(),
      urgency: this.getUpgradeUrgency(),
      optimizations: this.getCostOptimizations()
    }
  }
}

// Singleton instance
let apiTierManagerInstance: ApiTierManager | null = null

export function getApiTierManager(): ApiTierManager {
  if (!apiTierManagerInstance) {
    apiTierManagerInstance = new ApiTierManager()
  }
  return apiTierManagerInstance
}

/**
 * Utility functions for easy access
 */
export function getCurrentTierInfo() {
  const manager = getApiTierManager()
  return manager.getTierRecommendation()
}

export function shouldShowUpgradePrompt(): boolean {
  const manager = getApiTierManager()
  const urgency = manager.getUpgradeUrgency()
  return ['high', 'critical'].includes(urgency)
}

export function getUpgradeMessage(): string {
  const manager = getApiTierManager()
  const recommendation = manager.getTierRecommendation()
  const urgency = manager.getUpgradeUrgency()
  
  if (urgency === 'critical') {
    return `⚠️ Critical: ${recommendation.reason}. Upgrade to ${recommendation.recommendedTier} immediately.`
  } else if (urgency === 'high') {
    return `🚨 ${recommendation.reason}. Consider upgrading to ${recommendation.recommendedTier}.`
  } else if (urgency === 'medium') {
    return `💡 ${recommendation.reason}. Upgrading to ${recommendation.recommendedTier} could improve performance.`
  }
  
  return `✅ ${recommendation.reason}`
}
