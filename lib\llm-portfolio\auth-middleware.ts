/**
 * Authentication and Security Middleware for LLM Portfolio API
 */

import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { LLMError } from './types'

// Rate limiting storage (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

export interface AuthContext {
  userId: string
  userEmail?: string
  isAuthenticated: boolean
  rateLimitRemaining: number
}

export interface AuthMiddlewareOptions {
  requireAuth?: boolean
  rateLimitPerHour?: number
  allowedRoles?: string[]
  requireApiKey?: boolean
  apiKeyHeader?: string
}

/**
 * Authentication middleware for LLM portfolio endpoints
 */
export async function authMiddleware(
  request: NextRequest,
  options: AuthMiddlewareOptions = {}
): Promise<{ success: boolean; context?: AuthContext; response?: NextResponse }> {
  const {
    requireAuth = true,
    rateLimitPerHour = 10,
    allowedRoles = [],
    requireApiKey = false,
    apiKeyHeader = 'x-api-key'
  } = options

  try {
    // Initialize Supabase client for token validation
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )

    let userId: string | null = null
    let userEmail: string | null = null

    // Check API key if required
    if (requireApiKey) {
      const apiKey = request.headers.get(apiKeyHeader)
      if (!apiKey || !isValidApiKey(apiKey)) {
        return {
          success: false,
          response: NextResponse.json(
            { error: 'Invalid or missing API key' },
            { status: 401 }
          )
        }
      }
    }

    // Extract user from JWT token
    if (requireAuth) {
      const authHeader = request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return {
          success: false,
          response: NextResponse.json(
            { error: 'Missing or invalid authorization header' },
            { status: 401 }
          )
        }
      }

      const token = authHeader.substring(7)

      try {
        const { data: { user }, error } = await supabase.auth.getUser(token)
        
        if (error || !user) {
          return {
            success: false,
            response: NextResponse.json(
              { error: 'Invalid or expired token' },
              { status: 401 }
            )
          }
        }

        userId = user.id
        userEmail = user.email

        // Check user roles if specified
        if (allowedRoles.length > 0) {
          const userRole = user.user_metadata?.role || 'user'
          if (!allowedRoles.includes(userRole)) {
            return {
              success: false,
              response: NextResponse.json(
                { error: 'Insufficient permissions' },
                { status: 403 }
              )
            }
          }
        }

      } catch (jwtError) {
        return {
          success: false,
          response: NextResponse.json(
            { error: 'Token validation failed' },
            { status: 401 }
          )
        }
      }
    } else {
      // For non-auth endpoints, try to extract user from query params or headers
      userId = request.nextUrl.searchParams.get('user_id') || 
               request.headers.get('x-user-id') ||
               'anonymous'
    }

    // Rate limiting check
    const clientId = userId || getClientIdentifier(request)
    const rateLimitResult = checkRateLimit(clientId, rateLimitPerHour)
    
    if (!rateLimitResult.allowed) {
      return {
        success: false,
        response: NextResponse.json(
          { 
            error: 'Rate limit exceeded',
            retryAfter: Math.ceil((rateLimitResult.resetTime - Date.now()) / 1000)
          },
          { 
            status: 429,
            headers: {
              'Retry-After': Math.ceil((rateLimitResult.resetTime - Date.now()) / 1000).toString(),
              'X-RateLimit-Limit': rateLimitPerHour.toString(),
              'X-RateLimit-Remaining': '0',
              'X-RateLimit-Reset': rateLimitResult.resetTime.toString()
            }
          }
        )
      }
    }

    // Create auth context
    const context: AuthContext = {
      userId: userId!,
      userEmail: userEmail || undefined,
      isAuthenticated: !!userId && userId !== 'anonymous',
      rateLimitRemaining: rateLimitResult.remaining
    }

    return {
      success: true,
      context
    }

  } catch (error) {
    console.error('Auth middleware error:', error)
    return {
      success: false,
      response: NextResponse.json(
        { error: 'Authentication service unavailable' },
        { status: 503 }
      )
    }
  }
}

/**
 * Rate limiting implementation
 */
function checkRateLimit(clientId: string, limitPerHour: number): {
  allowed: boolean
  remaining: number
  resetTime: number
} {
  const now = Date.now()
  const oneHour = 60 * 60 * 1000
  
  const existing = rateLimitStore.get(clientId)
  
  if (!existing || now > existing.resetTime) {
    // First request or reset time passed
    const resetTime = now + oneHour
    rateLimitStore.set(clientId, { count: 1, resetTime })
    
    return {
      allowed: true,
      remaining: limitPerHour - 1,
      resetTime
    }
  }
  
  if (existing.count >= limitPerHour) {
    return {
      allowed: false,
      remaining: 0,
      resetTime: existing.resetTime
    }
  }
  
  // Increment counter
  existing.count++
  
  return {
    allowed: true,
    remaining: limitPerHour - existing.count,
    resetTime: existing.resetTime
  }
}

/**
 * Get client identifier for rate limiting
 */
function getClientIdentifier(request: NextRequest): string {
  // Try to get IP address
  const forwarded = request.headers.get('x-forwarded-for')
  const realIp = request.headers.get('x-real-ip')
  const ip = forwarded?.split(',')[0] || realIp || 'unknown'
  
  return `ip:${ip}`
}

/**
 * Validate API key (implement your own logic)
 */
function isValidApiKey(apiKey: string): boolean {
  // In production, validate against database or environment variable
  const validApiKeys = process.env.LLM_API_KEYS?.split(',') || []
  return validApiKeys.includes(apiKey)
}

/**
 * Middleware wrapper for Next.js API routes
 */
export function withAuth(
  handler: (request: NextRequest, context: AuthContext) => Promise<NextResponse>,
  options: AuthMiddlewareOptions = {}
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const authResult = await authMiddleware(request, options)
    
    if (!authResult.success) {
      return authResult.response!
    }
    
    try {
      return await handler(request, authResult.context!)
    } catch (error) {
      console.error('API handler error:', error)
      
      if (error instanceof LLMError) {
        return NextResponse.json(
          { error: error.message, code: error.code },
          { status: error.code === 'RATE_LIMIT_EXCEEDED' ? 429 : 500 }
        )
      }
      
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}

/**
 * Security headers middleware
 */
export function addSecurityHeaders(response: NextResponse): NextResponse {
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-XSS-Protection', '1; mode=block')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()')
  
  return response
}

/**
 * CORS middleware for LLM API endpoints
 */
export function addCorsHeaders(response: NextResponse, origin?: string): NextResponse {
  const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000']
  
  if (origin && allowedOrigins.includes(origin)) {
    response.headers.set('Access-Control-Allow-Origin', origin)
  }
  
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-API-Key')
  response.headers.set('Access-Control-Max-Age', '86400')
  
  return response
}

/**
 * Input validation middleware
 */
export function validateInput(data: any, schema: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = []
  
  // Basic validation - in production, use a proper validation library like Zod
  if (schema.required) {
    for (const field of schema.required) {
      if (!data[field]) {
        errors.push(`Missing required field: ${field}`)
      }
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Audit logging middleware
 */
export async function logApiAccess(
  request: NextRequest,
  context: AuthContext,
  response: NextResponse,
  processingTime: number
): Promise<void> {
  try {
    const logEntry = {
      userId: context.userId,
      endpoint: request.nextUrl.pathname,
      method: request.method,
      userAgent: request.headers.get('user-agent'),
      ip: getClientIdentifier(request),
      statusCode: response.status,
      processingTimeMs: processingTime,
      timestamp: new Date().toISOString()
    }
    
    // Log to your preferred logging service
    console.log('API Access:', JSON.stringify(logEntry))
    
  } catch (error) {
    console.error('Failed to log API access:', error)
  }
}

/**
 * Clean up expired rate limit entries
 */
export function cleanupRateLimitStore(): void {
  const now = Date.now()
  
  for (const [key, value] of rateLimitStore.entries()) {
    if (now > value.resetTime) {
      rateLimitStore.delete(key)
    }
  }
}

// Clean up rate limit store every hour
setInterval(cleanupRateLimitStore, 60 * 60 * 1000)
