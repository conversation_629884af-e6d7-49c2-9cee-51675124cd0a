-- Check for VARCHAR constraint issues in stocks table
-- Run this in your Supabase SQL Editor to identify problematic data

-- 1. Check ticker symbols longer than 10 characters
SELECT 
  ticker, 
  LENGTH(ticker) as ticker_length,
  company_name
FROM stocks 
WHERE LENGTH(ticker) > 10
ORDER BY LENGTH(ticker) DESC
LIMIT 10;

-- 2. Check stock_type values longer than expected
SELECT 
  stock_type, 
  LENGTH(stock_type) as stock_type_length,
  COUNT(*) as count
FROM stocks 
WHERE LENGTH(stock_type) > 20
GROUP BY stock_type, LENGTH(stock_type)
ORDER BY LENGTH(stock_type) DESC
LIMIT 10;

-- 3. Check exchange values that might be too long
SELECT 
  exchange, 
  LENGTH(exchange) as exchange_length,
  COUNT(*) as count
FROM stocks 
WHERE LENGTH(exchange) > 10
GROUP BY exchange, LENGTH(exchange)
ORDER BY LENGTH(exchange) DESC
LIMIT 10;

-- 4. Check sector values that might be too long
SELECT 
  sector, 
  LENGTH(sector) as sector_length,
  COUNT(*) as count
FROM stocks 
WHERE LENGTH(sector) > 50
GROUP BY sector, LEN<PERSON><PERSON>(sector)
ORDER BY LENGTH(sector) DESC
LIMIT 10;

-- 5. Check the table structure to see column constraints
SELECT 
  column_name,
  data_type,
  character_maximum_length,
  is_nullable
FROM information_schema.columns 
WHERE table_name = 'stocks' 
  AND table_schema = 'public'
ORDER BY ordinal_position;
