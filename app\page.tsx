"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Wallet, TrendingUp, Shield, BookOpen, Users, Play, CheckCircle, Sparkles, Zap } from "lucide-react"

export default function LandingPage() {
  const router = useRouter()
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })

  useEffect(() => {
    // Check if user is already authenticated
    const isAuthenticated = localStorage.getItem("investry_auth")
    if (isAuthenticated) {
      router.push("/dashboard")
      return
    }

    // Mouse follower effect
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY })
    }

    window.addEventListener("mousemove", handleMouseMove)
    return () => window.removeEventListener("mousemove", handleMouseMove)
  }, [router])

  const features = [
    {
      icon: TrendingUp,
      title: "Smart Portfolio Building",
      description: "AI-powered recommendations based on your goals and preferences",
    },
    {
      icon: BookOpen,
      title: "Learn While You Invest",
      description: "Interactive courses designed for all experience levels",
    },
    {
      icon: Shield,
      title: "Risk Management",
      description: "Built-in safeguards to protect your investments",
    },
    {
      icon: Users,
      title: "Community Support",
      description: "Connect with other investors and share insights",
    },
  ]

  const testimonials = [
    {
      name: "Sarah Chen",
      title: "Software Engineer",
      text: "investry helped me start investing with just $50. Now I have a diversified portfolio!",
      rating: 5,
    },
    {
      name: "Marcus Johnson",
      title: "Business Analyst",
      text: "The educational content is amazing. I learned more here than anywhere else.",
      rating: 5,
    },
    {
      name: "Emma Rodriguez",
      title: "Marketing Manager",
      text: "Perfect for beginners. The app guides you through everything step by step.",
      rating: 5,
    },
  ]

  return (
    <div className="min-h-screen bg-slate-900 text-white overflow-hidden relative">
      {/* Mouse follower glow */}
      <div
        className="fixed pointer-events-none z-0 w-96 h-96 rounded-full opacity-20 blur-3xl transition-all duration-300"
        style={{
          background: "radial-gradient(circle, rgba(34,197,94,0.3) 0%, transparent 70%)",
          left: mousePosition.x - 192,
          top: mousePosition.y - 192,
        }}
      />

      {/* Animated background particles */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-emerald-400/20 rounded-full animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 3}s`,
            }}
          />
        ))}
      </div>

      {/* Navigation */}
      <nav className="relative z-10 border-b border-slate-700/50 bg-slate-800/30 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="relative animate-float">
                <Wallet className="h-8 w-8 text-emerald-400 drop-shadow-neon" />
                <div className="absolute inset-0 animate-ping">
                  <Wallet className="h-8 w-8 text-emerald-400/30" />
                </div>
              </div>
              <span className="text-2xl font-bold bg-gradient-to-r from-emerald-400 via-blue-400 to-purple-400 bg-clip-text text-transparent animate-gradient">
                investry
              </span>
            </div>

            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={() => router.push("/auth/signin")}
                className="text-slate-300 hover:text-white hover:bg-slate-700/50"
              >
                Sign In
              </Button>
              <Button
                onClick={() => router.push("/auth/signup")}
                className="bg-gradient-to-r from-emerald-500 to-blue-500 hover:from-emerald-600 hover:to-blue-600 text-white shadow-neon hover:shadow-neon-lg transition-all duration-300"
              >
                Get Started
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative z-10 container mx-auto px-4 py-20">
        <div className="text-center space-y-8 max-w-4xl mx-auto">
          <div className="space-y-4">
            <Badge className="bg-emerald-500/20 text-emerald-400 border-emerald-400/30 hover:bg-emerald-500/30 transition-colors">
              <Sparkles className="w-3 h-3 mr-1" />
              AI-Powered Investing
            </Badge>

            <h1 className="text-5xl md:text-7xl font-bold leading-tight">
              <span className="bg-gradient-to-r from-white via-emerald-400 to-blue-400 bg-clip-text text-transparent animate-gradient">
                Smart Investing
              </span>
              <br />
              <span className="text-white">Made Simple</span>
            </h1>

            <p className="text-xl text-slate-300 max-w-2xl mx-auto leading-relaxed">
              Start your investment journey with as little as $10. Learn, invest, and grow your wealth with AI-powered
              recommendations tailored to your interests.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              size="lg"
              onClick={() => router.push("/auth/signup")}
              className="bg-gradient-to-r from-emerald-500 to-blue-500 hover:from-emerald-600 hover:to-blue-600 text-white px-8 py-4 text-lg shadow-neon hover:shadow-neon-lg hover:scale-105 transition-all duration-300"
            >
              <Zap className="w-5 h-5 mr-2" />
              Start Investing Now
            </Button>

            <Button
              variant="outline"
              size="lg"
              className="border-slate-600 text-slate-300 hover:bg-slate-700/50 px-8 py-4 text-lg hover:border-emerald-400/50 transition-all duration-300 bg-transparent"
            >
              <Play className="w-5 h-5 mr-2" />
              Watch Demo
            </Button>
          </div>

          {/* Floating wallet animation */}
          <div className="relative mt-16">
            <div className="animate-float-slow">
              <div className="relative mx-auto w-32 h-32 bg-gradient-to-r from-emerald-500/20 to-blue-500/20 rounded-full flex items-center justify-center border border-emerald-400/30 shadow-neon">
                <Wallet className="w-16 h-16 text-emerald-400 drop-shadow-neon" />
                <div className="absolute inset-0 rounded-full border-2 border-emerald-400/20 animate-ping" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="relative z-10 container mx-auto px-4 py-20">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-white to-emerald-400 bg-clip-text text-transparent">
            Why Choose investry?
          </h2>
          <p className="text-slate-400 text-lg max-w-2xl mx-auto">
            We've built the perfect platform for investors with features designed to help you succeed.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map((feature, index) => (
            <Card
              key={index}
              className="bg-slate-800/50 border-slate-700/50 hover:border-emerald-400/50 transition-all duration-300 hover:shadow-neon hover:scale-105 group"
            >
              <CardContent className="p-6 text-center space-y-4">
                <div className="mx-auto w-12 h-12 bg-emerald-500/20 rounded-lg flex items-center justify-center group-hover:bg-emerald-500/30 transition-colors">
                  <feature.icon className="w-6 h-6 text-emerald-400" />
                </div>
                <h3 className="font-semibold text-white">{feature.title}</h3>
                <p className="text-slate-400 text-sm">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative z-10 container mx-auto px-4 py-20">
        <Card className="bg-gradient-to-r from-emerald-500/10 to-blue-500/10 border-emerald-400/30 shadow-neon">
          <CardContent className="p-12 text-center space-y-6">
            <h2 className="text-4xl font-bold bg-gradient-to-r from-white to-emerald-400 bg-clip-text text-transparent">
              Ready to Start Your Investment Journey?
            </h2>
            <p className="text-slate-300 text-lg max-w-2xl mx-auto">
              Join investry today and take the first step towards financial independence. No experience required – we'll
              guide you every step of the way.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                size="lg"
                onClick={() => router.push("/auth/signup")}
                className="bg-gradient-to-r from-emerald-500 to-blue-500 hover:from-emerald-600 hover:to-blue-600 text-white px-8 py-4 text-lg shadow-neon hover:shadow-neon-lg hover:scale-105 transition-all duration-300"
              >
                <CheckCircle className="w-5 h-5 mr-2" />
                Create Free Account
              </Button>

              <div className="flex items-center gap-2 text-slate-400">
                <CheckCircle className="w-4 h-4 text-emerald-400" />
                <span>No fees to start</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Footer */}
      <footer className="relative z-10 border-t border-slate-700/50 bg-slate-800/30 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center gap-2 mb-4 md:mb-0">
              <Wallet className="h-6 w-6 text-emerald-400" />
              <span className="font-bold text-emerald-400">investry</span>
            </div>

            <div className="flex items-center gap-6 text-slate-400">
              <Button variant="ghost" size="sm" className="hover:text-white">
                Privacy
              </Button>
              <Button variant="ghost" size="sm" className="hover:text-white">
                Terms
              </Button>
              <Button variant="ghost" size="sm" className="hover:text-white">
                Support
              </Button>
            </div>
          </div>

          <div className="mt-6 pt-6 border-t border-slate-700/50 text-center text-slate-500">
            <p>&copy; 2024 investry. Empowering investors of all backgrounds.</p>
          </div>
        </div>
      </footer>

      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-10px); }
        }
        
        @keyframes float-slow {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-20px); }
        }
        
        @keyframes gradient {
          0%, 100% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
        }
        
        .animate-float {
          animation: float 3s ease-in-out infinite;
        }
        
        .animate-float-slow {
          animation: float-slow 4s ease-in-out infinite;
        }
        
        .animate-gradient {
          background-size: 200% 200%;
          animation: gradient 3s ease infinite;
        }
        
        .drop-shadow-neon {
          filter: drop-shadow(0 0 10px rgba(34, 197, 94, 0.5));
        }
        
        .shadow-neon {
          box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
        }
        
        .shadow-neon-lg {
          box-shadow: 0 0 30px rgba(34, 197, 94, 0.4);
        }
        
        .shadow-neon-blue {
          box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
        }
      `}</style>
    </div>
  )
}
