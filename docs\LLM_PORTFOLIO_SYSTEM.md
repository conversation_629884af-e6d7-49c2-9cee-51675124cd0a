# LLM Portfolio Generation System

## Overview

The LLM Portfolio Generation System is a production-grade backend service that replaces the existing rule-based portfolio generation with AI-powered analysis. The system is designed for **internal use only** - end users never directly interact with the LLM. Instead, the site owner (developer) issues prompts to the LLM via a secure backend pipeline.

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    API Layer (Next.js)                     │
├─────────────────────────────────────────────────────────────┤
│  /api/portfolio/generate-llm  │  /api/portfolio/llm-status  │
│  /api/portfolio/user-stats    │  /api/portfolio/cache-mgmt  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                 Authentication Middleware                   │
│  • JWT Validation  • Rate Limiting  • API Key Validation   │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   Core Service Layer                        │
├─────────────────────────────────────────────────────────────┤
│  LLMPortfolioEngine    │  PromptTemplateBuilder             │
│  PortfolioCache        │  PortfolioFallback                 │
│  SupabaseLogger        │  LLMClient                         │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    External Services                        │
├─────────────────────────────────────────────────────────────┤
│  OpenAI API            │  Supabase Database                 │
│  In-Memory Cache       │  Existing Stock APIs               │
└─────────────────────────────────────────────────────────────┘
```

## Key Features

### 🤖 LLM Integration
- **OpenAI GPT-4** integration with structured JSON responses
- **Intelligent prompting** that converts survey data into comprehensive investment analysis
- **Response validation** to ensure portfolio quality and compliance

### 🚀 Performance & Reliability
- **Multi-layer caching** (memory + database) to avoid redundant API calls
- **Intelligent fallback** to rule-based system when LLM fails
- **Rate limiting** and error handling for production stability

### 🔒 Security & Compliance
- **JWT authentication** for all API endpoints
- **API key validation** for additional security layer
- **Data anonymization** - User IDs and PII removed from LLM prompts
- **Input sanitization** - Malicious inputs filtered and sanitized
- **Response filtering** - LLM responses checked for sensitive data leakage
- **Comprehensive audit logging** - All security events tracked and monitored
- **Row-level security** in Supabase for data isolation
- **Security monitoring** - Real-time threat detection and analysis

### 📊 Monitoring & Analytics
- **Real-time health checks** for all system components
- **Usage tracking** and cost monitoring for API calls
- **Performance metrics** and generation statistics
- **Cache hit rates** and optimization insights

## Installation & Setup

### 1. Database Setup

First, create the LLM system tables in Supabase:

```sql
-- Run this in Supabase SQL Editor
\i scripts/create-llm-tables-supabase.sql
```

### 2. Environment Configuration

Add these variables to your `.env.local`:

```env
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000
OPENAI_TEMPERATURE=0.7

# LLM System Configuration
LLM_CACHE_TTL_HOURS=24
LLM_MAX_RETRIES=3
LLM_RATE_LIMIT_PER_USER=10
LLM_ENABLE_FALLBACK=true

# Optional: API Keys for additional security
LLM_API_KEYS=your-internal-api-key-1,your-internal-api-key-2
```

### 3. Install Dependencies

```bash
npm install openai --legacy-peer-deps
```

### 4. Test the System

```bash
# Run the test script
node scripts/test-llm-system.js

# Or run the test suite
npm test lib/llm-portfolio/__tests__/
```

## API Endpoints

### POST /api/portfolio/generate-llm

Generate a personalized portfolio using LLM analysis.

**Authentication:** Bearer token required  
**Rate Limit:** 10 requests/hour per user

**Request Body:**
```json
{
  "surveyData": {
    "primaryGoal": "Long-term wealth building",
    "timeHorizon": "10+ years",
    "riskTolerance": 3,
    "experienceLevel": "Beginner",
    "interestedThemes": ["Technology", "Healthcare"],
    "monthlyInvestment": 1000,
    "major": "computer-science"
  },
  "userMajor": "computer-science",
  "additionalContext": "Optional context for the LLM"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "portfolio": {
      "allocations": [...],
      "riskLevel": "Moderate",
      "expectedReturn": "8-12%",
      "strategy": "...",
      "rebalanceFrequency": "Quarterly",
      "rationale": "..."
    },
    "metadata": {
      "source": "llm",
      "confidence": 0.9,
      "processingTimeMs": 2500,
      "cacheHit": false
    }
  },
  "rateLimitRemaining": 9
}
```

### GET /api/portfolio/llm-status

Get system health and status information.

**Authentication:** Optional  
**Rate Limit:** 60 requests/hour

**Response:**
```json
{
  "success": true,
  "data": {
    "health": {
      "status": "healthy",
      "checks": {
        "configuration": true,
        "database": true,
        "llmApi": true,
        "cache": true
      }
    },
    "statistics": {
      "totalUsers": 150,
      "totalGenerations": 1250,
      "cacheHitRate": 0.65,
      "avgProcessingTime": 2100
    }
  }
}
```

### GET /api/portfolio/user-stats

Get user-specific generation statistics.

**Authentication:** Bearer token required
**Rate Limit:** 30 requests/hour per user

### GET /api/portfolio/security-monitor

Monitor security events and threats in the LLM system.

**Authentication:** Bearer token required (Admin/Security roles only)
**Rate Limit:** 100 requests/hour

**Query Parameters:**
- `timeframe`: `1h`, `24h`, `7d`, `30d` (default: `24h`)
- `user_id`: Specific user to monitor (optional)

**Response:**
```json
{
  "success": true,
  "data": {
    "securitySummary": {
      "totalSecurityEvents": 15,
      "riskLevel": "medium",
      "threatCategories": {
        "inputSanitization": 8,
        "responseFiltering": 3,
        "promptInjection": 2,
        "suspiciousActivity": 2
      },
      "recentThreats": [...]
    },
    "recommendations": [
      "Review recent prompt injection attempts",
      "Enhanced monitoring recommended"
    ]
  }
}
```

### POST /api/portfolio/reweight

Reweight an existing portfolio using AI analysis based on user feedback.

**Authentication:** Bearer token required
**Rate Limit:** 5 requests/hour per user

**Request Body:**
```json
{
  "currentPortfolio": {
    "allocations": [...],
    "riskLevel": "Moderate",
    "expectedReturn": "8-12%",
    "strategy": "Current investment strategy",
    "rationale": "Current rationale"
  },
  "reweightReason": "I want to reduce risk and increase bond allocation to 50%",
  "surveyData": {
    "primaryGoal": "Long-term wealth building",
    "timeHorizon": "10+ years",
    "riskTolerance": 3,
    "experienceLevel": "Intermediate"
  },
  "userMajor": "computer-science"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "newPortfolio": {
      "allocations": [...],
      "riskLevel": "Conservative",
      "expectedReturn": "6-8%",
      "strategy": "Updated strategy addressing user concerns",
      "rationale": "Detailed explanation of changes made"
    },
    "originalPortfolio": {...},
    "reweightReason": "User's original request",
    "comparison": {
      "changes": [
        {
          "symbol": "BND",
          "name": "Vanguard Total Bond Market ETF",
          "oldAllocation": 30,
          "newAllocation": 50,
          "change": 20,
          "changeType": "increased"
        }
      ],
      "summary": {
        "holdingsAdded": 1,
        "holdingsRemoved": 0,
        "holdingsModified": 3,
        "riskLevelChanged": true,
        "expectedReturnChanged": true
      }
    },
    "metadata": {
      "source": "llm",
      "confidence": 0.9,
      "processingTimeMs": 3200,
      "type": "reweighting"
    }
  }
}
```

## Usage Examples

### Basic Portfolio Generation

```typescript
import { generateEnhancedPortfolio } from '@/lib/enhanced-portfolio-generator'

const result = await generateEnhancedPortfolio(
  surveyData,
  userId,
  userMajor,
  {
    useLLM: true,
    fallbackToRules: true,
    additionalContext: 'User is a first-time investor'
  }
)

console.log('Generated portfolio:', result.portfolio)
console.log('Source:', result.source) // 'llm', 'cache', or 'rules'
console.log('Confidence:', result.confidence)
```

### System Health Check

```typescript
import { checkLLMSystemHealth } from '@/lib/llm-portfolio'

const health = await checkLLMSystemHealth()
console.log('System status:', health.status)
console.log('Component checks:', health.checks)
```

### Manual Cache Management

```typescript
import { getLLMPortfolioEngine } from '@/lib/llm-portfolio'

const engine = getLLMPortfolioEngine()
const stats = await engine.getGenerationStats(userId)
console.log('User stats:', stats)
```

## Configuration Options

### LLM Configuration
- **Model Selection:** Choose between GPT-4, GPT-4-turbo, or GPT-3.5-turbo
- **Token Limits:** Control response length and cost
- **Temperature:** Adjust creativity vs consistency (0.0-2.0)
- **Retry Logic:** Configure retry attempts and backoff

### Cache Configuration
- **TTL:** How long to cache results (1-168 hours)
- **Size Limits:** Maximum cache entries per user
- **Cleanup Intervals:** Automatic cache maintenance

### Security Configuration
- **Rate Limiting:** Requests per hour per user
- **API Keys:** Additional authentication layer
- **Audit Logging:** Comprehensive activity tracking

## Monitoring & Maintenance

### Health Monitoring

The system provides comprehensive health checks:

```bash
# Check system status
curl -X GET http://localhost:3000/api/portfolio/llm-status

# Check user statistics
curl -X GET http://localhost:3000/api/portfolio/user-stats \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Performance Metrics

Key metrics to monitor:
- **Response Time:** Target < 3 seconds for LLM generation
- **Cache Hit Rate:** Target > 60% for optimal performance
- **Error Rate:** Target < 1% for production stability
- **API Costs:** Monitor token usage and costs

### Maintenance Tasks

Regular maintenance includes:
- **Cache Cleanup:** Automatic expired entry removal
- **Log Rotation:** Archive old audit logs (30+ days)
- **Usage Analysis:** Review patterns and optimize prompts
- **Cost Monitoring:** Track API usage and optimize

## Troubleshooting

### Common Issues

**1. LLM Generation Fails**
- Check OpenAI API key validity
- Verify rate limits not exceeded
- Review error logs for specific issues
- Ensure fallback system is enabled

**2. High Response Times**
- Check cache hit rates
- Monitor API response times
- Review prompt complexity
- Consider model optimization

**3. Cache Issues**
- Verify Supabase connection
- Check cache TTL settings
- Monitor memory usage
- Review cleanup intervals

**4. Authentication Errors**
- Validate JWT tokens
- Check API key configuration
- Review rate limiting settings
- Verify user permissions

### Debug Mode

Enable debug logging:

```env
NODE_ENV=development
DEBUG=llm-portfolio:*
```

### Support

For technical support:
1. Check the health status endpoint
2. Review audit logs in Supabase
3. Run the test script for diagnostics
4. Check environment configuration

## Security Considerations

### Data Protection
- All user data encrypted in transit and at rest
- PII handling compliant with privacy regulations
- Audit trails for all data access

### API Security
- Rate limiting prevents abuse
- JWT validation ensures authorized access
- API keys provide additional security layer
- CORS configuration restricts origins

### LLM Security
- Prompts sanitized to prevent injection
- Responses validated before storage
- No sensitive data sent to external APIs
- Fallback prevents service disruption

## Cost Management

### API Cost Optimization
- Intelligent caching reduces API calls
- Prompt optimization minimizes token usage
- Model selection balances cost vs quality
- Usage monitoring prevents overruns

### Estimated Costs (OpenAI GPT-4)
- **Per Portfolio Generation:** ~$0.05-0.15
- **Monthly (100 users, 5 portfolios each):** ~$25-75
- **Cache Hit Rate Impact:** 60% hit rate = 40% cost reduction

## Future Enhancements

### Planned Features
- **Multi-LLM Support:** Anthropic Claude, Google Gemini
- **Advanced Caching:** Redis integration for distributed caching
- **A/B Testing:** Compare LLM vs rule-based results
- **Custom Prompts:** User-specific prompt templates
- **Real-time Updates:** WebSocket-based portfolio updates

### Scalability Improvements
- **Horizontal Scaling:** Multiple LLM service instances
- **Load Balancing:** Distribute requests across providers
- **Edge Caching:** CDN-based response caching
- **Async Processing:** Queue-based generation for high volume

## Deployment Guide

### Production Deployment

1. **Environment Setup**
   ```bash
   # Set production environment variables
   OPENAI_API_KEY=your_production_api_key
   NODE_ENV=production
   LLM_ENABLE_FALLBACK=true
   LLM_RATE_LIMIT_PER_USER=20
   ```

2. **Database Migration**
   ```sql
   -- Run in production Supabase
   \i scripts/create-llm-tables-supabase.sql
   ```

3. **Security Configuration**
   ```env
   # Add production API keys
   LLM_API_KEYS=prod-key-1,prod-key-2
   ALLOWED_ORIGINS=https://yourdomain.com
   ```

4. **Monitoring Setup**
   - Configure health check endpoints
   - Set up alerting for system failures
   - Monitor API usage and costs

### Vercel Deployment

The system is fully compatible with Vercel deployment:

```bash
# Deploy to Vercel
vercel --prod

# Set environment variables in Vercel dashboard
# or use Vercel CLI
vercel env add OPENAI_API_KEY
```

### Docker Deployment (Optional)

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```
