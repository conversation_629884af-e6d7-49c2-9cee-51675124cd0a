# Plaid Integration Setup Guide

## Overview

Complete implementation of Plaid integration for secure bank account linking and automatic round-up investing in Investry.

## What's Been Implemented

### ✅ 1. Dependencies Added
- `plaid`: ^11.0.0 (Node.js Plaid API client)
- `react-plaid-link`: ^3.5.2 (React component for Plaid Link)

### ✅ 2. Database Schema
**File**: `database/bank-account-tables.sql`

**Tables Created**:
- `linked_accounts`: Store connected bank accounts
- `transactions`: Track user transactions for round-ups
- `roundups`: Individual round-up records
- `ach_transfers`: ACH transfer tracking
- `roundup_settings`: User preferences for round-ups

**Key Features**:
- Row Level Security (RLS) enabled
- Proper indexes for performance
- Helper functions for calculations
- Triggers for timestamp updates

### ✅ 3. API Endpoints

#### Plaid Integration APIs:
- `POST /api/plaid/create-link-token`: Initialize Plaid Link
- `POST /api/plaid/exchange-token`: Exchange public token for access token
- `POST /api/plaid/webhook`: Handle Plaid webhooks for transactions

#### Data APIs:
- `GET /api/linked-accounts`: Fetch user's linked accounts
- `GET /api/roundup-history`: Get roundup transaction history

### ✅ 4. Frontend Components
**File**: `components/plaid-link.tsx`

**Features**:
- Secure Plaid Link integration
- Error handling and loading states
- Success/error callbacks
- Customizable button or children
- Error boundary for graceful failures

### ✅ 5. Updated Payments Page
**File**: `app/payments/page.tsx`

**New Features**:
- Bank account linking interface
- Round-up explanation and benefits
- Connected accounts display
- Security information
- Transaction history integration

## Setup Instructions

### Step 1: Get Plaid Credentials

1. **Sign up for Plaid**: Go to [dashboard.plaid.com](https://dashboard.plaid.com)
2. **Create an application** for Investry
3. **Get your credentials**:
   - Client ID
   - Sandbox Secret (for development)
   - Production Secret (for live)

### Step 2: Update Environment Variables

Update your `.env.local` file with actual Plaid credentials:

```env
# Plaid Configuration
PLAID_CLIENT_ID=your_actual_client_id_here
PLAID_SECRET=your_actual_sandbox_secret_here
PLAID_ENV=sandbox
NEXT_PUBLIC_PLAID_ENV=sandbox
```

**For Production**:
```env
PLAID_ENV=production
NEXT_PUBLIC_PLAID_ENV=production
PLAID_SECRET=your_production_secret_here
```

### Step 3: Set up Database

1. **Run the SQL script** in your Supabase SQL editor:
   ```sql
   -- Execute database/bank-account-tables.sql
   ```

2. **Verify tables created**:
   - linked_accounts
   - transactions
   - roundups
   - ach_transfers
   - roundup_settings

### Step 4: Install Dependencies

```bash
npm install plaid react-plaid-link
```

### Step 5: Configure Webhooks

1. **In Plaid Dashboard**:
   - Go to Webhooks section
   - Add webhook URL: `https://yourdomain.com/api/plaid/webhook`
   - Select webhook types: `TRANSACTIONS`, `ITEM`

2. **For local development**:
   - Use ngrok or similar to expose localhost
   - Update webhook URL in Plaid dashboard

## How It Works

### 1. Account Linking Flow

```
User clicks "Connect Bank Account"
    ↓
Plaid Link opens in modal
    ↓
User authenticates with bank
    ↓
Plaid returns public token
    ↓
Exchange for access token
    ↓
Store account info in database
    ↓
Enable round-ups automatically
```

### 2. Round-up Processing

```
Plaid webhook receives transaction
    ↓
Calculate round-up amount
    ↓
Store transaction and roundup
    ↓
Check if batch threshold reached
    ↓
Initiate ACH transfer
    ↓
Update user balance
    ↓
Mark roundups as processed
```

### 3. Security Features

- **No credential storage**: Plaid handles authentication
- **Encrypted access tokens**: Should be encrypted in production
- **Row Level Security**: Users only see their own data
- **Webhook verification**: Validates Plaid webhook signatures
- **Read-only access**: Only transaction data, no account control

## Testing

### Sandbox Testing

Plaid provides test credentials for sandbox:

**Test Bank**: 
- Institution: First Platypus Bank
- Username: `user_good`
- Password: `pass_good`

**Test Scenarios**:
1. **Successful linking**: Use good credentials
2. **Invalid credentials**: Use `user_bad` / `pass_bad`
3. **MFA required**: Use `user_mfa` / `pass_mfa`

### Transaction Testing

In sandbox mode, Plaid provides:
- Historical transactions (last 2 years)
- Recurring transactions
- Various merchant categories
- Different transaction amounts

## Production Considerations

### 1. Security Enhancements

```typescript
// Encrypt access tokens before storing
const encryptedToken = encrypt(accessToken, process.env.ENCRYPTION_KEY)

// Store encrypted token
await supabase.from('linked_accounts').insert({
  plaid_access_token: encryptedToken,
  // ... other fields
})
```

### 2. Error Handling

- **Item errors**: Handle expired credentials
- **Rate limiting**: Implement proper retry logic
- **Webhook failures**: Queue and retry failed webhooks
- **User notifications**: Alert users of connection issues

### 3. Compliance

- **Data retention**: Implement data deletion policies
- **User consent**: Clear terms for data usage
- **Audit logging**: Track all financial operations
- **Encryption**: Encrypt sensitive data at rest

## ACH Processing Integration

The current implementation creates ACH transfer records but doesn't process them. To complete the system:

### Option 1: Stripe Connect
```typescript
// In ach transfer processing
const transfer = await stripe.transfers.create({
  amount: Math.round(amount * 100), // Convert to cents
  currency: 'usd',
  destination: userStripeAccount,
})
```

### Option 2: Dwolla
```typescript
// Using Dwolla for ACH
const transfer = await dwolla.post('transfers', {
  _links: {
    source: { href: bankAccountUrl },
    destination: { href: investryAccountUrl }
  },
  amount: { value: amount.toString(), currency: 'USD' }
})
```

## Monitoring and Analytics

### Key Metrics to Track

1. **Conversion Rates**:
   - Link completion rate
   - Round-up opt-in rate
   - Average round-up amount

2. **System Health**:
   - Webhook processing time
   - Failed transaction rate
   - ACH success rate

3. **User Engagement**:
   - Active linked accounts
   - Monthly round-up volume
   - User retention

### Recommended Monitoring

```typescript
// Add to webhook handler
console.log('Metrics:', {
  userId,
  transactionCount: transactions.length,
  totalRoundups: roundupAmount,
  processingTime: Date.now() - startTime
})
```

## Next Steps

1. **Get Plaid credentials** and update environment variables
2. **Run database migrations** to create tables
3. **Test in sandbox** with provided test accounts
4. **Set up webhooks** for transaction processing
5. **Implement ACH processing** with chosen provider
6. **Add monitoring** and error tracking
7. **Plan production deployment** with security enhancements

This implementation provides a solid foundation for automatic round-up investing with proper security, error handling, and scalability considerations.
