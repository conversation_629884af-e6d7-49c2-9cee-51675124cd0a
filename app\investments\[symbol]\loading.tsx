export default function StockDetailLoading() {
  return (
    <div className="container mx-auto px-4 py-6 space-y-6">
      {/* Header Skeleton */}
      <div className="flex items-center justify-between">
        <div className="h-10 w-32 bg-slate-700 rounded animate-pulse"></div>
        <div className="flex gap-2">
          <div className="h-10 w-20 bg-slate-700 rounded animate-pulse"></div>
          <div className="h-10 w-20 bg-slate-700 rounded animate-pulse"></div>
        </div>
      </div>

      {/* Stock Overview Skeleton */}
      <div className="bg-slate-800/50 border border-slate-700 rounded-lg p-6 animate-pulse">
        <div className="flex items-start justify-between mb-6">
          <div className="space-y-2">
            <div className="h-8 w-20 bg-slate-700 rounded"></div>
            <div className="h-6 w-48 bg-slate-700 rounded"></div>
          </div>
          <div className="text-right space-y-2">
            <div className="h-10 w-32 bg-slate-700 rounded"></div>
            <div className="h-5 w-28 bg-slate-700 rounded"></div>
          </div>
        </div>

        {/* Key Metrics Skeleton */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="flex items-center gap-2">
              <div className="h-4 w-4 bg-slate-700 rounded"></div>
              <div className="space-y-1">
                <div className="h-3 w-16 bg-slate-700 rounded"></div>
                <div className="h-4 w-20 bg-slate-700 rounded"></div>
              </div>
            </div>
          ))}
        </div>

        {/* Tags Skeleton */}
        <div className="mb-6">
          <div className="h-4 w-12 bg-slate-700 rounded mb-2"></div>
          <div className="flex flex-wrap gap-2">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="h-6 w-16 bg-slate-700 rounded"></div>
            ))}
          </div>
        </div>

        {/* Description Skeleton */}
        <div>
          <div className="h-4 w-16 bg-slate-700 rounded mb-2"></div>
          <div className="space-y-2">
            <div className="h-3 w-full bg-slate-700 rounded"></div>
            <div className="h-3 w-3/4 bg-slate-700 rounded"></div>
            <div className="h-3 w-5/6 bg-slate-700 rounded"></div>
          </div>
        </div>
      </div>

      {/* Tabs Skeleton */}
      <div className="space-y-4">
        <div className="flex space-x-1 bg-slate-800/50 p-1 rounded-lg">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="h-10 w-24 bg-slate-700 rounded animate-pulse"></div>
          ))}
        </div>
        <div className="bg-slate-800/50 border border-slate-700 rounded-lg p-6">
          <div className="h-64 bg-slate-700 rounded animate-pulse"></div>
        </div>
      </div>
    </div>
  )
}
