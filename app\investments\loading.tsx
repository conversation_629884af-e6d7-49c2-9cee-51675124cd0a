export default function InvestmentsLoading() {
  return (
    <div className="min-h-screen bg-slate-900 text-white">
      {/* Header */}
      <div className="border-b border-slate-700 bg-slate-800/50">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="h-5 w-5 bg-slate-700 rounded animate-pulse"></div>
              <div>
                <div className="h-8 w-32 bg-slate-700 rounded animate-pulse mb-2"></div>
                <div className="h-4 w-48 bg-slate-700 rounded animate-pulse"></div>
              </div>
            </div>
            <div className="h-10 w-32 bg-slate-700 rounded animate-pulse"></div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8 space-y-8">
        {/* Portfolio Summary Skeleton */}
        <div className="bg-slate-800/50 border border-slate-700 rounded-lg p-6 animate-pulse">
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <div className="h-4 w-32 bg-slate-700 rounded"></div>
              <div className="h-10 w-48 bg-slate-700 rounded"></div>
              <div className="h-4 w-40 bg-slate-700 rounded"></div>
            </div>
            <div className="text-right space-y-2">
              <div className="h-4 w-24 bg-slate-700 rounded"></div>
              <div className="h-4 w-28 bg-slate-700 rounded"></div>
            </div>
          </div>
        </div>

        {/* Holdings Skeleton */}
        <div className="space-y-6">
          <div className="h-8 w-32 bg-slate-700 rounded animate-pulse"></div>
          <div className="grid gap-4">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="bg-slate-800/50 border border-slate-700 rounded-lg p-6 animate-pulse">
                <div className="flex items-center justify-between mb-4">
                  <div className="space-y-2">
                    <div className="h-5 w-16 bg-slate-700 rounded"></div>
                    <div className="h-4 w-32 bg-slate-700 rounded"></div>
                    <div className="h-3 w-24 bg-slate-700 rounded"></div>
                  </div>
                  <div className="space-y-2">
                    <div className="h-5 w-20 bg-slate-700 rounded"></div>
                    <div className="h-4 w-16 bg-slate-700 rounded"></div>
                  </div>
                </div>
                <div className="flex justify-between text-sm mb-3">
                  <div className="h-3 w-20 bg-slate-700 rounded"></div>
                  <div className="h-3 w-16 bg-slate-700 rounded"></div>
                </div>
                <div className="space-y-2">
                  <div className="w-full bg-slate-700 rounded-full h-2"></div>
                  <div className="h-3 w-24 bg-slate-700 rounded"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
