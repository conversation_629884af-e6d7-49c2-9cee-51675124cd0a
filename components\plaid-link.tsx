"use client"

import { use<PERSON><PERSON>back, useEffect, useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Building2, AlertCircle } from "lucide-react"
import { useNotifications } from "@/components/notification-system"

// Temporary mock for usePlaidLink until package is installed
const usePlaidLink = ({ token, onSuccess, onError, onExit }: any) => {
  const [ready, setReady] = useState(false)

  useEffect(() => {
    if (token) {
      setReady(true)
    }
  }, [token])

  const open = () => {
    // Simulate Plaid Link for demo purposes
    setTimeout(() => {
      const mockPublicToken = 'public-sandbox-' + Math.random().toString(36).substr(2, 9)
      const mockMetadata = {
        institution: { name: 'Demo Bank', institution_id: 'demo_bank' },
        accounts: [{ id: 'demo_account', name: 'Demo Checking' }]
      }
      onSuccess(mockPublicToken, mockMetadata)
    }, 2000)
  }

  return { open, ready }
}

interface PlaidLinkProps {
  userId: string
  onSuccess: (accounts: any[]) => void
  onError?: (error: any) => void
  disabled?: boolean
  children?: React.ReactNode
}

export function PlaidLink({ 
  userId, 
  onSuccess, 
  onError, 
  disabled = false,
  children 
}: PlaidLinkProps) {
  const { addNotification } = useNotifications()
  const [linkToken, setLinkToken] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)

  // Create link token when component mounts
  useEffect(() => {
    const createLinkToken = async () => {
      try {
        const response = await fetch('/api/plaid/create-link-token', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ userId }),
        })

        if (!response.ok) {
          throw new Error('Failed to create link token')
        }

        const data = await response.json()
        setLinkToken(data.link_token)
      } catch (error) {
        console.error('Error creating link token:', error)
        addNotification({
          type: "error",
          title: "Connection Error",
          message: "Unable to initialize bank connection. Please try again.",
        })
      }
    }

    if (userId) {
      createLinkToken()
    }
  }, [userId, addNotification])

  const onPlaidSuccess = useCallback(async (publicToken: string, metadata: any) => {
    try {
      setLoading(true)
      
      // Exchange public token for access token
      const response = await fetch('/api/plaid/exchange-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          publicToken,
          userId,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to link account')
      }

      const data = await response.json()
      
      if (data.success) {
        addNotification({
          type: "success",
          title: "Account Connected!",
          message: `Successfully linked your ${data.institution.name} account. Round-ups are now enabled.`,
        })
        
        onSuccess(data.accounts)
      } else {
        throw new Error(data.error || 'Failed to link account')
      }
      
    } catch (error) {
      console.error('Error linking account:', error)
      addNotification({
        type: "error",
        title: "Connection Failed",
        message: "Failed to link your bank account. Please try again.",
      })
      
      if (onError) {
        onError(error)
      }
    } finally {
      setLoading(false)
    }
  }, [userId, onSuccess, onError, addNotification])

  const onPlaidError = useCallback((error: any) => {
    console.error('Plaid Link error:', error)
    
    // Don't show error notification for user-initiated exits
    if (error.error_code !== 'USER_EXIT') {
      addNotification({
        type: "error",
        title: "Connection Error",
        message: "There was an issue connecting to your bank. Please try again.",
      })
    }
    
    if (onError) {
      onError(error)
    }
  }, [onError, addNotification])

  const onPlaidExit = useCallback((error: any, metadata: any) => {
    console.log('Plaid Link exit:', { error, metadata })
    // User exited the flow - no action needed
  }, [])

  const { open, ready } = usePlaidLink({
    token: linkToken,
    onSuccess: onPlaidSuccess,
    onError: onPlaidError,
    onExit: onPlaidExit,
  })

  const handleClick = () => {
    if (ready && !loading && !disabled) {
      open()
    }
  }

  if (!linkToken) {
    return (
      <Button
        disabled
        className="bg-slate-600 text-slate-400 cursor-not-allowed"
      >
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-slate-400 mr-2"></div>
        Initializing...
      </Button>
    )
  }

  if (children) {
    // Custom children - clone and add click handler
    return (
      <div onClick={handleClick} className="cursor-pointer">
        {children}
      </div>
    )
  }

  // Default button
  return (
    <Button
      onClick={handleClick}
      disabled={!ready || loading || disabled}
      className="bg-emerald-600 hover:bg-emerald-700 disabled:bg-slate-600 disabled:text-slate-400"
    >
      {loading ? (
        <>
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
          Connecting...
        </>
      ) : (
        <>
          <Building2 className="h-4 w-4 mr-2" />
          Connect Bank Account
        </>
      )}
    </Button>
  )
}

// Error boundary component for Plaid Link
export function PlaidLinkErrorBoundary({ children }: { children: React.ReactNode }) {
  const [hasError, setHasError] = useState(false)

  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      if (event.error?.message?.includes('Plaid')) {
        setHasError(true)
      }
    }

    window.addEventListener('error', handleError)
    return () => window.removeEventListener('error', handleError)
  }, [])

  if (hasError) {
    return (
      <div className="flex items-center gap-2 p-4 bg-red-500/10 border border-red-500/30 rounded-lg">
        <AlertCircle className="h-5 w-5 text-red-400" />
        <div>
          <p className="text-red-400 font-medium">Bank Connection Unavailable</p>
          <p className="text-sm text-red-300">
            Please refresh the page and try again. If the issue persists, contact support.
          </p>
        </div>
      </div>
    )
  }

  return <>{children}</>
}
