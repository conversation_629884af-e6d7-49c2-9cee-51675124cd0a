/**
 * Stock History Service
 * Comprehensive service for fetching historical stock data with multi-provider support and caching
 */

import { getCacheManager } from './cache-manager'
import { 
  Timeframe, 
  HistoricalDataPoint, 
  StockHistoryResponse, 
  HistoryApiResponse,
  StockHistoryError,
  HistoryServiceConfig,
  DEFAULT_HISTORY_CONFIG,
  TIMEFRAME_CACHE_CONFIG,
  API_PROVIDERS
} from './types/stock-history'

export class StockHistoryService {
  private cacheManager = getCacheManager()
  private config: HistoryServiceConfig
  private apiUsage = new Map<string, { count: number, resetTime: number }>()

  constructor(config: Partial<HistoryServiceConfig> = {}) {
    this.config = { ...DEFAULT_HISTORY_CONFIG, ...config }
  }

  /**
   * Get historical stock data with intelligent fallback and caching
   */
  async getHistoricalData(
    symbol: string, 
    timeframe: Timeframe = '1M'
  ): Promise<StockHistoryResponse> {
    const cacheKey = this.getCacheKey(symbol, timeframe)
    
    // Try cache first
    if (this.config.enableCaching) {
      const cached = this.cacheManager.get<StockHistoryResponse>(cacheKey)
      if (cached) {
        return { ...cached, cached: true }
      }
    }

    // Get available providers sorted by priority
    const providers = this.getAvailableProviders(timeframe)
    let lastError: StockHistoryError | null = null

    for (const provider of providers) {
      try {
        // Check rate limits
        if (!this.canUseProvider(provider)) {
          continue
        }

        const response = await this.fetchFromProvider(symbol, timeframe, provider)
        
        if (response.success && response.data) {
          const result: StockHistoryResponse = {
            symbol: symbol.toUpperCase(),
            timeframe,
            data: response.data,
            source: provider,
            lastUpdated: new Date().toISOString(),
            cached: false,
            dataPoints: response.data.length
          }

          // Cache the result
          if (this.config.enableCaching) {
            const cacheConfig = TIMEFRAME_CACHE_CONFIG[timeframe]
            this.cacheManager.set(cacheKey, result, cacheConfig.ttl, [`stock_history`, symbol])
          }

          // Update rate limit tracking
          this.updateRateLimit(provider)

          return result
        }
      } catch (error) {
        lastError = error instanceof StockHistoryError 
          ? error 
          : new StockHistoryError(`Provider ${provider} failed: ${error}`, 'PROVIDER_ERROR', provider)
        
        console.warn(`Stock history provider ${provider} failed for ${symbol}:`, lastError.message)
        
        // If this provider is retryable and we haven't exceeded max retries, continue
        if (!lastError.retryable || !this.config.enableFallback) {
          break
        }
      }
    }

    // All providers failed
    throw lastError || new StockHistoryError(
      `No providers available for ${symbol} ${timeframe}`,
      'NO_PROVIDERS_AVAILABLE'
    )
  }

  /**
   * Get multiple timeframes for a symbol (batch request)
   */
  async getMultipleTimeframes(
    symbol: string, 
    timeframes: Timeframe[]
  ): Promise<Record<Timeframe, StockHistoryResponse>> {
    const results: Record<string, StockHistoryResponse> = {}
    
    // Execute requests in parallel
    const promises = timeframes.map(async (timeframe) => {
      try {
        const data = await this.getHistoricalData(symbol, timeframe)
        return { timeframe, data }
      } catch (error) {
        console.error(`Failed to fetch ${timeframe} data for ${symbol}:`, error)
        return { timeframe, data: null }
      }
    })

    const responses = await Promise.allSettled(promises)
    
    responses.forEach((response) => {
      if (response.status === 'fulfilled' && response.value.data) {
        results[response.value.timeframe] = response.value.data
      }
    })

    return results as Record<Timeframe, StockHistoryResponse>
  }

  /**
   * Invalidate cache for a symbol
   */
  invalidateCache(symbol: string, timeframe?: Timeframe): void {
    if (timeframe) {
      const cacheKey = this.getCacheKey(symbol, timeframe)
      this.cacheManager.delete(cacheKey)
    } else {
      // Invalidate all timeframes for this symbol
      this.cacheManager.deleteByTags([symbol])
    }
  }

  /**
   * Get cache statistics for monitoring
   */
  getCacheStats(): any {
    return this.cacheManager.getStats()
  }

  /**
   * Get API usage statistics
   */
  getApiUsage(): Record<string, any> {
    const usage: Record<string, any> = {}
    
    for (const [provider, stats] of this.apiUsage.entries()) {
      const providerConfig = API_PROVIDERS[provider]
      usage[provider] = {
        requests: stats.count,
        limit: providerConfig?.rateLimit.requests || 0,
        resetTime: stats.resetTime,
        available: this.canUseProvider(provider)
      }
    }

    return usage
  }

  // Private helper methods
  private getCacheKey(symbol: string, timeframe: Timeframe): string {
    return `${this.config.cachePrefix}:${symbol.toUpperCase()}:${timeframe}`
  }

  private getAvailableProviders(timeframe: Timeframe): string[] {
    return Object.entries(API_PROVIDERS)
      .filter(([_, config]) => config.supports.timeframes.includes(timeframe))
      .sort((a, b) => a[1].priority - b[1].priority)
      .map(([name]) => name)
  }

  private canUseProvider(provider: string): boolean {
    const providerConfig = API_PROVIDERS[provider]
    if (!providerConfig) return false

    const usage = this.apiUsage.get(provider)
    if (!usage) return true

    const now = Date.now()
    
    // Reset counter if period has passed
    if (now >= usage.resetTime) {
      this.apiUsage.set(provider, { count: 0, resetTime: now + providerConfig.rateLimit.period })
      return true
    }

    return usage.count < providerConfig.rateLimit.requests
  }

  private updateRateLimit(provider: string): void {
    const providerConfig = API_PROVIDERS[provider]
    if (!providerConfig) return

    const usage = this.apiUsage.get(provider) || { 
      count: 0, 
      resetTime: Date.now() + providerConfig.rateLimit.period 
    }
    
    usage.count++
    this.apiUsage.set(provider, usage)
  }

  private async fetchFromProvider(
    symbol: string, 
    timeframe: Timeframe, 
    provider: string
  ): Promise<HistoryApiResponse> {
    switch (provider) {
      case 'polygon':
        return this.fetchPolygonHistory(symbol, timeframe)
      case 'twelvedata':
        return this.fetchTwelveDataHistory(symbol, timeframe)
      case 'alphavantage':
        return this.fetchAlphaVantageHistory(symbol, timeframe)
      case 'finnhub':
        return this.fetchFinnhubHistory(symbol, timeframe)
      default:
        throw new StockHistoryError(`Unknown provider: ${provider}`, 'UNKNOWN_PROVIDER')
    }
  }

  // Provider-specific methods
  private async fetchPolygonHistory(symbol: string, timeframe: Timeframe): Promise<HistoryApiResponse> {
    const apiKey = process.env.POLYGON_IO_API_KEY
    if (!apiKey) {
      throw new StockHistoryError('Polygon API key not configured', 'API_KEY_MISSING', 'polygon')
    }

    try {
      const { from, to, multiplier, timespan } = this.getPolygonTimeParams(timeframe)
      const url = `https://api.polygon.io/v2/aggs/ticker/${symbol}/range/${multiplier}/${timespan}/${from}/${to}?adjusted=true&sort=asc&apikey=${apiKey}`

      const response = await fetch(url)
      const data = await response.json()

      if (!response.ok || data.status !== 'OK') {
        throw new StockHistoryError(
          data.error || `Polygon API error: ${response.status}`,
          'API_ERROR',
          'polygon',
          response.status >= 500
        )
      }

      if (!data.results || data.results.length === 0) {
        return { success: false, error: 'No data available', source: 'polygon' }
      }

      const historicalData: HistoricalDataPoint[] = data.results.map((item: any) => ({
        timestamp: item.t,
        close: item.c,
        open: item.o,
        high: item.h,
        low: item.l,
        volume: item.v
      }))

      return { success: true, data: historicalData, source: 'polygon' }
    } catch (error) {
      if (error instanceof StockHistoryError) throw error
      throw new StockHistoryError(`Polygon fetch failed: ${error}`, 'FETCH_ERROR', 'polygon', true)
    }
  }

  private async fetchTwelveDataHistory(symbol: string, timeframe: Timeframe): Promise<HistoryApiResponse> {
    const apiKey = process.env.TWELVE_DATA_API_KEY
    if (!apiKey) {
      throw new StockHistoryError('Twelve Data API key not configured', 'API_KEY_MISSING', 'twelvedata')
    }

    try {
      const { interval, outputsize } = this.getTwelveDataParams(timeframe)
      const url = `https://api.twelvedata.com/time_series?symbol=${symbol}&interval=${interval}&outputsize=${outputsize}&apikey=${apiKey}`

      const response = await fetch(url)
      const data = await response.json()

      if (!response.ok || data.status === 'error') {
        throw new StockHistoryError(
          data.message || `Twelve Data API error: ${response.status}`,
          'API_ERROR',
          'twelvedata',
          response.status >= 500
        )
      }

      if (!data.values || data.values.length === 0) {
        return { success: false, error: 'No data available', source: 'twelvedata' }
      }

      const historicalData: HistoricalDataPoint[] = data.values.map((item: any) => ({
        timestamp: new Date(item.datetime).getTime(),
        close: parseFloat(item.close),
        open: parseFloat(item.open),
        high: parseFloat(item.high),
        low: parseFloat(item.low),
        volume: parseInt(item.volume) || 0
      })).reverse() // Twelve Data returns newest first, we want oldest first

      return { success: true, data: historicalData, source: 'twelvedata' }
    } catch (error) {
      if (error instanceof StockHistoryError) throw error
      throw new StockHistoryError(`Twelve Data fetch failed: ${error}`, 'FETCH_ERROR', 'twelvedata', true)
    }
  }

  private async fetchAlphaVantageHistory(symbol: string, timeframe: Timeframe): Promise<HistoryApiResponse> {
    const apiKey = process.env.ALPHA_VANTAGE_API_KEY
    if (!apiKey) {
      throw new StockHistoryError('Alpha Vantage API key not configured', 'API_KEY_MISSING', 'alphavantage')
    }

    try {
      const { function: func, outputsize } = this.getAlphaVantageParams(timeframe)
      const url = `https://www.alphavantage.co/query?function=${func}&symbol=${symbol}&outputsize=${outputsize}&apikey=${apiKey}`

      const response = await fetch(url)
      const data = await response.json()

      if (!response.ok) {
        throw new StockHistoryError(
          `Alpha Vantage API error: ${response.status}`,
          'API_ERROR',
          'alphavantage',
          response.status >= 500
        )
      }

      if (data['Error Message']) {
        throw new StockHistoryError(data['Error Message'], 'API_ERROR', 'alphavantage')
      }

      if (data['Note']) {
        throw new StockHistoryError('API rate limit exceeded', 'RATE_LIMIT', 'alphavantage', true)
      }

      const timeSeriesKey = Object.keys(data).find(key => key.includes('Time Series'))
      if (!timeSeriesKey || !data[timeSeriesKey]) {
        return { success: false, error: 'No data available', source: 'alphavantage' }
      }

      const timeSeries = data[timeSeriesKey]
      const historicalData: HistoricalDataPoint[] = Object.entries(timeSeries)
        .map(([date, values]: [string, any]) => ({
          timestamp: new Date(date).getTime(),
          close: parseFloat(values['4. close']),
          open: parseFloat(values['1. open']),
          high: parseFloat(values['2. high']),
          low: parseFloat(values['3. low']),
          volume: parseInt(values['5. volume']) || 0
        }))
        .sort((a, b) => a.timestamp - b.timestamp) // Sort by timestamp ascending

      return { success: true, data: historicalData, source: 'alphavantage' }
    } catch (error) {
      if (error instanceof StockHistoryError) throw error
      throw new StockHistoryError(`Alpha Vantage fetch failed: ${error}`, 'FETCH_ERROR', 'alphavantage', true)
    }
  }

  private async fetchFinnhubHistory(symbol: string, timeframe: Timeframe): Promise<HistoryApiResponse> {
    const apiKey = process.env.FINNHUB_API_KEY
    if (!apiKey) {
      throw new StockHistoryError('Finnhub API key not configured', 'API_KEY_MISSING', 'finnhub')
    }

    try {
      const { from, to, resolution } = this.getFinnhubParams(timeframe)
      const url = `https://finnhub.io/api/v1/stock/candle?symbol=${symbol}&resolution=${resolution}&from=${from}&to=${to}&token=${apiKey}`

      const response = await fetch(url)
      const data = await response.json()

      if (!response.ok) {
        throw new StockHistoryError(
          `Finnhub API error: ${response.status}`,
          'API_ERROR',
          'finnhub',
          response.status >= 500
        )
      }

      if (data.s !== 'ok') {
        return { success: false, error: data.s || 'No data available', source: 'finnhub' }
      }

      if (!data.c || data.c.length === 0) {
        return { success: false, error: 'No data available', source: 'finnhub' }
      }

      const historicalData: HistoricalDataPoint[] = data.c.map((close: number, index: number) => ({
        timestamp: data.t[index] * 1000, // Convert to milliseconds
        close,
        open: data.o[index],
        high: data.h[index],
        low: data.l[index],
        volume: data.v[index]
      }))

      return { success: true, data: historicalData, source: 'finnhub' }
    } catch (error) {
      if (error instanceof StockHistoryError) throw error
      throw new StockHistoryError(`Finnhub fetch failed: ${error}`, 'FETCH_ERROR', 'finnhub', true)
    }
  }

  // Parameter conversion helpers for each API
  private getPolygonTimeParams(timeframe: Timeframe) {
    const now = new Date()
    const to = now.toISOString().split('T')[0] // YYYY-MM-DD format
    let from: string
    let multiplier: number
    let timespan: string

    switch (timeframe) {
      case '1D':
        from = new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        multiplier = 5
        timespan = 'minute'
        break
      case '1W':
        from = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        multiplier = 1
        timespan = 'hour'
        break
      case '1M':
        from = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        multiplier = 1
        timespan = 'day'
        break
      case '3M':
        from = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        multiplier = 1
        timespan = 'day'
        break
      case '1Y':
        from = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        multiplier = 1
        timespan = 'day'
        break
      case 'All':
        from = '2000-01-01' // Far back date
        multiplier = 1
        timespan = 'day'
        break
      default:
        from = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        multiplier = 1
        timespan = 'day'
    }

    return { from, to, multiplier, timespan }
  }

  private getTwelveDataParams(timeframe: Timeframe) {
    let interval: string
    let outputsize: number

    switch (timeframe) {
      case '1D':
        interval = '5min'
        outputsize = 288 // 24 hours * 12 (5-min intervals per hour)
        break
      case '1W':
        interval = '1h'
        outputsize = 168 // 7 days * 24 hours
        break
      case '1M':
        interval = '1day'
        outputsize = 30
        break
      case '3M':
        interval = '1day'
        outputsize = 90
        break
      case '1Y':
        interval = '1day'
        outputsize = 365
        break
      case 'All':
        interval = '1day'
        outputsize = 5000 // Max for Twelve Data
        break
      default:
        interval = '1day'
        outputsize = 30
    }

    return { interval, outputsize }
  }

  private getAlphaVantageParams(timeframe: Timeframe) {
    let func: string
    let outputsize: string

    switch (timeframe) {
      case '1D':
        func = 'TIME_SERIES_INTRADAY&interval=5min'
        outputsize = 'compact'
        break
      case '1W':
      case '1M':
      case '3M':
        func = 'TIME_SERIES_DAILY'
        outputsize = 'compact'
        break
      case '1Y':
      case 'All':
        func = 'TIME_SERIES_DAILY'
        outputsize = 'full'
        break
      default:
        func = 'TIME_SERIES_DAILY'
        outputsize = 'compact'
    }

    return { function: func, outputsize }
  }

  private getFinnhubParams(timeframe: Timeframe) {
    const now = Math.floor(Date.now() / 1000) // Unix timestamp
    let from: number
    let resolution: string

    switch (timeframe) {
      case '1D':
        from = now - 24 * 60 * 60
        resolution = '5'
        break
      case '1W':
        from = now - 7 * 24 * 60 * 60
        resolution = '60'
        break
      case '1M':
        from = now - 30 * 24 * 60 * 60
        resolution = 'D'
        break
      case '3M':
        from = now - 90 * 24 * 60 * 60
        resolution = 'D'
        break
      case '1Y':
        from = now - 365 * 24 * 60 * 60
        resolution = 'D'
        break
      case 'All':
        from = now - 10 * 365 * 24 * 60 * 60 // 10 years back
        resolution = 'D'
        break
      default:
        from = now - 30 * 24 * 60 * 60
        resolution = 'D'
    }

    return { from, to: now, resolution }
  }

  // Additional caching utilities

  /**
   * Preload cache for popular symbols and timeframes
   */
  async preloadCache(symbols: string[], timeframes: Timeframe[] = ['1D', '1W', '1M']): Promise<void> {
    const promises: Promise<any>[] = []

    for (const symbol of symbols) {
      for (const timeframe of timeframes) {
        promises.push(
          this.getHistoricalData(symbol, timeframe).catch(error => {
            console.warn(`Failed to preload ${symbol} ${timeframe}:`, error.message)
          })
        )
      }
    }

    await Promise.allSettled(promises)
  }

  /**
   * Get cache hit rate for monitoring
   */
  getCacheHitRate(): number {
    const stats = this.cacheManager.getStats()
    const total = stats.hits + stats.misses
    return total > 0 ? (stats.hits / total) * 100 : 0
  }

  /**
   * Warm cache for a specific symbol across all timeframes
   */
  async warmCache(symbol: string): Promise<Record<Timeframe, boolean>> {
    const timeframes: Timeframe[] = ['1D', '1W', '1M', '3M', '1Y', 'All']
    const results: Record<string, boolean> = {}

    const promises = timeframes.map(async (timeframe) => {
      try {
        await this.getHistoricalData(symbol, timeframe)
        return { timeframe, success: true }
      } catch (error) {
        console.warn(`Failed to warm cache for ${symbol} ${timeframe}:`, error)
        return { timeframe, success: false }
      }
    })

    const responses = await Promise.allSettled(promises)

    responses.forEach((response) => {
      if (response.status === 'fulfilled') {
        results[response.value.timeframe] = response.value.success
      }
    })

    return results as Record<Timeframe, boolean>
  }

  /**
   * Clear expired cache entries
   */
  clearExpiredCache(): number {
    return this.cacheManager.cleanup()
  }

  /**
   * Get detailed cache information for a symbol
   */
  getCacheInfo(symbol: string): Record<Timeframe, any> {
    const timeframes: Timeframe[] = ['1D', '1W', '1M', '3M', '1Y', 'All']
    const info: Record<string, any> = {}

    for (const timeframe of timeframes) {
      const cacheKey = this.getCacheKey(symbol, timeframe)
      const cached = this.cacheManager.get(cacheKey)
      const config = TIMEFRAME_CACHE_CONFIG[timeframe]

      info[timeframe] = {
        cached: !!cached,
        ttl: config.ttl,
        priority: config.priority,
        maxAge: config.maxAge
      }
    }

    return info as Record<Timeframe, any>
  }

  /**
   * Force refresh cache for a symbol and timeframe
   */
  async forceRefresh(symbol: string, timeframe: Timeframe): Promise<StockHistoryResponse> {
    // Clear existing cache
    this.invalidateCache(symbol, timeframe)

    // Fetch fresh data
    return this.getHistoricalData(symbol, timeframe)
  }

  /**
   * Batch cache invalidation
   */
  invalidateBatch(symbols: string[], timeframes?: Timeframe[]): void {
    for (const symbol of symbols) {
      if (timeframes) {
        for (const timeframe of timeframes) {
          this.invalidateCache(symbol, timeframe)
        }
      } else {
        this.invalidateCache(symbol)
      }
    }
  }

  /**
   * Get cache size and memory usage
   */
  getCacheMetrics(): {
    totalEntries: number
    memoryUsage: number
    hitRate: number
    oldestEntry: Date | null
    newestEntry: Date | null
  } {
    const stats = this.cacheManager.getStats()
    return {
      totalEntries: stats.totalEntries,
      memoryUsage: stats.memoryUsage,
      hitRate: this.getCacheHitRate(),
      oldestEntry: stats.oldestEntry ? new Date(stats.oldestEntry) : null,
      newestEntry: stats.newestEntry ? new Date(stats.newestEntry) : null
    }
  }
}

// Singleton instance
let stockHistoryService: StockHistoryService | null = null

export function getStockHistoryService(): StockHistoryService {
  if (!stockHistoryService) {
    stockHistoryService = new StockHistoryService()
  }
  return stockHistoryService
}
