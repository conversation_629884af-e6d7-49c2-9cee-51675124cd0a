import { NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Initialize Supabase
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''
)

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id')
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }
    
    // Get user's linked accounts
    const { data: accounts, error } = await supabase
      .from('linked_accounts')
      .select(`
        id,
        account_name,
        account_mask,
        account_type,
        account_subtype,
        institution_name,
        is_active,
        roundups_enabled,
        created_at
      `)
      .eq('user_id', userId)
      .eq('is_active', true)
      .order('created_at', { ascending: false })
    
    if (error) {
      console.error('Error fetching linked accounts:', error)
      return NextResponse.json(
        { error: 'Failed to fetch linked accounts' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      accounts: accounts || []
    })
    
  } catch (error) {
    console.error('Linked accounts API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
