"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { TopNav } from "@/components/top-nav"
import { 
  CreditCard, 
  History, 
  Settings, 
  Download, 
  Calendar,
  DollarSign,
  CheckCircle,
  XCircle,
  Clock,
  ArrowLeft
} from "lucide-react"
import { useNotifications } from "@/components/notification-system"
import { useAuth } from "@/components/auth-provider"

interface Payment {
  id: string
  amount: number
  currency: string
  status: string
  payment_method: string
  customer_email: string
  created_at: string
  stripe_session_id: string
}

export default function PaymentManagePage() {
  const router = useRouter()
  const { addNotification } = useNotifications()
  const { user, loading: authLoading } = useAuth()
  const [loading, setLoading] = useState(true)
  const [payments, setPayments] = useState<Payment[]>([])
  const [userBalance, setUserBalance] = useState(0)

  useEffect(() => {
    if (authLoading) return

    if (!user) {
      router.push("/auth/signin")
      return
    }

    fetchUserData()
  }, [user, authLoading, router])

  const fetchUserData = async () => {
    try {
      setLoading(true)

      // Fetch user balance
      const balanceResponse = await fetch(`/api/user-balance?user_id=${user?.id}`)
      if (balanceResponse.ok) {
        const balanceData = await balanceResponse.json()
        setUserBalance(balanceData.balance)
      }

      // Fetch payment history
      const paymentsResponse = await fetch(`/api/payment-history?user_id=${user?.id}&limit=50`)
      if (paymentsResponse.ok) {
        const paymentsData = await paymentsResponse.json()
        setPayments(paymentsData.payments)
      }

    } catch (error) {
      console.error('Error fetching user data:', error)
      addNotification({
        type: "error",
        title: "Error",
        message: "Failed to load payment data. Please try again.",
      })
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-emerald-500" />
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />
      default:
        return <Clock className="h-4 w-4 text-slate-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    const statusLower = status.toLowerCase()
    const variants = {
      completed: "bg-emerald-500/20 text-emerald-400 border-emerald-500/30",
      failed: "bg-red-500/20 text-red-400 border-red-500/30",
      pending: "bg-yellow-500/20 text-yellow-400 border-yellow-500/30"
    }
    
    return (
      <Badge className={`${variants[statusLower as keyof typeof variants] || variants.pending} border`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-900 text-white">
        <TopNav />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-500"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-900 text-white">
      <TopNav />
      
      <div className="container mx-auto px-4 py-8 space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push("/payments")}
              className="border-slate-600 text-slate-300 hover:bg-slate-700"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Payments
            </Button>
            <div>
              <h1 className="text-3xl font-bold">Payment Management</h1>
              <p className="text-slate-400 mt-1">View your payment history and manage settings</p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-sm text-slate-400">Current Balance</p>
            <p className="text-2xl font-bold text-emerald-400">${userBalance.toFixed(2)}</p>
          </div>
        </div>

        <Tabs defaultValue="history" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 bg-slate-800">
            <TabsTrigger value="history" className="data-[state=active]:bg-slate-700">
              <History className="h-4 w-4 mr-2" />
              Payment History
            </TabsTrigger>
            <TabsTrigger value="settings" className="data-[state=active]:bg-slate-700">
              <Settings className="h-4 w-4 mr-2" />
              Payment Settings
            </TabsTrigger>
          </TabsList>

          <TabsContent value="history" className="space-y-6">
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <History className="h-5 w-5 text-emerald-400" />
                    Transaction History
                  </CardTitle>
                  <CardDescription>All your payment transactions and their status</CardDescription>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className="border-slate-600 text-slate-300 hover:bg-slate-700"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </CardHeader>
              <CardContent>
                {payments.length === 0 ? (
                  <div className="text-center py-12">
                    <CreditCard className="h-16 w-16 text-slate-600 mx-auto mb-4" />
                    <p className="text-slate-400 text-lg">No payment history yet</p>
                    <p className="text-sm text-slate-500 mt-2">Your transactions will appear here after you make your first payment</p>
                    <Button
                      className="mt-4 bg-emerald-600 hover:bg-emerald-700"
                      onClick={() => router.push("/payments")}
                    >
                      <DollarSign className="h-4 w-4 mr-2" />
                      Add Funds Now
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {payments.map((payment) => (
                      <div
                        key={payment.id}
                        className="flex items-center justify-between p-4 bg-slate-700/50 rounded-lg border border-slate-600"
                      >
                        <div className="flex items-center gap-4">
                          {getStatusIcon(payment.status)}
                          <div>
                            <p className="font-medium text-white">
                              ${payment.amount.toFixed(2)} {payment.currency.toUpperCase()}
                            </p>
                            <p className="text-sm text-slate-400">
                              {payment.payment_method} • {formatDate(payment.created_at)}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          {getStatusBadge(payment.status)}
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-slate-400 hover:text-white"
                          >
                            View Details
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5 text-emerald-400" />
                  Payment Settings
                </CardTitle>
                <CardDescription>Manage your payment preferences and methods</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="text-center py-8">
                  <CreditCard className="h-12 w-12 text-slate-600 mx-auto mb-4" />
                  <p className="text-slate-400">Payment method management coming soon</p>
                  <p className="text-sm text-slate-500 mt-1">
                    You'll be able to save and manage payment methods here
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
