/**
 * Portfolio Stock Integration Service
 * Connects PortfolioCalculator with StockHistoryService for comprehensive portfolio analysis
 */

import { getStockHistoryService } from './stock-history-service'
import { getUserCapitalResolver } from './user-capital-resolver'
import { createClient } from './supabase'
import type { Timeframe } from './types/stock-history'

export interface PortfolioStockData {
  symbol: string
  shares: number
  averageCost: number
  currentPrice: number
  historicalPrices: Array<{ timestamp: number; close: number }>
  totalValue: number
  gainLoss: number
  gainLossPercent: number
}

export interface PortfolioTimeSeriesData {
  date: string
  timestamp: number
  portfolioValue: number
  cashValue: number
  totalValue: number
  dailyReturn: number
  dailyReturnPercent: number
  totalReturn: number
  totalReturnPercent: number
}

export interface PortfolioPerformanceResult {
  userId: string
  portfolioId: string
  timeframe: Timeframe
  currentMetrics: {
    totalValue: number
    totalInvested: number
    totalCash: number
    totalReturn: number
    totalReturnPercent: number
    holdingsCount: number
  }
  timeSeries: PortfolioTimeSeriesData[]
  holdings: PortfolioStockData[]
  lastUpdated: string
}

export class PortfolioStockIntegration {
  private supabase = createClient()
  private stockHistoryService = getStockHistoryService()
  private capitalResolver = getUserCapitalResolver()

  /**
   * Calculate complete portfolio performance with historical data
   */
  async calculatePortfolioPerformance(
    userId: string,
    timeframe: Timeframe = '1M',
    portfolioId?: string
  ): Promise<PortfolioPerformanceResult | null> {
    try {
      // Get portfolio
      const portfolio = await this.getPortfolio(userId, portfolioId)
      if (!portfolio) {
        return this.getEmptyResult(userId, 'default', timeframe)
      }

      // Get current holdings
      const holdings = await this.getPortfolioHoldings(portfolio.id)
      
      // Get user capital summary
      const capitalSummary = await this.capitalResolver.getUserCapitalSummary(userId)
      
      // Get historical data for all holdings
      const holdingsWithHistory = await this.enrichHoldingsWithHistoricalData(holdings, timeframe)
      
      // Calculate current metrics
      const currentMetrics = this.calculateCurrentMetrics(holdingsWithHistory, capitalSummary)
      
      // Build time series
      const timeSeries = await this.buildPortfolioTimeSeries(
        userId,
        holdingsWithHistory,
        timeframe,
        portfolio.initial_value || 10000
      )

      return {
        userId,
        portfolioId: portfolio.id,
        timeframe,
        currentMetrics,
        timeSeries,
        holdings: holdingsWithHistory,
        lastUpdated: new Date().toISOString()
      }

    } catch (error) {
      console.error('Error calculating portfolio performance:', error)
      return null
    }
  }

  /**
   * Get portfolio (default if not specified)
   */
  private async getPortfolio(userId: string, portfolioId?: string) {
    let query = this.supabase
      .from('portfolios')
      .select('*')
      .eq('user_id', userId)

    if (portfolioId) {
      query = query.eq('id', portfolioId)
    } else {
      query = query.eq('is_default', true)
    }

    const { data, error } = await query.single()
    if (error) {
      console.error('Error fetching portfolio:', error)
      return null
    }
    return data
  }

  /**
   * Get portfolio holdings
   */
  private async getPortfolioHoldings(portfolioId: string) {
    const { data, error } = await this.supabase
      .from('portfolio_holdings')
      .select('*')
      .eq('portfolio_id', portfolioId)
      .gt('shares', 0)

    if (error) {
      console.error('Error fetching holdings:', error)
      return []
    }
    return data || []
  }

  /**
   * Enrich holdings with historical price data
   */
  private async enrichHoldingsWithHistoricalData(
    holdings: any[],
    timeframe: Timeframe
  ): Promise<PortfolioStockData[]> {
    const enrichedHoldings: PortfolioStockData[] = []

    for (const holding of holdings) {
      try {
        // Get historical data for this symbol
        const historicalData = await this.stockHistoryService.getHistoricalData(
          holding.symbol,
          timeframe
        )

        const currentPrice = historicalData.data.length > 0 
          ? historicalData.data[historicalData.data.length - 1].close
          : holding.average_cost

        const totalValue = holding.shares * currentPrice
        const totalCost = holding.shares * holding.average_cost
        const gainLoss = totalValue - totalCost
        const gainLossPercent = totalCost > 0 ? (gainLoss / totalCost) * 100 : 0

        enrichedHoldings.push({
          symbol: holding.symbol,
          shares: holding.shares,
          averageCost: holding.average_cost,
          currentPrice,
          historicalPrices: historicalData.data.map(d => ({
            timestamp: d.timestamp,
            close: d.close
          })),
          totalValue,
          gainLoss,
          gainLossPercent
        })

      } catch (error) {
        console.error(`Error getting historical data for ${holding.symbol}:`, error)
        
        // Fallback to current data only
        const currentPrice = holding.current_price || holding.average_cost
        const totalValue = holding.shares * currentPrice
        const totalCost = holding.shares * holding.average_cost
        const gainLoss = totalValue - totalCost
        const gainLossPercent = totalCost > 0 ? (gainLoss / totalCost) * 100 : 0

        enrichedHoldings.push({
          symbol: holding.symbol,
          shares: holding.shares,
          averageCost: holding.average_cost,
          currentPrice,
          historicalPrices: [],
          totalValue,
          gainLoss,
          gainLossPercent
        })
      }
    }

    return enrichedHoldings
  }

  /**
   * Calculate current portfolio metrics
   */
  private calculateCurrentMetrics(
    holdings: PortfolioStockData[],
    capitalSummary: any
  ) {
    const totalInvested = holdings.reduce((sum, h) => sum + h.totalValue, 0)
    const totalCash = capitalSummary?.totalCash || 0
    const totalValue = totalInvested + totalCash
    const totalCost = holdings.reduce((sum, h) => sum + (h.shares * h.averageCost), 0)
    const totalReturn = totalInvested - totalCost
    const totalReturnPercent = totalCost > 0 ? (totalReturn / totalCost) * 100 : 0

    return {
      totalValue,
      totalInvested,
      totalCash,
      totalReturn,
      totalReturnPercent,
      holdingsCount: holdings.length
    }
  }

  /**
   * Build portfolio time series data
   */
  private async buildPortfolioTimeSeries(
    userId: string,
    holdings: PortfolioStockData[],
    timeframe: Timeframe,
    initialValue: number
  ): Promise<PortfolioTimeSeriesData[]> {
    try {
      // Get the date range for the timeframe
      const { startDate, endDate } = this.getDateRange(timeframe)

      // Get capital history
      const capitalHistory = await this.capitalResolver.getCapitalHistory(
        userId,
        startDate,
        endDate,
        'daily'
      )

      // Build time series by combining historical prices
      const timeSeries: PortfolioTimeSeriesData[] = []

      for (const capitalPoint of capitalHistory) {
        let portfolioValue = 0

        // Calculate portfolio value for this date
        for (const holding of holdings) {
          if (holding.historicalPrices.length > 0) {
            // Find the closest historical price for this date
            const targetTimestamp = capitalPoint.timestamp
            const closestPrice = this.findClosestPrice(holding.historicalPrices, targetTimestamp)
            portfolioValue += holding.shares * (closestPrice || holding.averageCost)
          } else {
            // Use current price if no historical data
            portfolioValue += holding.shares * holding.currentPrice
          }
        }

        const totalValue = portfolioValue + capitalPoint.cashBalance
        const totalReturn = totalValue - initialValue
        const totalReturnPercent = initialValue > 0 ? (totalReturn / initialValue) * 100 : 0

        // Calculate daily return (compared to previous day)
        const previousPoint = timeSeries[timeSeries.length - 1]
        const dailyReturn = previousPoint ? totalValue - previousPoint.totalValue : 0
        const dailyReturnPercent = previousPoint && previousPoint.totalValue > 0 
          ? (dailyReturn / previousPoint.totalValue) * 100 
          : 0

        timeSeries.push({
          date: capitalPoint.date,
          timestamp: capitalPoint.timestamp,
          portfolioValue,
          cashValue: capitalPoint.cashBalance,
          totalValue,
          dailyReturn,
          dailyReturnPercent,
          totalReturn,
          totalReturnPercent
        })
      }

      return timeSeries

    } catch (error) {
      console.error('Error building time series:', error)
      return []
    }
  }

  /**
   * Find closest historical price for a given timestamp
   */
  private findClosestPrice(
    historicalPrices: Array<{ timestamp: number; close: number }>,
    targetTimestamp: number
  ): number | null {
    if (historicalPrices.length === 0) return null

    let closest = historicalPrices[0]
    let minDiff = Math.abs(historicalPrices[0].timestamp - targetTimestamp)

    for (const price of historicalPrices) {
      const diff = Math.abs(price.timestamp - targetTimestamp)
      if (diff < minDiff) {
        minDiff = diff
        closest = price
      }
    }

    return closest.close
  }

  /**
   * Get date range for timeframe
   */
  private getDateRange(timeframe: Timeframe) {
    const now = new Date()
    const endDate = now.toISOString()
    let startDate: string

    switch (timeframe) {
      case '1D':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString()
        break
      case '1W':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString()
        break
      case '1M':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString()
        break
      case '3M':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000).toISOString()
        break
      case '1Y':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000).toISOString()
        break
      case 'All':
        startDate = new Date(now.getTime() - 2 * 365 * 24 * 60 * 60 * 1000).toISOString()
        break
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString()
    }

    return { startDate, endDate }
  }

  /**
   * Return empty result for edge cases
   */
  private getEmptyResult(
    userId: string,
    portfolioId: string,
    timeframe: Timeframe
  ): PortfolioPerformanceResult {
    return {
      userId,
      portfolioId,
      timeframe,
      currentMetrics: {
        totalValue: 0,
        totalInvested: 0,
        totalCash: 0,
        totalReturn: 0,
        totalReturnPercent: 0,
        holdingsCount: 0
      },
      timeSeries: [],
      holdings: [],
      lastUpdated: new Date().toISOString()
    }
  }
}

// Singleton instance
let portfolioStockIntegration: PortfolioStockIntegration | null = null

export function getPortfolioStockIntegration(): PortfolioStockIntegration {
  if (!portfolioStockIntegration) {
    portfolioStockIntegration = new PortfolioStockIntegration()
  }
  return portfolioStockIntegration
}
