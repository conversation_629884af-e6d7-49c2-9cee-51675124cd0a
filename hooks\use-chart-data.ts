/**
 * Chart Data Hooks
 * Custom hooks for fetching and managing chart data
 */

'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import { createClient } from '@/lib/supabase'
import { TimeRange, ValueType, ChartDataPoint } from '@/components/charts/chart-base'

export interface UseStockChartDataOptions {
  symbol: string
  timeRange: TimeRange
  autoRefresh?: boolean
  refreshInterval?: number
}

export interface UsePortfolioChartDataOptions {
  timeRange: TimeRange
  portfolioId?: string
  autoRefresh?: boolean
  refreshInterval?: number
}

export interface ChartDataState<T = ChartDataPoint[]> {
  data: T
  loading: boolean
  error: string | null
  lastUpdated: Date | null
  refresh: () => Promise<void>
}

/**
 * Generate mock stock data for testing
 */
function generateMockStockData(symbol: string, timeRange: TimeRange): ChartDataPoint[] {
  const now = Date.now()
  const dataPoints = getDataPointsForTimeRange(timeRange)
  const interval = getIntervalForTimeRange(timeRange)

  // Base price for different symbols
  const basePrices: Record<string, number> = {
    'AAPL': 175,
    'MSFT': 380,
    'GOOGL': 140,
    'AMZN': 145,
    'TSLA': 250,
    'NVDA': 450,
    'META': 320,
    'NFLX': 400
  }

  const basePrice = basePrices[symbol] || 100
  let currentPrice = basePrice

  const data: ChartDataPoint[] = []

  for (let i = dataPoints - 1; i >= 0; i--) {
    const timestamp = now - (i * interval)

    // Add some realistic price movement
    const volatility = 0.02 // 2% volatility
    const change = (Math.random() - 0.5) * volatility * currentPrice
    currentPrice = Math.max(currentPrice + change, basePrice * 0.8) // Don't go below 80% of base

    data.push({
      timestamp,
      value: Number(currentPrice.toFixed(2)),
      date: new Date(timestamp).toISOString()
    })
  }

  return data
}

/**
 * Get number of data points for timeframe
 */
function getDataPointsForTimeRange(timeRange: TimeRange): number {
  switch (timeRange) {
    case '1D': return 24 // Hourly data
    case '1W': return 7 // Daily data
    case '1M': return 30 // Daily data
    case '3M': return 90 // Daily data
    case '1Y': return 52 // Weekly data
    case 'All': return 100 // Sample data
    default: return 30
  }
}

/**
 * Get interval in milliseconds for timeframe
 */
function getIntervalForTimeRange(timeRange: TimeRange): number {
  switch (timeRange) {
    case '1D': return 60 * 60 * 1000 // 1 hour
    case '1W': return 24 * 60 * 60 * 1000 // 1 day
    case '1M': return 24 * 60 * 60 * 1000 // 1 day
    case '3M': return 24 * 60 * 60 * 1000 // 1 day
    case '1Y': return 7 * 24 * 60 * 60 * 1000 // 1 week
    case 'All': return 30 * 24 * 60 * 60 * 1000 // 1 month
    default: return 24 * 60 * 60 * 1000
  }
}

/**
 * Generate mock portfolio data
 */
function generateMockPortfolioData(timeRange: TimeRange) {
  const now = Date.now()
  const dataPoints = getDataPointsForTimeRange(timeRange)
  const interval = getIntervalForTimeRange(timeRange)

  const initialValue = 10000
  let currentValue = initialValue

  const valueTimeline: ChartDataPoint[] = []
  const deposits: ChartDataPoint[] = []

  for (let i = dataPoints - 1; i >= 0; i--) {
    const timestamp = now - (i * interval)

    // Add some portfolio growth with occasional deposits
    const growthRate = 0.001 // 0.1% average growth per period
    const volatility = 0.015 // 1.5% volatility

    const growth = growthRate + (Math.random() - 0.5) * volatility
    currentValue = currentValue * (1 + growth)

    // Occasionally add deposits (10% chance)
    if (Math.random() < 0.1 && i > 0) {
      const depositAmount = 500 + Math.random() * 1500 // $500-$2000
      currentValue += depositAmount
      deposits.push({
        timestamp,
        value: depositAmount,
        date: new Date(timestamp).toISOString(),
        label: `Deposit: $${depositAmount.toFixed(2)}`
      })
    }

    valueTimeline.push({
      timestamp,
      value: Number(currentValue.toFixed(2)),
      date: new Date(timestamp).toISOString()
    })
  }

  const totalInvested = initialValue + deposits.reduce((sum, d) => sum + d.value, 0)
  const netReturn = {
    amount: currentValue - totalInvested,
    percentage: ((currentValue - totalInvested) / totalInvested) * 100
  }

  return {
    valueTimeline,
    totalInvested,
    currentValue,
    netReturn,
    deposits
  }
}

/**
 * Hook for fetching stock price data
 */
export function useStockChartData({
  symbol,
  timeRange,
  autoRefresh = false,
  refreshInterval = 60000 // 1 minute
}: UseStockChartDataOptions): ChartDataState {
  const [data, setData] = useState<ChartDataPoint[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)

  const fetchData = useCallback(async () => {
    if (!symbol) return

    try {
      setLoading(true)
      setError(null)

      // Get authenticated Supabase client for headers
      const supabase = createClient()
      const { data: { session } } = await supabase.auth.getSession()

      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }

      // Add auth header if user is logged in
      if (session) {
        headers['Authorization'] = `Bearer ${session.access_token}`
      }

      const response = await fetch(`/api/stock/${symbol}/history?timeframe=${timeRange}`, {
        headers
      })

      if (!response.ok) {
        // If API fails, use mock data for now
        console.warn(`Stock API failed for ${symbol}, using mock data`)
        const mockData = generateMockStockData(symbol, timeRange)
        setData(mockData)
        setLastUpdated(new Date())
        return
      }

      const result = await response.json()

      if (!result.success && result.error) {
        // If API returns error, use mock data
        console.warn(`Stock API error for ${symbol}: ${result.error}, using mock data`)
        const mockData = generateMockStockData(symbol, timeRange)
        setData(mockData)
        setLastUpdated(new Date())
        return
      }

      // Transform API data to chart format
      const chartData: ChartDataPoint[] = (result.data || []).map((point: any) => ({
        timestamp: point.timestamp,
        value: point.close,
        date: new Date(point.timestamp).toISOString()
      }))

      setData(chartData)
      setLastUpdated(new Date())
    } catch (err) {
      console.error('Error fetching stock data:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch stock data')
      setData([])
    } finally {
      setLoading(false)
    }
  }, [symbol, timeRange])

  const refresh = useCallback(async () => {
    await fetchData()
  }, [fetchData])

  // Initial fetch and dependency updates
  useEffect(() => {
    fetchData()
  }, [fetchData])

  // Auto-refresh setup
  useEffect(() => {
    if (!autoRefresh || !symbol) return

    const interval = setInterval(fetchData, refreshInterval)
    return () => clearInterval(interval)
  }, [autoRefresh, refreshInterval, fetchData, symbol])

  return {
    data,
    loading,
    error,
    lastUpdated,
    refresh
  }
}

/**
 * Hook for fetching portfolio performance data
 */
export function usePortfolioChartData({
  timeRange,
  portfolioId,
  autoRefresh = false,
  refreshInterval = 300000 // 5 minutes
}: UsePortfolioChartDataOptions): ChartDataState<{
  valueTimeline: ChartDataPoint[]
  totalInvested: number
  currentValue: number
  netReturn: { amount: number; percentage: number }
  deposits?: ChartDataPoint[]
}> {
  const [data, setData] = useState({
    valueTimeline: [] as ChartDataPoint[],
    totalInvested: 0,
    currentValue: 0,
    netReturn: { amount: 0, percentage: 0 },
    deposits: [] as ChartDataPoint[]
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)

  const fetchData = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      // Get authenticated Supabase client
      const supabase = createClient()
      const { data: { session } } = await supabase.auth.getSession()

      if (!session) {
        // If not logged in, show mock data for demo purposes
        console.warn('User not logged in, using mock portfolio data')
        const mockData = generateMockPortfolioData(timeRange)
        setData(mockData)
        setLastUpdated(new Date())
        return
      }

      const params = new URLSearchParams({
        timeframe: timeRange,
        details: 'true'
      })

      if (portfolioId) {
        params.append('portfolio_id', portfolioId)
      }

      const response = await fetch(`/api/portfolio/performance?${params}`, {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Please log in to view portfolio data')
        }
        // If API fails, use mock data for now
        console.warn(`Portfolio API failed, using mock data`)
        const mockData = generateMockPortfolioData(timeRange)
        setData(mockData)
        setLastUpdated(new Date())
        return
      }

      const result = await response.json()

      if (!result.success && result.error) {
        // If API returns error, use mock data
        console.warn(`Portfolio API error: ${result.error}, using mock data`)
        const mockData = generateMockPortfolioData(timeRange)
        setData(mockData)
        setLastUpdated(new Date())
        return
      }

      // Transform API data to chart format
      const valueTimeline: ChartDataPoint[] = (result.valueTimeline || []).map((point: any) => ({
        timestamp: point.timestamp,
        value: point.value,
        date: new Date(point.timestamp).toISOString()
      }))

      // Extract deposit information if available
      const deposits: ChartDataPoint[] = []
      if (result.details?.capitalHistory) {
        result.details.capitalHistory.forEach((point: any) => {
          if (point.deposits > 0) {
            deposits.push({
              timestamp: point.timestamp,
              value: point.deposits,
              date: point.date,
              label: `Deposit: $${point.deposits.toFixed(2)}`
            })
          }
        })
      }

      setData({
        valueTimeline,
        totalInvested: result.totalInvested || 0,
        currentValue: result.currentValue || 0,
        netReturn: result.netReturn || { amount: 0, percentage: 0 },
        deposits
      })
      setLastUpdated(new Date())
    } catch (err) {
      console.error('Error fetching portfolio data:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch portfolio data')
      setData({
        valueTimeline: [],
        totalInvested: 0,
        currentValue: 0,
        netReturn: { amount: 0, percentage: 0 },
        deposits: []
      })
    } finally {
      setLoading(false)
    }
  }, [timeRange, portfolioId])

  const refresh = useCallback(async () => {
    await fetchData()
  }, [fetchData])

  // Initial fetch and dependency updates
  useEffect(() => {
    fetchData()
  }, [fetchData])

  // Auto-refresh setup
  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(fetchData, refreshInterval)
    return () => clearInterval(interval)
  }, [autoRefresh, refreshInterval, fetchData])

  return {
    data,
    loading,
    error,
    lastUpdated,
    refresh
  }
}

/**
 * Hook for managing chart state (time range, value type, etc.)
 */
export function useChartState(initialTimeRange: TimeRange = '1M', initialValueType: ValueType = 'absolute') {
  const [timeRange, setTimeRange] = useState<TimeRange>(initialTimeRange)
  const [valueType, setValueType] = useState<ValueType>(initialValueType)

  const handleTimeRangeChange = useCallback((range: TimeRange) => {
    setTimeRange(range)
  }, [])

  const handleValueTypeChange = useCallback((type: ValueType) => {
    setValueType(type)
  }, [])

  return {
    timeRange,
    valueType,
    setTimeRange: handleTimeRangeChange,
    setValueType: handleValueTypeChange
  }
}

/**
 * Hook for transforming data based on value type
 */
export function useTransformedChartData(
  data: ChartDataPoint[],
  valueType: ValueType,
  baseValue?: number
) {
  return useMemo(() => {
    if (valueType === 'absolute' || !baseValue) {
      return data
    }

    // Transform to percentage change from base value
    return data.map(point => ({
      ...point,
      value: ((point.value - baseValue) / baseValue) * 100
    }))
  }, [data, valueType, baseValue])
}

/**
 * Hook for calculating chart statistics
 */
export function useChartStats(data: ChartDataPoint[]) {
  return useMemo(() => {
    if (data.length === 0) {
      return {
        min: 0,
        max: 0,
        first: 0,
        last: 0,
        change: 0,
        changePercent: 0,
        volatility: 0
      }
    }

    const values = data.map(d => d.value)
    const min = Math.min(...values)
    const max = Math.max(...values)
    const first = values[0]
    const last = values[values.length - 1]
    const change = last - first
    const changePercent = first !== 0 ? (change / first) * 100 : 0

    // Calculate volatility (standard deviation)
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
    const volatility = Math.sqrt(variance)

    return {
      min,
      max,
      first,
      last,
      change,
      changePercent,
      volatility
    }
  }, [data])
}

/**
 * Hook for debounced chart updates
 */
export function useDebouncedChartUpdate(callback: () => void, delay: number = 500) {
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null)

  const debouncedUpdate = useCallback(() => {
    if (debounceTimer) {
      clearTimeout(debounceTimer)
    }

    const timer = setTimeout(callback, delay)
    setDebounceTimer(timer)
  }, [callback, delay, debounceTimer])

  useEffect(() => {
    return () => {
      if (debounceTimer) {
        clearTimeout(debounceTimer)
      }
    }
  }, [debounceTimer])

  return debouncedUpdate
}
