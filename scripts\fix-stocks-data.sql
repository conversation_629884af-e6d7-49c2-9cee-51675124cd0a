-- Fix stocks database issues
-- Run this in your Supabase SQL Editor
-- Note: stocks_with_tags is a view, so we only update the base stocks table

-- 1. Fix ticker symbol issues (APPL -> AAPL, etc.)
-- Only update if the new ticker fits in VARCHAR(10)
UPDATE stocks
SET ticker = 'AAPL'
WHERE ticker = 'APPL' AND company_name ILIKE '%Apple Inc%' AND LENGTH('AAPL') <= 10;

-- 2. Fix stock_type field (currently contains exchange info)
-- Move exchange info to proper exchange field and set correct stock_type
-- Handle VARCHAR constraints properly
UPDATE stocks
SET
  exchange = CASE
    WHEN stock_type = 'NYSE' THEN 'NYSE'
    WHEN stock_type = 'NASDAQ' THEN 'NASDAQ'
    WHEN stock_type = 'AMEX' THEN 'AMEX'
    WHEN LENGTH(stock_type) <= 10 THEN stock_type
    ELSE NULL
  END,
  stock_type = CASE
    WHEN asset_class = 'Fixed Income' THEN 'ETF'
    WHEN company_name ILIKE '%ETF%' OR company_name ILIKE '%Fund%' THEN 'ETF'
    WHEN company_name ILIKE '%REIT%' THEN 'REIT'
    ELSE 'Common Stock'
  END
WHERE stock_type IN ('NYSE', 'NASDAQ', 'AMEX') OR stock_type IS NULL;

-- 3. Populate sectors based on company names and descriptions
UPDATE stocks 
SET sector = CASE 
  -- Technology
  WHEN company_name ILIKE '%Apple%' OR company_name ILIKE '%Microsoft%' OR company_name ILIKE '%Google%' 
    OR company_name ILIKE '%Amazon%' OR company_name ILIKE '%Meta%' OR company_name ILIKE '%Tesla%'
    OR company_name ILIKE '%Netflix%' OR company_name ILIKE '%Adobe%' OR company_name ILIKE '%Oracle%'
    OR company_name ILIKE '%Salesforce%' OR company_name ILIKE '%Intel%' OR company_name ILIKE '%AMD%'
    OR company_name ILIKE '%NVIDIA%' OR company_name ILIKE '%Cisco%' OR company_name ILIKE '%IBM%'
    OR description ILIKE '%software%' OR description ILIKE '%technology%' OR description ILIKE '%computer%'
    OR description ILIKE '%internet%' OR description ILIKE '%cloud%' OR description ILIKE '%semiconductor%'
    THEN 'Technology'
  
  -- Healthcare
  WHEN company_name ILIKE '%Johnson%Johnson%' OR company_name ILIKE '%Pfizer%' OR company_name ILIKE '%Merck%'
    OR company_name ILIKE '%AbbVie%' OR company_name ILIKE '%Bristol%' OR company_name ILIKE '%Eli Lilly%'
    OR company_name ILIKE '%Amgen%' OR company_name ILIKE '%Gilead%' OR company_name ILIKE '%Biogen%'
    OR description ILIKE '%pharmaceutical%' OR description ILIKE '%healthcare%' OR description ILIKE '%medical%'
    OR description ILIKE '%drug%' OR description ILIKE '%biotech%' OR description ILIKE '%hospital%'
    THEN 'Healthcare'
  
  -- Financial Services
  WHEN company_name ILIKE '%Bank%' OR company_name ILIKE '%JPMorgan%' OR company_name ILIKE '%Wells Fargo%'
    OR company_name ILIKE '%Goldman%' OR company_name ILIKE '%Morgan Stanley%' OR company_name ILIKE '%Citigroup%'
    OR company_name ILIKE '%American Express%' OR company_name ILIKE '%Visa%' OR company_name ILIKE '%Mastercard%'
    OR description ILIKE '%banking%' OR description ILIKE '%financial%' OR description ILIKE '%insurance%'
    OR description ILIKE '%investment%' OR description ILIKE '%credit%' OR description ILIKE '%loan%'
    THEN 'Financial Services'
  
  -- Consumer Discretionary
  WHEN company_name ILIKE '%Disney%' OR company_name ILIKE '%Nike%' OR company_name ILIKE '%Starbucks%'
    OR company_name ILIKE '%Home Depot%' OR company_name ILIKE '%McDonald%' OR company_name ILIKE '%Walmart%'
    OR company_name ILIKE '%Target%' OR company_name ILIKE '%Costco%' OR company_name ILIKE '%Ford%'
    OR description ILIKE '%retail%' OR description ILIKE '%restaurant%' OR description ILIKE '%automotive%'
    OR description ILIKE '%entertainment%' OR description ILIKE '%consumer%' OR description ILIKE '%apparel%'
    THEN 'Consumer Discretionary'
  
  -- Energy
  WHEN company_name ILIKE '%Exxon%' OR company_name ILIKE '%Chevron%' OR company_name ILIKE '%ConocoPhillips%'
    OR company_name ILIKE '%Marathon%' OR company_name ILIKE '%Valero%' OR company_name ILIKE '%Kinder Morgan%'
    OR description ILIKE '%oil%' OR description ILIKE '%gas%' OR description ILIKE '%energy%'
    OR description ILIKE '%petroleum%' OR description ILIKE '%pipeline%' OR description ILIKE '%refining%'
    THEN 'Energy'
  
  -- Utilities
  WHEN company_name ILIKE '%Electric%' OR company_name ILIKE '%Power%' OR company_name ILIKE '%Utility%'
    OR company_name ILIKE '%Gas%' OR company_name ILIKE '%Water%' OR company_name ILIKE '%Energy%'
    OR description ILIKE '%utility%' OR description ILIKE '%electric%' OR description ILIKE '%power%'
    OR description ILIKE '%water%' OR description ILIKE '%gas utility%'
    THEN 'Utilities'
  
  -- Real Estate
  WHEN company_name ILIKE '%REIT%' OR company_name ILIKE '%Real Estate%' OR company_name ILIKE '%Property%'
    OR description ILIKE '%real estate%' OR description ILIKE '%property%' OR description ILIKE '%REIT%'
    THEN 'Real Estate'
  
  -- Materials
  WHEN company_name ILIKE '%Steel%' OR company_name ILIKE '%Mining%' OR company_name ILIKE '%Chemical%'
    OR company_name ILIKE '%Dow%' OR company_name ILIKE '%DuPont%' OR company_name ILIKE '%3M%'
    OR description ILIKE '%materials%' OR description ILIKE '%chemical%' OR description ILIKE '%mining%'
    OR description ILIKE '%steel%' OR description ILIKE '%aluminum%' OR description ILIKE '%paper%'
    THEN 'Materials'
  
  -- Industrials
  WHEN company_name ILIKE '%Boeing%' OR company_name ILIKE '%Caterpillar%' OR company_name ILIKE '%General Electric%'
    OR company_name ILIKE '%Honeywell%' OR company_name ILIKE '%Lockheed%' OR company_name ILIKE '%Raytheon%'
    OR description ILIKE '%aerospace%' OR description ILIKE '%defense%' OR description ILIKE '%industrial%'
    OR description ILIKE '%manufacturing%' OR description ILIKE '%machinery%' OR description ILIKE '%transportation%'
    THEN 'Industrials'
  
  -- Communication Services
  WHEN company_name ILIKE '%Verizon%' OR company_name ILIKE '%AT&T%' OR company_name ILIKE '%T-Mobile%'
    OR company_name ILIKE '%Comcast%' OR company_name ILIKE '%Charter%' OR company_name ILIKE '%Facebook%'
    OR description ILIKE '%telecommunications%' OR description ILIKE '%media%' OR description ILIKE '%broadcasting%'
    OR description ILIKE '%wireless%' OR description ILIKE '%cable%' OR description ILIKE '%satellite%'
    THEN 'Communication Services'
  
  -- Consumer Staples
  WHEN company_name ILIKE '%Procter%' OR company_name ILIKE '%Coca-Cola%' OR company_name ILIKE '%PepsiCo%'
    OR company_name ILIKE '%Walmart%' OR company_name ILIKE '%Costco%' OR company_name ILIKE '%Kroger%'
    OR description ILIKE '%consumer staples%' OR description ILIKE '%food%' OR description ILIKE '%beverage%'
    OR description ILIKE '%household%' OR description ILIKE '%personal care%' OR description ILIKE '%tobacco%'
    THEN 'Consumer Staples'
  
  ELSE 'Other'
END
WHERE sector IS NULL;

-- 4. Set default exchanges based on common patterns
UPDATE stocks
SET exchange = CASE
  WHEN exchange IS NULL AND (
    company_name ILIKE '%Apple%' OR company_name ILIKE '%Microsoft%' OR company_name ILIKE '%Amazon%'
    OR company_name ILIKE '%Google%' OR company_name ILIKE '%Meta%' OR company_name ILIKE '%Tesla%'
    OR company_name ILIKE '%Netflix%' OR company_name ILIKE '%Intel%' OR company_name ILIKE '%AMD%'
    OR company_name ILIKE '%NVIDIA%' OR company_name ILIKE '%Cisco%' OR company_name ILIKE '%Oracle%'
  ) THEN 'NASDAQ'
  WHEN exchange IS NULL AND (
    company_name ILIKE '%JPMorgan%' OR company_name ILIKE '%Johnson%Johnson%' OR company_name ILIKE '%Exxon%'
    OR company_name ILIKE '%Procter%' OR company_name ILIKE '%Visa%' OR company_name ILIKE '%Mastercard%'
    OR company_name ILIKE '%Walmart%' OR company_name ILIKE '%Disney%' OR company_name ILIKE '%Boeing%'
  ) THEN 'NYSE'
  WHEN exchange IS NULL THEN 'NYSE'  -- Default for unknowns
  ELSE exchange
END;



-- 5. Set market cap based on company size (estimated)
UPDATE stocks
SET market_cap = CASE
  -- Mega Cap (>$200B)
  WHEN company_name ILIKE '%Apple%' THEN '2.8T'
  WHEN company_name ILIKE '%Microsoft%' THEN '2.4T'
  WHEN company_name ILIKE '%Amazon%' THEN '1.5T'
  WHEN company_name ILIKE '%Google%' OR company_name ILIKE '%Alphabet%' THEN '1.7T'
  WHEN company_name ILIKE '%Tesla%' THEN '800B'
  WHEN company_name ILIKE '%Meta%' OR company_name ILIKE '%Facebook%' THEN '750B'
  WHEN company_name ILIKE '%Berkshire%' THEN '700B'
  WHEN company_name ILIKE '%NVIDIA%' THEN '1.2T'

  -- Large Cap ($10B-$200B)
  WHEN company_name ILIKE '%Netflix%' THEN '180B'
  WHEN company_name ILIKE '%Disney%' THEN '200B'
  WHEN company_name ILIKE '%JPMorgan%' THEN '450B'
  WHEN company_name ILIKE '%Johnson%Johnson%' THEN '420B'
  WHEN company_name ILIKE '%Visa%' THEN '500B'
  WHEN company_name ILIKE '%Mastercard%' THEN '380B'
  WHEN company_name ILIKE '%Procter%' THEN '350B'
  WHEN company_name ILIKE '%Coca-Cola%' THEN '260B'
  WHEN company_name ILIKE '%PepsiCo%' THEN '230B'
  WHEN company_name ILIKE '%Intel%' THEN '200B'
  WHEN company_name ILIKE '%Cisco%' THEN '190B'
  WHEN company_name ILIKE '%Oracle%' THEN '300B'
  WHEN company_name ILIKE '%Salesforce%' THEN '220B'
  WHEN company_name ILIKE '%Adobe%' THEN '240B'
  WHEN company_name ILIKE '%Walmart%' THEN '450B'
  WHEN company_name ILIKE '%Home Depot%' THEN '350B'
  WHEN company_name ILIKE '%Boeing%' THEN '130B'
  WHEN company_name ILIKE '%McDonald%' THEN '200B'
  WHEN company_name ILIKE '%Nike%' THEN '180B'

  -- Mid Cap ($2B-$10B) - ETFs and smaller companies
  WHEN stock_type = 'ETF' AND assets IS NOT NULL THEN assets
  WHEN company_name ILIKE '%REIT%' THEN '5B'
  WHEN sector = 'Real Estate' THEN '3B'
  WHEN sector = 'Utilities' THEN '8B'
  WHEN sector = 'Materials' THEN '6B'

  -- Small Cap (<$2B) - Default for unknowns
  ELSE '1.5B'
END
WHERE market_cap IS NULL;



-- 6. Set asset_class properly
UPDATE stocks
SET asset_class = CASE
  WHEN stock_type = 'ETF' THEN 'ETF'
  WHEN stock_type = 'REIT' THEN 'Real Estate'
  WHEN sector = 'Real Estate' THEN 'Real Estate'
  ELSE 'Equity'
END
WHERE asset_class IS NULL;

-- 7. Update timestamps
UPDATE stocks SET updated_at = NOW();

-- Success message
SELECT 'Stock data cleanup completed successfully!' as message;