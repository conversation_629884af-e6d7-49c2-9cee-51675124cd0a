-- Enhanced Stock Database Schema for Real-time Data
-- Run this in your Supabase SQL Editor

-- 1. Add new columns to existing stocks table for real-time data
ALTER TABLE stocks 
ADD COLUMN IF NOT EXISTS current_price DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS price_change DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS price_change_percent DECIMAL(5,2),
ADD COLUMN IF NOT EXISTS volume BIGINT,
ADD COLUMN IF NOT EXISTS day_high DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS day_low DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS day_open DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS previous_close DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS last_price_update TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS data_source VARCHAR(20) DEFAULT 'manual',
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS api_symbol VARCHAR(20); -- Sometimes different from ticker

-- 2. <PERSON>reate price history table for tracking historical data
CREATE TABLE IF NOT EXISTS stock_price_history (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  stock_id INTEGER REFERENCES stocks(id) ON DELETE CASCADE,
  ticker VARCHAR(10) NOT NULL,
  price DECIMAL(10,2) NOT NULL,
  volume BIGINT,
  high DECIMAL(10,2),
  low DECIMAL(10,2),
  open DECIMAL(10,2),
  close DECIMAL(10,2),
  date DATE NOT NULL,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  data_source VARCHAR(20) DEFAULT 'api',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(stock_id, date)
);

-- 3. Create real-time quotes table for current market data
CREATE TABLE IF NOT EXISTS stock_quotes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  ticker VARCHAR(10) NOT NULL,
  price DECIMAL(10,2) NOT NULL,
  change DECIMAL(10,2),
  change_percent DECIMAL(5,2),
  volume BIGINT,
  high DECIMAL(10,2),
  low DECIMAL(10,2),
  open DECIMAL(10,2),
  previous_close DECIMAL(10,2),
  market_cap BIGINT,
  data_source VARCHAR(20) NOT NULL,
  quote_time TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(ticker, data_source)
);

-- 4. Create API usage tracking table
CREATE TABLE IF NOT EXISTS api_usage_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  api_provider VARCHAR(20) NOT NULL,
  endpoint VARCHAR(100),
  request_count INTEGER DEFAULT 1,
  success_count INTEGER DEFAULT 0,
  error_count INTEGER DEFAULT 0,
  last_request TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  date DATE DEFAULT CURRENT_DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(api_provider, endpoint, date)
);

-- 5. Create market status table
CREATE TABLE IF NOT EXISTS market_status (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  market VARCHAR(10) DEFAULT 'US',
  is_open BOOLEAN NOT NULL,
  status VARCHAR(20) NOT NULL, -- 'open', 'closed', 'premarket', 'afterhours'
  next_open TIMESTAMP WITH TIME ZONE,
  next_close TIMESTAMP WITH TIME ZONE,
  timezone VARCHAR(50) DEFAULT 'America/New_York',
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_stocks_ticker ON stocks(ticker);
CREATE INDEX IF NOT EXISTS idx_stocks_active ON stocks(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_stocks_price_update ON stocks(last_price_update);
CREATE INDEX IF NOT EXISTS idx_stock_quotes_ticker ON stock_quotes(ticker);
CREATE INDEX IF NOT EXISTS idx_stock_quotes_source ON stock_quotes(data_source);
CREATE INDEX IF NOT EXISTS idx_stock_quotes_time ON stock_quotes(quote_time);
CREATE INDEX IF NOT EXISTS idx_price_history_stock_date ON stock_price_history(stock_id, date);
CREATE INDEX IF NOT EXISTS idx_price_history_ticker_date ON stock_price_history(ticker, date);
CREATE INDEX IF NOT EXISTS idx_api_usage_provider_date ON api_usage_log(api_provider, date);

-- 7. Create updated stocks_with_tags view to include real-time data
DROP VIEW IF EXISTS stocks_with_tags;
CREATE VIEW stocks_with_tags AS
SELECT 
  s.*,
  COALESCE(s.metadata->>'tags', '[]')::json as tags,
  sq.price as real_time_price,
  sq.change as real_time_change,
  sq.change_percent as real_time_change_percent,
  sq.volume as real_time_volume,
  sq.quote_time as real_time_updated
FROM stocks s
LEFT JOIN stock_quotes sq ON s.ticker = sq.ticker 
  AND sq.data_source = (
    SELECT data_source 
    FROM stock_quotes sq2 
    WHERE sq2.ticker = s.ticker 
    ORDER BY sq2.quote_time DESC 
    LIMIT 1
  )
WHERE s.is_active = true;

-- 8. Create function to update stock prices
CREATE OR REPLACE FUNCTION update_stock_price(
  p_ticker VARCHAR(10),
  p_price DECIMAL(10,2),
  p_change DECIMAL(10,2) DEFAULT NULL,
  p_change_percent DECIMAL(5,2) DEFAULT NULL,
  p_volume BIGINT DEFAULT NULL,
  p_high DECIMAL(10,2) DEFAULT NULL,
  p_low DECIMAL(10,2) DEFAULT NULL,
  p_open DECIMAL(10,2) DEFAULT NULL,
  p_previous_close DECIMAL(10,2) DEFAULT NULL,
  p_data_source VARCHAR(20) DEFAULT 'api'
)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
BEGIN
  -- Update the stocks table
  UPDATE stocks 
  SET 
    current_price = p_price,
    price_change = p_change,
    price_change_percent = p_change_percent,
    volume = p_volume,
    day_high = p_high,
    day_low = p_low,
    day_open = p_open,
    previous_close = p_previous_close,
    last_price_update = NOW(),
    data_source = p_data_source,
    updated_at = NOW()
  WHERE ticker = p_ticker;

  -- Insert or update the stock_quotes table
  INSERT INTO stock_quotes (
    ticker, price, change, change_percent, volume, 
    high, low, open, previous_close, data_source, quote_time
  )
  VALUES (
    p_ticker, p_price, p_change, p_change_percent, p_volume,
    p_high, p_low, p_open, p_previous_close, p_data_source, NOW()
  )
  ON CONFLICT (ticker, data_source) 
  DO UPDATE SET
    price = EXCLUDED.price,
    change = EXCLUDED.change,
    change_percent = EXCLUDED.change_percent,
    volume = EXCLUDED.volume,
    high = EXCLUDED.high,
    low = EXCLUDED.low,
    open = EXCLUDED.open,
    previous_close = EXCLUDED.previous_close,
    quote_time = EXCLUDED.quote_time,
    updated_at = NOW();

  RETURN TRUE;
END;
$$;

-- 9. Create function to log API usage
CREATE OR REPLACE FUNCTION log_api_usage(
  p_provider VARCHAR(20),
  p_endpoint VARCHAR(100) DEFAULT NULL,
  p_success BOOLEAN DEFAULT TRUE
)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
BEGIN
  INSERT INTO api_usage_log (api_provider, endpoint, success_count, error_count)
  VALUES (
    p_provider, 
    p_endpoint, 
    CASE WHEN p_success THEN 1 ELSE 0 END,
    CASE WHEN p_success THEN 0 ELSE 1 END
  )
  ON CONFLICT (api_provider, endpoint, date)
  DO UPDATE SET
    request_count = api_usage_log.request_count + 1,
    success_count = api_usage_log.success_count + CASE WHEN p_success THEN 1 ELSE 0 END,
    error_count = api_usage_log.error_count + CASE WHEN p_success THEN 0 ELSE 1 END,
    last_request = NOW();

  RETURN TRUE;
END;
$$;

-- 10. Create function to clean old data
CREATE OR REPLACE FUNCTION cleanup_old_stock_data()
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
BEGIN
  -- Delete price history older than 2 years
  DELETE FROM stock_price_history 
  WHERE date < CURRENT_DATE - INTERVAL '2 years';

  -- Delete API usage logs older than 6 months
  DELETE FROM api_usage_log 
  WHERE date < CURRENT_DATE - INTERVAL '6 months';

  -- Delete old stock quotes (keep only latest per source)
  DELETE FROM stock_quotes sq1
  WHERE EXISTS (
    SELECT 1 FROM stock_quotes sq2
    WHERE sq2.ticker = sq1.ticker 
    AND sq2.data_source = sq1.data_source
    AND sq2.quote_time > sq1.quote_time
  );

  RETURN TRUE;
END;
$$;

-- 11. Insert initial market status
INSERT INTO market_status (market, is_open, status, timezone)
VALUES ('US', false, 'closed', 'America/New_York')
ON CONFLICT DO NOTHING;

-- 12. Update existing stocks with API symbols where different
UPDATE stocks SET api_symbol = ticker WHERE api_symbol IS NULL;

-- 13. Set some popular stocks as active for testing
UPDATE stocks 
SET is_active = true 
WHERE ticker IN ('AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'NFLX', 'DIS', 'V');

-- 14. Create RLS policies for new tables (if RLS is enabled)
-- Enable RLS on new tables
ALTER TABLE stock_price_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE stock_quotes ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_usage_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE market_status ENABLE ROW LEVEL SECURITY;

-- Create policies for public read access
CREATE POLICY "Public read access" ON stock_price_history FOR SELECT USING (true);
CREATE POLICY "Public read access" ON stock_quotes FOR SELECT USING (true);
CREATE POLICY "Public read access" ON market_status FOR SELECT USING (true);

-- API usage log should be restricted
CREATE POLICY "Service role access" ON api_usage_log FOR ALL USING (auth.role() = 'service_role');

-- Success message
SELECT 'Enhanced stock schema created successfully! 
New features:
- Real-time price tracking
- Price history storage  
- API usage monitoring
- Market status tracking
- Improved performance indexes
- Data cleanup functions' as message;
