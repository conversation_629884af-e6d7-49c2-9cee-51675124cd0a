/**
 * LLM Portfolio System Status API Endpoint
 * GET /api/portfolio/llm-status
 */

import { NextRequest, NextResponse } from 'next/server'
import { withAuth, addSecurityHeaders } from '@/lib/llm-portfolio/auth-middleware'
import { checkLLMSystemHealth, getLLMSystemStats, getConfigStatus } from '@/lib/llm-portfolio'
import type { AuthContext } from '@/lib/llm-portfolio/auth-middleware'

async function statusHandler(
  request: NextRequest,
  context: AuthContext
): Promise<NextResponse> {
  try {
    // Get system health check
    const healthCheck = await checkLLMSystemHealth()
    
    // Get system statistics
    const stats = await getLLMSystemStats()
    
    // Get configuration status
    const configStatus = getConfigStatus()

    const response = NextResponse.json({
      success: true,
      data: {
        health: healthCheck,
        statistics: stats,
        configuration: configStatus,
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      }
    })

    addSecurityHeaders(response)
    return response

  } catch (error) {
    console.error('Status check error:', error)
    
    const response = NextResponse.json(
      {
        success: false,
        error: 'Failed to get system status',
        message: error.message
      },
      { status: 500 }
    )

    addSecurityHeaders(response)
    return response
  }
}

// Export with minimal auth (allow internal monitoring)
export const GET = withAuth(statusHandler, {
  requireAuth: false,
  rateLimitPerHour: 60
})

// Handle OPTIONS for CORS
export async function OPTIONS() {
  const response = new NextResponse(null, { status: 200 })
  addSecurityHeaders(response)
  return response
}
