import { NextResponse } from 'next/server'
import Stripe from 'stripe'

// Initialize Stripe with your secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2023-10-16',
})

export async function POST(request: Request) {
  try {
    const { priceId, userId, planName } = await request.json()
    
    // Validate required fields
    if (!priceId || !userId) {
      return NextResponse.json(
        { error: 'Price ID and User ID are required' },
        { status: 400 }
      )
    }
    
    // Create or retrieve customer
    let customer
    try {
      // Try to find existing customer by metadata
      const customers = await stripe.customers.list({
        limit: 1,
        expand: ['data'],
      })
      
      const existingCustomer = customers.data.find(
        (c) => c.metadata?.userId === userId
      )
      
      if (existingCustomer) {
        customer = existingCustomer
      } else {
        // Create new customer
        customer = await stripe.customers.create({
          metadata: {
            userId: userId,
          },
        })
      }
    } catch (error) {
      console.error('Error handling customer:', error)
      return NextResponse.json(
        { error: 'Failed to create customer' },
        { status: 500 }
      )
    }
    
    // Create a Stripe checkout session for subscription
    const session = await stripe.checkout.sessions.create({
      customer: customer.id,
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${process.env.NEXT_PUBLIC_BASE_URL}/subscription/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXT_PUBLIC_BASE_URL}/pricing`,
      metadata: {
        userId: userId,
        planName: planName || 'Unknown Plan',
      },
      subscription_data: {
        metadata: {
          userId: userId,
          planName: planName || 'Unknown Plan',
        },
        trial_period_days: 14, // 14-day free trial
      },
      allow_promotion_codes: true,
      billing_address_collection: 'required',
      customer_update: {
        address: 'auto',
        name: 'auto',
      },
    })
    
    return NextResponse.json({
      sessionId: session.id,
      url: session.url,
    })
    
  } catch (error) {
    console.error('Subscription session creation error:', error)
    return NextResponse.json(
      { error: 'Failed to create subscription session' },
      { status: 500 }
    )
  }
}
