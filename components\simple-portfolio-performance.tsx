"use client"

/**
 * Simple Portfolio Performance Component
 * A lightweight, working portfolio performance tracker without endless loops
 */

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { TrendingUp, TrendingDown, RefreshCw } from 'lucide-react'
import { ResponsiveContainer, AreaChart, Area, XAxis, YAxis, Tooltip } from 'recharts'

interface PortfolioData {
  totalValue: number
  totalReturn: number
  totalReturnPercent: number
  dailyChange: number
  dailyChangePercent: number
  chartData: Array<{
    date: string
    value: number
    timestamp: number
  }>
}

interface SimplePortfolioPerformanceProps {
  portfolioId?: string
  period?: '1D' | '1W' | '1M' | '3M' | '1Y'
  onPeriodChange?: (period: string) => void
}

export function SimplePortfolioPerformance({ 
  portfolioId = 'default',
  period = '1M',
  onPeriodChange
}: SimplePortfolioPerformanceProps) {
  const [data, setData] = useState<PortfolioData | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Generate realistic mock data based on period
  const generateMockData = (selectedPeriod: string): PortfolioData => {
    const periods = {
      '1D': { days: 1, baseReturn: 0.5 },
      '1W': { days: 7, baseReturn: 1.2 },
      '1M': { days: 30, baseReturn: 3.4 },
      '3M': { days: 90, baseReturn: 8.7 },
      '1Y': { days: 365, baseReturn: 12.5 }
    }

    const config = periods[selectedPeriod as keyof typeof periods] || periods['1M']
    const initialValue = 10000
    const targetReturn = (config.baseReturn / 100) * initialValue
    const currentValue = initialValue + targetReturn

    // Generate chart data points
    const chartData = []
    let value = initialValue
    const dailyGrowth = targetReturn / config.days

    for (let i = 0; i <= Math.min(config.days, 30); i++) {
      const date = new Date()
      date.setDate(date.getDate() - (config.days - i))
      
      // Add some realistic volatility
      const volatility = (Math.random() - 0.5) * (dailyGrowth * 0.5)
      value += dailyGrowth + volatility
      
      chartData.push({
        date: date.toISOString().split('T')[0],
        value: Math.max(value, initialValue * 0.8), // Don't go below 80% of initial
        timestamp: date.getTime()
      })
    }

    // Ensure the last value matches our target
    if (chartData.length > 0) {
      chartData[chartData.length - 1].value = currentValue
    }

    const previousValue = chartData.length > 1 ? chartData[chartData.length - 2].value : initialValue
    const dailyChange = currentValue - previousValue
    const dailyChangePercent = (dailyChange / previousValue) * 100

    return {
      totalValue: currentValue,
      totalReturn: targetReturn,
      totalReturnPercent: config.baseReturn,
      dailyChange,
      dailyChangePercent,
      chartData
    }
  }

  const fetchData = async () => {
    setLoading(true)
    setError(null)

    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // For now, use mock data
      const mockData = generateMockData(period)
      setData(mockData)
    } catch (err) {
      setError('Failed to load portfolio data')
      console.error('Portfolio fetch error:', err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [period]) // Only re-fetch when period changes

  const handleRefresh = () => {
    fetchData()
  }

  const handlePeriodChange = (newPeriod: string) => {
    if (onPeriodChange) {
      onPeriodChange(newPeriod)
    }
  }

  if (loading) {
    return (
      <Card className="bg-slate-800/50 border-slate-700">
        <CardHeader>
          <CardTitle className="text-emerald-300">Portfolio Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-40">
            <RefreshCw className="h-8 w-8 animate-spin text-slate-400" />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error || !data) {
    return (
      <Card className="bg-slate-800/50 border-slate-700">
        <CardHeader>
          <CardTitle className="text-emerald-300">Portfolio Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-40 text-slate-400">
            <div className="text-center">
              <TrendingUp className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">{error || 'No data available'}</p>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleRefresh}
                className="mt-2"
              >
                Try Again
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  const isPositive = data.totalReturn >= 0

  return (
    <Card className="bg-slate-800/50 border-slate-700">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-emerald-300">Portfolio Performance</CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Performance Stats */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-slate-400">Total Value</p>
            <p className="text-2xl font-bold text-white">
              ${data.totalValue.toLocaleString('en-US', { minimumFractionDigits: 2 })}
            </p>
          </div>
          <div>
            <p className="text-sm text-slate-400">Total Return</p>
            <div className="flex items-center gap-2">
              {isPositive ? (
                <TrendingUp className="h-4 w-4 text-emerald-400" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-400" />
              )}
              <div>
                <p className={`text-lg font-semibold ${isPositive ? 'text-emerald-400' : 'text-red-400'}`}>
                  {isPositive ? '+' : ''}${Math.abs(data.totalReturn).toFixed(2)}
                </p>
                <p className={`text-sm ${isPositive ? 'text-emerald-400' : 'text-red-400'}`}>
                  {isPositive ? '+' : ''}{data.totalReturnPercent.toFixed(2)}%
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Period Selector */}
        <div className="flex gap-2">
          {['1D', '1W', '1M', '3M', '1Y'].map((p) => (
            <Button
              key={p}
              variant={period === p ? "default" : "ghost"}
              size="sm"
              onClick={() => handlePeriodChange(p)}
              className={`text-xs px-2 py-1 ${
                period === p
                  ? "bg-emerald-500 text-white"
                  : "text-slate-400 hover:text-white"
              }`}
            >
              {p}
            </Button>
          ))}
        </div>

        {/* Chart */}
        <div className="h-32">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={data.chartData}>
              <defs>
                <linearGradient id="performanceGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop 
                    offset="5%" 
                    stopColor={isPositive ? "#10b981" : "#ef4444"} 
                    stopOpacity={0.3}
                  />
                  <stop 
                    offset="95%" 
                    stopColor={isPositive ? "#10b981" : "#ef4444"} 
                    stopOpacity={0}
                  />
                </linearGradient>
              </defs>
              <XAxis 
                dataKey="date" 
                axisLine={false}
                tickLine={false}
                tick={false}
              />
              <YAxis hide />
              <Tooltip 
                contentStyle={{
                  backgroundColor: '#1f2937',
                  border: '1px solid #374151',
                  borderRadius: '6px',
                  color: '#f9fafb'
                }}
                formatter={(value: number) => [
                  new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD'
                  }).format(value),
                  'Portfolio Value'
                ]}
                labelFormatter={(label) => {
                  const date = new Date(label)
                  return date.toLocaleDateString('en-US', { 
                    month: 'short', 
                    day: 'numeric',
                    year: 'numeric'
                  })
                }}
              />
              <Area 
                type="monotone" 
                dataKey="value" 
                stroke={isPositive ? "#10b981" : "#ef4444"}
                strokeWidth={2}
                fillOpacity={1} 
                fill="url(#performanceGradient)" 
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        {/* Daily Change */}
        <div className="flex items-center justify-between text-sm">
          <div>
            <span className="text-slate-400">Daily Change: </span>
            <span className={`font-medium ${
              data.dailyChange >= 0 ? "text-emerald-400" : "text-red-400"
            }`}>
              {data.dailyChange >= 0 ? "+" : ""}
              ${Math.abs(data.dailyChange).toFixed(2)} 
              ({data.dailyChange >= 0 ? "+" : ""}{data.dailyChangePercent.toFixed(2)}%)
            </span>
          </div>
          <div className="text-xs text-slate-500">
            Updated {new Date().toLocaleTimeString()}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
