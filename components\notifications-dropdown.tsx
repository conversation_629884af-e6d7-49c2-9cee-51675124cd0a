"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Bell, TrendingUp, Target, Info, CheckCircle, X } from "lucide-react"
import { usePersistentNotifications } from "@/components/persistent-notifications"

export function NotificationsDropdown() {
  const router = useRouter()
  const {
    getRecentNotifications,
    unreadCount,
    markAsRead,
    markAsUnread,
    deleteNotification
  } = usePersistentNotifications()
  
  const recentNotifications = getRecentNotifications(5)

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'portfolio': return { icon: TrendingUp, color: 'text-emerald-400' }
      case 'goal': return { icon: Target, color: 'text-yellow-400' }
      case 'market': return { icon: Info, color: 'text-blue-400' }
      case 'educational': return { icon: CheckCircle, color: 'text-purple-400' }
      case 'system': return { icon: Bell, color: 'text-slate-400' }
      default: return { icon: Bell, color: 'text-slate-400' }
    }
  }

  const getTimeAgo = (date: Date) => {
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) return "Just now"
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    
    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `${diffInHours}h ago`
    
    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays}d ago`
  }

  const handleNotificationClick = (notification: any) => {
    // Mark as read when clicked
    if (!notification.read) {
      markAsRead(notification.id)
    }
    
    // Navigate to action URL if available
    if (notification.actionUrl) {
      router.push(notification.actionUrl)
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
            >
              {unreadCount > 9 ? '9+' : unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80 bg-slate-800 border-slate-700">
        <DropdownMenuLabel className="flex items-center justify-between">
          <span>Notifications</span>
          {unreadCount > 0 && (
            <Badge variant="secondary" className="bg-slate-700">
              {unreadCount} unread
            </Badge>
          )}
        </DropdownMenuLabel>
        <DropdownMenuSeparator className="bg-slate-700" />
        
        {recentNotifications.length === 0 ? (
          <div className="p-4 text-center text-slate-400">
            <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No notifications</p>
          </div>
        ) : (
          <>
            {recentNotifications.map((notification) => {
              const { icon: IconComponent, color } = getNotificationIcon(notification.type)
              
              return (
                <DropdownMenuItem
                  key={notification.id}
                  className={`group p-3 cursor-pointer focus:bg-slate-700 ${
                    !notification.read ? 'bg-slate-700/50' : ''
                  }`}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className="flex items-start gap-3 w-full">
                    <div className={`mt-0.5 ${color}`}>
                      <IconComponent className="h-4 w-4" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <p className={`text-sm font-medium truncate ${
                            notification.read ? 'text-slate-300' : 'text-white'
                          }`}>
                            {notification.title}
                          </p>
                          <p className={`text-xs mt-1 line-clamp-2 ${
                            notification.read ? 'text-slate-500' : 'text-slate-400'
                          }`}>
                            {notification.message}
                          </p>
                          <p className="text-xs text-slate-500 mt-1">
                            {getTimeAgo(notification.createdAt)}
                          </p>
                        </div>
                        <div className="flex gap-1 ml-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 hover:bg-slate-600"
                            onClick={(e) => {
                              e.stopPropagation()
                              if (notification.read) {
                                markAsUnread(notification.id)
                              } else {
                                markAsRead(notification.id)
                              }
                            }}
                            title={notification.read ? "Mark as unread" : "Mark as read"}
                          >
                            {notification.read ? (
                              <Bell className="h-3 w-3" />
                            ) : (
                              <CheckCircle className="h-3 w-3" />
                            )}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 hover:bg-slate-600"
                            onClick={(e) => {
                              e.stopPropagation()
                              deleteNotification(notification.id)
                            }}
                            title="Delete notification"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                      {!notification.read && (
                        <div className="w-2 h-2 bg-emerald-400 rounded-full absolute right-2 top-3" />
                      )}
                    </div>
                  </div>
                </DropdownMenuItem>
              )
            })}
            
            <DropdownMenuSeparator className="bg-slate-700" />
            <DropdownMenuItem
              className="p-3 text-center cursor-pointer focus:bg-slate-700"
              onClick={() => router.push('/notifications')}
            >
              <span className="text-sm text-slate-400 hover:text-white">
                View all notifications
              </span>
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
