/**
 * Configuration for LLM Portfolio Generation System
 */

import type { LLMSystemConfig } from './types'

/**
 * Get LLM system configuration from environment variables
 */
export function getLLMConfig(): LLMSystemConfig {
  return {
    llm: {
      apiKey: process.env.OPENAI_API_KEY || '',
      model: process.env.OPENAI_MODEL || 'gpt-4',
      maxTokens: parseInt(process.env.OPENAI_MAX_TOKENS || '2000'),
      temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.7'),
      maxRetries: parseInt(process.env.LLM_MAX_RETRIES || '3'),
      timeoutMs: 30000
    },
    cache: {
      ttlHours: parseInt(process.env.LLM_CACHE_TTL_HOURS || '24'),
      maxEntries: 1000,
      cleanupIntervalMs: 60 * 60 * 1000 // 1 hour
    },
    rateLimitPerUser: parseInt(process.env.LLM_RATE_LIMIT_PER_USER || '10'),
    enableFallback: process.env.LLM_ENABLE_FALLBACK !== 'false',
    enableAuditLogging: true
  }
}

/**
 * Validate LLM configuration
 */
export function validateLLMConfig(config: LLMSystemConfig): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  // Validate LLM config
  if (!config.llm.apiKey) {
    errors.push('OpenAI API key is required (OPENAI_API_KEY)')
  }

  if (!config.llm.model) {
    errors.push('OpenAI model is required (OPENAI_MODEL)')
  }

  if (config.llm.maxTokens <= 0 || config.llm.maxTokens > 4000) {
    errors.push('Max tokens must be between 1 and 4000')
  }

  if (config.llm.temperature < 0 || config.llm.temperature > 2) {
    errors.push('Temperature must be between 0 and 2')
  }

  if (config.llm.maxRetries < 1 || config.llm.maxRetries > 10) {
    errors.push('Max retries must be between 1 and 10')
  }

  // Validate cache config
  if (config.cache.ttlHours < 1 || config.cache.ttlHours > 168) {
    errors.push('Cache TTL must be between 1 and 168 hours (1 week)')
  }

  if (config.cache.maxEntries < 10 || config.cache.maxEntries > 10000) {
    errors.push('Cache max entries must be between 10 and 10000')
  }

  // Validate rate limiting
  if (config.rateLimitPerUser < 1 || config.rateLimitPerUser > 100) {
    errors.push('Rate limit per user must be between 1 and 100')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Get development/testing configuration
 */
export function getTestLLMConfig(): LLMSystemConfig {
  return {
    llm: {
      apiKey: 'test-key',
      model: 'gpt-3.5-turbo',
      maxTokens: 1000,
      temperature: 0.5,
      maxRetries: 2,
      timeoutMs: 10000
    },
    cache: {
      ttlHours: 1,
      maxEntries: 100,
      cleanupIntervalMs: 5 * 60 * 1000 // 5 minutes
    },
    rateLimitPerUser: 5,
    enableFallback: true,
    enableAuditLogging: false
  }
}

/**
 * Configuration constants
 */
export const LLM_CONFIG_CONSTANTS = {
  DEFAULT_MODEL: 'gpt-4',
  DEFAULT_MAX_TOKENS: 2000,
  DEFAULT_TEMPERATURE: 0.7,
  DEFAULT_CACHE_TTL_HOURS: 24,
  DEFAULT_RATE_LIMIT: 10,
  MIN_CACHE_TTL_HOURS: 1,
  MAX_CACHE_TTL_HOURS: 168,
  MIN_RATE_LIMIT: 1,
  MAX_RATE_LIMIT: 100,
  SUPPORTED_MODELS: [
    'gpt-4',
    'gpt-4-turbo',
    'gpt-3.5-turbo'
  ]
} as const

/**
 * Environment variable names
 */
export const ENV_VARS = {
  OPENAI_API_KEY: 'OPENAI_API_KEY',
  OPENAI_MODEL: 'OPENAI_MODEL',
  OPENAI_MAX_TOKENS: 'OPENAI_MAX_TOKENS',
  OPENAI_TEMPERATURE: 'OPENAI_TEMPERATURE',
  LLM_MAX_RETRIES: 'LLM_MAX_RETRIES',
  LLM_CACHE_TTL_HOURS: 'LLM_CACHE_TTL_HOURS',
  LLM_RATE_LIMIT_PER_USER: 'LLM_RATE_LIMIT_PER_USER',
  LLM_ENABLE_FALLBACK: 'LLM_ENABLE_FALLBACK'
} as const

/**
 * Check if LLM system is properly configured
 */
export function isLLMConfigured(): boolean {
  const config = getLLMConfig()
  const validation = validateLLMConfig(config)
  return validation.isValid
}

/**
 * Get configuration status for debugging
 */
export function getConfigStatus(): {
  configured: boolean
  errors: string[]
  config: Partial<LLMSystemConfig>
} {
  const config = getLLMConfig()
  const validation = validateLLMConfig(config)
  
  return {
    configured: validation.isValid,
    errors: validation.errors,
    config: {
      llm: {
        ...config.llm,
        apiKey: config.llm.apiKey ? '***configured***' : 'missing'
      },
      cache: config.cache,
      rateLimitPerUser: config.rateLimitPerUser,
      enableFallback: config.enableFallback,
      enableAuditLogging: config.enableAuditLogging
    }
  }
}

/**
 * Create a configuration object with overrides
 */
export function createLLMConfig(overrides: Partial<LLMSystemConfig> = {}): LLMSystemConfig {
  const baseConfig = getLLMConfig()
  
  return {
    llm: {
      ...baseConfig.llm,
      ...overrides.llm
    },
    cache: {
      ...baseConfig.cache,
      ...overrides.cache
    },
    rateLimitPerUser: overrides.rateLimitPerUser ?? baseConfig.rateLimitPerUser,
    enableFallback: overrides.enableFallback ?? baseConfig.enableFallback,
    enableAuditLogging: overrides.enableAuditLogging ?? baseConfig.enableAuditLogging
  }
}
