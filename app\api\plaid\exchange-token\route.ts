import { NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Temporary mock implementation until Plaid package is installed
// TODO: Replace with actual Plaid implementation after running npm install

// Initialize Supabase
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''
)

export async function POST(request: Request) {
  try {
    const { publicToken, userId } = await request.json()

    if (!publicToken || !userId) {
      return NextResponse.json(
        { error: 'Public token and user ID are required' },
        { status: 400 }
      )
    }

    // Mock implementation for demo purposes
    // TODO: Replace with actual Plaid API calls after npm install
    const mockAccessToken = 'access-sandbox-' + Math.random().toString(36).substr(2, 9)
    const mockItemId = 'item-' + Math.random().toString(36).substr(2, 9)

    const institution = {
      name: 'Demo Bank',
      institution_id: 'demo_bank'
    }

    const accounts = [
      {
        account_id: 'demo_account_' + Math.random().toString(36).substr(2, 9),
        name: 'Demo Checking Account',
        mask: '1234',
        type: 'depository',
        subtype: 'checking'
      }
    ]
    
    // Store linked accounts in database
    const linkedAccounts = []
    
    for (const account of accounts) {
      // Only store checking and savings accounts
      if (account.subtype === 'checking' || account.subtype === 'savings') {
        const { data, error } = await supabase
          .from('linked_accounts')
          .insert({
            user_id: userId,
            plaid_account_id: account.account_id,
            plaid_item_id: mockItemId,
            plaid_access_token: mockAccessToken, // In production, encrypt this
            account_name: account.name,
            account_mask: account.mask || '',
            account_type: account.type,
            account_subtype: account.subtype || '',
            institution_name: institution.name,
            institution_id: institution.institution_id,
            is_active: true,
            roundups_enabled: true,
          })
          .select()
          .single()
        
        if (error) {
          console.error('Error storing linked account:', error)
          continue
        }
        
        linkedAccounts.push(data)
      }
    }
    
    // Create default roundup settings for the user if they don't exist
    const { error: settingsError } = await supabase
      .from('roundup_settings')
      .upsert({
        user_id: userId,
        multiplier: 1.0,
        minimum_roundup: 0.01,
        maximum_roundup: 5.00,
        batch_threshold: 5.00,
        auto_invest: true,
        categories_enabled: [],
        categories_disabled: [],
      })
    
    if (settingsError) {
      console.error('Error creating roundup settings:', settingsError)
    }
    
    return NextResponse.json({
      success: true,
      accounts: linkedAccounts,
      institution: {
        name: institution.name,
        id: institution.institution_id,
      },
    })
    
  } catch (error) {
    console.error('Error exchanging Plaid token:', error)
    return NextResponse.json(
      { error: 'Failed to link account' },
      { status: 500 }
    )
  }
}
