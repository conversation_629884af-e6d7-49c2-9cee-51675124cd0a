-- Portfolio Performance Tracking & Investment System Enhancement
-- This migration adds comprehensive portfolio tracking, investment transactions, and user capital management

-- =============================================================================
-- SECTION 1: ENHANCED USER CAPITAL STORAGE SYSTEM
-- =============================================================================

-- Extend user_balances table to support different balance types
ALTER TABLE user_balances
ADD COLUMN IF NOT EXISTS balance_type VARCHAR(20) DEFAULT 'cash' CHECK (balance_type IN ('cash', 'invested', 'pending')),
ADD COLUMN IF NOT EXISTS available_for_investment DECIMAL(12, 2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS total_invested DECIMAL(12, 2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS pending_transactions DECIMAL(12, 2) DEFAULT 0.00;

-- Create wallet transactions table for tracking all money movements
CREATE TABLE IF NOT EXISTS wallet_transactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('deposit', 'withdrawal', 'investment', 'dividend', 'fee', 'refund')),
    amount DECIMAL(12, 2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD' NOT NULL,
    description TEXT,
    reference_id UUID, -- Links to investment transactions, payments, etc.
    reference_type VARCHAR(50), -- 'investment_transaction', 'payment', 'roundup', etc.
    status VARCHAR(20) DEFAULT 'completed' CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
    processed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for wallet transactions
CREATE INDEX IF NOT EXISTS idx_wallet_transactions_user_id ON wallet_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_wallet_transactions_type ON wallet_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_wallet_transactions_reference ON wallet_transactions(reference_id, reference_type);
CREATE INDEX IF NOT EXISTS idx_wallet_transactions_date ON wallet_transactions(processed_at);

-- =============================================================================
-- SECTION 2: INVESTMENT TRANSACTION SYSTEM
-- =============================================================================

-- Enhance the existing transactions table for comprehensive investment tracking
-- First, check if the table exists and add missing columns
DO $$
BEGIN
    -- Add columns if they don't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'transactions' AND column_name = 'order_type') THEN
        ALTER TABLE transactions ADD COLUMN order_type VARCHAR(20) DEFAULT 'market' CHECK (order_type IN ('market', 'limit', 'stop_loss'));
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'transactions' AND column_name = 'status') THEN
        ALTER TABLE transactions ADD COLUMN status VARCHAR(20) DEFAULT 'completed' CHECK (status IN ('pending', 'completed', 'cancelled', 'failed'));
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'transactions' AND column_name = 'executed_at') THEN
        ALTER TABLE transactions ADD COLUMN executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'transactions' AND column_name = 'notes') THEN
        ALTER TABLE transactions ADD COLUMN notes TEXT;
    END IF;
END $$;

-- Enhance portfolio_holdings table for better tracking
ALTER TABLE portfolio_holdings
ADD COLUMN IF NOT EXISTS total_cost DECIMAL(15,2) GENERATED ALWAYS AS (shares * average_cost) STORED,
ADD COLUMN IF NOT EXISTS unrealized_gain_loss DECIMAL(15,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS unrealized_gain_loss_percent DECIMAL(8,4) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS last_price_update TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS target_percentage DECIMAL(5,2) DEFAULT 0.00; -- Target allocation percentage

-- Create portfolio performance cache table for efficient queries
CREATE TABLE IF NOT EXISTS portfolio_performance_cache (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    portfolio_id UUID REFERENCES portfolios(id) ON DELETE CASCADE,
    cache_key VARCHAR(100) NOT NULL, -- e.g., 'daily_performance', 'weekly_performance'
    cache_data JSONB NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(portfolio_id, cache_key)
);

-- Create index for cache lookups
CREATE INDEX IF NOT EXISTS idx_portfolio_cache_lookup ON portfolio_performance_cache(portfolio_id, cache_key, expires_at);

-- Add portfolio performance tracking fields
ALTER TABLE portfolios
ADD COLUMN IF NOT EXISTS initial_value DECIMAL(15,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS performance_start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS last_performance_update TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS cache_ttl_minutes INTEGER DEFAULT 1; -- Configurable cache TTL

-- Update existing portfolios to have a performance start date
UPDATE portfolios
SET performance_start_date = created_at,
    initial_value = 0.00
WHERE performance_start_date IS NULL;

-- =============================================================================
-- SECTION 3: DATABASE FUNCTIONS FOR TRANSACTION PROCESSING
-- =============================================================================

-- Function to process investment transactions (buy/sell)
CREATE OR REPLACE FUNCTION process_investment_transaction(
    p_user_id UUID,
    p_portfolio_id UUID,
    p_symbol VARCHAR(10),
    p_transaction_type VARCHAR(10),
    p_shares DECIMAL(15,6),
    p_price DECIMAL(10,2),
    p_fees DECIMAL(10,2) DEFAULT 0
)
RETURNS UUID AS $$
DECLARE
    v_transaction_id UUID;
    v_total_amount DECIMAL(15,2);
    v_current_shares DECIMAL(15,6) := 0;
    v_current_avg_cost DECIMAL(10,2) := 0;
    v_new_avg_cost DECIMAL(10,2);
    v_new_shares DECIMAL(15,6);
BEGIN
    -- Calculate total transaction amount
    v_total_amount := (p_shares * p_price) + p_fees;

    -- Get current holdings
    SELECT shares, average_cost INTO v_current_shares, v_current_avg_cost
    FROM portfolio_holdings
    WHERE portfolio_id = p_portfolio_id AND symbol = p_symbol;

    -- If no existing holdings, set defaults
    IF v_current_shares IS NULL THEN
        v_current_shares := 0;
        v_current_avg_cost := 0;
    END IF;

    -- Calculate new holdings based on transaction type
    IF p_transaction_type = 'buy' THEN
        -- Check if user has sufficient balance
        IF NOT EXISTS (
            SELECT 1 FROM user_balances
            WHERE user_id = p_user_id
            AND balance >= v_total_amount
        ) THEN
            RAISE EXCEPTION 'Insufficient balance for transaction';
        END IF;

        -- Calculate new average cost and shares
        v_new_shares := v_current_shares + p_shares;
        v_new_avg_cost := ((v_current_shares * v_current_avg_cost) + (p_shares * p_price) + p_fees) / v_new_shares;

        -- Update user balance
        UPDATE user_balances
        SET balance = balance - v_total_amount,
            total_invested = total_invested + v_total_amount,
            updated_at = NOW()
        WHERE user_id = p_user_id;

    ELSIF p_transaction_type = 'sell' THEN
        -- Check if user has sufficient shares
        IF v_current_shares < p_shares THEN
            RAISE EXCEPTION 'Insufficient shares for sale';
        END IF;

        v_new_shares := v_current_shares - p_shares;
        -- Keep the same average cost for remaining shares
        v_new_avg_cost := v_current_avg_cost;

        -- Update user balance (add proceeds minus fees)
        UPDATE user_balances
        SET balance = balance + (p_shares * p_price) - p_fees,
            total_invested = total_invested - (p_shares * v_current_avg_cost),
            updated_at = NOW()
        WHERE user_id = p_user_id;
    END IF;

    -- Insert transaction record
    INSERT INTO transactions (
        user_id, portfolio_id, symbol, transaction_type, shares, price,
        total_amount, fees, status, executed_at
    ) VALUES (
        p_user_id, p_portfolio_id, p_symbol, p_transaction_type, p_shares,
        p_price, v_total_amount, p_fees, 'completed', NOW()
    ) RETURNING id INTO v_transaction_id;

    -- Update or insert portfolio holdings
    INSERT INTO portfolio_holdings (portfolio_id, symbol, shares, average_cost, current_price, updated_at)
    VALUES (p_portfolio_id, p_symbol, v_new_shares, v_new_avg_cost, p_price, NOW())
    ON CONFLICT (portfolio_id, symbol)
    DO UPDATE SET
        shares = v_new_shares,
        average_cost = v_new_avg_cost,
        current_price = p_price,
        updated_at = NOW();

    -- Remove holding if shares become zero
    IF v_new_shares = 0 THEN
        DELETE FROM portfolio_holdings
        WHERE portfolio_id = p_portfolio_id AND symbol = p_symbol;
    END IF;

    -- Insert wallet transaction record
    INSERT INTO wallet_transactions (
        user_id, transaction_type, amount, description, reference_id, reference_type
    ) VALUES (
        p_user_id,
        CASE WHEN p_transaction_type = 'buy' THEN 'investment' ELSE 'dividend' END,
        v_total_amount,
        p_transaction_type || ' ' || p_shares || ' shares of ' || p_symbol,
        v_transaction_id,
        'investment_transaction'
    );

    -- Invalidate portfolio cache
    DELETE FROM portfolio_performance_cache WHERE portfolio_id = p_portfolio_id;

    RETURN v_transaction_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to calculate and update portfolio total value
CREATE OR REPLACE FUNCTION calculate_portfolio_value(p_portfolio_id UUID)
RETURNS DECIMAL(15,2) AS $$
DECLARE
    v_total_value DECIMAL(15,2) := 0;
    v_cash_balance DECIMAL(15,2) := 0;
    v_user_id UUID;
BEGIN
    -- Get portfolio owner
    SELECT user_id INTO v_user_id FROM portfolios WHERE id = p_portfolio_id;

    -- Calculate total value from holdings
    SELECT COALESCE(SUM(shares * COALESCE(current_price, average_cost)), 0)
    INTO v_total_value
    FROM portfolio_holdings
    WHERE portfolio_id = p_portfolio_id;

    -- Add cash balance (available for investment)
    SELECT COALESCE(balance, 0) INTO v_cash_balance
    FROM user_balances
    WHERE user_id = v_user_id AND balance_type = 'cash';

    v_total_value := v_total_value + v_cash_balance;

    -- Update portfolio total value
    UPDATE portfolios
    SET total_value = v_total_value, updated_at = NOW()
    WHERE id = p_portfolio_id;

    RETURN v_total_value;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get cached portfolio performance or calculate if expired
CREATE OR REPLACE FUNCTION get_cached_portfolio_performance(
    p_portfolio_id UUID,
    p_cache_key VARCHAR(100) DEFAULT 'daily_performance'
)
RETURNS JSONB AS $$
DECLARE
    v_cache_data JSONB;
    v_ttl_minutes INTEGER;
    v_portfolio_data JSONB;
    v_total_value DECIMAL(15,2);
    v_initial_value DECIMAL(15,2);
    v_performance_data JSONB;
BEGIN
    -- Get cache TTL from portfolio settings
    SELECT cache_ttl_minutes INTO v_ttl_minutes
    FROM portfolios WHERE id = p_portfolio_id;

    -- Try to get cached data
    SELECT cache_data INTO v_cache_data
    FROM portfolio_performance_cache
    WHERE portfolio_id = p_portfolio_id
    AND cache_key = p_cache_key
    AND expires_at > NOW();

    -- If cache hit, return cached data
    IF v_cache_data IS NOT NULL THEN
        RETURN v_cache_data;
    END IF;

    -- Cache miss - calculate fresh data
    SELECT total_value, initial_value INTO v_total_value, v_initial_value
    FROM portfolios WHERE id = p_portfolio_id;

    -- Recalculate portfolio value
    v_total_value := calculate_portfolio_value(p_portfolio_id);

    -- Build performance data
    v_performance_data := jsonb_build_object(
        'portfolio_id', p_portfolio_id,
        'current_value', v_total_value,
        'initial_value', v_initial_value,
        'total_return', v_total_value - v_initial_value,
        'total_return_percent',
        CASE
            WHEN v_initial_value > 0 THEN
                ROUND(((v_total_value - v_initial_value) / v_initial_value * 100)::numeric, 4)
            ELSE 0
        END,
        'last_updated', NOW(),
        'cache_key', p_cache_key
    );

    -- Store in cache
    INSERT INTO portfolio_performance_cache (
        portfolio_id, cache_key, cache_data, expires_at
    ) VALUES (
        p_portfolio_id, p_cache_key, v_performance_data,
        NOW() + (v_ttl_minutes || ' minutes')::INTERVAL
    )
    ON CONFLICT (portfolio_id, cache_key)
    DO UPDATE SET
        cache_data = EXCLUDED.cache_data,
        expires_at = EXCLUDED.expires_at,
        updated_at = NOW();

    RETURN v_performance_data;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create portfolio performance history table for tracking daily performance
CREATE TABLE IF NOT EXISTS portfolio_performance_history (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  portfolio_id UUID REFERENCES portfolios(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  total_value DECIMAL(15,2) NOT NULL,
  daily_return DECIMAL(15,2) DEFAULT 0,
  daily_return_percent DECIMAL(8,4) DEFAULT 0,
  total_return DECIMAL(15,2) DEFAULT 0,
  total_return_percent DECIMAL(8,4) DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(portfolio_id, date)
);

-- Create index for faster performance queries
CREATE INDEX IF NOT EXISTS idx_portfolio_performance_portfolio_date 
ON portfolio_performance_history(portfolio_id, date);

-- Create a view for easy portfolio performance queries
CREATE OR REPLACE VIEW portfolio_performance_summary AS
SELECT 
  p.id as portfolio_id,
  p.user_id,
  p.name as portfolio_name,
  p.initial_value,
  p.performance_start_date,
  p.total_value as current_value,
  (p.total_value - p.initial_value) as total_return,
  CASE 
    WHEN p.initial_value > 0 THEN 
      ((p.total_value - p.initial_value) / p.initial_value * 100)
    ELSE 0 
  END as total_return_percent,
  EXTRACT(DAYS FROM (NOW() - p.performance_start_date)) as days_since_creation,
  p.created_at,
  p.updated_at
FROM portfolios p;

-- Function to calculate portfolio performance for a date range
CREATE OR REPLACE FUNCTION get_portfolio_performance(
  p_portfolio_id UUID,
  p_start_date DATE DEFAULT NULL,
  p_end_date DATE DEFAULT NULL
)
RETURNS TABLE (
  date DATE,
  total_value DECIMAL(15,2),
  daily_return DECIMAL(15,2),
  daily_return_percent DECIMAL(8,4),
  total_return DECIMAL(15,2),
  total_return_percent DECIMAL(8,4)
) AS $$
BEGIN
  -- Set default dates if not provided
  IF p_start_date IS NULL THEN
    SELECT performance_start_date::DATE INTO p_start_date 
    FROM portfolios WHERE id = p_portfolio_id;
  END IF;
  
  IF p_end_date IS NULL THEN
    p_end_date := CURRENT_DATE;
  END IF;

  RETURN QUERY
  SELECT 
    pph.date,
    pph.total_value,
    pph.daily_return,
    pph.daily_return_percent,
    pph.total_return,
    pph.total_return_percent
  FROM portfolio_performance_history pph
  WHERE pph.portfolio_id = p_portfolio_id
    AND pph.date >= p_start_date
    AND pph.date <= p_end_date
  ORDER BY pph.date;
END;
$$ LANGUAGE plpgsql;

-- Enhanced function to update portfolio performance with caching
CREATE OR REPLACE FUNCTION update_portfolio_performance(p_portfolio_id UUID)
RETURNS VOID AS $$
DECLARE
  v_current_value DECIMAL(15,2);
  v_initial_value DECIMAL(15,2);
  v_previous_value DECIMAL(15,2);
  v_daily_return DECIMAL(15,2);
  v_daily_return_percent DECIMAL(8,4);
  v_total_return DECIMAL(15,2);
  v_total_return_percent DECIMAL(8,4);
BEGIN
  -- Recalculate current portfolio value
  v_current_value := calculate_portfolio_value(p_portfolio_id);

  -- Get initial value
  SELECT initial_value INTO v_initial_value
  FROM portfolios WHERE id = p_portfolio_id;

  -- Get previous day's value
  SELECT total_value INTO v_previous_value
  FROM portfolio_performance_history
  WHERE portfolio_id = p_portfolio_id
    AND date = CURRENT_DATE - INTERVAL '1 day';

  -- If no previous value, use initial value
  IF v_previous_value IS NULL THEN
    v_previous_value := v_initial_value;
  END IF;

  -- Calculate returns
  v_daily_return := v_current_value - v_previous_value;
  v_daily_return_percent := CASE
    WHEN v_previous_value > 0 THEN (v_daily_return / v_previous_value * 100)
    ELSE 0
  END;

  v_total_return := v_current_value - v_initial_value;
  v_total_return_percent := CASE
    WHEN v_initial_value > 0 THEN (v_total_return / v_initial_value * 100)
    ELSE 0
  END;

  -- Insert or update today's performance
  INSERT INTO portfolio_performance_history (
    portfolio_id, date, total_value, daily_return, daily_return_percent,
    total_return, total_return_percent
  ) VALUES (
    p_portfolio_id, CURRENT_DATE, v_current_value, v_daily_return,
    v_daily_return_percent, v_total_return, v_total_return_percent
  )
  ON CONFLICT (portfolio_id, date)
  DO UPDATE SET
    total_value = EXCLUDED.total_value,
    daily_return = EXCLUDED.daily_return,
    daily_return_percent = EXCLUDED.daily_return_percent,
    total_return = EXCLUDED.total_return,
    total_return_percent = EXCLUDED.total_return_percent,
    created_at = NOW();

  -- Update portfolio's last performance update timestamp
  UPDATE portfolios
  SET last_performance_update = NOW()
  WHERE id = p_portfolio_id;

  -- Clear cache to force refresh
  DELETE FROM portfolio_performance_cache WHERE portfolio_id = p_portfolio_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================================================
-- SECTION 4: SECURITY POLICIES AND PERMISSIONS
-- =============================================================================

-- Enable RLS on new tables (with existence checks)
DO $$
BEGIN
    -- Enable RLS on wallet_transactions if it exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'wallet_transactions') THEN
        ALTER TABLE wallet_transactions ENABLE ROW LEVEL SECURITY;
    END IF;

    -- Enable RLS on portfolio_performance_cache if it exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'portfolio_performance_cache') THEN
        ALTER TABLE portfolio_performance_cache ENABLE ROW LEVEL SECURITY;
    END IF;

    -- Enable RLS on portfolio_performance_history if it exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'portfolio_performance_history') THEN
        ALTER TABLE portfolio_performance_history ENABLE ROW LEVEL SECURITY;
    END IF;
END $$;

-- Wallet transactions policies (with existence checks)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'wallet_transactions'
        AND policyname = 'Users can view their own wallet transactions'
    ) THEN
        CREATE POLICY "Users can view their own wallet transactions" ON wallet_transactions
          FOR SELECT USING (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'wallet_transactions'
        AND policyname = 'Users can insert their own wallet transactions'
    ) THEN
        CREATE POLICY "Users can insert their own wallet transactions" ON wallet_transactions
          FOR INSERT WITH CHECK (auth.uid() = user_id);
    END IF;
END $$;

-- Portfolio performance cache policies (with existence checks)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'portfolio_performance_cache'
        AND policyname = 'Users can view their own portfolio cache'
    ) THEN
        CREATE POLICY "Users can view their own portfolio cache" ON portfolio_performance_cache
          FOR SELECT USING (
            portfolio_id IN (
              SELECT id FROM portfolios WHERE user_id = auth.uid()
            )
          );
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'portfolio_performance_cache'
        AND policyname = 'Users can manage their own portfolio cache'
    ) THEN
        CREATE POLICY "Users can manage their own portfolio cache" ON portfolio_performance_cache
          FOR ALL USING (
            portfolio_id IN (
              SELECT id FROM portfolios WHERE user_id = auth.uid()
            )
          );
    END IF;
END $$;

-- Portfolio performance history policies (with existence checks)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'portfolio_performance_history'
        AND policyname = 'Users can view their own portfolio performance'
    ) THEN
        CREATE POLICY "Users can view their own portfolio performance" ON portfolio_performance_history
          FOR SELECT USING (
            portfolio_id IN (
              SELECT id FROM portfolios WHERE user_id = auth.uid()
            )
          );
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'portfolio_performance_history'
        AND policyname = 'Users can insert their own portfolio performance'
    ) THEN
        CREATE POLICY "Users can insert their own portfolio performance" ON portfolio_performance_history
          FOR INSERT WITH CHECK (
            portfolio_id IN (
              SELECT id FROM portfolios WHERE user_id = auth.uid()
            )
          );
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'portfolio_performance_history'
        AND policyname = 'Users can update their own portfolio performance'
    ) THEN
        CREATE POLICY "Users can update their own portfolio performance" ON portfolio_performance_history
          FOR UPDATE USING (
            portfolio_id IN (
              SELECT id FROM portfolios WHERE user_id = auth.uid()
            )
          );
    END IF;
END $$;

-- Grant necessary permissions
GRANT SELECT ON portfolio_performance_summary TO authenticated;
GRANT EXECUTE ON FUNCTION get_portfolio_performance(UUID, DATE, DATE) TO authenticated;
GRANT EXECUTE ON FUNCTION update_portfolio_performance(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION process_investment_transaction(UUID, UUID, VARCHAR, VARCHAR, DECIMAL, DECIMAL, DECIMAL) TO authenticated;
GRANT EXECUTE ON FUNCTION calculate_portfolio_value(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_cached_portfolio_performance(UUID, VARCHAR) TO authenticated;

-- Insert sample performance data for existing portfolios (optional)
-- This creates a baseline performance entry for today
INSERT INTO portfolio_performance_history (portfolio_id, date, total_value, daily_return, daily_return_percent, total_return, total_return_percent)
SELECT 
  id as portfolio_id,
  CURRENT_DATE as date,
  total_value,
  0 as daily_return,
  0 as daily_return_percent,
  (total_value - initial_value) as total_return,
  CASE 
    WHEN initial_value > 0 THEN ((total_value - initial_value) / initial_value * 100)
    ELSE 0 
  END as total_return_percent
FROM portfolios
WHERE id NOT IN (
  SELECT DISTINCT portfolio_id 
  FROM portfolio_performance_history 
  WHERE date = CURRENT_DATE
);

-- =============================================================================
-- SECTION 5: DOCUMENTATION AND COMMENTS
-- =============================================================================

-- Table comments
COMMENT ON TABLE wallet_transactions IS 'Tracks all user money movements including deposits, withdrawals, and investments';
COMMENT ON TABLE portfolio_performance_cache IS 'Caches portfolio performance calculations with configurable TTL';
COMMENT ON TABLE portfolio_performance_history IS 'Tracks daily portfolio performance metrics';

-- Column comments
COMMENT ON COLUMN user_balances.balance_type IS 'Type of balance: cash, invested, or pending';
COMMENT ON COLUMN user_balances.available_for_investment IS 'Cash available for making investments';
COMMENT ON COLUMN user_balances.total_invested IS 'Total amount currently invested in securities';
COMMENT ON COLUMN portfolios.initial_value IS 'Starting value of the portfolio for performance calculations';
COMMENT ON COLUMN portfolios.performance_start_date IS 'Date from which to start tracking performance';
COMMENT ON COLUMN portfolios.cache_ttl_minutes IS 'Cache time-to-live in minutes for performance calculations';

-- Function comments
COMMENT ON FUNCTION process_investment_transaction(UUID, UUID, VARCHAR, VARCHAR, DECIMAL, DECIMAL, DECIMAL) IS 'Processes buy/sell transactions with automatic portfolio updates';
COMMENT ON FUNCTION calculate_portfolio_value(UUID) IS 'Calculates and updates total portfolio value including cash and holdings';
COMMENT ON FUNCTION get_cached_portfolio_performance(UUID, VARCHAR) IS 'Returns cached portfolio performance or calculates fresh if expired';
COMMENT ON FUNCTION get_portfolio_performance(UUID, DATE, DATE) IS 'Retrieves portfolio performance data for a date range';
COMMENT ON FUNCTION update_portfolio_performance(UUID) IS 'Updates portfolio performance metrics for the current date';

-- =============================================================================
-- SECTION 6: ADDITIONAL HELPER FUNCTIONS
-- =============================================================================

-- Function to get user's available balance for investments
CREATE OR REPLACE FUNCTION get_available_investment_balance(p_user_id UUID)
RETURNS DECIMAL(12,2) AS $$
DECLARE
    v_balance DECIMAL(12,2) := 0;
BEGIN
    SELECT COALESCE(balance, 0) INTO v_balance
    FROM user_balances
    WHERE user_id = p_user_id AND balance_type = 'cash';

    RETURN v_balance;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's portfolio summary
CREATE OR REPLACE FUNCTION get_user_portfolio_summary(p_user_id UUID)
RETURNS TABLE (
    portfolio_id UUID,
    portfolio_name VARCHAR(100),
    total_value DECIMAL(15,2),
    total_return DECIMAL(15,2),
    total_return_percent DECIMAL(8,4),
    holdings_count BIGINT,
    last_updated TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id,
        p.name,
        p.total_value,
        (p.total_value - p.initial_value) as total_return,
        CASE
            WHEN p.initial_value > 0 THEN
                ((p.total_value - p.initial_value) / p.initial_value * 100)::DECIMAL(8,4)
            ELSE 0::DECIMAL(8,4)
        END as total_return_percent,
        COALESCE(h.holdings_count, 0) as holdings_count,
        p.updated_at
    FROM portfolios p
    LEFT JOIN (
        SELECT portfolio_id, COUNT(*) as holdings_count
        FROM portfolio_holdings
        WHERE shares > 0
        GROUP BY portfolio_id
    ) h ON p.id = h.portfolio_id
    WHERE p.user_id = p_user_id
    ORDER BY p.created_at;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate transaction before processing
CREATE OR REPLACE FUNCTION validate_investment_transaction(
    p_user_id UUID,
    p_portfolio_id UUID,
    p_symbol VARCHAR(10),
    p_transaction_type VARCHAR(10),
    p_shares DECIMAL(15,6),
    p_price DECIMAL(10,2)
)
RETURNS JSONB AS $$
DECLARE
    v_validation_result JSONB;
    v_user_balance DECIMAL(12,2);
    v_current_shares DECIMAL(15,6) := 0;
    v_total_cost DECIMAL(15,2);
    v_errors TEXT[] := '{}';
BEGIN
    -- Basic validation
    IF p_shares <= 0 THEN
        v_errors := array_append(v_errors, 'Shares must be greater than 0');
    END IF;

    IF p_price <= 0 THEN
        v_errors := array_append(v_errors, 'Price must be greater than 0');
    END IF;

    IF p_transaction_type NOT IN ('buy', 'sell') THEN
        v_errors := array_append(v_errors, 'Transaction type must be buy or sell');
    END IF;

    -- Check if portfolio belongs to user
    IF NOT EXISTS (SELECT 1 FROM portfolios WHERE id = p_portfolio_id AND user_id = p_user_id) THEN
        v_errors := array_append(v_errors, 'Portfolio not found or access denied');
    END IF;

    -- Transaction-specific validation
    IF p_transaction_type = 'buy' THEN
        v_total_cost := p_shares * p_price;
        v_user_balance := get_available_investment_balance(p_user_id);

        IF v_user_balance < v_total_cost THEN
            v_errors := array_append(v_errors, 'Insufficient balance for purchase');
        END IF;

    ELSIF p_transaction_type = 'sell' THEN
        SELECT COALESCE(shares, 0) INTO v_current_shares
        FROM portfolio_holdings
        WHERE portfolio_id = p_portfolio_id AND symbol = p_symbol;

        IF v_current_shares < p_shares THEN
            v_errors := array_append(v_errors, 'Insufficient shares for sale');
        END IF;
    END IF;

    -- Build result
    v_validation_result := jsonb_build_object(
        'valid', array_length(v_errors, 1) IS NULL,
        'errors', v_errors,
        'user_balance', v_user_balance,
        'current_shares', v_current_shares,
        'estimated_cost', p_shares * p_price
    );

    RETURN v_validation_result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions for new functions
GRANT EXECUTE ON FUNCTION get_available_investment_balance(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_portfolio_summary(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION validate_investment_transaction(UUID, UUID, VARCHAR, VARCHAR, DECIMAL, DECIMAL) TO authenticated;

-- =============================================================================
-- SECTION 7: BACKGROUND JOBS AND CACHE MANAGEMENT
-- =============================================================================

-- Function to refresh all expired portfolio caches
CREATE OR REPLACE FUNCTION refresh_expired_portfolio_caches()
RETURNS INTEGER AS $$
DECLARE
    v_portfolio_record RECORD;
    v_refreshed_count INTEGER := 0;
BEGIN
    -- Find all portfolios with expired caches or no cache
    FOR v_portfolio_record IN
        SELECT DISTINCT p.id as portfolio_id
        FROM portfolios p
        LEFT JOIN portfolio_performance_cache ppc ON p.id = ppc.portfolio_id
        WHERE ppc.expires_at IS NULL OR ppc.expires_at <= NOW()
    LOOP
        -- Refresh cache for this portfolio
        PERFORM get_cached_portfolio_performance(v_portfolio_record.portfolio_id, 'daily_performance');
        v_refreshed_count := v_refreshed_count + 1;
    END LOOP;

    RETURN v_refreshed_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clean up old cache entries
CREATE OR REPLACE FUNCTION cleanup_old_portfolio_caches(p_days_old INTEGER DEFAULT 7)
RETURNS INTEGER AS $$
DECLARE
    v_deleted_count INTEGER;
BEGIN
    DELETE FROM portfolio_performance_cache
    WHERE created_at < NOW() - (p_days_old || ' days')::INTERVAL;

    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;
    RETURN v_deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update cache TTL for a portfolio
CREATE OR REPLACE FUNCTION update_portfolio_cache_ttl(
    p_portfolio_id UUID,
    p_ttl_minutes INTEGER
)
RETURNS VOID AS $$
BEGIN
    UPDATE portfolios
    SET cache_ttl_minutes = p_ttl_minutes
    WHERE id = p_portfolio_id;

    -- Clear existing cache to force refresh with new TTL
    DELETE FROM portfolio_performance_cache
    WHERE portfolio_id = p_portfolio_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get cache statistics
CREATE OR REPLACE FUNCTION get_cache_statistics()
RETURNS TABLE (
    total_cached_portfolios BIGINT,
    expired_caches BIGINT,
    cache_hit_potential DECIMAL(5,2),
    oldest_cache TIMESTAMP WITH TIME ZONE,
    newest_cache TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        COUNT(*) as total_cached_portfolios,
        COUNT(CASE WHEN expires_at <= NOW() THEN 1 END) as expired_caches,
        CASE
            WHEN COUNT(*) > 0 THEN
                (COUNT(CASE WHEN expires_at > NOW() THEN 1 END)::DECIMAL / COUNT(*) * 100)
            ELSE 0
        END as cache_hit_potential,
        MIN(created_at) as oldest_cache,
        MAX(created_at) as newest_cache
    FROM portfolio_performance_cache;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions for cache management functions
GRANT EXECUTE ON FUNCTION refresh_expired_portfolio_caches() TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_old_portfolio_caches(INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION update_portfolio_cache_ttl(UUID, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION get_cache_statistics() TO authenticated;

-- =============================================================================
-- SECTION 8: DATABASE TRIGGERS FOR AUTOMATION
-- =============================================================================

-- Trigger function to update portfolio value when holdings change
CREATE OR REPLACE FUNCTION trigger_update_portfolio_value()
RETURNS TRIGGER AS $$
BEGIN
    -- Update portfolio total value when holdings change
    PERFORM calculate_portfolio_value(COALESCE(NEW.portfolio_id, OLD.portfolio_id));

    -- Clear cache for affected portfolio
    DELETE FROM portfolio_performance_cache
    WHERE portfolio_id = COALESCE(NEW.portfolio_id, OLD.portfolio_id);

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers for portfolio_holdings table
DROP TRIGGER IF EXISTS trigger_portfolio_holdings_update ON portfolio_holdings;
CREATE TRIGGER trigger_portfolio_holdings_update
    AFTER INSERT OR UPDATE OR DELETE ON portfolio_holdings
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_portfolio_value();

-- Trigger function to update timestamps
CREATE OR REPLACE FUNCTION trigger_update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create timestamp triggers
DROP TRIGGER IF EXISTS trigger_user_balances_timestamp ON user_balances;
CREATE TRIGGER trigger_user_balances_timestamp
    BEFORE UPDATE ON user_balances
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_timestamp();

DROP TRIGGER IF EXISTS trigger_wallet_transactions_timestamp ON wallet_transactions;
CREATE TRIGGER trigger_wallet_transactions_timestamp
    BEFORE UPDATE ON wallet_transactions
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_timestamp();

DROP TRIGGER IF EXISTS trigger_portfolio_holdings_timestamp ON portfolio_holdings;
CREATE TRIGGER trigger_portfolio_holdings_timestamp
    BEFORE UPDATE ON portfolio_holdings
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_timestamp();

-- Trigger function to validate balance changes
CREATE OR REPLACE FUNCTION trigger_validate_balance_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Prevent negative balances (except for specific cases)
    IF NEW.balance < 0 AND NEW.balance_type = 'cash' THEN
        RAISE EXCEPTION 'Cash balance cannot be negative. Current: %, Attempted: %', OLD.balance, NEW.balance;
    END IF;

    -- Log significant balance changes
    IF ABS(NEW.balance - OLD.balance) > 1000 THEN
        INSERT INTO wallet_transactions (
            user_id, transaction_type, amount, description, reference_type
        ) VALUES (
            NEW.user_id,
            CASE WHEN NEW.balance > OLD.balance THEN 'deposit' ELSE 'withdrawal' END,
            ABS(NEW.balance - OLD.balance),
            'Automatic balance adjustment',
            'balance_trigger'
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create balance validation trigger
DROP TRIGGER IF EXISTS trigger_validate_balance ON user_balances;
CREATE TRIGGER trigger_validate_balance
    BEFORE UPDATE ON user_balances
    FOR EACH ROW
    WHEN (OLD.balance IS DISTINCT FROM NEW.balance)
    EXECUTE FUNCTION trigger_validate_balance_change();

-- =============================================================================
-- SECTION 9: AUDIT LOGGING AND SECURITY ENHANCEMENTS
-- =============================================================================

-- Create audit log table for financial transactions
CREATE TABLE IF NOT EXISTS financial_audit_log (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    table_name VARCHAR(50) NOT NULL,
    operation VARCHAR(10) NOT NULL CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')),
    old_values JSONB,
    new_values JSONB,
    changed_fields TEXT[],
    transaction_id UUID, -- Reference to the transaction that caused this change
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for audit log
CREATE INDEX IF NOT EXISTS idx_audit_log_user_id ON financial_audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_log_table_operation ON financial_audit_log(table_name, operation);
CREATE INDEX IF NOT EXISTS idx_audit_log_created_at ON financial_audit_log(created_at);

-- Audit trigger function
CREATE OR REPLACE FUNCTION trigger_financial_audit()
RETURNS TRIGGER AS $$
DECLARE
    v_old_values JSONB;
    v_new_values JSONB;
    v_changed_fields TEXT[] := '{}';
    v_field_name TEXT;
BEGIN
    -- Convert OLD and NEW to JSONB
    IF TG_OP = 'DELETE' THEN
        v_old_values := to_jsonb(OLD);
        v_new_values := NULL;
    ELSIF TG_OP = 'INSERT' THEN
        v_old_values := NULL;
        v_new_values := to_jsonb(NEW);
    ELSE -- UPDATE
        v_old_values := to_jsonb(OLD);
        v_new_values := to_jsonb(NEW);

        -- Find changed fields
        FOR v_field_name IN SELECT jsonb_object_keys(v_new_values)
        LOOP
            IF v_old_values->>v_field_name IS DISTINCT FROM v_new_values->>v_field_name THEN
                v_changed_fields := array_append(v_changed_fields, v_field_name);
            END IF;
        END LOOP;
    END IF;

    -- Insert audit record
    INSERT INTO financial_audit_log (
        user_id, table_name, operation, old_values, new_values, changed_fields
    ) VALUES (
        COALESCE(NEW.user_id, OLD.user_id),
        TG_TABLE_NAME,
        TG_OP,
        v_old_values,
        v_new_values,
        v_changed_fields
    );

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create audit triggers for financial tables
DROP TRIGGER IF EXISTS audit_user_balances ON user_balances;
CREATE TRIGGER audit_user_balances
    AFTER INSERT OR UPDATE OR DELETE ON user_balances
    FOR EACH ROW
    EXECUTE FUNCTION trigger_financial_audit();

DROP TRIGGER IF EXISTS audit_wallet_transactions ON wallet_transactions;
CREATE TRIGGER audit_wallet_transactions
    AFTER INSERT OR UPDATE OR DELETE ON wallet_transactions
    FOR EACH ROW
    EXECUTE FUNCTION trigger_financial_audit();

DROP TRIGGER IF EXISTS audit_transactions ON transactions;
CREATE TRIGGER audit_transactions
    AFTER INSERT OR UPDATE OR DELETE ON transactions
    FOR EACH ROW
    EXECUTE FUNCTION trigger_financial_audit();

DROP TRIGGER IF EXISTS audit_portfolio_holdings ON portfolio_holdings;
CREATE TRIGGER audit_portfolio_holdings
    AFTER INSERT OR UPDATE OR DELETE ON portfolio_holdings
    FOR EACH ROW
    EXECUTE FUNCTION trigger_financial_audit();

-- Enable RLS on audit log (with existence check)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'financial_audit_log') THEN
        ALTER TABLE financial_audit_log ENABLE ROW LEVEL SECURITY;
    END IF;
END $$;

-- Audit log policies (users can only see their own audit records)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'financial_audit_log'
        AND policyname = 'Users can view their own audit log'
    ) THEN
        CREATE POLICY "Users can view their own audit log" ON financial_audit_log
          FOR SELECT USING (auth.uid() = user_id);
    END IF;
END $$;

-- =============================================================================
-- SECTION 10: DATA VALIDATION CONSTRAINTS
-- =============================================================================

-- Add additional constraints for data integrity
ALTER TABLE user_balances
ADD CONSTRAINT check_balance_reasonable CHECK (balance >= -10000 AND balance <= 10000000),
ADD CONSTRAINT check_currency_valid CHECK (currency IN ('USD', 'EUR', 'GBP', 'CAD'));

ALTER TABLE wallet_transactions
ADD CONSTRAINT check_amount_not_zero CHECK (amount != 0),
ADD CONSTRAINT check_amount_reasonable CHECK (amount >= -1000000 AND amount <= 1000000);

ALTER TABLE transactions
ADD CONSTRAINT check_shares_positive CHECK (shares > 0),
ADD CONSTRAINT check_price_positive CHECK (price > 0),
ADD CONSTRAINT check_total_amount_positive CHECK (total_amount > 0);

ALTER TABLE portfolio_holdings
ADD CONSTRAINT check_shares_non_negative CHECK (shares >= 0),
ADD CONSTRAINT check_average_cost_non_negative CHECK (average_cost >= 0);

-- =============================================================================
-- SECTION 11: FINAL SETUP AND INITIALIZATION
-- =============================================================================

-- Function to initialize user portfolio system
CREATE OR REPLACE FUNCTION initialize_user_portfolio_system(p_user_id UUID)
RETURNS UUID AS $$
DECLARE
    v_portfolio_id UUID;
BEGIN
    -- Create default portfolio if none exists
    IF NOT EXISTS (SELECT 1 FROM portfolios WHERE user_id = p_user_id) THEN
        INSERT INTO portfolios (user_id, name, description, is_default, initial_value)
        VALUES (p_user_id, 'My Portfolio', 'Default investment portfolio', true, 0.00)
        RETURNING id INTO v_portfolio_id;
    ELSE
        SELECT id INTO v_portfolio_id
        FROM portfolios
        WHERE user_id = p_user_id AND is_default = true
        LIMIT 1;
    END IF;

    -- Initialize user balance if none exists (start at $0)
    INSERT INTO user_balances (user_id, balance, balance_type, available_for_investment)
    VALUES (p_user_id, 0.00, 'cash', 0.00)
    ON CONFLICT (user_id, currency) DO NOTHING;

    -- Create initial performance record
    PERFORM get_cached_portfolio_performance(v_portfolio_id, 'daily_performance');

    RETURN v_portfolio_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permission for initialization function
GRANT EXECUTE ON FUNCTION initialize_user_portfolio_system(UUID) TO authenticated;

-- Final permissions and grants
GRANT SELECT ON financial_audit_log TO authenticated;

-- Create helpful views for frontend
CREATE OR REPLACE VIEW user_portfolio_overview AS
SELECT
    p.id as portfolio_id,
    p.user_id,
    p.name as portfolio_name,
    p.total_value,
    p.initial_value,
    (p.total_value - p.initial_value) as total_return,
    CASE
        WHEN p.initial_value > 0 THEN
            ROUND(((p.total_value - p.initial_value) / p.initial_value * 100)::numeric, 2)
        ELSE 0
    END as total_return_percent,
    ub.balance as cash_balance,
    ub.total_invested,
    COUNT(ph.id) as holdings_count,
    p.last_performance_update,
    p.created_at
FROM portfolios p
LEFT JOIN user_balances ub ON p.user_id = ub.user_id AND ub.balance_type = 'cash'
LEFT JOIN portfolio_holdings ph ON p.id = ph.portfolio_id AND ph.shares > 0
GROUP BY p.id, p.user_id, p.name, p.total_value, p.initial_value,
         ub.balance, ub.total_invested, p.last_performance_update, p.created_at;

-- Grant access to the view
GRANT SELECT ON user_portfolio_overview TO authenticated;

-- Enable security barrier on the view (views cannot have RLS policies, but security barrier helps)
ALTER VIEW user_portfolio_overview SET (security_barrier = true);

-- Note: Views inherit security from their underlying tables
-- The user_portfolio_overview view is secure because:
-- 1. portfolios table has RLS enabled with user-based policies
-- 2. user_balances table has RLS enabled with user-based policies
-- 3. portfolio_holdings table has RLS enabled with portfolio-based policies
-- 4. The view joins these tables, so only user's own data will be visible
