"use client"

import React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Slider } from "@/components/ui/slider"
import { Progress } from "@/components/ui/progress"
import { Input } from "@/components/ui/input"
import { ArrowLeft, ArrowRight, Target, Clock, Shield, User, Lightbulb, DollarSign, CheckCircle } from "lucide-react"

export interface SurveyData {
  primaryGoal: string
  timeHorizon: string
  riskTolerance: number
  experienceLevel: string
  interestedThemes: string[]
  monthlyInvestment: number
  major?: string
}

interface OnboardingSurveyProps {
  onComplete: (data: SurveyData) => void
}

const SURVEY_STEPS = [
  { title: "Investment Goals", icon: Target, color: "text-blue-400" },
  { title: "Time Horizon", icon: Clock, color: "text-purple-400" },
  { title: "Risk Tolerance", icon: Shield, color: "text-orange-400" },
  { title: "Experience Level", icon: User, color: "text-green-400" },
  { title: "Investment Themes", icon: Lightbulb, color: "text-yellow-400" },
  { title: "Monthly Investment", icon: DollarSign, color: "text-emerald-400" },
  { title: "Review", icon: CheckCircle, color: "text-cyan-400" },
]

export function OnboardingSurvey({ onComplete }: OnboardingSurveyProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [isManualInput, setIsManualInput] = useState(false)
  const [manualInputValue, setManualInputValue] = useState("")
  const [surveyData, setSurveyData] = useState<SurveyData>({
    primaryGoal: "",
    timeHorizon: "",
    riskTolerance: 3,
    experienceLevel: "",
    interestedThemes: [],
    monthlyInvestment: 500,
    major: "computer-science",
  })

  const handleNext = () => {
    if (currentStep < SURVEY_STEPS.length - 1) {
      setCurrentStep(currentStep + 1)
      // Reset manual input when changing steps
      setIsManualInput(false)
      setManualInputValue("")
    }
  }

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
      // Reset manual input when changing steps
      setIsManualInput(false)
      setManualInputValue("")
    }
  }

  const handleManualInputClick = () => {
    setIsManualInput(true)
    setManualInputValue(surveyData.monthlyInvestment.toString())
  }

  const handleManualInputSave = () => {
    const value = parseInt(manualInputValue)
    if (!isNaN(value) && value >= 10 && value <= 5000) {
      setSurveyData({ ...surveyData, monthlyInvestment: value })
      setIsManualInput(false)
      setManualInputValue("")
    } else {
      // Show error or reset to valid range
      if (value < 10) {
        setSurveyData({ ...surveyData, monthlyInvestment: 10 })
      } else if (value > 5000) {
        setSurveyData({ ...surveyData, monthlyInvestment: 5000 })
      }
      setIsManualInput(false)
      setManualInputValue("")
    }
  }

  const handleManualInputCancel = () => {
    setIsManualInput(false)
    setManualInputValue("")
  }

  const handleManualInputKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleManualInputSave()
    } else if (e.key === 'Escape') {
      handleManualInputCancel()
    }
  }

  const handleComplete = () => {
    // Validate survey data before completing
    const validatedData: SurveyData = {
      primaryGoal: surveyData.primaryGoal || "Long-term wealth building",
      timeHorizon: surveyData.timeHorizon || "5–10 years",
      riskTolerance: surveyData.riskTolerance || 3,
      experienceLevel: surveyData.experienceLevel || "Beginner",
      interestedThemes:
        surveyData.interestedThemes.length > 0
          ? surveyData.interestedThemes
          : ["ETFs / Index Funds", "Blue-chip Stocks"],
      monthlyInvestment: surveyData.monthlyInvestment || 500,
      major: surveyData.major || "computer-science",
    }

    console.log("Survey completed with data:", validatedData)
    onComplete(validatedData)
  }

  const canProceed = () => {
    switch (currentStep) {
      case 0:
        return surveyData.primaryGoal !== ""
      case 1:
        return surveyData.timeHorizon !== ""
      case 2:
        return true // Risk tolerance always has a value
      case 3:
        return surveyData.experienceLevel !== ""
      case 4:
        return surveyData.interestedThemes.length > 0
      case 5:
        return surveyData.monthlyInvestment > 0
      case 6:
        return true // Review step
      default:
        return false
    }
  }

  const progress = ((currentStep + 1) / SURVEY_STEPS.length) * 100

  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="space-y-6">
            <div className="text-center space-y-4 mb-8">
              <h2 className="text-3xl font-bold text-white">What's your primary investment goal?</h2>
              <p className="text-lg text-slate-300">This helps us understand what you're trying to achieve.</p>
            </div>
            <RadioGroup
              value={surveyData.primaryGoal}
              onValueChange={(value) => setSurveyData({ ...surveyData, primaryGoal: value })}
              className="space-y-4"
            >
              {[
                {
                  value: "Long-term wealth building",
                  label: "Long-term wealth building",
                  desc: "Growing wealth over many years",
                },
                { value: "Retirement planning", label: "Retirement planning", desc: "Saving for retirement" },
                {
                  value: "Saving for a major purchase",
                  label: "Saving for a major purchase",
                  desc: "House, car, or other big expense",
                },
                {
                  value: "Generating passive income",
                  label: "Generating passive income",
                  desc: "Regular dividends and income",
                },
                {
                  value: "Learning about investing",
                  label: "Learning about investing",
                  desc: "Educational and experimental",
                },
              ].map((option) => (
                <div
                  key={option.value}
                  className="flex items-start space-x-3 p-4 rounded-lg border border-slate-700 hover:border-slate-600 transition-colors hover:border-emerald-500/50 hover:bg-emerald-500/5"
                >
                  <RadioGroupItem value={option.value} id={option.value} className="mt-1" />
                  <div className="flex-1">
                    <Label htmlFor={option.value} className="text-white font-medium cursor-pointer">
                      {option.label}
                    </Label>
                    <p className="text-sm text-slate-400 mt-1">{option.desc}</p>
                  </div>
                </div>
              ))}
            </RadioGroup>
          </div>
        )

      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center space-y-4 mb-8">
              <h2 className="text-3xl font-bold text-white">What's your investment time horizon?</h2>
              <p className="text-lg text-slate-300">How long do you plan to keep your money invested?</p>
            </div>
            <RadioGroup
              value={surveyData.timeHorizon}
              onValueChange={(value) => setSurveyData({ ...surveyData, timeHorizon: value })}
              className="space-y-4"
            >
              {[
                { value: "Less than 1 year", label: "Less than 1 year", desc: "Short-term goals" },
                { value: "1–3 years", label: "1–3 years", desc: "Medium-term planning" },
                { value: "3–5 years", label: "3–5 years", desc: "Moderate long-term" },
                { value: "5–10 years", label: "5–10 years", desc: "Long-term growth" },
                { value: "10+ years", label: "10+ years", desc: "Maximum growth potential" },
              ].map((option) => (
                <div
                  key={option.value}
                  className="flex items-start space-x-3 p-4 rounded-lg border border-slate-700 hover:border-slate-600 transition-colors hover:border-emerald-500/50 hover:bg-emerald-500/5"
                >
                  <RadioGroupItem value={option.value} id={option.value} className="mt-1" />
                  <div className="flex-1">
                    <Label htmlFor={option.value} className="text-white font-medium cursor-pointer">
                      {option.label}
                    </Label>
                    <p className="text-sm text-slate-400 mt-1">{option.desc}</p>
                  </div>
                </div>
              ))}
            </RadioGroup>
          </div>
        )

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center space-y-4 mb-8">
              <h2 className="text-3xl font-bold text-white">What's your risk tolerance?</h2>
              <p className="text-lg text-slate-300">How comfortable are you with investment volatility?</p>
            </div>
            <div className="space-y-6">
              <div className="px-4">
                <Slider
                  value={[surveyData.riskTolerance]}
                  onValueChange={(value) => setSurveyData({ ...surveyData, riskTolerance: value[0] })}
                  max={5}
                  min={1}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-slate-400 mt-2">
                  <span>Conservative</span>
                  <span>Moderate</span>
                  <span>Aggressive</span>
                </div>
              </div>
              <div className="bg-gradient-to-r from-slate-800/50 to-emerald-800/20 p-4 rounded-lg border border-emerald-500/20">
                <h3 className="text-white font-medium mb-2">
                  {surveyData.riskTolerance === 1 && "Very Conservative Approach"}
                  {surveyData.riskTolerance === 2 && "Conservative Approach"}
                  {surveyData.riskTolerance === 3 && "Moderate Approach"}
                  {surveyData.riskTolerance === 4 && "Aggressive Approach"}
                  {surveyData.riskTolerance === 5 && "Very Aggressive Approach"}
                </h3>
                <p className="text-slate-400 text-sm">
                  {surveyData.riskTolerance === 1 &&
                    "I can't afford to lose any money right now. Focus on capital preservation with minimal risk and steady returns."}
                  {surveyData.riskTolerance === 2 &&
                    "I prefer stability over growth. Focus on capital preservation with steady, predictable returns. Lower volatility but potentially lower returns."}
                  {surveyData.riskTolerance === 3 &&
                    "I want a balanced approach between growth and stability. Moderate volatility with reasonable return expectations."}
                  {surveyData.riskTolerance === 4 &&
                    "I'm comfortable with market fluctuations for higher returns. Emphasis on growth potential with higher volatility."}
                  {surveyData.riskTolerance === 5 &&
                    "I'm willing to take significant risks for maximum growth potential. High volatility but potential for highest returns over time."}
                </p>
              </div>
            </div>
          </div>
        )

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center space-y-4 mb-8">
              <h2 className="text-3xl font-bold text-white">What's your investment experience?</h2>
              <p className="text-lg text-slate-300">This helps us tailor recommendations to your knowledge level.</p>
            </div>
            <RadioGroup
              value={surveyData.experienceLevel}
              onValueChange={(value) => setSurveyData({ ...surveyData, experienceLevel: value })}
              className="space-y-4"
            >
              {[
                { value: "Beginner", label: "Beginner", desc: "New to investing" },
                { value: "Some experience", label: "Some experience", desc: "Basic knowledge and some investments" },
                { value: "Experienced", label: "Experienced", desc: "Comfortable with various investment types" },
                { value: "Expert", label: "Expert", desc: "Advanced knowledge and active trader" },
              ].map((option) => (
                <div
                  key={option.value}
                  className="flex items-start space-x-3 p-4 rounded-lg border border-slate-700 hover:border-slate-600 transition-colors hover:border-emerald-500/50 hover:bg-emerald-500/5"
                >
                  <RadioGroupItem value={option.value} id={option.value} className="mt-1" />
                  <div className="flex-1">
                    <Label htmlFor={option.value} className="text-white font-medium cursor-pointer">
                      {option.label}
                    </Label>
                    <p className="text-sm text-slate-400 mt-1">{option.desc}</p>
                  </div>
                </div>
              ))}
            </RadioGroup>
          </div>
        )

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center space-y-4 mb-8">
              <h2 className="text-3xl font-bold text-white">Which investment themes interest you?</h2>
              <p className="text-lg text-slate-300">Select all that apply. We'll weight your portfolio accordingly.</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {[
                { value: "Tech & AI", label: "Technology & AI", desc: "Software, hardware, artificial intelligence" },
                { value: "Green Energy", label: "Green Energy", desc: "Solar, wind, electric vehicles" },
                {
                  value: "Healthcare & Biotech",
                  label: "Healthcare & Biotech",
                  desc: "Pharmaceuticals, medical devices",
                },
                { value: "Real Estate", label: "Real Estate", desc: "REITs and property investments" },
                { value: "ETFs / Index Funds", label: "ETFs / Index Funds", desc: "Diversified, low-cost investing" },
                { value: "Blue-chip Stocks", label: "Blue-chip Stocks", desc: "Established, stable companies" },
                { value: "Dividend Income", label: "Dividend Income", desc: "Regular income-generating stocks" },
              ].map((theme) => (
                <div
                  key={theme.value}
                  className="flex items-start space-x-3 p-4 rounded-lg border border-slate-700 hover:border-slate-600 transition-colors hover:border-blue-500/50 hover:bg-blue-500/5"
                >
                  <Checkbox
                    id={theme.value}
                    checked={surveyData.interestedThemes.includes(theme.value)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setSurveyData({
                          ...surveyData,
                          interestedThemes: [...surveyData.interestedThemes, theme.value],
                        })
                      } else {
                        setSurveyData({
                          ...surveyData,
                          interestedThemes: surveyData.interestedThemes.filter((t) => t !== theme.value),
                        })
                      }
                    }}
                    className="mt-1"
                  />
                  <div className="flex-1">
                    <Label htmlFor={theme.value} className="text-white font-medium cursor-pointer">
                      {theme.label}
                    </Label>
                    <p className="text-sm text-slate-400 mt-1">{theme.desc}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )

      case 5:
        return (
          <div className="space-y-6">
            <div className="text-center space-y-4 mb-8">
              <h2 className="text-3xl font-bold text-white">How much do you plan to invest monthly?</h2>
              <p className="text-lg text-slate-300">This helps us understand your investment capacity.</p>
            </div>
            <div className="space-y-6">
              <div className="px-4">
                <Slider
                  value={[surveyData.monthlyInvestment]}
                  onValueChange={(value) => setSurveyData({ ...surveyData, monthlyInvestment: value[0] })}
                  max={5000}
                  min={10}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-slate-400 mt-2">
                  <span>$10</span>
                  <span>$2,500</span>
                  <span>$5,000+</span>
                </div>
              </div>
              <div className="bg-slate-800/50 p-4 rounded-lg text-center">
                {isManualInput ? (
                  <div className="space-y-3">
                    <Input
                      type="number"
                      value={manualInputValue}
                      onChange={(e) => setManualInputValue(e.target.value)}
                      onKeyDown={handleManualInputKeyPress}
                      min={10}
                      max={5000}
                      className="text-center text-2xl font-bold bg-slate-700 border-emerald-400 text-emerald-400 max-w-xs mx-auto"
                      placeholder="Enter amount"
                      autoFocus
                    />
                    <div className="flex gap-2 justify-center">
                      <Button
                        size="sm"
                        onClick={handleManualInputSave}
                        className="bg-emerald-500 hover:bg-emerald-600"
                      >
                        Save
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleManualInputCancel}
                        className="border-slate-600"
                      >
                        Cancel
                      </Button>
                    </div>
                    <p className="text-slate-400 text-sm">per month</p>
                  </div>
                ) : (
                  <div
                    className="cursor-pointer hover:bg-slate-700/50 p-2 rounded-lg transition-colors"
                    onClick={handleManualInputClick}
                  >
                    <div className="text-3xl font-bold text-emerald-400 mb-2">
                      ${surveyData.monthlyInvestment.toLocaleString()}
                    </div>
                    <p className="text-slate-400 text-sm">per month</p>
                    <p className="text-slate-500 text-xs mt-1">Click to edit manually</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )

      case 6:
        return (
          <div className="space-y-6">
            <div className="text-center space-y-4 mb-8">
              <h2 className="text-3xl font-bold text-white">Review Your Preferences</h2>
              <p className="text-lg text-slate-300">
                Make sure everything looks correct before we generate your portfolio. Click any section to edit.
              </p>
            </div>
            <div className="space-y-4">
              <button
                onClick={() => setCurrentStep(0)}
                className="w-full bg-slate-800/50 p-4 rounded-lg hover:bg-slate-700/50 transition-colors text-left border border-transparent hover:border-emerald-500/30"
              >
                <h3 className="text-white font-medium mb-2 flex items-center justify-between">
                  Investment Goal
                  <span className="text-emerald-400 text-sm">Edit</span>
                </h3>
                <p className="text-slate-400">{surveyData.primaryGoal}</p>
              </button>
              <button
                onClick={() => setCurrentStep(1)}
                className="w-full bg-slate-800/50 p-4 rounded-lg hover:bg-slate-700/50 transition-colors text-left border border-transparent hover:border-emerald-500/30"
              >
                <h3 className="text-white font-medium mb-2 flex items-center justify-between">
                  Time Horizon
                  <span className="text-emerald-400 text-sm">Edit</span>
                </h3>
                <p className="text-slate-400">{surveyData.timeHorizon}</p>
              </button>
              <button
                onClick={() => setCurrentStep(2)}
                className="w-full bg-slate-800/50 p-4 rounded-lg hover:bg-slate-700/50 transition-colors text-left border border-transparent hover:border-emerald-500/30"
              >
                <h3 className="text-white font-medium mb-2 flex items-center justify-between">
                  Risk Tolerance
                  <span className="text-emerald-400 text-sm">Edit</span>
                </h3>
                <p className="text-slate-400">
                  {surveyData.riskTolerance <= 2 && "Conservative"}
                  {surveyData.riskTolerance === 3 && "Moderate"}
                  {surveyData.riskTolerance >= 4 && "Aggressive"} ({surveyData.riskTolerance}/5)
                </p>
              </button>
              <button
                onClick={() => setCurrentStep(3)}
                className="w-full bg-slate-800/50 p-4 rounded-lg hover:bg-slate-700/50 transition-colors text-left border border-transparent hover:border-emerald-500/30"
              >
                <h3 className="text-white font-medium mb-2 flex items-center justify-between">
                  Experience Level
                  <span className="text-emerald-400 text-sm">Edit</span>
                </h3>
                <p className="text-slate-400">{surveyData.experienceLevel}</p>
              </button>
              <button
                onClick={() => setCurrentStep(4)}
                className="w-full bg-slate-800/50 p-4 rounded-lg hover:bg-slate-700/50 transition-colors text-left border border-transparent hover:border-emerald-500/30"
              >
                <h3 className="text-white font-medium mb-2 flex items-center justify-between">
                  Investment Themes
                  <span className="text-emerald-400 text-sm">Edit</span>
                </h3>
                <p className="text-slate-400">{surveyData.interestedThemes.join(", ")}</p>
              </button>
              <button
                onClick={() => setCurrentStep(5)}
                className="w-full bg-slate-800/50 p-4 rounded-lg hover:bg-slate-700/50 transition-colors text-left border border-transparent hover:border-emerald-500/30"
              >
                <h3 className="text-white font-medium mb-2 flex items-center justify-between">
                  Monthly Investment
                  <span className="text-emerald-400 text-sm">Edit</span>
                </h3>
                <p className="text-slate-400">${surveyData.monthlyInvestment.toLocaleString()}</p>
              </button>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto bg-slate-800/50 border-slate-700">
      <CardHeader className="pb-8">
        <div className="flex items-start justify-between mb-6">
          <div className="space-y-2">
            <CardTitle className="text-3xl font-bold text-white">Investment Profile Setup</CardTitle>
            <CardDescription className="text-lg text-slate-300 font-medium">
              Step {currentStep + 1} of {SURVEY_STEPS.length}: {SURVEY_STEPS[currentStep].title}
            </CardDescription>
          </div>
          <div className="flex items-center gap-3">
            <Progress value={progress} className="w-32 h-2" />
            <div className={`text-lg font-bold ${SURVEY_STEPS[currentStep].color} min-w-[60px] text-center`}>
              {Math.round(progress)}%
            </div>
          </div>
        </div>
        <div className="flex justify-center">
          <div className="flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br from-emerald-500/20 to-blue-500/20 border border-emerald-500/30">
            {React.createElement(SURVEY_STEPS[currentStep].icon, {
              className: `h-8 w-8 ${SURVEY_STEPS[currentStep].color}`,
            })}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-8">
        {renderStep()}

        <div className="flex justify-between pt-6 border-t border-slate-700">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentStep === 0}
            className="border-slate-600 text-slate-300 hover:bg-slate-700 bg-transparent"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>

          {currentStep === SURVEY_STEPS.length - 1 ? (
            <Button
              onClick={handleComplete}
              disabled={!canProceed()}
              className="bg-emerald-600 hover:bg-emerald-700 text-white"
            >
              Generate Portfolio
            </Button>
          ) : (
            <Button
              onClick={handleNext}
              disabled={!canProceed()}
              className="bg-emerald-600 hover:bg-emerald-700 text-white"
            >
              Next
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
