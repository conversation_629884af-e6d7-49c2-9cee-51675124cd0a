import { NextRequest, NextResponse } from 'next/server'
import { Configuration, PlaidApi, PlaidEnvironments } from 'plaid'
import { createClient } from '@supabase/supabase-js'

// Initialize Plaid client
const configuration = new Configuration({
  basePath: PlaidEnvironments[process.env.PLAID_ENV as keyof typeof PlaidEnvironments] || PlaidEnvironments.sandbox,
  baseOptions: {
    headers: {
      'PLAID-CLIENT-ID': process.env.PLAID_CLIENT_ID,
      'PLAID-SECRET': process.env.PLAID_SECRET,
    },
  },
})

const client = new PlaidApi(configuration)

// Initialize Supabase
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''
)

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { webhook_type, webhook_code, item_id } = body
    
    console.log('Received Plaid webhook:', { webhook_type, webhook_code, item_id })
    
    if (webhook_type === 'TRANSACTIONS') {
      await handleTransactionWebhook(body)
    } else if (webhook_type === 'ITEM') {
      await handleItemWebhook(body)
    }
    
    return NextResponse.json({ received: true })
    
  } catch (error) {
    console.error('Plaid webhook error:', error)
    return NextResponse.json({ error: 'Webhook handler failed' }, { status: 500 })
  }
}

async function handleTransactionWebhook(webhookData: any) {
  const { webhook_code, item_id, new_transactions, removed_transactions } = webhookData
  
  try {
    // Get the linked account for this item
    const { data: linkedAccount, error: accountError } = await supabase
      .from('linked_accounts')
      .select('*')
      .eq('plaid_item_id', item_id)
      .eq('is_active', true)
      .single()
    
    if (accountError || !linkedAccount) {
      console.error('Linked account not found for item:', item_id)
      return
    }
    
    if (webhook_code === 'INITIAL_UPDATE' || webhook_code === 'HISTORICAL_UPDATE' || webhook_code === 'DEFAULT_UPDATE') {
      // Fetch new transactions
      const transactionsResponse = await client.transactionsGet({
        access_token: linkedAccount.plaid_access_token,
        start_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
        end_date: new Date(),
        count: 500,
      })
      
      const transactions = transactionsResponse.data.transactions
      
      for (const transaction of transactions) {
        // Only process debit transactions (purchases)
        if (transaction.amount > 0 && !transaction.pending) {
          await processTransaction(transaction, linkedAccount)
        }
      }
    }
    
    if (removed_transactions && removed_transactions.length > 0) {
      // Handle removed transactions
      for (const removedTransactionId of removed_transactions) {
        await supabase
          .from('transactions')
          .delete()
          .eq('plaid_transaction_id', removedTransactionId)
      }
    }
    
  } catch (error) {
    console.error('Error handling transaction webhook:', error)
  }
}

async function handleItemWebhook(webhookData: any) {
  const { webhook_code, item_id, error: itemError } = webhookData
  
  try {
    if (webhook_code === 'ERROR') {
      // Handle item errors (e.g., expired credentials)
      await supabase
        .from('linked_accounts')
        .update({ is_active: false })
        .eq('plaid_item_id', item_id)
      
      console.log('Item error, deactivated account:', item_id)
    }
    
  } catch (error) {
    console.error('Error handling item webhook:', error)
  }
}

async function processTransaction(transaction: any, linkedAccount: any) {
  try {
    // Check if transaction already exists
    const { data: existingTransaction } = await supabase
      .from('transactions')
      .select('id')
      .eq('plaid_transaction_id', transaction.transaction_id)
      .single()
    
    if (existingTransaction) {
      return // Transaction already processed
    }
    
    // Calculate roundup amount
    const roundupAmount = Math.ceil(transaction.amount) - transaction.amount
    
    // Only process if roundup amount is meaningful (> $0.01)
    if (roundupAmount < 0.01) {
      return
    }
    
    // Get user's roundup settings
    const { data: settings } = await supabase
      .from('roundup_settings')
      .select('*')
      .eq('user_id', linkedAccount.user_id)
      .single()
    
    if (!settings || !settings.auto_invest) {
      return // User has disabled auto-invest
    }
    
    // Apply multiplier and limits
    let finalRoundupAmount = roundupAmount * settings.multiplier
    finalRoundupAmount = Math.max(settings.minimum_roundup, finalRoundupAmount)
    finalRoundupAmount = Math.min(settings.maximum_roundup, finalRoundupAmount)
    
    // Store transaction
    const { data: storedTransaction, error: transactionError } = await supabase
      .from('transactions')
      .insert({
        user_id: linkedAccount.user_id,
        linked_account_id: linkedAccount.id,
        plaid_transaction_id: transaction.transaction_id,
        amount: transaction.amount,
        currency: transaction.iso_currency_code || 'USD',
        merchant_name: transaction.merchant_name || transaction.name,
        category: transaction.category,
        date: transaction.date,
        pending: transaction.pending,
        roundup_amount: finalRoundupAmount,
        roundup_processed: false,
      })
      .select()
      .single()
    
    if (transactionError) {
      console.error('Error storing transaction:', transactionError)
      return
    }
    
    // Create roundup record
    const { error: roundupError } = await supabase
      .from('roundups')
      .insert({
        user_id: linkedAccount.user_id,
        transaction_id: storedTransaction.id,
        linked_account_id: linkedAccount.id,
        original_amount: transaction.amount,
        roundup_amount: finalRoundupAmount,
        status: 'pending',
      })
    
    if (roundupError) {
      console.error('Error creating roundup:', roundupError)
      return
    }
    
    // Check if we should process batch transfer
    await checkAndProcessBatchTransfer(linkedAccount.user_id, linkedAccount.id, settings.batch_threshold)
    
  } catch (error) {
    console.error('Error processing transaction:', error)
  }
}

async function checkAndProcessBatchTransfer(userId: string, linkedAccountId: string, batchThreshold: number) {
  try {
    // Get pending roundups for this account
    const { data: pendingRoundups } = await supabase.rpc('get_pending_roundups_for_user', {
      p_user_id: userId,
      p_threshold: batchThreshold
    })
    
    if (pendingRoundups && pendingRoundups.length > 0) {
      for (const batch of pendingRoundups) {
        if (batch.linked_account_id === linkedAccountId) {
          // Process ACH transfer for this batch
          await initiateACHTransfer(userId, linkedAccountId, batch.total_amount)
          break
        }
      }
    }
    
  } catch (error) {
    console.error('Error checking batch transfer:', error)
  }
}

async function initiateACHTransfer(userId: string, linkedAccountId: string, amount: number) {
  try {
    // Create ACH transfer record
    const { data: achTransfer, error } = await supabase
      .from('ach_transfers')
      .insert({
        user_id: userId,
        linked_account_id: linkedAccountId,
        amount: amount,
        currency: 'USD',
        status: 'pending',
        transfer_type: 'roundup',
      })
      .select()
      .single()
    
    if (error) {
      console.error('Error creating ACH transfer record:', error)
      return
    }
    
    // Here you would integrate with your ACH processor (Dwolla, Stripe Connect, etc.)
    // For now, we'll simulate the transfer
    console.log(`Initiated ACH transfer: $${amount} for user ${userId}`)
    
    // Update roundups status to processed
    await supabase
      .from('roundups')
      .update({ 
        status: 'processed',
        processed_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .eq('linked_account_id', linkedAccountId)
      .eq('status', 'pending')
    
    // Update user balance
    await supabase.rpc('update_user_balance', {
      p_user_id: userId,
      p_amount: amount
    })
    
  } catch (error) {
    console.error('Error initiating ACH transfer:', error)
  }
}
