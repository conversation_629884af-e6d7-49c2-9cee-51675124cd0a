/**
 * LLM Portfolio System Tests
 */

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals'
import { 
  getLLMConfig, 
  validateLLMConfig, 
  getTestLLMConfig,
  checkLLMSystemHealth,
  resetLLMPortfolioEngine
} from '../index'
import { PromptTemplateBuilder } from '../prompt-template-builder'
import { PortfolioFallback } from '../portfolio-fallback'
import { generateEnhancedPortfolio } from '../../enhanced-portfolio-generator'
import type { SurveyData } from '@/components/onboarding-survey'

// Mock survey data for testing
const mockSurveyData: SurveyData = {
  primaryGoal: "Long-term wealth building",
  timeHorizon: "10+ years",
  riskTolerance: 3,
  experienceLevel: "Beginner",
  interestedThemes: ["Technology", "Healthcare"],
  monthlyInvestment: 1000,
  major: "computer-science"
}

describe('LLM Portfolio System', () => {
  beforeEach(() => {
    // Reset singleton before each test
    resetLLMPortfolioEngine()
  })

  afterEach(() => {
    // Clean up after each test
    resetLLMPortfolioEngine()
  })

  describe('Configuration', () => {
    it('should validate valid configuration', () => {
      const config = getTestLLMConfig()
      const validation = validateLLMConfig(config)
      
      expect(validation.isValid).toBe(true)
      expect(validation.errors).toHaveLength(0)
    })

    it('should reject invalid configuration', () => {
      const invalidConfig = {
        ...getTestLLMConfig(),
        llm: {
          ...getTestLLMConfig().llm,
          apiKey: '', // Invalid empty API key
          maxTokens: -1 // Invalid negative tokens
        }
      }
      
      const validation = validateLLMConfig(invalidConfig)
      
      expect(validation.isValid).toBe(false)
      expect(validation.errors.length).toBeGreaterThan(0)
    })
  })

  describe('PromptTemplateBuilder', () => {
    let promptBuilder: PromptTemplateBuilder

    beforeEach(() => {
      promptBuilder = new PromptTemplateBuilder()
    })

    it('should build a valid prompt from survey data', async () => {
      const prompt = await promptBuilder.buildPortfolioPrompt({
        surveyData: mockSurveyData,
        userId: 'test-user-123',
        userMajor: 'computer-science'
      })

      expect(prompt).toContain('expert financial advisor')
      expect(prompt).toContain('Long-term wealth building')
      expect(prompt).toContain('computer-science')
      expect(prompt).toContain('Technology')
      expect(prompt).toContain('Healthcare')
      expect(prompt).toContain('JSON')
    })

    it('should generate consistent prompt hashes', () => {
      const prompt = "Test prompt for hashing"
      const hash1 = promptBuilder.generatePromptHash(prompt)
      const hash2 = promptBuilder.generatePromptHash(prompt)
      
      expect(hash1).toBe(hash2)
      expect(hash1).toHaveLength(64) // SHA-256 hex length
    })

    it('should generate different hashes for different prompts', () => {
      const prompt1 = "First test prompt"
      const prompt2 = "Second test prompt"
      
      const hash1 = promptBuilder.generatePromptHash(prompt1)
      const hash2 = promptBuilder.generatePromptHash(prompt2)
      
      expect(hash1).not.toBe(hash2)
    })
  })

  describe('PortfolioFallback', () => {
    let fallback: PortfolioFallback

    beforeEach(() => {
      fallback = new PortfolioFallback(true)
    })

    it('should generate fallback portfolio', async () => {
      const portfolio = await fallback.generateFallbackPortfolio({
        surveyData: mockSurveyData,
        userId: 'test-user-123',
        userMajor: 'computer-science'
      })

      expect(portfolio).toBeDefined()
      expect(portfolio.allocations).toBeDefined()
      expect(portfolio.allocations.length).toBeGreaterThan(0)
      expect(portfolio.riskLevel).toBeDefined()
      expect(portfolio.expectedReturn).toBeDefined()
      expect(portfolio.strategy).toBeDefined()
      expect(portfolio.rationale).toContain('fallback')
    })

    it('should validate allocation percentages sum to 100', async () => {
      const portfolio = await fallback.generateFallbackPortfolio({
        surveyData: mockSurveyData,
        userId: 'test-user-123'
      })

      const totalAllocation = portfolio.allocations.reduce(
        (sum, alloc) => sum + alloc.allocation, 
        0
      )

      expect(Math.abs(totalAllocation - 100)).toBeLessThan(0.01)
    })

    it('should be available when enabled', () => {
      expect(fallback.isAvailable()).toBe(true)
    })

    it('should not be available when disabled', () => {
      const disabledFallback = new PortfolioFallback(false)
      expect(disabledFallback.isAvailable()).toBe(false)
    })
  })

  describe('Enhanced Portfolio Generator', () => {
    it('should generate portfolio with fallback when LLM unavailable', async () => {
      // This will use fallback since we don't have a real API key in tests
      const result = await generateEnhancedPortfolio(
        mockSurveyData,
        'test-user-123',
        'computer-science',
        {
          useLLM: true,
          fallbackToRules: true
        }
      )

      expect(result).toBeDefined()
      expect(result.portfolio).toBeDefined()
      expect(result.source).toBe('rules') // Should fallback to rules
      expect(result.confidence).toBeGreaterThan(0)
      expect(result.processingTimeMs).toBeGreaterThan(0)
      expect(result.metadata?.llmUsed).toBe(false)
    })

    it('should generate portfolio with rules only', async () => {
      const result = await generateEnhancedPortfolio(
        mockSurveyData,
        'test-user-123',
        'computer-science',
        {
          useLLM: false,
          fallbackToRules: true
        }
      )

      expect(result).toBeDefined()
      expect(result.portfolio).toBeDefined()
      expect(result.source).toBe('rules')
      expect(result.confidence).toBeGreaterThan(0)
      expect(result.metadata?.llmUsed).toBe(false)
    })

    it('should validate generated portfolio structure', async () => {
      const result = await generateEnhancedPortfolio(
        mockSurveyData,
        'test-user-123',
        'computer-science'
      )

      const portfolio = result.portfolio

      // Check required fields
      expect(portfolio.allocations).toBeDefined()
      expect(Array.isArray(portfolio.allocations)).toBe(true)
      expect(portfolio.riskLevel).toBeDefined()
      expect(portfolio.expectedReturn).toBeDefined()
      expect(portfolio.strategy).toBeDefined()
      expect(portfolio.rationale).toBeDefined()
      expect(portfolio.rebalanceFrequency).toBeDefined()

      // Check allocations
      expect(portfolio.allocations.length).toBeGreaterThan(0)
      
      portfolio.allocations.forEach((alloc, index) => {
        expect(alloc.symbol).toBeDefined()
        expect(alloc.name).toBeDefined()
        expect(typeof alloc.allocation).toBe('number')
        expect(alloc.allocation).toBeGreaterThan(0)
        expect(alloc.category).toBeDefined()
        expect(alloc.rationale).toBeDefined()
      })

      // Check total allocation
      const totalAllocation = portfolio.allocations.reduce(
        (sum, alloc) => sum + alloc.allocation, 
        0
      )
      expect(Math.abs(totalAllocation - 100)).toBeLessThan(0.01)
    })
  })

  describe('System Health Check', () => {
    it('should perform health check', async () => {
      const health = await checkLLMSystemHealth()

      expect(health).toBeDefined()
      expect(health.status).toBeDefined()
      expect(['healthy', 'degraded', 'unhealthy']).toContain(health.status)
      expect(health.checks).toBeDefined()
      expect(health.errors).toBeDefined()
      expect(Array.isArray(health.errors)).toBe(true)
    })
  })

  describe('Error Handling', () => {
    it('should handle invalid survey data gracefully', async () => {
      const invalidSurveyData = {
        ...mockSurveyData,
        riskTolerance: 10, // Invalid risk tolerance
        primaryGoal: '', // Empty goal
      }

      await expect(
        generateEnhancedPortfolio(
          invalidSurveyData as any,
          'test-user-123',
          'computer-science',
          { useLLM: false, fallbackToRules: true }
        )
      ).rejects.toThrow()
    })

    it('should handle missing user ID', async () => {
      await expect(
        generateEnhancedPortfolio(
          mockSurveyData,
          '', // Empty user ID
          'computer-science'
        )
      ).rejects.toThrow()
    })
  })

  describe('Performance', () => {
    it('should generate portfolio within reasonable time', async () => {
      const startTime = Date.now()
      
      const result = await generateEnhancedPortfolio(
        mockSurveyData,
        'test-user-123',
        'computer-science'
      )
      
      const endTime = Date.now()
      const actualTime = endTime - startTime

      expect(result.processingTimeMs).toBeLessThan(5000) // Should be under 5 seconds
      expect(actualTime).toBeLessThan(10000) // Total time under 10 seconds
    })
  })
})

// Integration tests would go here
describe('LLM Portfolio Integration Tests', () => {
  // These would test the full system with real API calls
  // Skip in CI/CD unless API keys are available
  
  it.skip('should generate portfolio using real LLM API', async () => {
    // This test would require real API keys and would be skipped in normal test runs
    // Only run when OPENAI_API_KEY is available and testing flag is set
  })
})
