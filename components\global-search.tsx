"use client"

import { useState, useEffect, useRef } from "react"
import { Search, TrendingUp, Building2, Tag, X, Home, BarChart3, <PERSON><PERSON><PERSON>, User, DollarSign } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { searchStocksByKeyword, STOCK_CATEGORIES, POPULAR_COMPANY_TAGS } from "@/lib/stock-classifier"
import { useRouter } from "next/navigation"

interface SearchResult {
  id: number
  symbol: string
  name: string
  price: number
  change: number
  changePercent: number
  category: string
  tags?: string[]
}

interface SiteResult {
  id: string
  title: string
  description: string
  path: string
  icon: React.ComponentType<{ className?: string }>
  category: 'page' | 'feature'
}

interface GlobalSearchProps {
  onClose?: () => void
  autoFocus?: boolean
}

// Site navigation data
const SITE_PAGES: SiteResult[] = [
  {
    id: 'dashboard',
    title: 'Dashboard',
    description: 'View your portfolio overview and market summary',
    path: '/dashboard',
    icon: Home,
    category: 'page'
  },
  {
    id: 'investments',
    title: 'Investments',
    description: 'Browse and manage your investment portfolio',
    path: '/investments',
    icon: BarChart3,
    category: 'page'
  },
  {
    id: 'search',
    title: 'Search Stocks',
    description: 'Find and research stocks and ETFs',
    path: '/search',
    icon: Search,
    category: 'feature'
  },
  {
    id: 'settings',
    title: 'Settings',
    description: 'Manage your account and preferences',
    path: '/settings',
    icon: Settings,
    category: 'page'
  },
  {
    id: 'onboarding',
    title: 'Onboarding',
    description: 'Set up your investment profile and goals',
    path: '/onboarding',
    icon: User,
    category: 'feature'
  }
]

export function GlobalSearch({ onClose, autoFocus = false }: GlobalSearchProps) {
  const [query, setQuery] = useState("")
  const [stockResults, setStockResults] = useState<SearchResult[]>([])
  const [siteResults, setSiteResults] = useState<SiteResult[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [activeTab, setActiveTab] = useState("all")
  const inputRef = useRef<HTMLInputElement>(null)
  const router = useRouter()

  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus()
    }
  }, [autoFocus])

  useEffect(() => {
    const performSearch = async () => {
      if (query.trim().length < 1) {
        setStockResults([])
        setSiteResults([])
        return
      }

      setIsLoading(true)
      try {
        const queryLower = query.trim().toLowerCase()

        // Search site pages
        const filteredSiteResults = SITE_PAGES.filter(page =>
          page.title.toLowerCase().includes(queryLower) ||
          page.description.toLowerCase().includes(queryLower)
        )
        setSiteResults(filteredSiteResults)

        // Search stocks (only if query is 2+ characters for performance)
        if (query.trim().length >= 2) {
          const searchResults = await searchStocksByKeyword(query.trim())
          setStockResults(searchResults)
        } else {
          setStockResults([])
        }
      } catch (error) {
        console.error("Search error:", error)
        setStockResults([])
        setSiteResults([])
      } finally {
        setIsLoading(false)
      }
    }

    const debounceTimer = setTimeout(performSearch, 300)
    return () => clearTimeout(debounceTimer)
  }, [query])

  const handleStockClick = (symbol: string) => {
    router.push(`/investments/${symbol.toLowerCase()}`)
    onClose?.()
  }

  const handleCategoryClick = (category: string) => {
    router.push(`/search?category=${encodeURIComponent(category)}`)
    onClose?.()
  }

  const handleTagClick = (tag: string) => {
    router.push(`/search?tag=${encodeURIComponent(tag)}`)
    onClose?.()
  }

  const clearSearch = () => {
    setQuery("")
    setResults([])
    inputRef.current?.focus()
  }

  return (
    <Card className="w-full max-w-4xl mx-auto bg-slate-800/95 backdrop-blur-sm border-slate-700">
      <CardContent className="p-0">
        <div className="flex items-center gap-3 px-6 py-4 border-b border-slate-700">
          <Search className="h-5 w-5 text-slate-400" />
          <Input
            ref={inputRef}
            placeholder="Search stocks, companies, or navigate to pages..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="border-0 bg-transparent text-white placeholder:text-slate-400 focus-visible:ring-0 text-lg"
          />
          {query && (
            <Button variant="ghost" size="sm" onClick={clearSearch} className="text-slate-400 hover:text-white">
              <X className="h-4 w-4" />
            </Button>
          )}
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose} className="text-slate-400 hover:text-white">
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3 bg-slate-800/50 border-b border-slate-700 rounded-none">
            <TabsTrigger value="all" className="data-[state=active]:bg-slate-700">
              All ({siteResults.length + stockResults.length})
            </TabsTrigger>
            <TabsTrigger value="stocks" className="data-[state=active]:bg-slate-700">
              Stocks ({stockResults.length})
            </TabsTrigger>
            <TabsTrigger value="pages" className="data-[state=active]:bg-slate-700">
              Pages ({siteResults.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="mt-0">
            <div className="max-h-96 overflow-y-auto">
              {isLoading ? (
                <div className="p-4 text-center text-slate-400">Searching...</div>
              ) : (siteResults.length === 0 && stockResults.length === 0) ? (
                <div className="p-4 text-center text-slate-400">
                  {query ? `No results found for "${query}"` : 'Start typing to search...'}
                </div>
              ) : (
                <div className="divide-y divide-slate-700">
                  {/* Site Results */}
                  {siteResults.map((result) => (
                    <div
                      key={result.id}
                      onClick={() => {
                        router.push(result.path)
                        onClose?.()
                      }}
                      className="p-4 hover:bg-slate-700/50 cursor-pointer transition-colors"
                    >
                      <div className="flex items-center gap-3">
                        <result.icon className="h-5 w-5 text-slate-400" />
                        <div className="flex-1">
                          <div className="text-white font-medium">{result.title}</div>
                          <div className="text-slate-400 text-sm">{result.description}</div>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {result.category}
                        </Badge>
                      </div>
                    </div>
                  ))}

                  {/* Stock Results */}
                  {stockResults.map((stock) => (
                    <div
                      key={stock.id}
                      onClick={() => handleStockClick(stock.symbol)}
                      className="p-4 hover:bg-slate-700/50 cursor-pointer transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3">
                            <span className="font-semibold text-white">{stock.symbol}</span>
                            <Badge variant="secondary" className="text-xs">
                              {stock.category}
                            </Badge>
                          </div>
                          <div className="text-slate-400 text-sm mt-1">{stock.name}</div>
                          {stock.tags && stock.tags.length > 0 && (
                            <div className="flex gap-1 mt-2">
                              {stock.tags.slice(0, 3).map((tag, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {typeof tag === "string" ? tag : String(tag)}
                                </Badge>
                              ))}
                            </div>
                          )}
                        </div>
                        <div className="text-right">
                          <div className="text-white font-medium">${stock.price.toFixed(2)}</div>
                          <div className={`text-sm ${stock.change >= 0 ? "text-green-400" : "text-red-400"}`}>
                            {stock.change >= 0 ? "+" : ""}
                            {stock.change.toFixed(2)} ({stock.changePercent.toFixed(2)}%)
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="stocks" className="mt-0">
            <div className="max-h-96 overflow-y-auto">
              {isLoading ? (
                <div className="p-4 text-center text-slate-400">Searching...</div>
              ) : stockResults.length === 0 ? (
                <div className="p-4 text-center text-slate-400">
                  {query ? `No stocks found for "${query}"` : 'Start typing to search stocks...'}
                </div>
              ) : (
                <div className="divide-y divide-slate-700">
                  {stockResults.map((stock) => (
                    <div
                      key={stock.id}
                      onClick={() => handleStockClick(stock.symbol)}
                      className="p-4 hover:bg-slate-700/50 cursor-pointer transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3">
                            <span className="font-semibold text-white">{stock.symbol}</span>
                            <Badge variant="secondary" className="text-xs">
                              {stock.category}
                            </Badge>
                          </div>
                          <div className="text-slate-400 text-sm mt-1">{stock.name}</div>
                          {stock.tags && stock.tags.length > 0 && (
                            <div className="flex gap-1 mt-2">
                              {stock.tags.slice(0, 3).map((tag, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {typeof tag === "string" ? tag : String(tag)}
                                </Badge>
                              ))}
                            </div>
                          )}
                        </div>
                        <div className="text-right">
                          <div className="text-white font-medium">${stock.price.toFixed(2)}</div>
                          <div className={`text-sm ${stock.change >= 0 ? "text-green-400" : "text-red-400"}`}>
                            {stock.change >= 0 ? "+" : ""}
                            {stock.change.toFixed(2)} ({stock.changePercent.toFixed(2)}%)
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="pages" className="mt-0">
            <div className="max-h-96 overflow-y-auto">
              {siteResults.length === 0 ? (
                <div className="p-4 text-center text-slate-400">
                  {query ? `No pages found for "${query}"` : 'Start typing to search pages...'}
                </div>
              ) : (
                <div className="divide-y divide-slate-700">
                  {siteResults.map((result) => (
                    <div
                      key={result.id}
                      onClick={() => {
                        router.push(result.path)
                        onClose?.()
                      }}
                      className="p-4 hover:bg-slate-700/50 cursor-pointer transition-colors"
                    >
                      <div className="flex items-center gap-3">
                        <result.icon className="h-5 w-5 text-slate-400" />
                        <div className="flex-1">
                          <div className="text-white font-medium">{result.title}</div>
                          <div className="text-slate-400 text-sm">{result.description}</div>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {result.category}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

// Search trigger component for headers
export function SearchTrigger({ onClick }: { onClick: () => void }) {
  const [isSearchOpen, setIsSearchOpen] = useState(false)

  return (
    <>
      <Button
        variant="outline"
        onClick={() => setIsSearchOpen(true)}
        className="w-full max-w-sm justify-start text-slate-400 border-slate-700 hover:bg-slate-800 hover:text-white bg-transparent"
      >
        <Search className="h-4 w-4 mr-2" />
        Search stocks & pages...
      </Button>

      {isSearchOpen && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-start justify-center pt-20">
          <GlobalSearch onClose={() => setIsSearchOpen(false)} autoFocus />
        </div>
      )}
    </>
  )
}
